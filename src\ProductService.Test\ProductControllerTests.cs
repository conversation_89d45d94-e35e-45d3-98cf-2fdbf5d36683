﻿using Common.Models;
using Common.Services;
using Elastic.Apm.Model;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Moq;
using ProductService.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace ProductService.Test;
public class ProductControllerTests
{
    private Mock<IProductService> productServiceMock;
    private ProductController productController;


    public ProductControllerTests()
    {
        productServiceMock = new Mock<IProductService>();
        productController = new ProductController(productServiceMock.Object, null);
    }


    [Fact]
    public async Task GetProductsList_ReturnsOkResult()
    {
        // Arrange
        var request = new GetProductsListRequest()
        {
            Page = 1,
            Size = 10
        };

        // Act
        var result = await productController.GetProductsList(request);

        // Assert
        var okResult = result as OkObjectResult;
        Assert.Equal(200, okResult.StatusCode);
        Assert.NotNull(okResult);
    }
    [Fact]
    public async Task GetProductsList_ShouldThrowsException()
    {
        // Arrange
        var request = new GetProductsListRequest()
        {
            Page = 0,
            Size = 0,
            OrderFieldName = "NotColumnInDatabase",
        };

        // Act
        var result = await productController.GetProductsList(request);

        //Assert
        var ex = Assert.ThrowsAsync<Exception>(() => productController.GetProductsList(request));
    }

    [Fact]
    public async Task GetProductIdsAndNames_ShouldReturnOkResult_WithListOfProductNames()
    {
        // Arrange
        var expectedProductNames = new List<BasicProductsInfo>
    {
        new BasicProductsInfo { Id = Guid.NewGuid(), Name = "Product 1" },
        new BasicProductsInfo { Id = Guid.NewGuid(), Name = "Product 2" }
    };

        productServiceMock.Setup(service => service.GetProductsNames())
                          .ReturnsAsync(expectedProductNames);

        // Act
        var result = await productController.GetProductsNames();

        // Assert
        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);
        Assert.Equal(StatusCodes.Status200OK, okResult.StatusCode);
        Assert.Equal(expectedProductNames, okResult.Value);
    }

    [Fact]
    public async Task GetProductDetails_Should_ReturnOkResult()
    {
        //Arrange 
        Guid ProductId = new Guid();
        var expectedProduct = new ProductDetailsResponse { };

        //Act 
        productServiceMock.Setup(x => x.GetProductDetails(ProductId)).ReturnsAsync(expectedProduct);
        var result = await productController.GetProductDetails(ProductId);

        //Assert
        Assert.IsType<OkObjectResult>(result);
    }

    [Fact]
    public async Task GetProductsListByChannel_ShouldReturnProductsWithOnlyEnImages_WhenIsCNPTrue()
    {
        // Arrange
        var expectedProducts = new List<ProductDetailsResponse>
{
    new ProductDetailsResponse
    {
        Code = "P001",
        Name = "Product 1",
        IsCNP = true,
        ProductImages = new List<ProductImagesResponse>
        {
            new ProductImagesResponse { ImageId = Guid.NewGuid(), Language = "EN", DisplayOrder = 1 }
        }
    },
    new ProductDetailsResponse
    {
        Code = "P002",
        Name = "Product 2",
        IsCNP = true,
        ProductImages = new List<ProductImagesResponse>
        {
            new ProductImagesResponse { ImageId = Guid.NewGuid(), Language = "EN", DisplayOrder = 1 }
        }
    }
};

        productServiceMock
            .Setup(service => service.GetProductsListByChannel(true))
            .ReturnsAsync(expectedProducts);

        // Act
        var result = await productController.GetProductsListByChannel(true);

        // Assert
        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);
        Assert.Equal(StatusCodes.Status200OK, okResult.StatusCode);

        var returnedProducts = okResult.Value as List<ProductDetailsResponse>;
        Assert.NotNull(returnedProducts);
        Assert.All(returnedProducts, product =>
        {
            Assert.True(product.IsCNP);
            Assert.All(product.ProductImages, image => Assert.Equal("EN", image.Language));
        });
    }

    [Fact]
    public async Task GetProductsListByChannel_ShouldReturnProductsWithOnlyEnImages_WhenIsCNPFalse()
    {
        // Arrange
        var expectedProducts = new List<ProductDetailsResponse>
    {
        new ProductDetailsResponse
        {
            Code = "P003",
            Name = "Product 3",
            IsCNP = false,
            ProductImages = new List<ProductImagesResponse>
            {
                new ProductImagesResponse { ImageId = Guid.NewGuid(), Language = "EN", DisplayOrder = 1 }
            }
        }
    };

        productServiceMock
            .Setup(service => service.GetProductsListByChannel(false))
            .ReturnsAsync(expectedProducts);

        // Act
        var result = await productController.GetProductsListByChannel(false);

        // Assert
        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);
        Assert.Equal(StatusCodes.Status200OK, okResult.StatusCode);

        var returnedProducts = okResult.Value as List<ProductDetailsResponse>;
        Assert.NotNull(returnedProducts);
        Assert.All(returnedProducts, product =>
        {
            Assert.False(product.IsCNP);
            Assert.All(product.ProductImages, image => Assert.Equal("EN", image.Language));
        });
    }
}
