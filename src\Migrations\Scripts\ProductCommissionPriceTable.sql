
CREATE TABLE ProductCommissionPrice (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    ProductID UNIQUEIDENTIFIER NOT NULL,
    MCCID UNIQUEIDENTIFIER NOT NULL,
    BusinessTypeID UNIQUEIDENTIFIER NOT NULL,
    CommissionFeeID UNIQUEIDENTIFIER NOT NULL,
    FeeType INT NOT NULL,  
    FeeValue DECIMAL(9, 2) NOT NULL,  
    BillingType INT NOT NULL, 
    BillingFrequency INT NOT NULL,  
    CreatedBy VARCHAR(255) NOT NULL,
    CreatedDate DATETIME NOT NULL,
    UpdatedBy VARCHAR(255),
    UpdatedDate DATETIME,

    -- Adding Unique Constraint
    CONSTRAINT UQ_Product_MCC_BusinessType_CommissionFee UNIQUE (ProductID, MCCID, BusinessTypeID, CommissionFeeID),

    FOREIG<PERSON> KEY (ProductID) REFERENCES Products(Id),
    FOREIG<PERSON> KEY (MCCID) REFERENCES MCC(Id),
    <PERSON>OR<PERSON><PERSON><PERSON> (BusinessTypeID) REFERENCES BusinessTypes(Id),
    <PERSON><PERSON>EI<PERSON><PERSON> (CommissionFeeID) REFERENCES CommissionFees(Id)
);
