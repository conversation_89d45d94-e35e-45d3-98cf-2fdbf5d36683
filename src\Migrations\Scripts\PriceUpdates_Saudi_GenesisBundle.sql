﻿USE PRODUCTS;
GO
DECLARE @ProductId UNIQUEIDENTIFIER
DECLARE @GoFamily UNIQUEIDENTIFIER
DECLARE @VISAProductId UNIQUEIDENTIFIER
DECLARE @MCProductId UNIQUEIDENTIFIER
DECLARE @MadaProductId UNIQUEIDENTIFIER

BEGIN TRAN

SELECT TOP 1 @GoFamily = ID FROM Category where Code = 'GO_FAMILY' and Counterparty = 'GEIDEA_SAUDI';

EXEC NewProductVersion_v2 @ProductCode = 'GENESIS_SMART', @ProductVersion = 0, @NewProductCode = 'GENESIS_SMART', @NewProductVersion = 1, @MarkObsolete = 1, @Counterparty = 'GEIDEA_SAUDI'

--SELECT new productId
SELECT TOP 1 @ProductId = ID FROM Products where Code = 'GENESIS_SMART' and [Version] = 1 and CounterParty = 'GEIDEA_SAUDI'

--Set Sabb Referral Channel for Genesis_Smart bundle
UPDATE Products SET [ReferralChannel] = 'SABB' WHERE Id = @ProductId

--Delete copied ProductCategories mappings for new product
DELETE FROM ProductCategories where ProductId = @ProductId 

--Insert ProductCategories mapping for new product
INSERT INTO ProductCategories(ProductId, CategoryId) VALUES (@ProductId, @GoFamily)

--Create new VISA_GENESIS and MC_GENESIS and MADA_GENESIS schemes

EXEC NewProductVersion_v2 @ProductCode = 'VISA_GENESIS', @ProductVersion = 0, @NewProductCode = 'VISA_GENESIS', @NewProductVersion = 1, @MarkObsolete = 1, @Counterparty = 'GEIDEA_SAUDI'

EXEC NewProductVersion_v2 @ProductCode = 'MC_GENESIS', @ProductVersion = 0, @NewProductCode = 'MC_GENESIS', @NewProductVersion = 1, @MarkObsolete = 1, @Counterparty = 'GEIDEA_SAUDI'

EXEC NewProductVersion_v2 @ProductCode = 'MADA_GENESIS', @ProductVersion = 0, @NewProductCode = 'MADA_GENESIS', @NewProductVersion = 1, @MarkObsolete = 1, @Counterparty = 'GEIDEA_SAUDI'

--SELECT new VisaGenesisProductId
SELECT TOP 1 @VISAProductId = ID FROM Products where Code = 'VISA_GENESIS' and [Version] = 1 and CounterParty = 'GEIDEA_SAUDI'

--SELECT new MCGenesisProductId
SELECT TOP 1 @MCProductId = ID FROM Products where Code = 'MC_GENESIS' and [Version] = 1 and CounterParty = 'GEIDEA_SAUDI'

--SELECT new MadaGenesisProductId
SELECT TOP 1 @MadaProductId = ID FROM Products where Code = 'MADA_GENESIS' and [Version] = 1 and CounterParty = 'GEIDEA_SAUDI'

--UPDATE PercentagePrices to 275 for VISA and MC
UPDATE Prices SET [PercentagePrice] = 275 WHERE ProductId IN (@VISAProductId,@MCProductId)

--SET threshold fees for mada (0.7% below 100 SAR, 0,8% above 100 SAR)
UPDATE Prices SET [PercentagePrice] = 70, [Threshold] = 10000 WHERE ProductId = @MadaProductId

INSERT INTO [PRODUCTS].[dbo].[Prices] ([ChargeFrequency],[ChargeType],[ExemptFromVAT],[ProductId],[PercentagePrice],[Threshold],[Priority],[ValidFrom],[DeletedFlag],[CreatedBy],[CreatedDate],[UpdatedBy],[Currency])
VALUES ('ONE_OFF','PURCHASE_CP',0,@MadaProductId,80,10000,1,GETUTCDATE(),0,'00000000-0000-0000-0000-000000000000',GETUTCDATE(),'00000000-0000-0000-0000-000000000000','SAR')

--DELETE old card scheme from product parts
DELETE FROM [ProductParts] WHERE [ProductId] = @ProductId and [PartId] IN
(SELECT Id FROM [Products] WHERE [Code] IN ('VISA_GENESIS','MC_GENESIS','MADA_GENESIS') and [CounterParty] = 'GEIDEA_SAUDI')

--ADD new Product Parts
INSERT INTO [ProductParts] ([ProductId], [PartId], [Quantity]) VALUES (@ProductId, @VISAProductId, 1), (@ProductId, @MCProductId, 1), (@ProductId, @MadaProductId, 1)

COMMIT TRAN