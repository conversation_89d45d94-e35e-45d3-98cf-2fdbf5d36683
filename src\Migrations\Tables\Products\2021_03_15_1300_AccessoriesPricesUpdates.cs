﻿using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_03_15_1300)]
public class AccessoriesPricesUpdates : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "ProductUpdateProcedure_v4.sql"));

        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "AccessoriesPricesUpdates.sql"));
    }
}
