﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Common.Data.ProductType;
using Common.Entities;
using Common.Models.ProductInstance;
using Common.Repositories;
using Common.Services;
using Geidea.Utils.Common;
using Geidea.Utils.Counterparty.Providers;

namespace Services;

public class ProductChangeSenderService : IProductChangeSenderService
{
    private readonly IProductInstancePublisherSelector publisherSelector;
    private readonly IProductInstanceRepository productInstanceRepository;
    private readonly ICounterpartyProvider counterpartyProvider;

    public ProductChangeSenderService(IProductInstancePublisherSelector publisherSelector,
        IProductInstanceRepository productInstanceRepository,
        ICounterpartyProvider counterpartyProvider)
    {
        this.publisherSelector = publisherSelector;
        this.counterpartyProvider = counterpartyProvider;
        this.productInstanceRepository = productInstanceRepository;
    }

    public async Task SendCreatedEvent(ProductInstanceEntity productInstance)
    {
        var publisher = GetPublisher(productInstance.Product.Type);

        if (publisher is IGatewayProductInstancePublisher gatewayPublisher &&
            counterpartyProvider.GetCode() == Constants.CounterpartyEgypt &&
            productInstance.CompanyId != null)
        {
            var meezaProductInstace = await GetMeezaProductInstance(productInstance.CompanyId.Value);
            gatewayPublisher.PublishCreatedEvent(productInstance, meezaProductInstace);
        }
        else
            publisher?.PublishCreatedEvent(productInstance);
    }

    public void SendUpdatedEvent(ProductInstanceEntity productInstance)
    {
        var publisher = GetPublisher(productInstance.Product.Type);
        publisher?.PublishUpdatedEvent(productInstance);
    }

    public void SendDeletedEvent(Guid id, string productType)
    {
        var publisher = GetPublisher(productType);
        publisher?.PublishDeletedEvent(id);
    }

    private IProductInstancePublisher? GetPublisher(string productType)
    {
        var prodType = Enum.Parse<ProductTypes>(productType);
        return publisherSelector.GetProductInstancePublisher(prodType);
    }

    private async Task<ProductInstanceEntity?> GetMeezaProductInstance(Guid companyId)
    {
        var productInstanceRequest = new FindProductInstanceRequest()
        {
            CompanyId = companyId,
            Types = new string[] { "MEEZA" },
            Skip = 0,
            Take = 1,
        };

        var productInstanceResult = (await productInstanceRepository.FindAsync(productInstanceRequest))?.FirstOrDefault();

        return productInstanceResult;
    }
}
