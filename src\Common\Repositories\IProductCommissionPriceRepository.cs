﻿using Common.Entities;
using Common.Models.ProductCommissionPrice;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Repositories;
public interface IProductCommissionPriceRepository : IRepository<Guid, ProductCommissionPriceEntity>
{
    Task<List<ProductCommissionPriceEntity>> GetExistCommissionPrices(Expression<Func<ProductCommissionPriceEntity, bool>> predicate);
    Task SaveCommissionPricesAsync(List<ProductCommissionPriceEntity> CommissionPrices);
    Task<ProductCommissionPriceEntity?> GetByIdAsync(Guid id);
    Task<ProductCommissionPriceListResponse> GetProductCommissionPriceList(ProductCommissionPriceListRequest request);
    Task<List<ProductCommissionPriceEntity>> GetProductCommissionPricesByIdsAsync(List<Guid> ids);
    Task<int> DeleteBulkAsync(List<Guid> ids);
    Task AddLogsAsync(List<ProductCommissionPriceLogEntity> logs);
    Task<ProductCommissionPriceEntity?> GetCommissionPriceByIdAsync(Guid id);
    Task UpdateCommissionPricesAsync(List<ProductCommissionPriceEntity> productCommissionPriceEntity);
}
