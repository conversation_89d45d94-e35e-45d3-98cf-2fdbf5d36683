﻿using FluentMigrator;

namespace Migrations.Tables.Prices;

[Migration(2021_07_29_1340)]
public class Prices_AddPricesIndex_DeletedFlag : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(
            $@"IF NOT EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'IDX_Prices_DeletedFlag' AND object_id = OBJECT_ID('[dbo].[Prices]'))
                 BEGIN
                    CREATE NONCLUSTERED INDEX [IDX_Prices_DeletedFlag]
                                ON [dbo].[Prices] ([DeletedFlag])
                                INCLUDE ([CreatedDate])
                 END");
    }
}
