﻿using Common.Models.ProductInstance;
using FluentValidation;

namespace Common.Validators;

public class GenerateCsrValidator : AbstractValidator<GenerateCsrRequest>
{
    public GenerateCsrValidator()
    {
        RuleFor(x => x.ProductInstanceId).NotEmpty();
        RuleFor(x => x.CommonName).NotEmpty();
        RuleFor(x => x.Country).Matches("^[A-Za-z]{2}$");
        RuleFor(x => x.Email).EmailAddress();
        RuleFor(x => x.Location).NotEmpty();
        RuleFor(x => x.Organization).NotEmpty();
        RuleFor(x => x.OrganizationalUnit).NotEmpty();
        RuleFor(x => x.State).NotEmpty();
    }
}
