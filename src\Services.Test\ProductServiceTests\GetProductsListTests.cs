﻿using AutoMapper;
using Common.Models;
using Common.Repositories;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace Services.Test;
public class GetProductsListTests
{
    private readonly Mock<ILogger<ProductService>> logger = new Mock<ILogger<ProductService>>();
    private readonly Mock<IProductInstanceRepository> productInstanceRepository = new Mock<IProductInstanceRepository>();
    private readonly Mock<IProductRepository> productRepository = new Mock<IProductRepository>();
    private readonly Mock<ICategoryRepository> categoryRepository = new Mock<ICategoryRepository>();
    private readonly Mock<IMapper> mapper = new Mock<IMapper>();
    private readonly ProductService productService;

    public GetProductsListTests()
    {
        productService = new ProductService(logger.Object, productRepository.Object, mapper.Object, categoryRepository.Object, productInstanceRepository.Object);
    }
    [Fact]
    public async Task GetProductsList_ShouldCallRepository_AndReturnProductsList()
    {
        var request = new GetProductsListRequest()
        {
            Page = 1,
            Size = 10
        };

        var productListResponse = new ProductListResponse();
        productRepository.Setup(x => x.GetProductsList(request))
                         .ReturnsAsync(productListResponse);

        // Act
        var returnedProductsList = await productService.GetProductsList(request);
        returnedProductsList.Should().NotBeNull();
    }

    [Fact]
    public async Task GetProductIdsAndNamesAsync_ShouldReturnListOfProductNames_WhenRepositoryReturnsData()
    {
        // Arrange
        var expectedProductsNames = new List<BasicProductsInfo>
        {
            new BasicProductsInfo { Id = Guid.NewGuid(), Name = "Product 1" },
            new BasicProductsInfo { Id = Guid.NewGuid(), Name = "Product 2" }
        };

        productRepository.Setup(repo => repo.GetProductsNamesAsync())
                         .ReturnsAsync(expectedProductsNames);

        // Act
        var result = await productService.GetProductsNames();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(expectedProductsNames);
        productRepository.Verify(repo => repo.GetProductsNamesAsync(), Times.Once);
    }
}
