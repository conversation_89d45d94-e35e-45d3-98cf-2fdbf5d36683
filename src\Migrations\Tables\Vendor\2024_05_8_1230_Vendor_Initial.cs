﻿using FluentMigrator;

namespace Migrations.Tables.Vendor;

[Migration(2024_05_08_1230)]
public class Vendor : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("Vendor")
            .WithColumn("Id").AsGuid().NotNullable().PrimaryKey().WithDefaultValue(SystemMethods.NewGuid)
            .WithColumn("Name").AsString().NotNullable()
            .WithColumn("TerminaType").AsString().NotNullable()
            .WithColumn("Prefix").AsString().NotNullable()
            .WithColumn("CreatedBy").AsString(150).NotNullable()
            .WithColumn("CreatedDate").AsDateTime2().NotNullable()
            .WithColumn("UpdatedBy").AsString(150).Nullable()
            .WithColumn("UpdatedDate").AsDateTime2().Nullable();

        Create.Index("IX_Vendor_Name_TerminaType")
                       .OnTable("Vendor")
                       .OnColumn("Name").Ascending()
                       .OnColumn("TerminaType").Ascending()
                       .WithOptions().Unique();
    }

    [Migration(2024_05_13_1230)]
    public class UpdateVendorColumn : ForwardOnlyMigration
    {
        public override void Up()
        {
            Rename.Column("TerminaType").OnTable("Vendor").To("TerminalType");
        }
    }

    [Migration(2024_05_13_1240)]
    public class UpdateVendorIndex : ForwardOnlyMigration
    {
        public override void Up()
        {
            Delete.Index("IX_Vendor_Name_TerminaType").OnTable("Vendor");
            Create.Index("IX_Vendor_Name_TerminalType")
                  .OnTable("Vendor")
                  .OnColumn("Name").Ascending()
                  .OnColumn("TerminalType").Ascending()
                  .WithOptions().Unique();
        }
    }
}