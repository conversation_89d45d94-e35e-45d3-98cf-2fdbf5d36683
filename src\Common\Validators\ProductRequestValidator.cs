﻿using Common.Data.ProductType;
using Common.Models;
using FluentValidation;
using System.Collections.Generic;

namespace Common.Validators;

public class ProductRequestValidator : AbstractValidator<ProductRequest>
{
    public ProductRequestValidator()
    {
        var availabilityConditions = new List<string> {
                Constants.Availability.Live,
                Constants.Availability.Obsolete
            };

        var flowConditions = new List<string>
            {
                Constants.Flow.HelpRequired,
                Constants.Flow.Preorder,
                Constants.Flow.Normal
            };

        var salesChannelConditions = new List<string>
            {
                Constants.SalesChannel.All,
                Constants.SalesChannel.Shop,
                Constants.SalesChannel.Onboarding
            };


        RuleFor(a => a.Availability).NotEmpty().Must(a => availabilityConditions.Contains(a)).WithMessage("Please only use: " + string.Join(",", availabilityConditions));
        RuleFor(a => a.Flow).NotEmpty().Must(a => flowConditions.Contains(a)).WithMessage("Please only use: " + string.Join(",", flowConditions));
        RuleFor(a => a.SalesChannel)
       .Must(a => string.IsNullOrEmpty(a) || salesChannelConditions.Contains(a))
       .WithMessage("Please only use: " + string.Join(string.Empty, salesChannelConditions));
        RuleFor(a => a.Code).NotEmpty().Length(1, 255);
        RuleFor(a => a.Version).GreaterThanOrEqualTo(0);
        RuleFor(c => c.Type).Length(1, 255).NotEmpty()
            .IsEnumName(typeof(ProductTypes), caseSensitive: true).WithMessage(Errors.InvalidType.Message);


    }
}
