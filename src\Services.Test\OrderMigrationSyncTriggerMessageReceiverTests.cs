﻿using Common.Entities;
using Common.Models;
using Common.Repositories;
using Common.Services;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Messages.Gsdk.Messages;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Threading.Tasks;

namespace Services.Test;
public class OrderMigrationSyncTriggerMessageReceiverTests
{
    private IOrderMigrationSyncScriptClient messageClient;
    private ITerminalDataSetRepository terminalDataSetRepository;

    private ILogger<OrderMigrationSyncTriggerMessageReceiver> logger;
    private OrderMigrationSyncTriggerMessageReceiver messageReceiver;

    [SetUp]
    public void Setup()
    {
        terminalDataSetRepository = Substitute.For<ITerminalDataSetRepository>();

        var serviceProvider = Substitute.For<IServiceProvider>();
        serviceProvider.GetService<ITerminalDataSetRepository>().Returns(terminalDataSetRepository);
        var serviceScope = Substitute.For<IServiceScope>();
        serviceScope.ServiceProvider.Returns(serviceProvider);
        var serviceScopeFactory = Substitute.For<IServiceScopeFactory>();
        serviceScopeFactory.CreateScope().Returns(serviceScope);

        messageClient = Substitute.For<IOrderMigrationSyncScriptClient>();
        logger = Substitute.For<ILogger<OrderMigrationSyncTriggerMessageReceiver>>();

        messageReceiver = new OrderMigrationSyncTriggerMessageReceiver(serviceScopeFactory, messageClient, logger);
    }


    [Test]
    public void StartReceivingShouldSubscribeAndUnsubscribeSuccessfully()
    {
        messageReceiver.StartReceiving();

        messageClient.Received(1).OnOrderMigrationTriggerSyncScript += Arg.Any<EventHandler<CreateOrderMigrationSyncScriptMessageReceivedRequestEventArgs>>();
        messageClient.Received(1).Connect();

        messageReceiver.Dispose();

        messageClient.Received(1).OnOrderMigrationTriggerSyncScript -= Arg.Any<EventHandler<CreateOrderMigrationSyncScriptMessageReceivedRequestEventArgs>>();
    }


    [Test]
    public void StartReceivingTriggerStoredProcedureToSyncronizeDataBetweenTerminalDataAndOrderMigration()
    {
        var trigger = new CreateOrderMigrationSyncScriptRequest() { OrderNumbers = new System.Collections.Generic.List<string>() };
        terminalDataSetRepository.TriggerOrderMigrationSyncAsync();

        messageReceiver.StartReceiving();
        messageClient.OnOrderMigrationTriggerSyncScript += Raise.EventWith(new CreateOrderMigrationSyncScriptMessageReceivedRequestEventArgs(trigger));

        terminalDataSetRepository.Received(2).TriggerOrderMigrationSyncAsync();
    }
}
