﻿using Common.Entities;
using Common.Models.ProductCommissionPrice;
using Common.Models.UnitPrice;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using ProductService.Controllers;
using Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace ProductService.Test;
public class ProductCommissionPriceControllerTests
{
    private readonly Mock<IProductCommissionPriceService> ProductCommissionPriceServiceMock;
    private readonly ProductCommissionPriceController ProductCommissionPriceController;

    public ProductCommissionPriceControllerTests()
    {
        ProductCommissionPriceServiceMock = new Mock<IProductCommissionPriceService>();
        ProductCommissionPriceController = new ProductCommissionPriceController(ProductCommissionPriceServiceMock.Object);
    }
    [Fact]
    public async Task CreateAsync_ShouldReturn_OkObjectResult_With_PRoductCommissionPricrResponse()
    {
        // Arrange
        var request = new ProductCommissionPriceCreateRequest
        {
            ProductIds = new List<Guid> { Guid.NewGuid() },
            MccIds = new List<Guid> { Guid.NewGuid() },
            BusinessTypeIds = new List<Guid> { Guid.NewGuid() },
            CommissionFeeIds = new List<Guid> { Guid.NewGuid() },
            FeeType = Common.Enums.ProductCommisssionPrices.FeeType.Flat,
            FeeValue = 10,
            BillingType = Common.Enums.ProductCommisssionPrices.CommisssionPricesBillingType.PostPaid,
            BillingFrequency = Common.Enums.ProductCommisssionPrices.PriceBillingFrequency.Monthly
        };
        var ExpectedResponse = new ProductCommissionPriceResponse
        {
            NewCommissionPriceList = new List<ProductCommissionPriceEntity> { },
            NewExistedCommissionPriceList = new List<ProductCommissionPriceEntity> { }
        };
        //Mock
        ProductCommissionPriceServiceMock.Setup(s => s.CreateAsync(request))
                            .ReturnsAsync(ExpectedResponse);
        //Act 
        var Result = await ProductCommissionPriceController.CreateAsync(request);
        var OkResult = Result as OkObjectResult;
        //Assert
        Assert.NotNull(Result);
        Assert.Equal(((int)HttpStatusCode.OK), OkResult.StatusCode);
    }
    [Fact]
    public async Task UpdateAsync_ShouldReturn_OkObjectResult()
    {
        // Arrange
        var Id = Guid.NewGuid();
        var request = new ProductComissionPriceUpdateRequest
        {
            FeeType = Common.Enums.ProductCommisssionPrices.FeeType.Flat,
            FeeValue = 50,
            BillingType = Common.Enums.ProductCommisssionPrices.CommisssionPricesBillingType.PostPaid,
            BillingFrequency = Common.Enums.ProductCommisssionPrices.PriceBillingFrequency.Monthly
        };
        var ExpectedResponse = new ProductCommissionPriceDetailsResponse { };
        //Mock
        ProductCommissionPriceServiceMock.Setup(s => s.UpdateAsync(Id, request))
                                         .ReturnsAsync(ExpectedResponse);
        //Act 
        var Result = await ProductCommissionPriceController.UpdateAsync(Id, request);
        var OkResult = Result as OkObjectResult;
        //Assert
        Assert.NotNull(Result);
        Assert.Equal(((int)HttpStatusCode.OK), OkResult.StatusCode);
    }
    [Fact]
    public async Task UpdateAsync_Should_Return_NotFound_When_Entity_Not_Found()
    {
        // Arrange
        var Id = Guid.NewGuid();
        var request = new ProductComissionPriceUpdateRequest
        {
            FeeType = Common.Enums.ProductCommisssionPrices.FeeType.Flat,
            FeeValue = 50,
            BillingType = Common.Enums.ProductCommisssionPrices.CommisssionPricesBillingType.PostPaid,
            BillingFrequency = Common.Enums.ProductCommisssionPrices.PriceBillingFrequency.Monthly
        };
        //Mock
        ProductCommissionPriceServiceMock.Setup(service => service.UpdateAsync(Id, request))
                                         .ThrowsAsync(new ServiceException(HttpStatusCode.NotFound, "Commission price not found!"));
        //Act
        var exception = await Assert.ThrowsAsync<ServiceException>(() => ProductCommissionPriceController.UpdateAsync(Id, request));
        //Assert
        Assert.NotNull(exception);
        Assert.Equal(HttpStatusCode.NotFound, exception.StatusCode);
    }
    [Fact]
    public async Task GetProductCommissionPriceList_ShouldReturnOk_WhenRequestIsValid()
    {
        //Arrange
        ProductCommissionPriceListRequest request = new ProductCommissionPriceListRequest();
        var ExpectedResponse = new ProductCommissionPriceListResponse();
        //Mock
        ProductCommissionPriceServiceMock.Setup(s => s.GetProductCommissionPriceList(request))
                               .ReturnsAsync(ExpectedResponse);
        //Act
        var result = await ProductCommissionPriceController.ListAsync(request);
        var OkResult = result as OkObjectResult;
        //Assert
        Assert.NotNull(OkResult);
        Assert.IsType<OkObjectResult>(OkResult);
        Assert.Equal(StatusCodes.Status200OK, OkResult.StatusCode);
    }
    [Fact]
    public async Task GetProductCommissionPriceList_ShouldReturnServiceException_WhenRequestIsInvalid()
    {
        //Arrange
        ProductCommissionPriceListRequest request = null;
        //Mock
        ProductCommissionPriceServiceMock.Setup(s => s.GetProductCommissionPriceList(request))
                               .ThrowsAsync(new ServiceException(HttpStatusCode.BadRequest));
        //Act
        var exception = await Assert.ThrowsAsync<ServiceException>(() => ProductCommissionPriceController.ListAsync(request));
        //Assert
        Assert.NotNull(exception);
        Assert.Equal(HttpStatusCode.BadRequest, exception.StatusCode);
    }
}
