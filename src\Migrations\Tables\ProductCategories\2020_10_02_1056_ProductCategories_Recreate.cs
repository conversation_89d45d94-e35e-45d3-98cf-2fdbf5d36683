﻿using FluentMigrator;

namespace Migrations.Tables.ProductCategories;

[Migration(2020_10_02_1056)]
public class ProductCategories_Recreate : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("ProductCategories")
          .WithColumn("ProductId").AsGuid().NotNullable().PrimaryKey().ForeignKey("Products", "Id")
          .WithColumn("CategoryId").AsGuid().NotNullable().PrimaryKey().ForeignKey("Category", "Id")
          ;
    }
}
