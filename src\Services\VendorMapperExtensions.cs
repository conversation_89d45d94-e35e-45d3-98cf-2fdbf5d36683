﻿using Common.Entities;
using Common.Models.Vendor;
using System.Collections.Generic;
using System.Linq;

namespace Services;
static class VendorMapperExtensions
{
    public static VendorEntity mapToVendorEntity(this VendorRequest request)
    {
        VendorEntity vendorEntity = new VendorEntity
        {
            Name = request.Name,
            TerminalType = request.TerminalType,
            Prefix = string.Join(",", request.Prefix ?? new List<string>()),
        };
        return vendorEntity;
    }

    public static VendorResponse mapToVendorResponse(this VendorEntity entity)
    {
        VendorResponse vendorResponse = new VendorResponse
        {
            Name = entity.Name,
            TerminalType = entity.TerminalType,
            Prefix = entity.Prefix.Split(',').ToList(),
            IsDefault = entity.IsDefault,
        };
        return vendorResponse;
    }
}
