﻿using AutoMapper;
using Common;
using Common.Entities;
using Common.Models.Search;
using Common.Models.TerminalDataSets;
using Common.Repositories;
using Common.Services.Acquirers;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Models;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Net;
using System.Reflection;
using System.Threading.Tasks;
using static Common.Constants;
using UtilsConstants = Geidea.Utils.Common.Constants;

namespace DataAccess.Repositories;

public class TerminalDataSetRepository : ITerminalDataSetRepository
{
    private readonly DbContext context;
    private readonly IMapper mapper;
    private readonly ILogger<TerminalDataSetRepository> logger;
    private readonly IQueryable<TerminalDataSetEntity> TerminalDataQuery;
    private readonly string userId = Guid.Empty.ToString();
    private readonly string counterparty;
    private IAcquirer _acquirer = null!;

    public TerminalDataSetRepository(DbContext context, IHttpContextAccessor contextAccessor,
        IMapper mapper, ILogger<TerminalDataSetRepository> logger, ICounterpartyProvider counterpartyProvider)
    {
        this.mapper = mapper;
        this.logger = logger;
        this.context = context;
        TerminalDataQuery = context.Set<TerminalDataSetEntity>().AsQueryable();
        var headerUserId = contextAccessor.HttpContext?.Request.Headers[UtilsConstants.UserIdHeaderName];
        if (!string.IsNullOrEmpty(headerUserId))
        {
            userId = headerUserId;
        }
        counterparty = counterpartyProvider.GetCode();
    }

    public async Task<List<TerminalDataSet>> CreateAsync(List<TerminalDataSet> terminalDataSets)
    {
        var terminalDataEntity = mapper.Map<List<TerminalDataSetEntity>>(terminalDataSets);
        var dateTimeNow = DateTime.UtcNow;


        foreach (TerminalDataSetEntity terminalEntity in terminalDataEntity)
        {
            terminalEntity.CreatedDate = dateTimeNow;
            terminalEntity.CreatedBy = userId;
            if (counterparty == UtilsConstants.CounterpartySaudi)
                terminalEntity.Availability = Constants.TerminalDataSetAvailability.Available;
            if (string.IsNullOrEmpty(terminalEntity.TID))
            {
                var TID = await GetTerminalIdSequenceAsync();
                terminalEntity.TID = TID;
                terminalEntity.FullTID = terminalEntity.TID;
            }
        };

        await context.Set<TerminalDataSetEntity>().AddRangeAsync(terminalDataEntity);

        await context.SaveChangesAsync();

        return mapper.Map<List<TerminalDataSet>>(terminalDataEntity);
    }

    public async Task<List<TerminalDataSet>> PatchAsync(
        List<TerminalDataSetPatchRequest> terminalDataSetPatchRequest)
    {
        var executionStrategy = context.Database.CreateExecutionStrategy();

        return await executionStrategy.ExecuteAsync(async () =>
        {
            await using var transaction = await context.Database.BeginTransactionAsync();
            try
            {
                var patchList = new List<TerminalDataSetEntity>();
                foreach (var terminalDataSet in terminalDataSetPatchRequest)
                {
                    var terminalData = await GetTerminalDataByIdAsync(terminalDataSet.TerminalDataSetId);

                    var terminalDataSetToUpdate = mapper.Map<TerminalDataSet>(terminalData);

                    terminalDataSet.TerminalDataSetPatchDocument!.ApplyTo(terminalDataSetToUpdate);
                    mapper.Map(terminalDataSetToUpdate, terminalData);

                    terminalData.UpdatedBy = userId;
                    terminalData.UpdatedDate = DateTime.UtcNow;

                    patchList.Add(terminalData);
                }

                context.Set<TerminalDataSetEntity>().UpdateRange(patchList);
                await context.SaveChangesAsync();

                await transaction.CommitAsync();

                return mapper.Map<List<TerminalDataSet>>(patchList);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error when terminal data patch is applied.");
                await transaction.RollbackAsync();
                throw new ServiceException(Errors.InvalidPatchRequest);
            }
        });
    }

    public async Task<TerminalDataSetEntity> GetTerminalDataByIdAsync(Guid terminalDataId)
    {
        var terminalData = await TerminalDataQuery.SingleOrDefaultAsync(td => td.Id == terminalDataId);

        if (terminalData == null)
        {
            logger.LogError($"Terminal data with id '{terminalDataId}' not found.");
            throw new ServiceException(HttpStatusCode.NotFound, Errors.TerminalDataSetNotFound);
        }

        return terminalData;
    }

    public async Task<SearchResponse<TerminalDataSetSearchResponse>> AdvancedSearchAsync(TerminalDataSetSearchRequest terminalDataRequest)
    {
        var terminalDataQuery = TerminalDataQuery.Include(t => t.Vendor).AsNoTracking();

        terminalDataQuery = ApplySingleValueFilters(terminalDataRequest, terminalDataQuery);

        terminalDataQuery = ApplyListFilters(terminalDataRequest, terminalDataQuery);

        terminalDataQuery = ApplyDateFilters(terminalDataRequest, terminalDataQuery);

        terminalDataQuery = KeywordSearchClause(terminalDataRequest, terminalDataQuery);

        var totalCount = terminalDataQuery.Count();
        var page = await terminalDataQuery.OrderBy($"{terminalDataRequest.OrderBy} {terminalDataRequest.Sort}, Id")
                                        .Skip(terminalDataRequest.Skip)
                                        .Take(terminalDataRequest.Take)
                                        .ToListAsync();

        var searchResponse = new SearchResponse<TerminalDataSetSearchResponse>();
        if (page != null)
        {
            searchResponse.Records = page.Select(o => mapper.Map<TerminalDataSetSearchResponse>(o)).ToList();
            searchResponse.ReturnedRecordCount = searchResponse.Records.Count;
            searchResponse.TotalRecordCount = totalCount;
        }
        return searchResponse;
    }

    public async Task<int> GetAvailableTerminalDataSetCountAsync(string acquiringLedger)
    {
        var result = await context.Set<TerminalDataSetEntity>().AsNoTracking()
                                    .CountAsync(t => t.AcquiringLedger!.Equals(acquiringLedger) &&
                                        t.Availability == Constants.TerminalDataSetAvailability.Available);

        return result;
    }

    public async Task<List<TerminalDataSet>> GetOrderMigrationValidationRequiredFieldsAsync(List<TerminalDataSet> terminalDataSets)
    {
        return await TerminalDataQuery.Where(x => x.Availability != Constants.TerminalDataSetAvailability.Invalid &&
                                           (terminalDataSets.Select(s => s.Tid).Contains(x.TID) ||
                                           terminalDataSets.Select(s => s.FullTid).Contains(x.FullTID) ||
                                           terminalDataSets.Select(s => s.Mid).Contains(x.MID) ||
                                           terminalDataSets.Select(s => s.Trsm).Contains(x.TRSM)))
                                .Select(x => new TerminalDataSet()
                                {
                                    Tid = x.TID,
                                    FullTid = x.FullTID,
                                    Mid = x.MID,
                                    Trsm = x.TRSM,
                                    Availability = x.Availability,
                                    AcquiringLedger = x.AcquiringLedger!
                                })
                                .AsNoTracking()
                                .ToListAsync();
    }

    public async Task<List<TerminalDataSetValidationRequiredFields>> GetValidationRequiredFieldsAsync(List<TerminalDataSet> terminalDataSets)
    {
        var terminalDataQuery = TerminalDataQuery.AsNoTracking();
        var listWithErrors = new List<TerminalDataSetValidationRequiredFields>();

        var tids = terminalDataSets.Where(x => !string.IsNullOrWhiteSpace(x.Tid)).Select(x => x.Tid).ToList();
        if (tids.Any())
        {
            var tidsAlreadyExisting = await terminalDataQuery.Where(x =>
                                                                        x.Availability != Constants.TerminalDataSetAvailability.Invalid &&
                                                                        tids.Contains(x.TID))
                                                             .Select(x => new TerminalDataSetValidationRequiredFields()
                                                             {
                                                                 Data = x.TID,
                                                                 Label = "Tid",
                                                                 Availability = x.Availability,
                                                                 AcquiringLedger = x.AcquiringLedger
                                                             }).ToListAsync();

            listWithErrors.AddRange(tidsAlreadyExisting.ToList());
        }

        var mids = terminalDataSets.Where(x => !string.IsNullOrWhiteSpace(x.Mid)).Select(x => x.Mid).ToList();
        if (mids.Any())
        {
            var midsAlreadyExisting = await terminalDataQuery.Where(x => x.Availability != Constants.TerminalDataSetAvailability.Invalid
                                                                         && x.AcquiringLedger == terminalDataSets[0].AcquiringLedger
                                                                         && mids.Contains(x.MID))
                                                             .Select(x => new TerminalDataSetValidationRequiredFields()
                                                             {
                                                                 Data = x.MID,
                                                                 Label = "Mid",
                                                                 Availability = x.Availability,
                                                                 AcquiringLedger = x.AcquiringLedger
                                                             }).ToListAsync();

            listWithErrors.AddRange(midsAlreadyExisting.ToList());
        }

        var trsm = terminalDataSets.Where(x => !string.IsNullOrWhiteSpace(x.Trsm)).Select(x => x.Trsm).ToList();
        if (trsm.Any())
        {
            var trsmAlreadyExisting = await terminalDataQuery.Where(x =>
                                                                        x.Availability != Constants.TerminalDataSetAvailability.Invalid &&
                                                                        trsm.Contains(x.TRSM))
                                                             .Select(x => new TerminalDataSetValidationRequiredFields()
                                                             {
                                                                 Data = x.TRSM,
                                                                 Label = "Trsm",
                                                                 Availability = x.Availability,
                                                                 AcquiringLedger = x.AcquiringLedger
                                                             }).ToListAsync();
            listWithErrors.AddRange(trsmAlreadyExisting.ToList());
        }

        var fullTid = terminalDataSets.Where(x => !string.IsNullOrWhiteSpace(x.FullTid)).Select(x => x.FullTid).ToList();
        if (fullTid.Any())
        {
            var fullTidAlreadyExisting = await terminalDataQuery.Where(x =>
                                                                        x.Availability != Constants.TerminalDataSetAvailability.Invalid &&
                                                                        fullTid.Contains(x.FullTID))
                                                             .Select(x => new TerminalDataSetValidationRequiredFields()
                                                             {
                                                                 Data = x.FullTID,
                                                                 Label = "FullTid",
                                                                 Availability = x.Availability,
                                                                 AcquiringLedger = x.AcquiringLedger
                                                             }).ToListAsync();
            listWithErrors.AddRange(fullTidAlreadyExisting.ToList());
        }

        return listWithErrors;
    }

    public async Task<List<TerminalDataSet>> GetTerminalDataByProductInstanceId(Guid[] productInstanceIds)
    {
        var terminalDataSets = await TerminalDataQuery.AsNoTracking()
            .Where(t => t.ProductInstanceId.HasValue && productInstanceIds.Contains(t.ProductInstanceId.Value))
            .ToListAsync();

        return mapper.Map<List<TerminalDataSet>>(terminalDataSets);
    }

    private static IQueryable<TerminalDataSetEntity> ApplyDateFilters(TerminalDataSetSearchRequest terminalDataRequest,
        IQueryable<TerminalDataSetEntity> terminalDataQuery)
    {
        if (terminalDataRequest.ConfigDateInterval?.FromDate != null)
            terminalDataQuery =
                terminalDataQuery.Where(x => x.ConfigDate >= terminalDataRequest.ConfigDateInterval.FromDate);

        if (terminalDataRequest.ConfigDateInterval?.ToDate != null)
            terminalDataQuery = terminalDataQuery.Where(x => x.ConfigDate <= terminalDataRequest.ConfigDateInterval.ToDate);

        return terminalDataQuery;
    }

    private static IQueryable<TerminalDataSetEntity> ApplyListFilters(TerminalDataSetSearchRequest terminalDataRequest,
        IQueryable<TerminalDataSetEntity> terminalDataQuery)
    {
        if (terminalDataRequest.OrderNumbers?.Any() ?? false)
            terminalDataQuery = terminalDataQuery.Where(x => terminalDataRequest.OrderNumbers.Contains(x.OrderNumber ?? string.Empty));

        if (terminalDataRequest.Availability != null && terminalDataRequest.Availability.Any())
            terminalDataQuery = terminalDataQuery.Where(x => terminalDataRequest.Availability.Contains(x.Availability));

        if (terminalDataRequest.AcquiringLedger != null && terminalDataRequest.AcquiringLedger.Any())
            terminalDataQuery = terminalDataQuery.Where(x => terminalDataRequest.AcquiringLedger.Contains(x.AcquiringLedger));

        if (terminalDataRequest.ChannelType != null && terminalDataRequest.ChannelType.Any())
            terminalDataQuery = terminalDataQuery.Where(x => terminalDataRequest.ChannelType.Contains(x.ChannelType));

        if (terminalDataRequest.VendorIds != null && terminalDataRequest.VendorIds.Any())
            terminalDataQuery = terminalDataQuery.Where(x => terminalDataRequest.VendorIds.Contains(x.VendorId));

        return terminalDataQuery;
    }

    private static IQueryable<TerminalDataSetEntity> ApplySingleValueFilters(TerminalDataSetSearchRequest terminalDataRequest,
        IQueryable<TerminalDataSetEntity> terminalDataQuery)
    {
        if (terminalDataRequest.ConfigDate != null)
            terminalDataQuery = terminalDataQuery.Where(x => x.ConfigDate == terminalDataRequest.ConfigDate);

        if (!string.IsNullOrWhiteSpace(terminalDataRequest.OrderNumber))
            terminalDataQuery = terminalDataQuery.Where(x => x.OrderNumber == terminalDataRequest.OrderNumber);

        if (!string.IsNullOrWhiteSpace(terminalDataRequest.FullTid))
            terminalDataQuery = terminalDataQuery.Where(x => x.FullTID == terminalDataRequest.FullTid);

        if (!string.IsNullOrWhiteSpace(terminalDataRequest.Tid))
            terminalDataQuery = terminalDataQuery.Where(x => x.TID == terminalDataRequest.Tid);

        if (!string.IsNullOrWhiteSpace(terminalDataRequest.Trsm))
            terminalDataQuery = terminalDataQuery.Where(x => x.TRSM == terminalDataRequest.Trsm);

        if (!string.IsNullOrWhiteSpace(terminalDataRequest.Mid))
            terminalDataQuery = terminalDataQuery.Where(x => x.MID == terminalDataRequest.Mid);

        if (terminalDataRequest.ProductInstanceId != null && terminalDataRequest.ProductInstanceId != Guid.Empty)
            terminalDataQuery = terminalDataQuery.Where(x => terminalDataRequest.ProductInstanceId.Equals(x.ProductInstanceId));

        return terminalDataQuery;
    }

    private static IQueryable<TerminalDataSetEntity> KeywordSearchClause(TerminalDataSetSearchRequest terminalDataRequest, IQueryable<TerminalDataSetEntity> terminalDataQuery)
    {
        if (!string.IsNullOrWhiteSpace(terminalDataRequest.Keyword) && terminalDataRequest.SearchIn != null && terminalDataRequest.SearchIn.Any())
        {
            var searchClauses = string.Join(" or ",
                                    terminalDataRequest.SearchIn.Select(x =>
                                    {
                                        var property = typeof(TerminalDataSetEntity).GetProperty(x, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                                        return property?.PropertyType != typeof(string) ? $"{x}.ToString().StartsWith(@0)" : $"{x}.StartsWith(@0)";
                                    }));
            terminalDataQuery = terminalDataQuery.Where(searchClauses, terminalDataRequest.Keyword);
            return terminalDataQuery;
        }
        return terminalDataQuery;
    }

    [ExcludeFromCodeCoverage]
    public void TriggerOrderMigrationSyncAsync()
    {
        try
        {
            using var command = context.Database.GetDbConnection().CreateCommand();
            command.CommandText = "[dbo].[SyncTerminalDataSetWithOrderMigration]";
            command.CommandType = System.Data.CommandType.StoredProcedure;

            if (command.Connection?.State == System.Data.ConnectionState.Closed)
            {
                command.Connection.Open();
            }

            command.ExecuteNonQuery();

        }
        catch (Exception ex)
        {
            logger.LogError("Error occured while trying to execute stored procedure to synchronize data, " +
                "at TimeStamp {datetime} with the message: {message}.", DateTime.UtcNow, ex.Message);
        }
    }

    public async Task<TerminalDataSetsResponse> GenerateTIDAndAddEditTerminalDataSet(TerminalDataSet terminalDataSet, IAcquirer acquirer)
    {
        var terminalDataSetRes = new TerminalDataSetsResponse();
        if (terminalDataSet != null)
        {
            ValidateAutoGeneratedAcquiringBank(terminalDataSet.AcquiringLedger);

            var terminalDataSetEntity = await TerminalDataQuery
                .Where(t => t.OrderNumber == terminalDataSet.OrderNumber &&
                t.ProductInstanceId == terminalDataSet.ProductInstanceId)
                .FirstOrDefaultAsync();

            if (terminalDataSetEntity != null)
            {
                if (string.IsNullOrEmpty(terminalDataSetEntity.TID))
                {
                    terminalDataSetEntity.TID = await GetTerminalIdSequenceAsync();
                    terminalDataSetEntity.FullTID = terminalDataSetEntity.TID;
                    terminalDataSetEntity.TRSM = terminalDataSetEntity.TID.Substring(terminalDataSetEntity.TID.Length - 6);

                    UpdateAvailabilityAndConfigDate(terminalDataSetEntity);

                    await context.SaveChangesAsync();
                }

                terminalDataSetRes.TId = terminalDataSetEntity.TID;
                terminalDataSetRes.FullTId = terminalDataSetEntity.FullTID;
                terminalDataSetRes.Trsm = terminalDataSetEntity.TRSM;
            }
            else
            {
                terminalDataSet.Tid = await GetTerminalIdSequenceAsync();
                terminalDataSet.FullTid = terminalDataSet.Tid;
                terminalDataSet.Trsm = terminalDataSet.Tid.Substring(terminalDataSet.Tid.Length - 6);

                terminalDataSet.Availability = GetAvailabilityForEgypt(terminalDataSet.Mid, terminalDataSet.Tid);
                terminalDataSet.ConfigDate = terminalDataSet.Availability == TerminalDataSetAvailability.Used ? DateTime.UtcNow : null;

                terminalDataSetRes.TId = terminalDataSet.Tid;
                terminalDataSetRes.FullTId = terminalDataSet.FullTid;
                terminalDataSetRes.Trsm = terminalDataSet.Trsm;

                await CreateAsync(new List<TerminalDataSet> { terminalDataSet });
            }
        }
        return terminalDataSetRes;
    }

    public async Task<string> GenerateMIDAndAddEditTerminalDataSet(TerminalDataSetMidRequest terminalDataSetMidRequest, IAcquirer acquirer)
    {
        string mid = "";

        var terminalDataSet = terminalDataSetMidRequest?.TerminalDataSet;

        if (terminalDataSet != null)
        {
            SetAcquirerInstance(acquirer);

            ValidateAutoGeneratedAcquiringBank(terminalDataSet.AcquiringLedger);

            var terminalDataSetEntity = await TerminalDataQuery
                .Where(t => t.OrderNumber == terminalDataSet.OrderNumber &&
                t.ProductInstanceId == terminalDataSet.ProductInstanceId)
                .FirstOrDefaultAsync();

            if (terminalDataSetEntity != null)
            {
                if (string.IsNullOrEmpty(terminalDataSetEntity.MID))
                {
                    terminalDataSetEntity.MID = await GenerateUniqueMID(terminalDataSetMidRequest?.StoresIds, terminalDataSet.AcquiringLedger, terminalDataSet?.BusinessId, terminalDataSet?.CardChannelType);
                    UpdateAvailabilityAndConfigDate(terminalDataSetEntity);

                    await context.SaveChangesAsync();
                }

                mid = terminalDataSetEntity.MID;
            }
            else
            {
                terminalDataSet.Mid = await GenerateUniqueMID(terminalDataSetMidRequest?.StoresIds, terminalDataSet.AcquiringLedger, terminalDataSet.BusinessId, terminalDataSet.CardChannelType);
                terminalDataSet.Availability = GetAvailabilityForEgypt(terminalDataSet.Mid, terminalDataSet.Tid);
                terminalDataSet.ConfigDate = terminalDataSet.Availability == TerminalDataSetAvailability.Used ? DateTime.UtcNow : null;

                mid = terminalDataSet.Mid;

                await CreateAsync(new List<TerminalDataSet> { terminalDataSet });
            }
        }
        return mid;
    }

    public async Task<List<TerminalDataSetsResponse>> GenerateTIDAndMIDAndAddEditTerminalDataSets(TerminalDataSetsRequest terminalDataSets, IAcquirer acquirer)
    {
        if (terminalDataSets.ProductInstancesData != null)
        {
            var productInstancesIds = terminalDataSets.ProductInstancesData.Select(p => p.ProductInstanceId).ToList();

            if (productInstancesIds != null)
            {
                SetAcquirerInstance(acquirer);

                ValidateAutoGeneratedAcquiringBank(terminalDataSets.AcquiringLedger);

                terminalDataSets.AcquiringLedger = terminalDataSets.AcquiringLedger == Constants.AcquiringLedger.GeIdea ? "DEFAULT_BANK" : terminalDataSets.AcquiringLedger;

                var terminalDataEntities = await TerminalDataQuery
                    .Where(t => t.OrderNumber == terminalDataSets.OrderNumber
                     && productInstancesIds.Contains(t.ProductInstanceId))
                    .ToListAsync();

                var toBeCreated = await GetDataSetsToBeAddedAndUpdated(terminalDataSets, terminalDataEntities);

                if (toBeCreated.Count > 0)
                    await CreateAsync(toBeCreated);
                else
                    await context.SaveChangesAsync();

                return MapTerminalDataToResponse(terminalDataEntities, toBeCreated);
            }
        }

        return new List<TerminalDataSetsResponse>();
    }

    public async Task<List<TerminalDataSetsResponse>> GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(TerminalDataSetsMidTidRequest terminalDataSetsMidTid, IAcquirer acquirer)
    {
        if (terminalDataSetsMidTid == null || terminalDataSetsMidTid.TerminalDataSets == null
          || terminalDataSetsMidTid.TerminalDataSets.Count == 0) return new List<TerminalDataSetsResponse>();

        var terminalDataSets = terminalDataSetsMidTid.TerminalDataSets;

        ValidateAutoGeneratedAcquiringBank(terminalDataSets.First().AcquiringLedger);

        SetAcquirerInstance(acquirer);

        var productInstancesIds = terminalDataSets.Select(t => t.ProductInstanceId)
            .ToList();

        var ordersNumbers = terminalDataSets.Select(t => t.OrderNumber)
           .ToList();

        var terminalDataSetEntities = await TerminalDataQuery
            .Where(t => ordersNumbers.Contains(t.OrderNumber) &&
            productInstancesIds.Contains(t.ProductInstanceId))
            .ToListAsync();

        var toBeCreated = await GetDataSetsToBeAddedAndUpdated(terminalDataSets, terminalDataSetEntities, terminalDataSetsMidTid.StoresIds);

        if (toBeCreated.Count > 0)
            await CreateAsync(toBeCreated);
        else
            await context.SaveChangesAsync();

        return MapTerminalDataToResponse(terminalDataSetEntities, toBeCreated);
    }

    public async Task<List<TerminalDataSetsResponse>> AddUpdateTerminalDataSetMcc(TerminalDataSetsMidTidRequest terminalDataSetsRequest)
    {
        var terminalDataSets = terminalDataSetsRequest.TerminalDataSets;

        var productInstancesIds = terminalDataSets.Select(t => t.ProductInstanceId)
            .ToList();

        var ordersNumbers = terminalDataSets.Select(t => t.OrderNumber)
           .ToList();

        var terminalDataSetEntities = await TerminalDataQuery
            .Where(t => ordersNumbers.Contains(t.OrderNumber) &&
            productInstancesIds.Contains(t.ProductInstanceId))
            .ToListAsync();

        List<TerminalDataSet> toBeCreated = new List<TerminalDataSet>();

        foreach (var terminalDataSet in terminalDataSets)
        {
            var terminalDataEntity = terminalDataSetEntities
                .FirstOrDefault(t => t.ProductInstanceId == terminalDataSet.ProductInstanceId
                && t.OrderNumber == terminalDataSet.OrderNumber &&
                t.StoreId == terminalDataSet.StoreId);

            if (terminalDataEntity != null)
            {
                terminalDataEntity.MCC = terminalDataSet.MCC;
            }
            else
            {
                terminalDataSet.Availability = GetAvailabilityForEgypt(null, null);
                toBeCreated.Add(terminalDataSet);
            }
        }

        if (toBeCreated.Count > 0)
            await CreateAsync(toBeCreated);
        else
            await context.SaveChangesAsync();

        return MapTerminalDataToResponse(terminalDataSetEntities, toBeCreated);
    }

    public async Task<string> GetTerminalIdSequenceAsync()
    {
        var result = new Microsoft.Data.SqlClient.SqlParameter("@result", System.Data.SqlDbType.Int)
        {
            Direction = System.Data.ParameterDirection.Output
        };
        try
        {
            await context.Database.ExecuteSqlRawAsync("SELECT @result = (NEXT VALUE FOR TERMINALID_SEQUENCE)", result);
        }
        catch (Exception ex)
        {
            logger.LogError("Unable to generate new TID sequence '{ex.Message}'  ", ex.Message);
        }


        var sequenceId = result?.Value?.ToString() ?? string.Empty;

        var sequenceString = "10000000";

        sequenceString = string.Concat(sequenceString.AsSpan(0, sequenceString.Length - sequenceId.Length), sequenceId);

        return sequenceString;
    }

    private static List<TerminalDataSetsResponse> MapTerminalDataToResponse(List<TerminalDataSetEntity> terminalDataEntities, List<TerminalDataSet> toBeCreated)
    {
        var terminalInfo = terminalDataEntities.Select(t => new TerminalDataSetsResponse
        {
            MIDMerchantReference = t.MID,
            ProductInstanceId = t.ProductInstanceId,
            ProviderBank = t.AcquiringLedger,
            TId = t.TID,
            FullTId = t.FullTID,
            MCC = t.MCC,
            Trsm = t.TRSM,
            ConnectionType = t.ConnectionType,
            ChannelType = t.ChannelType,
            MPGSKEY = t.MPGSKEY,
            MPGSMID = t.MPGSMID
        });

        var createdTerminalInfo = toBeCreated.Select(t => new TerminalDataSetsResponse
        {
            MIDMerchantReference = t.Mid,
            ProductInstanceId = t.ProductInstanceId,
            ProviderBank = t.AcquiringLedger,
            TId = t.Tid,
            FullTId = t.FullTid,
            MCC = t.MCC,
            Trsm = t.Trsm,
            ConnectionType = t.ConnectionType,
            ChannelType = t.ChannelType,
            MPGSKEY = t.MPGSKEY,
            MPGSMID = t.MPGSMID
        });

        return terminalInfo.Concat(createdTerminalInfo).ToList();
    }

    private void ValidateAutoGeneratedAcquiringBank(string acquiringBank)
    {
        if (string.IsNullOrEmpty(acquiringBank) ||
            (!acquiringBank.Equals(AcquiringLedger.NBEBank, StringComparison.OrdinalIgnoreCase) &&
            !acquiringBank.Equals(AcquiringLedger.ALXBank, StringComparison.OrdinalIgnoreCase) &&
            !acquiringBank.Equals(AcquiringLedger.GeIdea, StringComparison.OrdinalIgnoreCase)))
        {
            logger.LogError("Invalid acquiring bank '{acquiringBank}' in ValidateAutoGeneratedAcquiringBank", acquiringBank);
            throw new ServiceException(Errors.InvalidAutoGeneratedAcquirer);
        }
    }

    private async Task<List<TerminalDataSet>> GetDataSetsToBeAddedAndUpdated(TerminalDataSetsRequest terminalDataSets, List<TerminalDataSetEntity> terminalDataSetEntities)
    {
        List<TerminalDataSet> toBeCreated = new List<TerminalDataSet>();
        List<string> generatedTidsInMemory = new List<string>();
        string? merchantMID = terminalDataSets.Mid;

        if (terminalDataSets.ProductInstancesData != null)
        {
            foreach (var productInstance in terminalDataSets.ProductInstancesData)
            {
                var terminalDataEntity = terminalDataSetEntities
                    .FirstOrDefault(t => t.ProductInstanceId == productInstance.ProductInstanceId);

                if (terminalDataEntity != null)
                {
                    var terminalDataSetMainInfo = new TerminalDataSet
                    {
                        AcquiringLedger = terminalDataSets.AcquiringLedger,
                        ConnectionType = productInstance.ConnectionType,
                        ChannelType = productInstance.ChannelType,
                        MerchantTag = terminalDataSets.MerchantTag,
                        Model = terminalDataSets.Model,
                        BusinessId = terminalDataSets.BusinessId,
                        CardChannelType = terminalDataSets.CardChannelType
                    };

                    merchantMID = await UpdateDataSet(terminalDataEntity, generatedTidsInMemory, terminalDataSets.MerchantStoresIds, terminalDataSetMainInfo, merchantMID);
                }
                else
                {
                    merchantMID = await AddDataSet(terminalDataSets, generatedTidsInMemory, toBeCreated, terminalDataSets.MerchantStoresIds, merchantMID, productInstance.ProductInstanceId);
                }
            }
        }

        return toBeCreated;
    }

    private async Task<List<TerminalDataSet>> GetDataSetsToBeAddedAndUpdated(List<TerminalDataSet> terminalDataSets, List<TerminalDataSetEntity> terminalDataSetEntities, List<Guid>? storesIds)
    {
        List<TerminalDataSet> toBeCreated = new List<TerminalDataSet>();
        List<string> generatedTidsInMemory = new List<string>();
        string? merchantMID = null;

        foreach (var terminalDataSet in terminalDataSets)
        {
            var terminalDataEntity = terminalDataSetEntities
                .FirstOrDefault(t => t.ProductInstanceId == terminalDataSet.ProductInstanceId
                && t.OrderNumber == terminalDataSet.OrderNumber &&
                t.StoreId == terminalDataSet.StoreId);

            if (terminalDataEntity != null)
            {
                merchantMID = await UpdateDataSet(terminalDataEntity, generatedTidsInMemory, storesIds, terminalDataSet, merchantMID);
            }
            else
            {
                merchantMID = await AddDataSet(terminalDataSet, generatedTidsInMemory, toBeCreated, storesIds, merchantMID);
            }
        }

        return toBeCreated;
    }

    private async Task<string?> UpdateDataSet(TerminalDataSetEntity terminalDataSetEntity, List<string> generatedTidsInMemory,
                                              List<Guid>? merchantStoresIds, TerminalDataSet terminalDataSet, string? merchantMID)
    {
        if (string.IsNullOrEmpty(terminalDataSetEntity.TID))
        {
            terminalDataSetEntity.TID = await GetTerminalIdSequenceAsync();
            generatedTidsInMemory.Add(terminalDataSetEntity.TID);
            terminalDataSetEntity.FullTID = terminalDataSetEntity.TID;
            terminalDataSetEntity.TRSM = terminalDataSetEntity.TID.Substring(terminalDataSetEntity.TID.Length - 6);
        }

        if (string.IsNullOrEmpty(terminalDataSetEntity.MID))
        {
            if (string.IsNullOrEmpty(merchantMID))
                merchantMID = await GenerateUniqueMID(merchantStoresIds, terminalDataSet.AcquiringLedger, terminalDataSet.BusinessId, terminalDataSet.CardChannelType);

            terminalDataSetEntity.MID = merchantMID;
        }

        if (!string.IsNullOrEmpty(terminalDataSet.ConnectionType))
            terminalDataSetEntity.ConnectionType = terminalDataSet.ConnectionType;

        if (!string.IsNullOrEmpty(terminalDataSet.ChannelType))
            terminalDataSetEntity.ChannelType = terminalDataSet.ChannelType;

        if (!string.IsNullOrEmpty(terminalDataSet.MCC))
            terminalDataSetEntity.MCC = terminalDataSet.MCC;

        UpdateAvailabilityAndConfigDate(terminalDataSetEntity);

        return merchantMID;
    }

    private static void UpdateAvailabilityAndConfigDate(TerminalDataSetEntity terminalDataSetEntity)
    {
        string oldAvaliability = terminalDataSetEntity.Availability;
        terminalDataSetEntity.Availability = GetAvailabilityForEgypt(terminalDataSetEntity.MID, terminalDataSetEntity.TID);

        if (terminalDataSetEntity.Availability == TerminalDataSetAvailability.Used && oldAvaliability != TerminalDataSetAvailability.Used)
        {
            terminalDataSetEntity.ConfigDate = DateTime.UtcNow;
        }
    }

    private async Task<string?> AddDataSet(TerminalDataSetsRequest terminalDataSets, List<string> generatedTidsInMemory, List<TerminalDataSet> toBeCreated, List<Guid>? merchantStoresIds, string? merchantMID, Guid? productInstanceId)
    {
        TerminalDataSet terminalDataSet = new TerminalDataSet
        {
            OrderNumber = terminalDataSets.OrderNumber,
            ProductInstanceId = productInstanceId,
            AcquiringLedger = terminalDataSets.AcquiringLedger,
            StoreId = terminalDataSets.StoreId,
            MerchantTag = terminalDataSets.MerchantTag,
            ConnectionType = terminalDataSets.ProductInstancesData?.FirstOrDefault(p => p.ProductInstanceId == productInstanceId)?.ConnectionType,
            ChannelType = terminalDataSets.ProductInstancesData?.FirstOrDefault(p => p.ProductInstanceId == productInstanceId)?.ChannelType,
            Model = terminalDataSets.Model,
            BusinessId = terminalDataSets.BusinessId,
            CardChannelType = terminalDataSets.CardChannelType,
            Mid = terminalDataSets.Mid,
            MCC = terminalDataSets.MCC
        };

        return await AddDataSet(terminalDataSet, generatedTidsInMemory, toBeCreated, merchantStoresIds, merchantMID);
    }

    private async Task<string?> AddDataSet(TerminalDataSet terminalDataSet, List<string> generatedTidsInMemory, List<TerminalDataSet> toBeCreated,
        List<Guid>? merchantStoresIds, string? merchantMID)
    {
        terminalDataSet.Tid = await GetTerminalIdSequenceAsync();
        terminalDataSet.FullTid = terminalDataSet.Tid;
        terminalDataSet.Trsm = terminalDataSet.Tid.Substring(terminalDataSet.Tid.Length - 6);

        if (string.IsNullOrEmpty(merchantMID))
            merchantMID = await GenerateUniqueMID(merchantStoresIds, terminalDataSet.AcquiringLedger, terminalDataSet.BusinessId, terminalDataSet.CardChannelType);

        terminalDataSet.Mid = merchantMID;

        terminalDataSet.Availability = GetAvailabilityForEgypt(terminalDataSet.Mid, terminalDataSet.Tid);
        terminalDataSet.ConfigDate = terminalDataSet.Availability == TerminalDataSetAvailability.Used ? DateTime.UtcNow : null;

        generatedTidsInMemory.Add(terminalDataSet.Tid);
        toBeCreated.Add(terminalDataSet);

        return merchantMID;
    }

    private async Task<string> GenerateUniqueMID(List<Guid>? storeIds, string acquirerBank, string? BusinessId, string? CardChannelType)
    {
        var merchantMID = string.Empty;

        if (storeIds?.Count > 0)
        {
            merchantMID = await TerminalDataQuery.AsNoTracking()
                .Where(c => c.Counterparty == counterparty && c.MID != null &&
                c.StoreId.HasValue && storeIds.Contains(c.StoreId.Value))
                .Select(c => c.MID)
                .FirstOrDefaultAsync();

            if (string.IsNullOrEmpty(merchantMID) && !string.IsNullOrEmpty(BusinessId))
            {
                var maxMID = await TerminalDataQuery.AsNoTracking()
                    .Where(c => c.Counterparty == counterparty && c.AcquiringLedger == acquirerBank && c.ConfigDate != null && c.MID != null && c.MID.StartsWith(BusinessId))
                    .Select(c => new { c.MID, c.ConfigDate })
                    .GroupBy(c => c.MID)
                    .Select(c => new { MID = c.Key, ConfigDate = c.Min(m => m.ConfigDate) })
                    .OrderByDescending(c => c.ConfigDate)
                    .Select(c => c.MID)
                    .FirstOrDefaultAsync();

                return _acquirer.GenerateUAEUniqueMID(maxMID, BusinessId, CardChannelType);

            }
            else
            {
                merchantMID = string.Empty;
                return merchantMID;
            }
            //else
            //{
            //    return _acquirer.GenerateUAEUniqueMID(string.Empty, BusinessId, CardChannelType);
            //}
        }

        return merchantMID;
    }

    public async Task<List<OrderTid>> GetTidByOrderId(List<string?> orderNumbers)
    {
        if (orderNumbers == null || !orderNumbers.Any())
        {
            logger.LogWarning("The provided order numbers list is null or empty.");
            return new List<OrderTid>();
        }

        var terminalDataList = await TerminalDataQuery
            .Where(td => orderNumbers.Contains(td.OrderNumber))
            .ToListAsync();
        var result = terminalDataList.Select(td => new OrderTid
        {
            Mid = td.MID,
            Tid = td.TID,
            OrderNumber = td.OrderNumber
        }).ToList();

        return result;
    }


    private static string GetAvailabilityForEgypt(string? Mid, string? Tid)
    {
        if (string.IsNullOrWhiteSpace(Tid))
            return TerminalDataSetAvailability.Invalid;
        if (string.IsNullOrWhiteSpace(Mid))
            return TerminalDataSetAvailability.Available;
        return TerminalDataSetAvailability.Used;
    }

    private void SetAcquirerInstance(IAcquirer acquirer)
    {
        _acquirer = acquirer;
    }
}
