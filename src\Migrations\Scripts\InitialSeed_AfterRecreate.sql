﻿BEGIN TRANSACTION;

DECLAR<PERSON> @CategoryIds TABLE(ID UNIQ<PERSON>IDENTIFIER)
DECLARE @CategoryId UNIQUEIDENTIFIER

DECLARE @ProductIds TABLE(ID UNIQUEIDENTIFIER)
DECLARE @ProductId UNIQUEIDENTIFIER

DECLARE @PartIds TABLE(ID UNIQUEIDENTIFIER)
DECLAR<PERSON> @PartId UNIQUEIDENTIFIER

DECLARE @GoFamilyCategoryId UNIQUEIDENTIFIER
DECLARE @ProFamilyCategoryId UNIQUEIDENTIFIER
DECLARE @OnlineFamilyCategoryId UNIQUEIDENTIFIER
DECLARE @RestaurantCategoryId UNIQUEIDENTIFIER
DECLARE @RetailCategoryId UNIQUEIDENTIFIER

DECLARE @GoAirId UNIQUEIDENTIFIER
DECLARE @GoLiteId UNIQUEIDENTIFIER
DECLARE @GoSmartId UNIQUEIDENTIFIER
DECLARE @ProSmartId UNIQUE<PERSON>EN<PERSON><PERSON>ER
DECLARE @BusinessRestaurantId UNIQUE<PERSON>EN<PERSON>FIER
DECLARE @EnterpriseRestaurantId UNIQUE<PERSON>ENTIFIER
DECLARE @BusinessRetailId UNIQUEIDENTIFIER
DECLARE @EnterpriseRetailId UNIQUEIDENTIFIER
DECLARE @WebsiteBuilderBundleId UNIQUEIDENTIFIER
DECLARE @PaymentGatewayBundleId UNIQUEIDENTIFIER

DECLARE @EInvoicindId UNIQUEIDENTIFIER
DECLARE @MobilePosId UNIQUEIDENTIFIER
DECLARE @SmartPosId UNIQUEIDENTIFIER
DECLARE @PaymentGatewayId UNIQUEIDENTIFIER
DECLARE @GeideaGoAppId UNIQUEIDENTIFIER
DECLARE @PosRocketId UNIQUEIDENTIFIER
DECLARE @LingaPosId UNIQUEIDENTIFIER
DECLARE @TillPointId UNIQUEIDENTIFIER
DECLARE @WebsiteBuilderId UNIQUEIDENTIFIER

--Categories
INSERT INTO CATEGORY(Code, Type, CreatedBy, CreatedDateUtc, [Order]) OUTPUT inserted.Id INTO @CategoryIds
VALUES('GO_FAMILY', 0, 'n/a', GETUTCDATE(), 1) 

SELECT TOP 1 @GoFamilyCategoryId = ID FROM @CategoryIds 
DELETE FROM @CategoryIds

INSERT INTO CATEGORY(Code, Type, CreatedBy, CreatedDateUtc, [Order]) OUTPUT inserted.Id INTO @CategoryIds
VALUES('PRO_FAMILY', 0, 'n/a', GETUTCDATE(), 2) 

SELECT TOP 1 @ProFamilyCategoryId = ID FROM @CategoryIds 
DELETE FROM @CategoryIds

INSERT INTO CATEGORY(Code, Type, CreatedBy, CreatedDateUtc, [Order]) OUTPUT inserted.Id INTO @CategoryIds
VALUES('ONLINE_FAMILY', 0, 'n/a', GETUTCDATE(), 3) 

SELECT TOP 1 @OnlineFamilyCategoryId = ID FROM @CategoryIds 
DELETE FROM @CategoryIds

INSERT INTO CATEGORY(Code, Type, CreatedBy, CreatedDateUtc, ParentId, [Order]) OUTPUT inserted.Id INTO @CategoryIds
VALUES('RESTAURANT', 0, 'n/a', GETUTCDATE(), @ProFamilyCategoryId, 4) 

SELECT TOP 1 @RestaurantCategoryId = ID FROM @CategoryIds 
DELETE FROM @CategoryIds

INSERT INTO CATEGORY(Code, Type, CreatedBy, CreatedDateUtc, ParentId, [Order]) OUTPUT inserted.Id INTO @CategoryIds
VALUES('RETAIL', 0, 'n/a', GETUTCDATE(), @ProFamilyCategoryId, 5) 

SELECT TOP 1 @RetailCategoryId = ID FROM @CategoryIds 
DELETE FROM @CategoryIds

--Products
INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'E_INVOICING', 'ECR_ADDON', 'n/a', GETUTCDATE(), GETUTCDATE())

SELECT TOP 1 @EInvoicindId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'MOBILE_POS_SP530', 'M_POS', 'n/a', GETUTCDATE(), GETUTCDATE())

SELECT TOP 1 @MobilePosId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'SMARTPOS_A920', 'TERMINAL', 'n/a', GETUTCDATE(), GETUTCDATE())

SELECT TOP 1 @SmartPosId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'PAYMENT_GATEWAY2', 'GWAY', 'n/a', GETUTCDATE(), GETUTCDATE())

SELECT TOP 1 @PaymentGatewayId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'GEIDEA_GO_APP', 'MINI_ECR', 'n/a', GETUTCDATE(), GETUTCDATE())

SELECT TOP 1 @GeideaGoAppId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'POS_ROCKET', 'MINI_ECR', 'n/a', GETUTCDATE(), GETUTCDATE())

SELECT TOP 1 @PosRocketId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'LINGA_POS', 'MINI_ECR', 'n/a', GETUTCDATE(), GETUTCDATE())

SELECT TOP 1 @LingaPosId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'TILL_POINT', 'MINI_ECR', 'n/a', GETUTCDATE(), GETUTCDATE())

SELECT TOP 1 @TillPointId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'WEBSITE_BUILDER1', 'WSB', 'n/a', GETUTCDATE(), GETUTCDATE())

SELECT TOP 1 @WebsiteBuilderId = ID FROM @ProductIds 
DELETE FROM @ProductIds

--Bundles
 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle', 'GO_AIR', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @GoAirId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle', 'GO_LITE', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @GoLiteId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle', 'GO_SMART', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @GoSmartId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle_HelpRequired', 'BUSINESS1', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @BusinessRestaurantId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle_HelpRequired', 'ENTERPRISE1', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @EnterpriseRestaurantId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle_HelpRequired', 'BUSINESS2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @BusinessRetailId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle_HelpRequired', 'ENTERPRISE2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @EnterpriseRetailId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle', 'WEBSITE_BUILDER2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @WebsiteBuilderBundleId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle', 'PAYMENT_GATEWAY1', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @PaymentGatewayBundleId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle', 'PRO_SMART', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @ProSmartId = ID FROM @ProductIds 
 DELETE FROM @ProductIds
--Associations

 ---Go Air
 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@GoAirId, @GoFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @GoAirId, 2900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @EInvoicindId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @GeideaGoAppId)

 ---Go Lite
 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@GoLiteId, @GoFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @GoLiteId, 3900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @MobilePosId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @GeideaGoAppId)

 ---Go Smart
 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@GoSmartId, @GoFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @GoSmartId, 13900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @SmartPosId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @GeideaGoAppId)

------------------------------------------------------------------------------
--Pro Family
 --Restaurant
  ---Go Smart
 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@ProSmartId, @RestaurantCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @ProSmartId, 22900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @SmartPosId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @GeideaGoAppId)
  ---Business
  INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@BusinessRestaurantId, @RestaurantCategoryId)
  
  INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
  VALUES('MONTH', 'RECCURRING_CHARGE', 0, @BusinessRestaurantId, 32900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)

  INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @SmartPosId)
  INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @PosRocketId)

  ---Enterprise
  INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@EnterpriseRestaurantId, @RestaurantCategoryId)
  
  INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
  VALUES('MONTH', 'RECCURRING_CHARGE', 0, @EnterpriseRestaurantId, 49900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
  
  INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRestaurantId, @SmartPosId)
  INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRestaurantId, @LingaPosId)

 --Retail
  ---Go Smart
  INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@ProSmartId, @RetailCategoryId)

  ---Business
  INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@BusinessRetailId, @RetailCategoryId)
  
  INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
  VALUES('MONTH', 'RECCURRING_CHARGE', 0, @BusinessRetailId, 32900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
  
  INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @SmartPosId)  
  INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @PosRocketId)

  ---Enterprise
  INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@EnterpriseRetailId, @RetailCategoryId)

  INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
  VALUES('MONTH', 'RECCURRING_CHARGE', 0, @EnterpriseRetailId, 32900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
  
  INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @SmartPosId)
  INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @TillPointId)

------------------------------------------------------------------------------
--Online Family
 ---Website Builder
 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@WebsiteBuilderBundleId, @OnlineFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc,Currency, RentalPeriod)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @WebsiteBuilderBundleId, 9900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @EInvoicindId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @PaymentGatewayId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @WebsiteBuilderId)

 ---Payment Gateway
 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@PaymentGatewayBundleId, @OnlineFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @PaymentGatewayBundleId, 9900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @PaymentGatewayId)

--card schemes
 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Normal', 'MADA', 'SCHEME', 'n/a', GETUTCDATE(), GETUTCDATE())

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRestaurantId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @ProductId)

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, Threshold)
 VALUES('PURCHASE_CP', 1, @ProductId, 70, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 10000)

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, Threshold)
 VALUES('PURCHASE_CP', 1, @ProductId, 80, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 10000)

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('PURCHASE_CNP', 1, @ProductId, 175, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('REFUND_CP', 1, @ProductId, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('REFUND_CNP', 1, @ProductId, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Normal', 'VISA', 'SCHEME', 'n/a', GETUTCDATE(), GETUTCDATE())

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRestaurantId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @ProductId)

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('PURCHASE_CP', 1, @ProductId, 275, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('PURCHASE_CNP', 1, @ProductId, 290, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('REFUND_CP', 1, @ProductId, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('REFUND_CNP', 1, @ProductId, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Normal', 'MC', 'SCHEME', 'n/a', GETUTCDATE(), GETUTCDATE())

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRestaurantId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @ProductId)

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('PURCHASE_CP', 1, @ProductId, 275, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('PURCHASE_CNP', 1, @ProductId, 290, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('REFUND_CP', 1, @ProductId, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('REFUND_CNP', 1, @ProductId, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Normal', 'AMEX', 'SCHEME', 'n/a', GETUTCDATE(), GETUTCDATE())

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRestaurantId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @ProductId)

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('PURCHASE_CNP', 1, @ProductId, 100, 290, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('PURCHASE_CP', 1, @ProductId, 275, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('PURCHASE_CNP', 1, @ProductId, 290, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('REFUND_CP', 1, @ProductId, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('REFUND_CNP', 1, @ProductId, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

COMMIT;