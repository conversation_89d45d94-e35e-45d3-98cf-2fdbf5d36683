﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Common.Entities;
public class MetaDataMigrationFilesEntity
{
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [Key]
    public Guid Id { get; set; }
    public string? MigrationFileName { get; set; }
    public string? SelectQuery { get; set; }
    public string? UpdateQuery { get; set; }
    public string? Status { get; set; }
    public string? FailedIds { get; set; }
    public DateTime CreatedDate { get; set; }
}
