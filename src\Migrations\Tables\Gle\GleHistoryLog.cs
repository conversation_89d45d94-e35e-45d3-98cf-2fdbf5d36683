﻿using System;
using System.IO;
using FluentMigrator;

namespace Migrations.Tables.Gle;

[Migration(2022_11_14_0800)]
public class GleHistoryLog : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(AppDomain.CurrentDomain.BaseDirectory + Path.Combine("Scripts", "GleHistoryLog.sql"));
    }
}

[Migration(2022_12_13_1500)]
public class GleHistoryLog_AdaptToBusinessHierarchy : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(AppDomain.CurrentDomain.BaseDirectory + Path.Combine("Scripts", "GleHistoryLog.sql"));
    }
}

[Migration(2022_12_19_1400)]
public class GleHistoryLog_ShowAllErrors : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(AppDomain.CurrentDomain.BaseDirectory + Path.Combine("Scrip<PERSON>", "GleHistoryLog.sql"));
    }
}