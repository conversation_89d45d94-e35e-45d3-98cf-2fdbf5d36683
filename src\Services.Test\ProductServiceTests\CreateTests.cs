﻿using AutoMapper;
using Common;
using Common.Entities;
using Common.Models;
using Common.Repositories;
using Common.Services;
using DataAccess;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using NSubstitute;
using ProductService.Controllers;
using Services.Test.TestData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Threading.Tasks;
using Xunit;
using static Common.Constants;

namespace Services.Test.ProductServiceTests;

public class CreateTests
{
    private readonly Mock<ILogger<ProductService>> logger = new Mock<ILogger<ProductService>>();
    private readonly Mock<IProductRepository> productRepository = new Mock<IProductRepository>();
    private readonly Mock<IMapper> mapper = new Mock<IMapper>();
    private readonly Mock<ICategoryRepository> categoryRepository = new Mock<ICategoryRepository>();
    private readonly Mock<IProductInstanceRepository> productInstanceRepository = new Mock<IProductInstanceRepository>();
    private readonly ProductService productService;

    public CreateTests()
    {
        productService = new ProductService(
            logger.Object,
            productRepository.Object,
            mapper.Object,
            categoryRepository.Object,
            productInstanceRepository.Object);
    }
    [Theory]
    [ClassData(typeof(InvalidProductCreateData))]
    public async Task CreateAsync_InvalidData_ThrowsValidationException(ProductRequest invalidRequest)
    {
        productRepository.Setup(repo => repo.AnyAsync(It.IsAny<Expression<Func<ProductEntity, bool>>>())).ReturnsAsync(false);

        await Assert.ThrowsAsync<ServiceException>(() => productService.CreateAsync(invalidRequest));
    }
    [Theory]
    [ClassData(typeof(InvalidProductCreateData))]
    public async Task CreateAsync_ValidationFailure_ThrowsServiceException(ProductRequest invalidRequest)
    {
        productRepository.Setup(repo => repo.AnyAsync(It.IsAny<Expression<Func<ProductEntity, bool>>>())).ReturnsAsync(false);

        await Assert.ThrowsAsync<ServiceException>(() => productService.CreateAsync(invalidRequest));
    }
    [Theory]
    [ClassData(typeof(InvalidProductCreateData))]
    public async Task CreateAsync_InvalidVersion_ThrowsServiceException(ProductRequest invalidRequest)
    {
        productRepository.Setup(repo => repo.AnyAsync(It.IsAny<Expression<Func<ProductEntity, bool>>>())).ReturnsAsync(false);

        invalidRequest.Version = -1;

        await Assert.ThrowsAsync<ServiceException>(() => productService.CreateAsync(invalidRequest));
    }
    [Theory]
    [ClassData(typeof(InvalidProductCreateData))]
    public async Task CreateAsync_InvalidSalesChannel_ThrowsServiceException(ProductRequest invalidRequest)
    {
        productRepository.Setup(repo => repo.AnyAsync(It.IsAny<Expression<Func<ProductEntity, bool>>>())).ReturnsAsync(false);

        invalidRequest.SalesChannel = "INVALID_CHANNEL";  // Assume this is not a valid channel

        await Assert.ThrowsAsync<ServiceException>(() => productService.CreateAsync(invalidRequest));
    }
    [Theory]
    [ClassData(typeof(InvalidProductCreateData))]
    public async Task CreateAsync_EdgeCaseProductCode_ThrowsServiceException(ProductRequest invalidRequest)
    {
        productRepository.Setup(repo => repo.AnyAsync(It.IsAny<Expression<Func<ProductEntity, bool>>>())).ReturnsAsync(false);

        invalidRequest.Code = new string('x', 256);  // Assuming the maximum allowed length is 255

        await Assert.ThrowsAsync<ServiceException>(() => productService.CreateAsync(invalidRequest));
    }


    [Fact]
    public async Task CreateAsync_InvalidLanguageInImages_ThrowsServiceException()
    {
        // Arrange
        var invalidRequest = new ProductRequest
        {
            CategoryId = Guid.NewGuid(),
            Code = "VALID_CODE",
            Images = new List<ImageData>
            {
                new ImageData { ImageId = Guid.NewGuid(), Language = "invalid", DisplayOrder = 1 }
            }
        };

        productRepository.Setup(repo => repo.AnyAsync(It.IsAny<Expression<Func<ProductEntity, bool>>>())).ReturnsAsync(false);
        categoryRepository.Setup(repo => repo.ExistsAsync(It.IsAny<Guid>())).ReturnsAsync(true);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ServiceException>(() => productService.CreateAsync(invalidRequest));
        exception.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task CreateAsync_ExceedingImageLimits_ThrowsServiceException()
    {
        // Arrange
        var invalidRequest = new ProductRequest
        {
            CategoryId = Guid.NewGuid(),
            Code = "VALID_CODE",
            Images = Enumerable.Range(1, 7).Select(i => new ImageData
            {
                ImageId = Guid.NewGuid(),
                Language = "en",
                DisplayOrder = i
            }).ToList()
        };

        productRepository.Setup(repo => repo.AnyAsync(It.IsAny<Expression<Func<ProductEntity, bool>>>())).ReturnsAsync(false);
        categoryRepository.Setup(repo => repo.ExistsAsync(It.IsAny<Guid>())).ReturnsAsync(true);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ServiceException>(() => productService.CreateAsync(invalidRequest));
        exception.StatusCode.Should().Be(HttpStatusCode.InternalServerError);
    }
}