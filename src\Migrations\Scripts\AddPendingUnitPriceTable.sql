    CREATE TABLE PendingUnitPrice (
    Id UNIQUEIDENTIFIER PRIMARY KEY, -- Primary Key
    UnitPriceId UNIQUEIDENTIFIER NULL, -- Foreign Key to UnitPrice table (optional)
    ProductID UNIQUEIDENTIFIER NOT NULL, -- Foreign Key to Products table
    MCCID UNIQUEIDENTIFIER NOT NULL, -- Foreign Key to MCC table
    BusinessTypeID UNIQUEIDENTIFIER NOT NULL, -- Foreign Key to BusinessTypes table
    UnitPrice DECIMAL(9, 2) NOT NULL, -- Original Unit Price
    VATRate DECIMAL(9, 2) NOT NULL, -- VAT Rate
    VATType INT NOT NULL, -- Enum for VAT Type
    BillingType INT NOT NULL, -- Enum for Billing Type
    BillingFrequency INT NOT NULL, -- Enum for Billing Frequency
    NewPrice DECIMAL(9, 2) NULL, -- Requested new price
    ActionType INT NOT NULL, -- Enum for ActionTypesEnum (Add, Edit, Delete)
    Status INT NOT NULL, -- Enum for StatusEnum (Pending, Approved, Rejected)
    CreatedBy VARCHAR(255) NOT NULL, -- Created by user
    CreatedDate DATETIME NOT NULL, -- Creation timestamp
    UpdatedBy VARCHAR(255), -- Updated by user
    UpdatedDate DATETIME, -- Update timestamp
);

