﻿DECLARE @ListOfProducts TABLE(ProductName varchar(100))
INSERT INTO @ListOfProducts VALUES ('VX_675'),('Spectra_SP_530')

DECLARE @GoFamilyCategoryId UNIQUEIDENTIFIER
DECLARE @productName varchar(100)
DECLARE @ProductIds TABLE(ID UNIQUEIDENTIFIER)
DECLARE @IdtoInsert UNIQUEIDENTIFIER
DECLARE @VISA_POS UNIQUEIDENTIFIER
DECLARE @MC_POS UNIQUEIDENTIFIER
DECLARE @MADA_POS UNIQUEIDENTIFIER

SELECT TOP 1 @GoFamilyCategoryId = ID FROM [PRODUCTS].[dbo].[Category] where  Code = 'GO_FAMILY' and CounterParty = 'GEIDEA_SAUDI'
SELECT @VISA_POS = ID from [PRODUCTS].[dbo].[Products] where Code = 'VISA_POS' and CounterParty = 'GEIDEA_SAUDI'
SELECT @MC_POS = ID from [PRODUCTS].[dbo].[Products] where Code = 'MC_POS' and CounterParty = 'GEIDEA_SAUDI'
SELECT @MADA_POS = ID from [PRODUCTS].[dbo].[Products] where Code = 'MADA_POS' and CounterParty = 'GEIDEA_SAUDI'

DECLARE db_cursor_insert CURSOR FOR 
SELECT ProductName 
FROM @ListOfProducts

OPEN db_cursor_insert  
FETCH NEXT FROM db_cursor_insert INTO @productName 

WHILE @@FETCH_STATUS = 0  
BEGIN 

--add products
INSERT INTO [PRODUCTS].[dbo].[Products](Availability, Code, Type, Description, CreatedBy, CreatedDate, ValidFrom, [Version], Counterparty, Flow, SalesChannel, QuickOnboarding, ReferralChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Obsolete', @productName, 'TERMINAL', @productName, '00000000-0000-0000-0000-000000000000', GETUTCDATE(), GETUTCDATE(), 0, 'GEIDEA_SAUDI', 'Normal', NULL, 0, 'MERCHANT_MIGRATION')


SELECT TOP 1 @IdtoInsert = ID FROM @ProductIds
DELETE FROM @ProductIds


--prices
INSERT INTO [PRODUCTS].[dbo].[Prices] ([ChargeFrequency],[ChargeType],[ExemptFromVAT],[ProductId],[PerItemPrice],[Priority],[ValidFrom],[DeletedFlag],[CreatedBy],[CreatedDate],[Currency])
VALUES ('ONE_OFF', 'RETAIL_PRICE', 0, @IdtoInsert, 0, 1, GETUTCDATE(), 0, '00000000-0000-0000-0000-000000000000', GETUTCDATE(), 'SAR')


--insert in product category
INSERT INTO [PRODUCTS].[dbo].[ProductCategories](ProductId, CategoryId) VALUES(@IdtoInsert, @GoFamilyCategoryId)
 
 
--add part
 INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId, Quantity) VALUES(@IdtoInsert, @VISA_POS, 1)
 INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId, Quantity) VALUES(@IdtoInsert, @MC_POS, 1)
 INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId, Quantity) VALUES(@IdtoInsert, @MADA_POS, 1) 


 --add Sarie fee
 INSERT INTO [PRODUCTS].[dbo].[Prices] ([ChargeType],[ExemptFromVAT],[ProductId],[PerItemPrice],[Priority],[ValidFrom],[DeletedFlag],[CreatedBy],[CreatedDate],[Currency])
 VALUES ('SARIE_CHARGE', 0, @IdtoInsert, 0, 1, GETUTCDATE(), 0, '00000000-0000-0000-0000-000000000000', GETUTCDATE(), 'SAR')

FETCH NEXT FROM db_cursor_insert INTO @productName  
END 

CLOSE db_cursor_insert  
DEALLOCATE db_cursor_insert