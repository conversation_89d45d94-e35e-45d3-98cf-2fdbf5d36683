﻿using System;

namespace Common.Models;

public class FindPriceRequest
{
    public Guid? Id { get; set; }
    public string? ChargeFrequency { get; set; }
    public string? ChargeType { get; set; }
    public bool? ExemptFromVAT { get; set; }
    public Guid? ProductId { get; set; }
    public int? PerItemPrice { get; set; }
    public int? PercentagePrice { get; set; }
    public int? Threshold { get; set; }
    public string? ThresholdType { get; set; }
    public string? Group { get; set; }
    public bool OnlyValid { get; set; } = true;
    public string? Currency { get; set; } = null!;
}
