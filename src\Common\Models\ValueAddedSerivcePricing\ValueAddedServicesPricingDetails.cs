﻿using Common.Enums;
using Common.Enums.ProductCommisssionPrices;
using Common.Enums.UnitPrice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.ValueAddedSerivcePricing;
public class ValueAddedServicesPricingDetails
{
    public Guid Id { get; set; }
    public Guid ProductID { get; set; }
    public Guid MCCID { get; set; }
    public Guid BusinessTypeID { get; set; }
    public Guid VASID { get; set; }
    public decimal SubscriptionFee { get; set; }
    public VatType FeeType { get; set; }
    public BillingType BillingType { get; set; }
    public PriceBillingFrequency BillingFrequency { get; set; }
}
