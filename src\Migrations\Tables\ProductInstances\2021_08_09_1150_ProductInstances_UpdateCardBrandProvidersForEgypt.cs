﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_08_09_1150)]
public class ProductInstances_UpdateCardBrandProvidersForEgypt : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
DECLARE @egyptIds table(
            id  uniqueidentifier
            )
insert into @egyptIds
SELECT pi.[Id]
FROM ProductInstances pi
INNER JOIN Products p ON p.id = pi.ProductId
WHERE [Metadata] IS NOT NULL
AND p.Type = 'GWAY' 
AND ISJSON([Metadata]) > 0 
AND JSON_VALUE(pi.Metadata, '$.MerchantCountry') = 'EGY'
AND JSON_QUERY([Metadata], '$.CardBrandProviders') IS NOT NULL
AND JSON_QUERY([Metadata], '$.CardBrandProviders[0]') IS NOT NULL

UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.CardBrandProviders', JSON_QUERY(REPLACE(JSON_QUERY([Metadata], '$.CardBrandProviders'),'MADA','MEEZA'),'$')) 
FROM ProductInstances pi
INNER JOIN @egyptIds e ON e.id = pi.Id
WHERE JSON_QUERY([Metadata], '$.CardBrandProviders') LIKE '%MADA%';

UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.CardBrandProviders', JSON_QUERY(REPLACE(JSON_QUERY([Metadata], '$.CardBrandProviders'),']',', { ""CardBrand"": ""MEEZA"", ""AcquiringProvider"": ""MPGS"", ""ThreeDSecureProvider"": ""MPGS"" } ]'),'$')) 
FROM ProductInstances pi
INNER JOIN @egyptIds e ON e.id = pi.Id
WHERE JSON_QUERY([Metadata], '$.CardBrandProviders') NOT LIKE '%MEEZA%'");
    }
}
