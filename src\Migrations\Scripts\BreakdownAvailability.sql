USE PRODUCTS
GO

DECLARE @Id UNIQUEIDENTIFIER
DECLARE @Availability NVARCHAR(255)
DECLARE @Code NVARCHAR(255)
DECLARE @Type NVARCHAR(255)
DECLARE @Version INT

DECLARE @SalesChannel NVARCHAR(255)
DECLARE @Flow NVARCHAR(255)
DECLARE @NewAvailability NVARCHAR(255)
DECLARE @ProductIsObsolete BIT

DECLARE product_cursor CURSOR FOR
SELECT Id, [Availability], Code, [Type], [Version] FROM [dbo].[Products]
ORDER BY Code, [Version]

OPEN product_cursor

FETCH NEXT FROM product_cursor
INTO @Id, @Availability, @Code, @Type, @Version

WHILE @@FETCH_STATUS = 0
BEGIN

IF @Availability = 'Obsolete'
BEGIN
	SET @Availability = (SELECT TOP 1 [Availability] FROM [dbo].[Products]
	WHERE Code = @Code
	ORDER BY [Version] DESC)
	
	SET @SalesChannel = 'All'
	SET @Flow = 'Normal'
	SET @ProductIsObsolete = 1
END

IF @Availability = 'Bundle'
BEGIN
	SET @SalesChannel = 'All'
	SET @Flow = 'Normal'
	SET @NewAvailability = 'Live'
END

IF @Availability = 'Bundle_HelpRequired'
BEGIN
	SET @SalesChannel = 'Onboarding'
	SET @Flow = 'HelpRequired'
	SET @NewAvailability = 'Live'
END

IF @Availability = 'Bundle_Preorder'
BEGIN
	SET @SalesChannel = 'Onboarding'
	SET @Flow = 'Preorder'
	SET @NewAvailability = 'Live'
END

IF @Availability = 'Normal' AND @Type = 'SCHEME'
BEGIN
	SET @SalesChannel = 'All'
	SET @Flow = 'Normal'
	SET @NewAvailability = 'Live'
END

IF @Availability = 'Normal' AND @Type <> 'SCHEME'
BEGIN
	SET @SalesChannel = 'Shop'
	SET @Flow = 'Normal'
	SET @NewAvailability = 'Live'
END

IF @Availability = 'PartOf'
BEGIN
	SET @SalesChannel = 'All'
	SET @Flow = 'Normal'
	SET @NewAvailability = 'Live'
END

IF @ProductIsObsolete = 1
BEGIN
	SET @NewAvailability = 'Obsolete'
	SET @ProductIsObsolete = 0
END

UPDATE [dbo].[Products] SET [Availability] = @NewAvailability, SalesChannel = @SalesChannel, Flow = @Flow WHERE Id = @Id

FETCH NEXT FROM product_cursor
INTO @Id, @Availability, @Code, @Type, @Version
END

CLOSE product_cursor
DEALLOCATE product_cursor