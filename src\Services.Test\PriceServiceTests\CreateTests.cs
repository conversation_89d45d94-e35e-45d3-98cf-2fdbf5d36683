﻿using AutoMapper;
using Common;
using Common.Entities;
using Common.Models;
using Common.Services;
using DataAccess;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using ProductService;
using Services.Test.TestData;
using System;
using System.Net;
using System.Threading.Tasks;
using Geidea.Utils.Counterparty.Providers;
using Xunit;
using Common.Options;
using Microsoft.Extensions.Options;
using NSubstitute;

namespace Services.Test.PriceServiceTests;

public class CreateTests
{
    private readonly Mock<ILogger<PriceService>> logger = new Mock<ILogger<PriceService>>();
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly Mock<IProductService> productService = new Mock<IProductService>();
    private readonly DataContext context;
    private readonly PriceRepository priceRepository;
    private readonly PriceService priceService;
    private readonly IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;
    private readonly ICounterpartyProvider counterpartyProvider;
    public CreateTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
           .UseInMemoryDatabase(databaseName: "PriceCreateTests" + Guid.NewGuid().ToString())
           .Options;

        context = new DataContext(options, new CounterpartyProvider());

        softposEgyptFeatureToggle = Substitute.For<IOptions<SoftposEgyptFeatureToggle>>();
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = true });
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();

        var productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);
        priceRepository = new PriceRepository(context, httpContext.Object);
        priceService = new PriceService(logger.Object, productRepository, priceRepository,
            mapper, productService.Object);
    }

    [Theory]
    [ClassData(typeof(InvalidPriceData))]
    public async Task InvalidData(PriceRequest request)
    {
        await priceService
            .Invoking(x => x.CreateAsync(request))
            .Should().ThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task NoProduct()
    {
        var request = new PriceRequest
        {
            ChargeFrequency = "RECCURRING_CHARGE",
            ChargeType = "MONTH",
            Currency = "EUR",
            ExemptFromVAT = true,
            Group = "group",
            MaxPrice = 100,
            PercentagePrice = 10,
            PerItemPrice = 20,
            Priority = 0,
            RentalPeriod = 24,
            Threshold = 200,
            ThresholdType = "LT",
            ProductId = Guid.NewGuid()
        };

        await priceService
            .Invoking(x => x.CreateAsync(request))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
            x.ProblemDetails.Type == Errors.ProductNotFound.Code);
    }

    [Fact]
    public async Task ProductUsed()
    {
        var productInstance = new ProductInstanceEntity
        {
            Product = new ProductEntity
            {
                Code = "TEST"
            }
        };
        context.ProductInstances.Add(productInstance);
        context.SaveChanges();
        productService.Setup(x => x.IsProductUsedAsync(It.IsAny<Guid>())).Returns(Task.FromResult(true));

        var request = new PriceRequest
        {
            ChargeFrequency = "RECCURRING_CHARGE",
            ChargeType = "MONTH",
            Currency = "EUR",
            ExemptFromVAT = true,
            Group = "group",
            MaxPrice = 100,
            PercentagePrice = 10,
            PerItemPrice = 20,
            Priority = 0,
            RentalPeriod = 24,
            Threshold = 200,
            ThresholdType = "LT",
            ProductId = productInstance.ProductId
        };

        await priceService
            .Invoking(x => x.CreateAsync(request))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.InvalidProductPriceRequest.Code);
    }

    [Fact]
    public async Task CreatePrice()
    {
        var product = new ProductEntity
        {
            Code = "TEST"
        };
        context.Products.Add(product);
        context.SaveChanges();

        var request = new PriceRequest
        {
            ChargeFrequency = "RECCURRING_CHARGE",
            ChargeType = "MONTH",
            Currency = "EUR",
            ExemptFromVAT = true,
            Group = "group",
            MaxPrice = 100,
            PercentagePrice = 10,
            PerItemPrice = 20,
            Priority = 0,
            RentalPeriod = 24,
            Threshold = 200,
            ThresholdType = "LT",
            ProductId = product.Id
        };

        var created = await priceService.CreateAsync(request);

        var price = await priceRepository.FirstOrDefaultAsync(p => p.Id == created.Id);
        price.Should().NotBeNull();
    }
}
