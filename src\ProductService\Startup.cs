using Common.Models.ProductInstance;
using Common.Repositories;
using Common.Repositories.Gle;
using Common.Services;
using Common.Services.Acquirers;
using Common.Services.Gle;
using DataAccess;
using DataAccess.Repositories;
using DataAccess.Repositories.Gle;
using FluentValidation;
using Geidea.PaymentGateway.ConfigServiceClient;
using Geidea.Utils.Counterparty;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.DataAccess.Extensions;
using Geidea.Utils.Exceptions;
using Geidea.Utils.HealthChecks;
using Geidea.Utils.Logging;
using Geidea.Utils.Migrations.FluentMigrator;
using Geidea.Utils.Swagger;
using Geidea.Utils.Validation;
using Geidea.Utils.Versioning;
using GeideaPaymentGateway.Utils.RabbitMQ.Extensions;
using Messaging;
using Microsoft.AspNetCore.Authentication.Negotiate;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using ProductService.Extensions;
using ProductService.Filters;
using Services;
using Services.Acquirers;
using Services.Gle;
using Services.Settings;
using System;
using System.IO;
using System.Reflection;
using Elastic.Apm.NetCoreAll;
using Common.Options;
using System.Configuration;
using Geidea.Utils.HttpClientExtensions;
using System.Linq;
using FluentMigrator.Runner;
using MongoDB.Driver.Core.Configuration;

namespace ProductService;

public class Startup
{
    public Startup(IConfiguration configuration, IWebHostEnvironment environment)
    {
        Configuration = configuration;
        Environment = environment;
    }

    private IConfiguration Configuration { get; }
    private readonly IWebHostEnvironment Environment;

    public void ConfigureServices(IServiceCollection services)
    {
        services.AddRabbitMqOptions(Configuration)
            .Configure<ApplePaySettings>(Configuration.GetSection("ApplePay"))
            .Configure<ApexSettings>(Configuration.GetSection("ApexSettings"))
            .Configure<ExternalApiSettings>(Configuration.GetSection("ExternalApiSettings"));

        services.AddRabbitMqConnectionFactory();

        services.AddStructuredLogging(Configuration);
        services.AddControllers();

        services.AddMvc(options => options.EnableEndpointRouting = false)
           .AddNewtonsoftJson(opt => opt.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore);

        // Register options
        services.Configure<MeezaSettings>(Configuration.GetSection("Meeza"));
        services.Configure<CurrencySettings>(Configuration.GetSection("Currency"));
        services.Configure<MpgsAccountsSettings>(Configuration.GetSection("MpgsConfig"));

        services.Configure<DatabaseOptions>(o =>
        {
            Configuration.GetSection("ProductDatabase").Bind(o);
            new DatabaseOptionsValidator().ValidateAndThrow(o);
        });

        services.AddHealthCheckServicesAndOptions(Configuration);
        services.AddPollyHttpClient<IApexClient, ApexClient>(Configuration);

        // Register services
        services.AddHealthChecks()
                .AddCommonHealthChecks()
                .AddSqlServer(Configuration["ProductDatabase:ConnectionString"], timeout: TimeSpan.FromSeconds(5), name: "DbConnectivity")
                .AddRabbitMqHealthChecks(Configuration);
        services.AddSingleton<MigrationService>();
        services.AddTransient<IProductService, Services.ProductService>();
        services.AddTransient<IPriceService, PriceService>();
        services.AddTransient<IProductConfiguratorService, ProductConfiguratorService>();
        services.AddTransient<ICategoryService, CategoryService>();
        services.AddTransient<ICertificateService, CertificateService>();
        services.AddTransient<IOpenSslCommandProcessor, OpenSslCommandProcessor>();
        services.AddTransient<ITerminalDataSetService, TerminalDataSetService>();
        services.AddTransient<IOrderGeneratedReportService, OrderGeneratedReportService>();
        services.AddTransient<IMetaDataMigrationService, MetaDataMigrationService>();

        services.AddTransient<IGleMerchantService, GleMerchantService>();
        services.AddTransient<IGleStoreService, GleStoreService>();
        services.AddTransient<IGleTerminalService, GleTerminalService>();
        services.AddTransient<IGleUpdateHistoryService, GleUpdateHistoryService>();
        services.AddTransient<IVendorService, VendorService>();
        services.AddTransient<IApexService, ApexService>();
        services.AddTransient<IApexToken, ApexToken>();

        // Add data contexts & repositories
        services.AddRepository();
        services.AddTransient<IProductRepository, ProductRepository>();
        services.AddTransient<IPriceRepository, PriceRepository>();
        services.AddTransient<IProductInstanceRepository, ProductInstanceRepository>();
        services.AddTransient<ICategoryRepository, CategoryRepository>();
        services.AddTransient<ITerminalDataSetRepository, TerminalDataSetRepository>();
        services.AddTransient<IOrderGeneratedReportRepository, OrderGeneratedReportRepository>();

        services.AddTransient<IGleMerchantRepository, GleMerchantRepository>();
        services.AddTransient<IGleStoreRepository, GleStoreRepository>();
        services.AddTransient<IGleTerminalRepository, GleTerminalRepository>();
        services.AddTransient<IGleUpdateHistoryRepository, GleUpdateHistoryRepository>();

        services.AddTransient<IMetaDataMigrationFilesRepository, MetaDataMigrationFilesRepository>();
        services.AddTransient<IVendorRepository, VendorRepository>();

        services.AddTransient<IMetaDataMigrationFilesRepository, MetaDataMigrationFilesRepository>();

        AddDbContext(services);

        services.AddScoped<IProductInstancePublisher, GatewayConfigurationPublisher>();
        services.AddScoped<IProductInstancePublisher, MeezaConfigurationPublisher>();
        services.AddScoped<IProductInstancePublisher, TerminalConfigurationPublisher>();

        services.AddScoped<NbeAcquirer>();
        services.AddScoped<AlxAcquirer>();
        services.AddScoped<GeIdeaAcquirer>();

        services.AddTransient<ICommissionFeesRepository, CommissionFeesRepository>();
        services.AddTransient<INonTransactionalFeesRepository, NonTransactionalFeesRepository>();
        services.AddTransient<IMccCategoryRepository, MccCategoryRepository>();
        services.AddTransient<IMccRepository, MccRepository>();
        services.AddTransient<IBusinessTypeRepository, BusinessTypeRepository>();
        services.AddTransient<IUnitPriceRepository, UnitPriceRepository>();
        services.AddTransient<IPendingUnitPriceRepository, PendingUnitPriceRepository>();
        services.AddTransient<IPendingCommissionPriceRepository, PendingCommissionPriceRepository>();
        services.AddTransient<IProductCommissionPriceRepository, ProductCommissionPriceRepository>();
        services.AddTransient<IValueAddedServicePricingRepository, ValueAddedServicePricingRepository>();
        services.AddTransient<INonTransactionalPriceRepository, NonTransactionalPriceRepository>();
        services.AddTransient<IOnboardingUnitPriceRepository, OnboardingUnitPriceRepository>();
        services.AddTransient<IValueAddedServiceRepository, ValueAddedServiceRepository>();

        services.AddTransient<ICommissionFeesService, CommissionFeesService>();
        services.AddTransient<INonTransactionalFeesService, NonTransactionalFeesService>();
        services.AddTransient<IMccCategoryService, MccCategoryService>();
        services.AddTransient<IMccService, MccService>();
        services.AddTransient<IBusinessTypeService, BusinessTypeService>();
        services.AddTransient<IUnitPriceService, UnitPriceService>();
        services.AddTransient<IpendingUnitPriceService, PendingUnitPriceService>();
        services.AddTransient<IPendingCommissionPriceService, PendingCommissionPriceService>();
        services.AddTransient<IProductCommissionPriceService, ProductCommissionPriceService>();
        services.AddTransient<IValueAddedServicePricingService, ValueAddedServicePricingService>();
        services.AddTransient<INonTransactionalPriceService, NonTransactionalPriceService>();
        services.AddTransient<IOnboardingUnitPriceService, OnboardingUnitPriceService>();
        services.AddTransient<IValueAddedServiceService, ValueAddedServiceService>();

        services.AddScoped<Func<string, IAcquirer>>(
            serviceProvider => key =>
            {
                return key switch
                {
                    "NBE_BANK" => serviceProvider.GetRequiredService<NbeAcquirer>(),
                    "ALX_BANK" => serviceProvider.GetRequiredService<AlxAcquirer>(),
                    "GeIdea" => serviceProvider.GetRequiredService<GeIdeaAcquirer>(),
                    _ => throw new ServiceException(Common.Errors.InvalidAutoGeneratedAcquirer)
                };
            }
         );

        services.AddScoped<IProductInstancePublisherSelector, ProductInstancePublisherSelector>();
        services.AddScoped<ICounterpartyProvider, CounterpartyProvider>();
        services.AddScoped<IProductChangeSenderService, ProductChangeSenderService>();
        services
            .AddSingleton<IPointOfSaleResponseMessageClient, PointOfSaleResponseMessageClient>()
            .AddSingleton<PointOfSaleResponseMessageReceiver>();

        services.AddSingleton<IOrderMigrationSyncScriptClient, OrderMigrationSyncScriptClient>()
                .AddSingleton<OrderMigrationSyncTriggerMessageReceiver>();

        services.AddAutoMapper(typeof(Startup));

        services.AddSwaggerGen(options =>
        {
            options.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "Product Service",
                Version = "v1",
                Description = "Product Service"
            });

            options.IgnoreObsoleteActions();
            options.OperationFilter<CounterpartyHeaderFilter>();
            options.DocumentFilter<CommonSwaggerDocument>();

            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            options.IncludeXmlComments(xmlPath);
        });

        services.AddApplicationOptions(Configuration);

        services.AddAuthentication(NegotiateDefaults.AuthenticationScheme).AddNegotiate();

        ValidationHelpers.InitValidation();

        services.AddHttpContextAccessor();

    }

    public void Configure(IApplicationBuilder app, IWebHostEnvironment env, MigrationService migrationService)
    {
#if !DEBUG
        app.UseAllElasticApm(Configuration);
#endif

        app.UseMiddleware<SerilogRequestLogger>();
        app.UseMiddleware<ExceptionHandler>();
        app.UseMiddleware<CounterpartyHandler>();

        app.UseAuthentication();

        //Start Migrations
        var connectionString = Configuration["ProductDatabase:MigrationConnectionString"];
        var assemblyName = Configuration["ProductDatabase:MigrationAssembly"];

        if (Configuration.GetValue<bool>("Migrations:IsAutoMigrationEnabled"))
        {
            migrationService.MigrateUp(connectionString, assemblyName);
        }

        migrationService.ThrowIfHasMigrationsToApply(
            Configuration["ProductDatabase:ConnectionString"],
            Configuration["ProductDatabase:MigrationAssembly"]);
        //End Migrations

        app.UseVersionMiddleware();
        app.UseInternalConfigurationView();

        if (!env.IsProduction())
        {
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "ProductService");
                c.RoutePrefix = string.Empty;
            });
        }
        app.UseHealthChecks("/health", new HealthCheckOptions { AllowCachingResponses = false, ResponseWriter = HealthResponseWriter.WriteHealthCheckResponse });

        app.UseRouting();

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
        });
#if !DEBUG
        app.StartPointOfSaleResponseMessageConsumption();
        app.StartOrderMigrationRespondMessageConsumption();
#endif
    }

    private void AddDbContext(IServiceCollection services)
    {
        services.AddDbContext<DbContext, DataContext>(options =>
        {
            options.UseSqlServer(Configuration["ProductDatabase:ConnectionString"],
               sqlServerOptionsAction: sqlOptions =>
               {
                   sqlOptions.EnableRetryOnFailure(
                   maxRetryCount: 10,
                   maxRetryDelay: TimeSpan.FromSeconds(30),
                   errorNumbersToAdd: null);
               });
            options.EnableSensitiveDataLogging(Environment.IsDevelopment());
            if (Environment.IsDevelopment())
                options.ConfigureWarnings(warnings => warnings.Default(WarningBehavior.Log));
            else
                options.ConfigureWarnings(warnings => warnings.Ignore());
        });
    }
}
