﻿using Common.Models.MccCategory;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProductService.Controllers;
[Route("api/v1/[controller]")]
[ApiController]
public class MccCategoryController : ControllerBase
{
    private readonly IMccCategoryService mccCategoryService;

    public MccCategoryController(IMccCategoryService mccCategoryService)
    {
        this.mccCategoryService = mccCategoryService;
    }
    /// <summary>
    /// Get MCC categories list [Id, Name]
    /// </summary>
    /// <returns>
    /// Returns an <see cref="IActionResult"/> Indicating the result of operation.
    /// Success: returns http 200 ok with the list of MCC Categories.
    /// Failure: returns http 500 InternalServerError, with the result indicating the failure.
    /// </returns>
    [HttpGet("GetMccCategoriesList")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetMccCategoriesList()
    {
        var MccCategoriesList = await mccCategoryService.GetMccCategoriesList();
        return Ok(MccCategoriesList);
    }
}

