﻿using Common.Data;
using Common.Data.ProductType;
using Geidea.Utils.DataAccess.Entities;
using Geidea.Utils.Exceptions;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using Common.Entities.Gle;

namespace Common.Entities;

public class ProductInstanceEntity : AuditableEntity<Guid>
{
    public Guid? AgreementId { get; set; }
    public Guid? CompanyId { get; set; }
    public Guid? StoreId { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public bool DeletedFlag { get; set; }

    public Guid ProductId { get; set; }
    [ForeignKey("ProductId")]
    public ProductEntity Product { get; set; } = null!;

    public Guid? ParentId { get; set; }
    public Guid? ParentConfigurationId { get; set; }

    [ForeignKey("ParentId")]
    public ProductInstanceEntity? Parent { get; set; }
    public List<ProductInstanceEntity> Children { get; set; } = new List<ProductInstanceEntity>();
    public string? Metadata { get; set; }
    public string? EPosTicketId { get; set; }
    public bool EPosTicketCompleted { get; set; }
    public bool EPosBillPayments { get; set; }
    public DateTime? EPosLastUpdated { get; set; }
    public string? Mid { get; set; }
    public GatewayInstanceEntity? GatewayInstance { get; set; }
    public MeezaInstanceEntity? MeezaInstance { get; set; }
    public IReadOnlyCollection<GleTerminalEntity> GleTerminalEntities { get; set; } = null!;

    [NotMapped]
    public IData? Data
    {
        get => DeserializeObject();

        set => Metadata = JsonConvert.SerializeObject(value);
    }

    private IData? DeserializeObject()
    {
        if (Enum.TryParse<ProductTypes>(Product.Type, true, out var productType))
        {
            if (Metadata == null)
                return Activator.CreateInstance(productType.GetObjectType()) as IData;

            return JsonConvert.DeserializeObject(Metadata, productType.GetObjectType()) as IData;
        }

        throw new ServiceException(Errors.InvalidType);
    }
}
