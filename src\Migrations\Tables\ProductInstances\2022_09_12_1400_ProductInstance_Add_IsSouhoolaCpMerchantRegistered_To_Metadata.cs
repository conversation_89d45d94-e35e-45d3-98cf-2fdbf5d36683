﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_09_12_1400)]
public class ProductInstance_Add_IsSouhoolaCpMerchantRegistered_To_Metadata : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
                UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsSouhoolaCpMerchantRegistered', CAST(0 as BIT))
	            FROM ProductInstances pi
                INNER JOIN Products p ON p.id = pi.ProductId
                WHERE [Metadata] IS NOT NULL
                AND p.Type = 'GWAY' 
                AND ISJSON([Metadata]) > 0 
            ");
    }
}
