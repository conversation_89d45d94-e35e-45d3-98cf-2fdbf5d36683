﻿using AutoMapper;
using Common;
using Common.Entities;
using DataAccess;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using ProductService;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using Geidea.Utils.Counterparty.Providers;
using Xunit;
using Common.Options;
using Microsoft.Extensions.Options;
using NSubstitute;

namespace Services.Test.ProductServiceTests;

public class DeleteTests
{
    private readonly Mock<ILogger<ProductService>> logger = new Mock<ILogger<ProductService>>();
    private readonly Mock<ILogger<ProductInstanceRepository>> repoLogger = new Mock<ILogger<ProductInstanceRepository>>();
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private DataContext context;
    private ProductRepository productRepository;
    private ProductService productService;
    private IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;
    private ICounterpartyProvider counterpartyProvider;

    internal void Setup()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
           .UseInMemoryDatabase(databaseName: "ProductDeleteTests" + Guid.NewGuid().ToString())
           .Options;

        context = new DataContext(options, new CounterpartyProvider());

        softposEgyptFeatureToggle = Substitute.For<IOptions<SoftposEgyptFeatureToggle>>();
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = true });
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();

        productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);
        var categoryRepository = new CategoryRepository(context, httpContext.Object);
        var productInstanceRepo = new ProductInstanceRepository(context, httpContext.Object, repoLogger.Object, mapper);
        productService = new ProductService(logger.Object, productRepository, mapper, categoryRepository, productInstanceRepo);
    }

    [Fact]
    public async Task EmptyId()
    {
        Setup();

        await productService
            .Invoking(x => x.DeleteProductAsync(Guid.Empty))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.InvalidIdentifier.Code);
    }

    [Fact]
    public async Task UsedProduct()
    {
        Setup();
        var productInstance = new ProductInstanceEntity
        {
            Product = new ProductEntity
            {
                Code = "TEST"
            }
        };
        context.ProductInstances.Add(productInstance);
        context.SaveChanges();

        await productService
            .Invoking(x => x.DeleteProductAsync(productInstance.Product.Id))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.ProductInUse.Code);
    }

    [Fact]
    public async Task InexistingProduct()
    {
        Setup();

        await productService
            .Invoking(x => x.DeleteProductAsync(Guid.NewGuid()))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
                        x.ProblemDetails.Type == Errors.ProductNotFound.Code);
    }

    [Fact]
    public async Task DeleteProductAndParts()
    {
        Setup();
        var product = new ProductEntity
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000001"),
            Code = "TEST PROD",
            Parts = new List<ProductPartEntity>
                {
                    new ProductPartEntity
                    {
                        Part=new ProductEntity
                        {
                            Id= Guid.Parse("00000000-0000-0000-0000-000000000002"),
                            Code="TEST PART",
                            Parts = new List<ProductPartEntity>
                            {
                                new ProductPartEntity
                                {
                                    Part=new ProductEntity
                                    {
                                        Id= Guid.Parse("00000000-0000-0000-0000-000000000003"),
                                        Code="TEST SUBPART"
                                    }
                                }
                            }
                        },
                    }
                }
        };

        context.Products.Add(product);
        context.SaveChanges();

        await productService.DeleteProductAsync(product.Id);

        var retrievedProduct = await productRepository.FirstOrDefaultAsync(p => p.Id == Guid.Parse("00000000-0000-0000-0000-000000000001"));
        retrievedProduct.Should().BeNull();

        var part = await productRepository.FirstOrDefaultAsync(p => p.Id == Guid.Parse("00000000-0000-0000-0000-000000000002"));
        part.Should().BeNull();

        var subPart = await productRepository.FirstOrDefaultAsync(p => p.Id == Guid.Parse("00000000-0000-0000-0000-000000000003"));
        subPart.Should().BeNull();
    }
}
