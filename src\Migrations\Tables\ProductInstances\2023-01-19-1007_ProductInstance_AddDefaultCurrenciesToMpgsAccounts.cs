﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.ProductInstances;
[Migration(2023_01_25_1007)]
public class ProductInstance_AddDefaultCurrenciesToMpgsAccounts : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
             USE [PRODUCTS]

GO
/****** Object:  UserDefinedFunction [dbo].[AddMPGSAccountCurrnciesWithDefault]    Script Date: 1/24/2023 10:09:09 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
  CREATE OR ALTER     function [dbo].[AddMPGSAccountCurrnciesWithDefault]
		(
		 @OriginalMetaData nvarchar(MAX) , @CountryParty nvarchar(50), @IsMultiCurrencyEnabled nvarchar(5), @SupportedCurrencies nvarchar(MAX)
		)
		RETURNS nvarchar(MAX)
		    AS
		    BEGIN
	​	
			Declare @UpdatedMpgsAccountMetaData Nvarchar(MAX) ;
			Declare @UpdatedMpgsHistoryMetaData Nvarchar(MAX) ;
			Declare @DefaultCurrency as Nvarchar(200);

		
			SET @DefaultCurrency =  CASE 
									WHEN @IsMultiCurrencyEnabled = 'true' THEN @SupportedCurrencies
			                        WHEN @CountryParty = 'GEIDEA_EGYPT' THEN  N'[""EGP""]' 
			                        WHEN @CountryParty = 'GEIDEA_SAUDI' THEN  N'[""SAR""]'
								    WHEN @CountryParty = 'GEIDEA_UAE' THEN  N'[""AED""]'
		                            ELSE  N'[]'
		                       END;
		
			WITH EachMPGSAccountQuery AS (
			    SELECT JSON_MODIFY(t.[value], '$.Currencies',JSON_Query(@DefaultCurrency)  ) UpdatedMpgsAccount
			    FROM OPENJSON((SELECT JSON_QUERY(@OriginalMetaData, 'lax $.MpgsAccounts'))) t
			), UpdateMpgsAccountsQuery AS (
			    SELECT CONCAT('[',STRING_AGG(UpdatedMpgsAccount, ','),']') UpdatedMpgsAccounts
			    FROM EachMPGSAccountQuery
			)


			SELECT @UpdatedMpgsAccountMetaData = JSON_MODIFY(@OriginalMetaData, '$.MpgsAccounts', JSON_QUERY(UpdatedMpgsAccounts))
			FROM UpdateMpgsAccountsQuery;
		
		
	 
            WITH EachMPGSAccountHistoryQuery AS (
                SELECT JSON_MODIFY(h.[value], '$.Currencies',JSON_Query(@DefaultCurrency)  ) UpdatedMpgsAccountHistory
                FROM OPENJSON((SELECT JSON_QUERY(@OriginalMetaData, 'lax $.MpgsHistory'))) h
            ), UpdateMpgsAccountsHistoryQuery AS (
                SELECT CONCAT('[',STRING_AGG(UpdatedMpgsAccountHistory, ','),']') UpdatedMpgsAccountsHistory
                FROM EachMPGSAccountHistoryQuery
            )
        
            SELECT @UpdatedMpgsHistoryMetaData = JSON_MODIFY(@UpdatedMpgsAccountMetaData, '$.MpgsHistory', JSON_QUERY(UpdatedMpgsAccountsHistory))
            FROM UpdateMpgsAccountsHistoryQuery 
            ​
            Return @UpdatedMpgsHistoryMetaData​​
		
			
		END
            ");

        Execute.Sql(@"Update PRI set Metadata = [PRODUCTS].[dbo].[AddMPGSAccountCurrnciesWithDefault](PRI.Metadata , P.Counterparty,JSON_VALUE(PRI.Metadata,'$.IsMulticurrencyEnabled'), JSON_QUERY(PRI.Metadata,'$.Currencies'))
                      from [PRODUCTS].[dbo].[ProductInstances] PRI
                      join [PRODUCTS].[dbo].Products P
                      on PRI.ProductId = P.Id
                      WHERE [Metadata] IS NOT NULL
                      AND p.Type = 'GWAY'
                      AND ISJSON([Metadata]) > 0
");
    }
}
