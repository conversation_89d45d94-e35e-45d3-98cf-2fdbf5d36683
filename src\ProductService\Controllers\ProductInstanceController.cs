﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Models;
using Common.Models.ProductInstance;
using Common.Services;
using Geidea.ProductService.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;

namespace ProductService.Controllers;

[Route("api/v1/[controller]")]
[ApiController]
public class ProductInstanceController : ControllerBase
{
    private readonly IProductConfiguratorService _productConfiguratorService;

    public ProductInstanceController(IProductConfiguratorService ProductConfiguratorService)
    {
        _productConfiguratorService = ProductConfiguratorService;
    }

    /// <summary>
    /// Creates a new product instance.
    /// </summary>
    /// <returns>The newly created product instance.</returns>
    /// <response code="200">Returns the newly created product instance.</response>
    /// <response code="400">Returns the error.</response> 
    [HttpPost()]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> CreateProductInstance([FromBody] CreateProductInstanceRequest createProductInstanceRequest)
    {
        var createdConfiguration = await _productConfiguratorService.CreateAsync(createProductInstanceRequest);
        return Ok(createdConfiguration);
    }

    [HttpPost("bulk")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> CreateProductInstances([FromBody] List<CreateProductInstanceRequest> createProductInstancesRequest)
    {
        var createdConfigurations = await _productConfiguratorService.BulkCreateAsync(createProductInstancesRequest);
        return Ok(createdConfigurations);
    }

    /// <summary>
    /// Soft delete for a product instance based on different parameters.
    /// </summary>
    /// <response code="200">The product instance ids that have been deleted.</response>
    [HttpDelete]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [Produces("application/json")]
    public async Task<IActionResult> DeleteProductInstances([FromQuery] DeleteProductInstanceRequest deleteProductInstanceRequest)
    {
        var deletedConfiguration = await _productConfiguratorService.DeleteAsync(deleteProductInstanceRequest);
        return Ok(deletedConfiguration);
    }

    /// <summary>
    /// Get product instance by id.
    /// </summary>
    /// <response code="200">Returns a product instance</response>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [Produces("application/json")]
    public async Task<IActionResult> GetProductInstance(Guid id)
    {
        var productInstance = await _productConfiguratorService.FindByIdAsync(id);
        return Ok(productInstance);
    }

    [HttpGet]
    [Route("{storeId}/products")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetStoreProducts([FromRoute] Guid storeId)
    {
        var result = await _productConfiguratorService.GetProductInstancesForStore(storeId);
        return Ok(result);
    }

    [HttpPost("stores/products")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetMultipleStoresWithProducts([FromBody] IdsRequest request)
    {
        var result = await _productConfiguratorService.GetProductInstancesForMultipleStores(request.Ids);
        return Ok(result);
    }

    /// <summary>
    /// Search product instances based on criteria.
    /// </summary>
    /// <response code="200">Returns list of product instances</response>
    [HttpPost("Search")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [Produces("application/json")]
    public async Task<IActionResult> SearchProductInstances([FromBody] FindProductInstanceRequest findProductInstanceRequest)
    {
        var configurations = await _productConfiguratorService.FindAsync(findProductInstanceRequest);
        return Ok(configurations);
    }

    [HttpPost("SearchGatewayConfigurations")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [Produces("application/json")]
    public async Task<IActionResult> SearchGatewayConfigurations([FromBody] SearchGatewayConfigurationsRequest request)
    {
        var configurations = await _productConfiguratorService.SearchGatewayConfigurations(request);
        return Ok(configurations);
    }

    /// <summary>
    /// Returns the product instances corresponding to the requested ids.
    /// </summary>
    /// <response code="200">Search was successfull</response>
    [HttpPost("ids")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(ProductInstanceResponse[]), StatusCodes.Status200OK)]
    public async Task<IActionResult> FindByIds([FromBody] IdsRequest request)
    {
        var result = await _productConfiguratorService.FindByIdsAsync(request);
        return Ok(result);
    }

    /// <summary>
    /// Patch product instances.
    /// </summary>
    /// <response code="200">Returns updated product instance</response>
    /// <response code="404">Product instance with provided id not found</response> 
    /// <response code="404">Returns the error</response> 
    [HttpPatch("{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> PatchProductInstance(Guid id, [FromBody] JsonPatchDocument<UpdateProductInstanceRequest> patchDocument)
    {
        var createdConfiguration = await _productConfiguratorService.PatchAsync(id, patchDocument);
        return Ok(createdConfiguration);
    }

    [HttpPatch("updateTerminalProductInstancesMeta")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateTerminalProductInstancesMeta(List<UpdateProductInstanceMetaRequest> updateProductInstances)
    {
        var updatedIntanceIds = await _productConfiguratorService.UpdateTerminalProductInstancesMeta(updateProductInstances);
        return Ok(updatedIntanceIds);
    }

    /// <summary>
    /// Create default product instances for product
    /// </summary>
    /// <response code="200">Returns created instances</response>
    /// <response code="404">Product not found</response> 
    /// <response code="404">Returns the error</response>
    [HttpPost("default")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> CreateDefaultProductInstances(DefaultProductInstanceRequest request)
    {
        var createdInstances = await _productConfiguratorService.CreateDefaultProductInstancesAsync(request);
        return Ok(createdInstances);
    }

    /// <summary>
    /// Create default product instances for product, then product instances with its children
    /// </summary>
    /// <response code="200">Returns created instances</response>
    /// <response code="404">Product not found</response> 
    /// <response code="404">Returns the error</response>
    [HttpPost("defaultChildren")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> CreateDefaultProductInstancesChildren(DefaultProductInstanceRequest request)
    {
        var createdInstances = await _productConfiguratorService.CreateDefaultProductInstancesChildrenAsync(request);
        return Ok(createdInstances);
    }

    [HttpPost("default/merchantMigration")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> CreateMerchantMigrationDefaultProductInstances(MerchantMigrationProductInstanceRequest request)
    {
        var createdInstance = await _productConfiguratorService.CreateMerchantMigrationDefaultProductInstancesAsync(request);
        return Ok(createdInstance);
    }

    [HttpPost("base/search")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SearchProductInstanceBase(SearchProductInstanceBaseRequest request)
    {
        var response = await _productConfiguratorService.SearchProductInstanceBaseAsync(request);
        return Ok(response);
    }

    [HttpPost("storeName")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateProductInstancesStoreNameForMerchant(MerchantStoreNameProductInstanceRequest request)
    {
        await _productConfiguratorService.UpdateProductInstancesStoreNameForMerchant(request);
        return Ok();
    }

    [HttpPost("MpgsHistoryCleanup")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [Produces("application/json")]
    public async Task<IActionResult> DeleteMpgsHistoryOverEstimatedPeriod()
    {
        await _productConfiguratorService.CleanExpiredMPGSHistory();
        return Ok();
    }
    [HttpGet("IsSubscribedToPaymentGateway/{merchantId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> IsSubscribedToPaymentGateway(Guid merchantId)
    {
        return Ok(await _productConfiguratorService.IsSubscribedToPaymentGateway(merchantId));
    }
    [HttpPost("MigrateOldSubMerchantMetaData")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> MigrateOldSubMerchantMetaData(List<SubMerchantMigrationInfoProductInstance> request)
    {
        await _productConfiguratorService.UpdateProductInstancesMsoSubMerchantInfo(request);
        return Ok();
    }
    /// <summary>
    /// Get All gateway company Ids
    /// </summary>
    /// <response code="200">Returns list of companyIds</response>
    [HttpGet("GetGatewayCompanyIds")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [Produces("application/json")]
    public async Task<IActionResult> GetGatewayCompanyIds()
    {
        var configurations = await _productConfiguratorService.FindGatewayProductCompanyIds();
        return Ok(configurations);
    }
}
