﻿using Xunit;
using static Geidea.ProductService.Models.Helpers;

namespace Services.Test;

public class ModelsHelperTests
{
    [Theory]
    [InlineData("test-domain.com", "test-domain.com")]
    [InlineData("sub-domain.test-domain.com", "sub-domain.test-domain.com")]
    [InlineData("www.test-domain.com", "www.test-domain.com")]
    [InlineData("www.sub-domain.test-domain.com", "www.sub-domain.test-domain.com")]

    [InlineData("https://test-domain.com", "test-domain.com")]
    [InlineData("https://sub-domain.test-domain.com", "sub-domain.test-domain.com")]
    [InlineData("https://www.test-domain.com", "www.test-domain.com")]
    [InlineData("https://www.sub-domain.test-domain.com", "www.sub-domain.test-domain.com")]

    [InlineData("test-domain.com/path/test?test=test&dummy=dummy", "test-domain.com")]
    [InlineData("sub-domain.test-domain.com/path/test?test=test&dummy=dummy", "sub-domain.test-domain.com")]
    [InlineData("www.test-domain.com/path/test?test=test&dummy=dummy", "www.test-domain.com")]
    [InlineData("www.sub-domain.test-domain.com/path/test?test=test&dummy=dummy", "www.sub-domain.test-domain.com")]

    [InlineData("https://test-domain.com/path/test?test=test&dummy=dummy", "test-domain.com")]
    [InlineData("https://sub-domain.test-domain.com/path/test?test=test&dummy=dummy", "sub-domain.test-domain.com")]
    [InlineData("https://www.test-domain.com/path/test?test=test&dummy=dummy", "www.test-domain.com")]
    [InlineData("https://www.sub-domain.test-domain.com/path/test?test=test&dummy=dummy", "www.sub-domain.test-domain.com")]

    [InlineData("ftp://test-domain.com", "test-domain.com")]
    [InlineData("ftp://sub-domain.test-domain.com", "sub-domain.test-domain.com")]

    [InlineData(null, null)]
    [InlineData("abc", "abc")]
    [InlineData("http://abc", "abc")]
    [InlineData("www.abc", "www.abc")]
    [InlineData("www.abc test", null)]
    [InlineData(" ", null)]

    public void ExtractDomainNameFromURLShouldReturnOnlyDomainName(string url, string expectedDomain)
    {
        var result = ExtractDomainNameFromURL(url);

        Assert.Equal(result, expectedDomain);
    }
}
