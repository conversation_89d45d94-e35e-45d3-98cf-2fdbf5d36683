﻿using AutoMapper;
using Common.Entities.Gle;
using Common.Models.Gle;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using System;

namespace Services.Test.TestData;
public class MappingProfileHelper : Profile
{
    public MappingProfileHelper()
    {
        CreateMap<GleMerchantEntity, GleMerchant>()
            .ForMember(dest => dest.GleStores, opt
                => opt.MapFrom(src => src.GleStoreEntities));
        CreateMap<GleMerchant, GleMerchantEntity>()
            .ForMember(dest => dest.GleStoreEntities, opt
                => opt.MapFrom(src => src.GleStores));
        CreateMap<GleMerchantEntity, GleMerchantRequest>().ReverseMap();
        CreateMap<GleMerchantEntity, GleBase>().ReverseMap();
        CreateMap<Operation<UpdateGleMerchantRequest>, Operation<GleMerchantEntity>>().ReverseMap();
        CreateMap<JsonPatchDocument<UpdateGleMerchantRequest>, JsonPatchDocument<GleMerchantEntity>>().ReverseMap();

        CreateMap<GleStoreEntity, GleStore>().ForMember(dest => dest.GleTerminals, opt
            => opt.MapFrom(src => src.GleTerminalEntities));
        CreateMap<GleStore, GleStoreEntity>().ForMember(dest => dest.GleTerminalEntities, opt
            => opt.MapFrom(src => src.GleTerminals));
        CreateMap<GleStoreEntity, GleStoreRequest>().ReverseMap();
        CreateMap<GleStoreEntity, GleBase>().ReverseMap();
        CreateMap<Operation<UpdateGleStoreRequest>, Operation<GleStoreEntity>>().ReverseMap();
        CreateMap<JsonPatchDocument<UpdateGleStoreRequest>, JsonPatchDocument<GleStoreEntity>>().ReverseMap();

        CreateMap<GleTerminalEntity, GleTerminal>().ReverseMap();
        CreateMap<GleTerminalEntity, GleTerminalRequest>().ReverseMap();
        CreateMap<GleTerminalEntity, GleBase>().ReverseMap();
        CreateMap<GleTerminalEntity, UpdateGleTerminalRequest>().ReverseMap();
        CreateMap<Operation<UpdateGleTerminalRequest>, Operation<GleTerminalEntity>>().ReverseMap();
        CreateMap<JsonPatchDocument<UpdateGleTerminalRequest>, JsonPatchDocument<GleTerminalEntity>>().ReverseMap();

        CreateMap<GleUpdateHistoryRequest, GleUpdateHistoryEntity>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => Guid.NewGuid()));

        CreateMap<GleUpdateHistoryEntity, GleUpdateHistory>();
    }
}