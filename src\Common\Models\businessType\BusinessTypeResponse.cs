﻿using Common.Enums;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.businessType;
[ExcludeFromCodeCoverage]
public class BusinessTypeResponse
{
    public Guid Id { get; set; }
    public string Code { get; set; } = null!;
    public string? Name { get; set; }
    public string? NameAr { get; set; }
    public Status Status { get; set; }
}
