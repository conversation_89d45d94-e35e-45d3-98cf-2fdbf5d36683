﻿using Common.Enums.UnitPrice;
using System;

namespace Common.Views;
public class UnitPriceListView
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public string? ProductName { get; set; }
    public string ProductCode { get; set; } = null!;
    public Guid MccId { get; set; }
    public string MccName { get; set; } = null!;
    public string MccCode { get; set; } = null!;
    public string MccCategory { get; set; } = null!;
    public int? ProductDisplayOrder { get; set; }
    public Guid BusinessTypeId { get; set; }
    public string BusinessType { get; set; } = null!;
    public decimal UnitPrice { get; set; }
    public decimal VATRate { get; set; }
    public VatType VATType { get; set; }
    public BillingType BillingType { get; set; }
    public BillingFrequency BillingFrequency { get; set; }
    public DateTime CreatedDate { get; set; }
    public string? PendingRequestCreatedBy { get; set; }
    public DateTime? PendingRequestCreatedDate { get; set; }
    public ActionTypesEnum? PendingRequestActionType { get; set; }
    public Guid? PendingRequestId { get; set; }
}
