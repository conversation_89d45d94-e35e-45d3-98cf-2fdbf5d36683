﻿using System;
using System.Diagnostics.CodeAnalysis;

namespace Common.Models.Gle;

[ExcludeFromCodeCoverage]
public class GleRegistrationStatusInfo
{
    public Guid GleInstanceId { get; set; }

    public string RegistrationType { get; set; } = null!;

    public DateTime RequestDate { get; set; }

    public string? SubmittedBy { get; set; }

    public string RegistrationStatus { get; set; } = null!;

    public string? RegistrationResponse { get; set; }

    public string? ReferenceMMSId { get; set; }
}