﻿using System;

namespace Common.Models.Gle;
public class GleUpdateHistoryRequest
{
    public string? GleUserId { get; set; }
    public string? GleLoginId { get; set; }
    public string? GleLoginId2 { get; set; }
    public string? ParentGleUserId { get; set; }
    public string? RequestType { get; set; }
    public Guid MerchantId { get; set; }
    public string? OldValue { get; set; }
    public string? NewValue { get; set; }
    public string? GleStatus { get; set; }
    public string? GleResponse { get; set; }
    public Guid? UserId { get; set; }
    public Guid TypeId { get; set; }
    public string? Type { get; set; }
}
