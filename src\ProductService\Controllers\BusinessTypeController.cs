﻿using Common.Models;
using Common.Models.businessType;
using Common.Models.CommissionFees;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Services;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Threading.Tasks;

namespace ProductService.Controllers;
[Route("api/v1/[controller]")]
[ApiController]
public class BusinessTypeController : ControllerBase
{
    private readonly IBusinessTypeService businessTypeService;
    public BusinessTypeController(IBusinessTypeService businessTypeService)
    {
        this.businessTypeService = businessTypeService;
    }
    /// <summary>
    /// Get business types list, based on search, filteration and sorting data in the request.
    /// </summary>
    /// <param name="request">An object that contains search, filter, sort and pagination data.</param>
    /// <returns>
    ///  Returns an <see cref="IActionResult"/> Indicating the result of operation.
    /// Success: returns http 200 ok with the list of business types.
    /// Bad request: returns http 4oo bad request, with the result indicating the reason.
    /// Failure: returns http 500 InternalServerError, with the result indicating the failure.
    /// </returns>
    [HttpPost("GetBusinessTypesList")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetBusinessTypesList(GetBusinessTypesListRequest request)
    {
        var BusinessTypesList = await businessTypeService.GetBusinessTypesList(request);
        return Ok(BusinessTypesList);
    }
    /// <summary>
    /// Create new business type to be added in AdminPanel
    /// </summary>
    /// <returns>Returns the created business type </returns>
    [HttpPost("CreateBusinessType")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreateAsync([FromBody] BusinessTypeRequest request)
    {
        var createdbusinessType = await businessTypeService.CreateAsync(request);
        return Ok(createdbusinessType);
    }
    /// <summary>
    /// Update an existing business type.
    /// </summary>
    /// <param name="id">The ID of the business type to update.</param>
    /// <param name="request">An object that contains the updated data for the business type.</param>
    /// <returns>
    /// Returns an <see cref="IActionResult"/> indicating the result of the operation.
    /// Success: returns HTTP 200 OK with the updated business type.
    /// Not Found: returns HTTP 404 Not Found if the business type does not exist.
    /// Bad Request: returns HTTP 400 Bad Request if the input is invalid.
    /// Failure: returns HTTP 500 InternalServerError if an error occurs during the update.
    /// </returns>
    [HttpPut("UpdateBusinessType/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> UpdateAsync(Guid id, [FromBody] BusinessTypeUpdateRequest request)
    {

        var updatedBusinessType = await businessTypeService.UpdateAsync(id, request);
        return Ok(updatedBusinessType);

    }
    /// <summary>
    /// Get business type record details, based on the passed Id. 
    /// </summary>
    /// <param name="Id">a passed guid param, to check if existed or not.</param>
    /// <returns>Business type record details as type [BusinessTypeDetailsResponse]</returns>
    [HttpGet("GetBusinessTypeDetails/{Id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetBusinessTypeDetails(Guid Id)
    {
        var BusinessTypeDetailsObj = await businessTypeService.GetBusinessTypeDetails(Id);
        return Ok(BusinessTypeDetailsObj);
    }
    /// <summary>
    /// Update business type record status, active or inactive, based on the passed parameter.
    /// </summary>
    /// <param name="Id">Business type record Id</param>
    /// <param name="Status">boolean parameter to set the status, 0 for in active, and 1 for active</param>
    /// <returns>
    /// Returns an <see cref="IActionResult"/> Indicating the result of operation.
    /// Success: returns http 200 ok.
    /// Bad request: returns http 4oo bad request, with the result indicating the reason.
    /// Failure: returns http 404 NotFound, In case there is no record with the passed Id.
    /// </returns>
    [HttpPost("ToggleStatus/{Id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ToggleStatus(Guid Id, [FromHeader] bool Status)
    {
        var result = await businessTypeService.ToggleStatus(Id, Status);
        if (!result)
            return NotFound();

        return Ok(result);
    }
    /// <summary>
    /// get business types Ids , Names ;
    /// </summary>
    [HttpGet("GetBusinessTypesNames")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(List<BasicBusinessTypesInfo>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetBusinessTypesNamesAsync()
    {
        var businessTypesNames = await businessTypeService.GetBusinessTypesNamesAsync();
        return Ok(businessTypesNames);
    }
}
