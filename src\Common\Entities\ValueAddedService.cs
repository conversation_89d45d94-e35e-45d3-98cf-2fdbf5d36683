﻿using Common.Enums;
using Geidea.Utils.DataAccess.Entities;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Entities;
[ExcludeFromCodeCoverage]
public class ValueAddedService : AuditableEntity<Guid>
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string NameAr { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string DescriptionAr { get; set; } = string.Empty;
    public Guid ImageId { get; set; }
    public Status Status { get; set; }
    public ICollection<ValueAddedServicePricingEntity>? ValueAddedServicePricings { get; set; }
}
