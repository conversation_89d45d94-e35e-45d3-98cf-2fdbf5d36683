﻿using Common.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using ProductService.Controllers;
using System;
using System.Threading.Tasks;
using Xunit;

namespace ProductService.Test;
public class MetaDataMigrationFilesControllerTests
{
    private readonly Mock<IMetaDataMigrationService> metaDataMigrationFilesService;
    private readonly MetaDataMigrationFilesController controller;

    public MetaDataMigrationFilesControllerTests()
    {
        metaDataMigrationFilesService = new Mock<IMetaDataMigrationService>();

        controller = new MetaDataMigrationFilesController(metaDataMigrationFilesService.Object, new Mock<ILogger<MetaDataMigrationFilesController>>().Object);
    }

    [Fact]
    public async Task UpdateFailedMetaDataMigrations_Returns204NoContent()
    {
        var response = await controller.UpdateMetaDataMigrations();

        var okResult = response as OkObjectResult;
        Assert.NotNull(okResult);

        metaDataMigrationFilesService.Verify(x => x.MigrateMetaDataScripts(), Times.Once);
    }

    [Fact]
    public void UpdateFailedMetaDataMigration_Returns400BadRequest()
    {
        metaDataMigrationFilesService.Setup(x => x.MigrateMetaDataScripts()).ThrowsAsync(new Exception());

        Assert.ThrowsAsync<Exception>(() => controller.UpdateMetaDataMigrations());

        metaDataMigrationFilesService.Verify(x => x.MigrateMetaDataScripts(), Times.Once);
    }
}
