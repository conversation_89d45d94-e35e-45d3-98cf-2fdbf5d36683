﻿using System;
using System.Collections.Generic;

namespace Geidea.ProductService.Models;

public class CyberSourceAccount
{
    public string? CyberSourceMerchantId { get; set; }
    public string? CyberSourceMerchantKeyId { get; set; }
    public string? CyberSourceSharedSecretKey { get; set; }
    public IReadOnlyCollection<string> CardBrands { get; set; } = new List<string>();
    public IReadOnlyCollection<string> Currencies { get; set; } = new List<string>();
    public DateTimeOffset CreatedDate { get; set; }
}