﻿using System;
using System.Threading.Tasks;
using Common.Models.ProductInstance;

namespace Common.Services;

public interface ICertificateService
{
    Task<string> CreateCsrAndPrivateKey(GenerateCsrRequest request);
    Task<ProductInstanceResponse> UploadSignedCertificate(Guid productInstanceId, byte[] signedCertificate);

    Task<string> CreateSamsungCsrAndPrivateKey(GenerateCsrRequest request);
}
