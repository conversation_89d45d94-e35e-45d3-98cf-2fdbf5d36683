﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.Category;
[Migration(2024_05_11_1050)]
public class AddNameToCategoryEntity : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "AddNameToCategoryEntity.sql"));
    }
}
