﻿Go
create or alter procedure ChangePartFromProduct @ProductCode nvarchar(255), @ProductVersion int,
												@OldPartCode nvarchar(255), @OldPartVersion int,
												@NewPartCode nvarchar(255), @NewPartVersion int,
												@Counterparty nvarchar(255)
as
<PERSON><PERSON><PERSON>

 declare @productId UNIQUEIDEN<PERSON><PERSON>ER
 declare @partId UNIQUEIDENTIFIER
 declare @oldPartId UNIQUEIDENTIFIER
 select top(1) @productId = Id from Products where Code=@ProductCode and [Version]=@ProductVersion and Counterparty=@Counterparty
 select top(1) @partId = Id from Products where Code=@NewPartCode and [Version]=@NewPartVersion and Counterparty=@Counterparty
 select top(1) @oldPartId = Id from Products where Code=@OldPartCode and [Version]=@OldPartVersion and Counterparty=@Counterparty
 
 delete from ProductParts where PartId = @oldPartId and ProductId = @productId
 insert into ProductParts(ProductId, PartId) values(@productId, @partId)

END