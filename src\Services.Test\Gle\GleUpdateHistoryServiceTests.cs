﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using Common.Entities.Gle;
using Common.Models;
using Common.Models.Gle;
using Common.Repositories.Gle;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;
using Services.Gle;
using Services.Test.TestData;
using Constants = Common.Constants;

namespace Services.Test.Gle;

public class GleUpdateHistoryServiceTests
{
    private readonly ILogger<GleUpdateHistoryService> logger = Substitute.For<ILogger<GleUpdateHistoryService>>();
    private IMapper mapper;
    private readonly IGleUpdateHistoryRepository gleUpdateHistoryRepository = Substitute.For<IGleUpdateHistoryRepository>();
    private GleUpdateHistoryService gleUpdateHistoryService;

    [SetUp]
    public void SetUp()
    {
        mapper = new Mapper(new MapperConfiguration(x => x.AddMaps(typeof(MappingProfileHelper))));
        gleUpdateHistoryService = new GleUpdateHistoryService(logger, mapper, gleUpdateHistoryRepository);
    }

    [Test]
    public async Task AddGleUpdateHistory_SuccessStatus()
    {
        //Arrange
        List<GleUpdateHistoryRequest> gleUpdateHistoryRequests = new List<GleUpdateHistoryRequest>();
        var request = new GleUpdateHistoryRequest
        {
            GleStatus = Constants.GleRegistrationStatus.Success,
            GleResponse = "Success",
            OldValue = Constants.GleUserCategoryCode.Chain,
            NewValue = Constants.GleUserCategoryCode.MasterBusiness,
            MerchantId = Guid.NewGuid(),
            RequestType = Constants.GleUpdateRequestType.UpdateMerchantTag,
            UserId = Guid.NewGuid()
        };
        gleUpdateHistoryRequests.Add(request);

        //Act and assert
        await gleUpdateHistoryService.Invoking(x => x.AddGleUpdateHistoryAsync(gleUpdateHistoryRequests))
            .Should().NotThrowAsync();

        await gleUpdateHistoryRepository.Invoking(x => x.AddGleUpdateHistoryAsync(Arg.Any<List<GleUpdateHistoryEntity>>()))
            .Should().NotThrowAsync();
    }

    [Test]
    public async Task AddGleUpdateHistory_FailedStatus()
    {
        //Arrange
        List<GleUpdateHistoryRequest> gleUpdateHistoryRequests = new List<GleUpdateHistoryRequest>();
        var request = new GleUpdateHistoryRequest
        {
            GleStatus = Constants.GleRegistrationStatus.Failed,
            GleResponse = "Failed",
            OldValue = Constants.GleUserCategoryCode.MasterBusiness,
            NewValue = Constants.GleUserCategoryCode.Wholesaler,
            MerchantId = Guid.NewGuid(),
            RequestType = Constants.GleUpdateRequestType.UpdateMerchantTag,
            UserId = Guid.NewGuid()
        };
        gleUpdateHistoryRequests.Add(request);
        //Act and assert
        await gleUpdateHistoryService.Invoking(x => x.AddGleUpdateHistoryAsync(gleUpdateHistoryRequests))
            .Should().NotThrowAsync();
    }

    [Test]
    public async Task UpdateGleUpdateHistory_FailedStatus()
    {
        //Arrange
        List<GleUpdateHistoryRequest> gleUpdateHistoryRequests = new List<GleUpdateHistoryRequest>();
        var request = new GleUpdateHistoryRequest
        {
            GleStatus = Constants.GleRegistrationStatus.Failed,
            GleResponse = "Failed",
            OldValue = Constants.GleUserCategoryCode.Retailer,
            NewValue = Constants.GleUserCategoryCode.MasterBusiness,
            MerchantId = Guid.Parse("ba18e2f5-974f-4b7c-b43b-8f5aaccc5549"),
            RequestType = Constants.GleUpdateRequestType.UpdateMerchantTag,
            UserId = Guid.NewGuid()
        };
        gleUpdateHistoryRequests.Add(request);

        //Act and assert
        await gleUpdateHistoryService.Invoking(x => x.AddGleUpdateHistoryAsync(gleUpdateHistoryRequests))
            .Should().NotThrowAsync();

        var entity = mapper.Map<GleUpdateHistoryEntity>(request);
        await gleUpdateHistoryRepository.Invoking(x => x.UpdateGleUpdateHistoryAsync(new List<GleUpdateHistoryEntity>() { entity }))
            .Should().NotThrowAsync();
    }

    [Test]
    public async Task GetGleUpdateHistoryByMerchantId_Success()
    {
        //Act and assert
        await gleUpdateHistoryService.Invoking(x => x.GetGleUpdateHistoryByMerchantIdAsync(Guid.Parse("ba18e2f5-974f-4b7c-b43b-8f5aaccc5549")))
            .Should().NotThrowAsync();

        await gleUpdateHistoryRepository.Received(1).FindGleUpdateHistoryAsync(Guid.Parse("ba18e2f5-974f-4b7c-b43b-8f5aaccc5549"), Constants.GleUpdateRequestType.UpdateMerchantTag);
    }

    [Test]
    public async Task UpdateGleUpdateHistoryAsync_Success()
    {
        List<GleUpdateHistoryEditRequest> request = new List<GleUpdateHistoryEditRequest> { new GleUpdateHistoryEditRequest()
        {
            Id = new Guid("58108a39-b572-4ab5-8966-cfc813f8fdf0"),
            GleStatus ="success",
            GleResponse ="success",
            UserId = Guid.NewGuid(),

        }};
        GleUpdateHistoryEntity entity = new()
        {
            Id = new Guid("58108a39-b572-4ab5-8966-cfc813f8fdf0"),
            CreatedBy = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.Now,
            GleUserId = "UserId",
            GleLoginId = "LoginId",
            GleLoginId2 = "LoginId2",
            ParentGleUserId = null,
            MerchantId = Guid.NewGuid(),
            RequestType = Constants.GleUpdateRequestType.UpdateMerchantTag,
            OldValue = "Old",
            NewValue = "New",
            GleStatus = "Success",
            GleResponse = "N/A"
        };
        List<GleUpdateHistoryEntity> gleUpdates = new List<GleUpdateHistoryEntity>
        {
            entity
        };

        gleUpdateHistoryRepository.FindGleUpdateHistoriesAsync(Arg.Any<List<Guid>>()).Returns(Task.FromResult(gleUpdates));

        await gleUpdateHistoryService.Invoking(x => x.UpdateGleUpdateHistoryAsync(request))
            .Should().NotThrowAsync();


    }
}