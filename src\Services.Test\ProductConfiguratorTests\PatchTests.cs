﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Common;
using Common.Entities;
using Common.Models.ProductInstance;
using Common.Repositories;
using Common.Services;
using DataAccess;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using ProductService;
using Services.Settings;
using Xunit;

namespace Services.Test.ProductConfiguratorTests;

public class PatchTests
{
    private readonly Mock<ILogger<ProductConfiguratorService>> logger = new Mock<ILogger<ProductConfiguratorService>>();
    private readonly Mock<ILogger<ProductInstanceRepository>> repoLogger = new Mock<ILogger<ProductInstanceRepository>>();
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly Mock<IProductService> productService = new Mock<IProductService>();
    private readonly Mock<ICounterpartyProvider> counterpartyProvider = new Mock<ICounterpartyProvider>();
    private readonly Mock<IProductChangeSenderService> productSender = new Mock<IProductChangeSenderService>();
    private readonly Mock<IOptionsMonitor<MeezaSettings>> meezaSettings = new Mock<IOptionsMonitor<MeezaSettings>>();
    private readonly Mock<IOptionsMonitor<CurrencySettings>> currencySettings = new Mock<IOptionsMonitor<CurrencySettings>>();
    private readonly Mock<IProductRepository> productRepository = new Mock<IProductRepository>();
    private readonly Mock<IOptionsMonitor<MpgsAccountsSettings>> mpgsAccountsSettings = new Mock<IOptionsMonitor<MpgsAccountsSettings>>();

    private readonly ProductInstanceRepository productInstanceRepo;
    private readonly ProductConfiguratorService productConfiguratorService;

    private readonly ProductInstanceEntity productInstance = new ProductInstanceEntity
    {
        Product = new ProductEntity
        {
            Type = "SCHEME",
            Code = "MC"
        },
        CompanyId = Guid.NewGuid(),
        StoreId = Guid.NewGuid()
    };
    private readonly ProductInstanceEntity productInstance1 = new ProductInstanceEntity
    {
        Product = new ProductEntity
        {
            Type = "SCHEME",
            Code = "MC"
        },
        CompanyId = Guid.NewGuid(),
        StoreId = Guid.NewGuid(),
        Children = new List<ProductInstanceEntity>
            {
                new ProductInstanceEntity
                {
                    Product = new ProductEntity
                    {
                        Type="TERMINAL",
                        Code="SOFTPOS"
                    }
                }
            }
    };
    private readonly ProductInstanceEntity productInstancePaymentGateway = new ProductInstanceEntity
    {
        Product = new ProductEntity
        {
            Type = "GWAY",
            Code = Constants.ProductCodes.PaymentGateway
        },
        CompanyId = Guid.NewGuid(),
        StoreId = Guid.NewGuid()
    };
    private readonly ProductInstanceEntity productInstancePGWBundle = new ProductInstanceEntity
    {
        Product = new ProductEntity
        {
            Type = "BUNDLE",
            Code = Constants.ProductCodes.PaymentGatewayBundle
        },
        CompanyId = Guid.NewGuid(),
        StoreId = Guid.NewGuid()
    };
    private readonly ProductInstanceEntity productInstancePGWBundle1 = new ProductInstanceEntity
    {
        Product = new ProductEntity
        {
            Type = "BUNDLE",
            Code = Constants.ProductCodes.PaymentGatewayBundle
        },
        CompanyId = Guid.NewGuid(),
        StoreId = Guid.NewGuid(),
        Children = new List<ProductInstanceEntity>
            {
                new ProductInstanceEntity
                {
                    Product = new ProductEntity
                    {
                        Type="GWAY",
                        Code=Constants.ProductCodes.PaymentGateway
                    }
                },
                new ProductInstanceEntity
                {
                    Product = new ProductEntity
                    {
                        Type="GWAY",
                        Code=Constants.ProductCodes.PaymentGateway
                    }
                }
            }
    };
    private readonly ProductInstanceEntity productInstancePayByLinkBundle = new ProductInstanceEntity
    {
        Product = new ProductEntity
        {
            Type = "BUNDLE",
            Code = Constants.ProductCodes.PayByLinkBundle
        },
        CompanyId = Guid.NewGuid(),
        StoreId = Guid.NewGuid()
    };
    private readonly ProductInstanceEntity productInstancePayByLinkBundle1 = new ProductInstanceEntity
    {
        Product = new ProductEntity
        {
            Type = "BUNDLE",
            Code = Constants.ProductCodes.PayByLinkBundle
        },
        CompanyId = Guid.NewGuid(),
        StoreId = Guid.NewGuid(),
        Children = new List<ProductInstanceEntity>
            {
                new ProductInstanceEntity
                {
                    Product = new ProductEntity
                    {
                        Type="GWAY",
                        Code=Constants.ProductCodes.PaymentGateway
                    }
                },
                new ProductInstanceEntity
                {
                    Product = new ProductEntity
                    {
                        Type="GWAY",
                        Code=Constants.ProductCodes.PaymentGateway
                    }
                }
            }
    };

    public PatchTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
           .UseInMemoryDatabase(databaseName: "ProductInstancePatchTests" + Guid.NewGuid().ToString())
           .Options;

        SetStoreIdsForBundleChildren();
        var context = new DataContext(options, new CounterpartyProvider());
        context.ProductInstances.Add(productInstance);
        context.ProductInstances.Add(productInstance1);
        context.ProductInstances.Add(productInstancePaymentGateway);
        context.ProductInstances.Add(productInstancePGWBundle);
        context.ProductInstances.Add(productInstancePGWBundle1);
        context.ProductInstances.Add(productInstancePayByLinkBundle);
        context.ProductInstances.Add(productInstancePayByLinkBundle1);
        context.SaveChanges();

        productInstanceRepo = new ProductInstanceRepository(context, httpContext.Object, repoLogger.Object, mapper);
        productConfiguratorService = new ProductConfiguratorService(
            meezaSettings.Object,
            logger.Object,
            mapper,
            productService.Object,
            productInstanceRepo,
            productRepository.Object,
            counterpartyProvider.Object,
            productSender.Object,
            currencySettings.Object, mpgsAccountsSettings.Object);
    }

    private void SetStoreIdsForBundleChildren()
    {
        foreach (var child in productInstancePGWBundle1.Children)
        {
            child.StoreId = productInstancePGWBundle1.StoreId;
        }

        foreach (var child in productInstancePayByLinkBundle1.Children)
        {
            child.StoreId = productInstancePayByLinkBundle1.StoreId;
        }
    }

    [Fact]
    public async Task InvalidPatch()
    {
        var patchDocument = new JsonPatchDocument<UpdateProductInstanceRequest>();
        patchDocument.Operations.Add(new Operation<UpdateProductInstanceRequest>("replace", "types", ""));

        await productConfiguratorService
            .Invoking(x => x.PatchAsync(productInstance.Id, patchDocument))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.InvalidPatchRequest.Code);
    }

    [Fact]
    public async Task InvalidPatch_ParentConfigurationId()
    {
        var patchDocument = new JsonPatchDocument<UpdateProductInstanceRequest>();
        patchDocument.Replace(x => x.ParentConfigurationId, Guid.NewGuid());

        await productConfiguratorService
            .Invoking(x => x.PatchAsync(productInstance.Id, patchDocument))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
            x.ProblemDetails.Type == Errors.ParentConfigurationNotFound.Code);
    }

    [Fact]
    public async Task ValidPatch_EposId()
    {
        var patchDocument = new JsonPatchDocument<UpdateProductInstanceRequest>();
        patchDocument.Replace(x => x.EPosTicketId, "ticket id");

        await productConfiguratorService.PatchAsync(productInstance.Id, patchDocument);

        var retrieved = await productInstanceRepo.FirstOrDefaultAsync(p => p.Id == productInstance.Id);
        retrieved.Should().NotBeNull();
        retrieved.EPosTicketId.Should().Be("ticket id");
    }

    [Fact]
    public async Task ValidPatch_ParentId()
    {
        var patchDocument = new JsonPatchDocument<UpdateProductInstanceRequest>();
        patchDocument.Replace(x => x.ParentId, productInstance.Id);

        await productConfiguratorService.PatchAsync(productInstance1.Id, patchDocument);
        var retrieved = await productInstanceRepo.FirstOrDefaultAsync(p => p.Id == productInstance1.Id);
        retrieved.Should().NotBeNull();
        retrieved.AgreementId.Should().Be(productInstance.AgreementId);
        retrieved.Children[0].AgreementId.Should().Be(productInstance.AgreementId);
    }

    [Fact]
    public async Task ValidPatch_ParentConfigurationId()
    {
        var patchDocument = new JsonPatchDocument<UpdateProductInstanceRequest>();
        patchDocument.Replace(x => x.ParentConfigurationId, productInstance.Id);

        await productConfiguratorService.PatchAsync(productInstance1.Id, patchDocument);
        var retrieved = await productInstanceRepo.FirstOrDefaultAsync(p => p.Id == productInstance1.Id);
        retrieved.Should().NotBeNull();
        retrieved.CompanyId.Should().Be(productInstance.CompanyId);
        retrieved.StoreId.Should().Be(productInstance.StoreId);
    }

    [Fact]
    public async Task InvalidPatchPaymentGatewayBundle_StoreId()
    {
        var patchDocument = new JsonPatchDocument<UpdateProductInstanceRequest>();
        patchDocument.Replace(x => x.StoreId, productInstancePGWBundle.StoreId);

        await productConfiguratorService
            .Invoking(x => x.PatchAsync(productInstancePGWBundle1.Id, patchDocument))
            .Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.StoreAlreadyHasPaymentGatewayBundleBadRequest.Code);
    }

    [Fact]
    public async Task InvalidPatchPayByLinkBundle_StoreId()
    {
        var patchDocument = new JsonPatchDocument<UpdateProductInstanceRequest>();
        patchDocument.Replace(x => x.StoreId, productInstancePayByLinkBundle.StoreId);

        await productConfiguratorService
            .Invoking(x => x.PatchAsync(productInstancePayByLinkBundle1.Id, patchDocument))
            .Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.StoreAlreadyHasPayByLinkBundleBadRequest.Code);
    }

    [Fact]
    public async Task InvalidPatchAddGatewayToStoreWithTwoGateways_StoreId()
    {
        var patchDocument = new JsonPatchDocument<UpdateProductInstanceRequest>();
        patchDocument.Replace(x => x.StoreId, productInstancePGWBundle1.StoreId);

        await productConfiguratorService
            .Invoking(x => x.PatchAsync(productInstancePaymentGateway.Id, patchDocument))
            .Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.StoreAlreadyHasPaymentGatewaysBadRequest.Code);
    }

    [Fact]
    public async Task ValidPatchAddGatewayToStore_StoreId()
    {
        var patchDocument = new JsonPatchDocument<UpdateProductInstanceRequest>();
        patchDocument.Replace(x => x.StoreId, productInstancePaymentGateway.StoreId);

        await productConfiguratorService.PatchAsync(productInstancePGWBundle.Id, patchDocument);
        var retrieved = await productInstanceRepo.FirstOrDefaultAsync(p => p.Id == productInstance.Id);
        retrieved.Should().NotBeNull();
        retrieved.StoreId.Should().Be(productInstance.StoreId);
    }

    [Fact]
    public async Task ValidPatchAddProductToStoreWithPGW_StoreId()
    {
        var patchDocument = new JsonPatchDocument<UpdateProductInstanceRequest>();
        patchDocument.Replace(x => x.StoreId, productInstancePGWBundle.StoreId);

        await productConfiguratorService.PatchAsync(productInstance.Id, patchDocument);
        var retrieved = await productInstanceRepo.FirstOrDefaultAsync(p => p.Id == productInstance.Id);
        retrieved.Should().NotBeNull();
        retrieved.StoreId.Should().Be(productInstance.StoreId);
    }
}
