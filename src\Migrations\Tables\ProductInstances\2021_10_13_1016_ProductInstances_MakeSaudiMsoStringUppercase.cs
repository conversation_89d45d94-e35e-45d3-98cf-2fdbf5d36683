﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_10_13_1016)]
public class ProductInstances_MakeSaudiMsoStringUppercase : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
                UPDATE pi
                SET Metadata = JSON_MODIFY(pi.Metadata, '$.MpgsMsoProvider', 'GEIDEA')
                FROM ProductInstances pi
                INNER JOIN Products p ON p.id=pi.ProductId
                WHERE p.Type='GWAY' AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.MpgsMsoProvider') = 'Geidea'");
    }
}
