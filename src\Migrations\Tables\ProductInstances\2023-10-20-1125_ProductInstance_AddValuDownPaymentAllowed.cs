﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;
[Migration(2023_10_20_1525)]
public class ProductInstance_AddValuDownPaymentAllowed : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
             USE [PRODUCTS]
             GO
             
             Update PRI set Metadata = JSON_MODIFY(PRI.Metadata,'$.AllowDownPaymentValu', CAST(0 as BIT))
             from [PRODUCTS].[dbo].[ProductInstances] PRI
             join [PRODUCTS].[dbo].Products P
             on PRI.ProductId = P.Id
             WHERE [Metadata] IS NOT NULL
             AND p.Type = 'GWAY'
             AND ISJSON([Metadata]) > 0
        ");
    }
}
