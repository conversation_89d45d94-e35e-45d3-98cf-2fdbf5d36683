﻿using System;

namespace Common.Models;

public class Price
{
    public string? ChargeFrequency { get; set; } = null!;
    public string ChargeType { get; set; } = null!;
    public bool ExemptFromVAT { get; set; }
    public Guid ProductId { get; set; }
    public Product Product { get; set; } = null!;
    public int? PerItemPrice { get; set; }
    public int? PercentagePrice { get; set; }
    public int? Threshold { get; set; }
    public string? Group { get; set; }
    public string? ThresholdType { get; set; }
    public int Priority { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public bool DeletedFlag { get; set; }
    public string? Currency { get; set; }
    public int? RentalPeriod { get; set; }
    public int? MaxPrice { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedDateUtc { get; set; }
    public string? UpdatedBy { get; set; }
    public DateTime? UpdatedDateUtc { get; set; }
    public Guid Id { get; set; }
}
