Create Or Alter View ProductCommissionPriceList
As
	Select 
			c.[Id], 
			p.Id AS ProductId,
            p.[Name] AS ProductName,
			m.Id AS MccId,
            m.[Name] AS MccName,
			b.Id As BusinessTypeId,
            b.[Name] AS BusinessType,
			b.Code As BusinessTypeCode,
			cf.Id As FeeId,
			cf.Name as FeeName,
			cf.NameAr As FeeNameAr,
			cf.Status As FeeStatus,
			cf.ImageId As FeeImageId,
			cf.Code as CommissionFeeCode,
			c.<PERSON>e<PERSON>,
			p.Code AS ProductCode,
            m.Code AS MccCode,
            mc.Name AS MccCategory,
			c.BillingType,
            c.Billing<PERSON>requency,
            c.CreatedDate,
			c.FeeType
		From
			[dbo].[ProductCommissionPrice] c
			JOIN [dbo].[Products]  p ON c.ProductID = p.Id
			JOIN [dbo].[BusinessTypes] b ON c.BusinessTypeID = b.Id
			JOIN [dbo].[Mcc] m ON c.MCCID = m.Id
			JOIN [dbo].[CommissionFees] cf ON c.CommissionFeeID = cf.Id
			JOIN [dbo].[MccCategory] mc ON m.MccCategoryId = mc.Id

Go