﻿using System;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Common;
using Common.Entities;
using Common.Models;
using Common.Options;
using Common.Services;
using DataAccess;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NSubstitute;
using ProductService;
using Xunit;

namespace Services.Test.PriceServiceTests;

public class PatchTests
{
    private readonly Mock<ILogger<PriceService>> logger = new Mock<ILogger<PriceService>>();
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly Mock<IProductService> productService = new Mock<IProductService>();
    private readonly DataContext context;
    private readonly PriceRepository priceRepository;
    private readonly PriceService priceService;
    private readonly IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;
    private readonly ICounterpartyProvider counterpartyProvider;
    private readonly PriceEntity priceEntity = new PriceEntity
    {
        ChargeFrequency = "RECCURRING_CHARGE",
        ChargeType = "MONTH",
        Currency = "EUR",
        ExemptFromVAT = true,
        Group = "group",
        MaxPrice = 100,
        PercentagePrice = 10,
        PerItemPrice = 20,
        Priority = 0,
        RentalPeriod = 24,
        Threshold = 200,
        ThresholdType = "LT",
        Product = new ProductEntity
        {
            Code = "TEST"
        }
    };

    public PatchTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
           .UseInMemoryDatabase(databaseName: "PricePatchTests" + Guid.NewGuid().ToString())
           .Options;

        context = new DataContext(options, new CounterpartyProvider());
        context.Prices.Add(priceEntity);
        context.SaveChanges();

        softposEgyptFeatureToggle = Substitute.For<IOptions<SoftposEgyptFeatureToggle>>();
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = true });
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();

        var productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);
        priceRepository = new PriceRepository(context, httpContext.Object);
        priceService = new PriceService(logger.Object, productRepository, priceRepository,
            mapper, productService.Object);
    }

    [Fact]
    public async Task PriceNotFound()
    {
        var patchDocument = new JsonPatchDocument<PriceRequest>();

        await priceService
            .Invoking(x => x.PatchAsync(Guid.NewGuid(), patchDocument))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
            x.ProblemDetails.Type == Errors.PriceNotFound.Code);
    }

    [Fact]
    public async Task ProductUsed()
    {
        var productId = Guid.NewGuid();
        var productPrice = new PriceEntity
        {
            ProductId = productId
        };
        context.Prices.Add(productPrice);
        context.SaveChanges();

        productService.Setup(x => x.IsProductUsedAsync(productId)).Returns(Task.FromResult(true));

        var patchDocument = new JsonPatchDocument<PriceRequest>();

        await priceService
            .Invoking(x => x.PatchAsync(productPrice.Id, patchDocument))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.InvalidProductPriceRequest.Code);
    }

    [Fact]
    public async Task InvalidPatch()
    {
        var patch = new JsonPatchDocument<PriceRequest>();
        patch.Operations.Add(new Operation<PriceRequest>("replace", "test", ""));

        await priceService
            .Invoking(x => x.PatchAsync(priceEntity.Id, patch))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.InvalidPatchRequest.Code);
    }

    [Fact]
    public async Task NotFoundNewProduct()
    {
        var patch = new JsonPatchDocument<PriceRequest>();
        patch.Replace(p => p.ProductId, Guid.NewGuid());

        await priceService
            .Invoking(x => x.PatchAsync(priceEntity.Id, patch))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
                        x.ProblemDetails.Type == Errors.ProductNotFound.Code);
    }

    [Fact]
    public async Task UsedNewProduct()
    {
        var product = new ProductEntity
        {
            Code = "TEST1"
        };
        context.Products.Add(product);
        context.SaveChanges();
        productService.Setup(x => x.IsProductUsedAsync(product.Id)).Returns(Task.FromResult(true));

        var patch = new JsonPatchDocument<PriceRequest>();
        patch.Replace(p => p.ProductId, product.Id);

        await priceService
            .Invoking(x => x.PatchAsync(priceEntity.Id, patch))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.InvalidProductPriceRequest.Code);
    }

    [Fact]
    public async Task ValidPatch()
    {
        var patch = new JsonPatchDocument<PriceRequest>();
        patch.Replace(p => p.ThresholdType, "GT");

        await priceService.PatchAsync(priceEntity.Id, patch);

        var updatedPrice = await priceRepository.FindAsync(new FindPriceRequest { Id = priceEntity.Id }, false);
        updatedPrice.Should().NotBeNull();
        updatedPrice.Should().NotBeEmpty();
        updatedPrice[0].ThresholdType.Should().Be("GT");
    }
}
