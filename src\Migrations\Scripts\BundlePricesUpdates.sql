﻿DECLARE @ProductId UNIQUEIDENTIFIER

--Pro smart bundle
EXEC NewProductVersion_v2 'PRO_SMART', 4, 'PRO_SMART', 5, 1

SELECT TOP 1 @ProductId = ID FROM Products where Code='PRO_SMART' and Version = 5

UPDATE Prices SET PerItemPrice = 9900 where ProductId = @ProductId

--Business Restaurant Bundle
EXEC NewProductVersion_v2 'BUSINESS_RESTAURANT', 3, 'BUSINESS_RESTAURANT', 4, 1

SELECT TOP 1 @ProductId = ID FROM Products where Code='BUSINESS_RESTAURANT' and Version = 4

UPDATE Prices SET PerItemPrice = 29900 where ProductId = @ProductId

--Business Retail Bundle
EXEC NewProductVersion_v2 'BUSINESS_RETAIL', 3, 'BUSINESS_RETAIL', 4, 1

SELECT TOP 1 @ProductId = ID FROM Products where Code='BUSINESS_RETAIL' and Version = 4

UPDATE Prices SET PerItemPrice = 29900 where ProductId = @ProductId

--Website Builder Bundle
EXEC NewProductVersion_v2 'WEBSITE_BUILDER_BUNDLE', 3, 'WEBSITE_BUILDER_BUNDLE', 4, 1

SELECT TOP 1 @ProductId = ID FROM Products where Code='WEBSITE_BUILDER_BUNDLE' and Version = 4

UPDATE Prices SET PerItemPrice = 4900 where ProductId = @ProductId