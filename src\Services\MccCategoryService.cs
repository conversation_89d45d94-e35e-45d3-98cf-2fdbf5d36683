﻿using Common;
using Common.Entities;
using Common.Models.MccCategory;
using Common.Repositories;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Services;
public class MccCategoryService : IMccCategoryService
{
    private readonly ILogger<MccCategoryService> logger;
    private readonly IMccCategoryRepository mccCategoryRepository;
    public MccCategoryService(ILogger<MccCategoryService> logger, IMccCategoryRepository mccCategoryRepository)
    {
        this.logger = logger;
        this.mccCategoryRepository = mccCategoryRepository;
    }
    public async Task<List<MccCategoriesListResponse>> GetMccCategoriesList()
    {
        try
        {
            return await mccCategoryRepository.GetMccCategoriesList();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "something has gone wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, Errors.InternalServerError);
        }
    }
}
