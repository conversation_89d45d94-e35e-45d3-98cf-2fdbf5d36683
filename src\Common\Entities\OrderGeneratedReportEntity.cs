﻿using Geidea.Utils.DataAccess.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Common.Entities;

public class OrderGeneratedReportEntity : Entity<long>
{
    [MaxLength(255)]
    public string OrderNumber { get; set; } = string.Empty;

    [MaxLength(255)]
    public string Counterparty { get; set; } = string.Empty;

    [MaxLength(255)]
    public string AcquiringBank { get; set; } = string.Empty;

    [MaxLength(255)]
    public string ReportName { get; set; } = string.Empty;

    public DateTime CreationDate { get; set; }
}
