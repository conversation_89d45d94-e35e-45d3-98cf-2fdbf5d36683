﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Common;
using Common.Entities.Gle;
using DataAccess.Repositories.Gle;
using DataAccess.Test.TestData;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;

namespace DataAccess.Test.Gle;

public class GleMerchantRepositoryTests
{
    private readonly IHttpContextAccessor contextAccessor = Substitute.For<IHttpContextAccessor>();
    private readonly ILogger<GleMerchantRepository> logger = Substitute.For<ILogger<GleMerchantRepository>>();
    private readonly ILogger<GleBaseRepository<GleMerchantEntity>> baseLogger = Substitute.For<ILogger<GleBaseRepository<GleMerchantEntity>>>();
    private DataContext context;
    private GleMerchantRepository gleMerchantRepository;

    private readonly Guid merchantId = Guid.NewGuid();

    [SetUp]
    public async Task SetUp()
    {
        context = DbContextHelper.CreateInMemoryDatabase(Substitute.For<ICounterpartyProvider>());
        gleMerchantRepository = new GleMerchantRepository(context, contextAccessor, baseLogger, logger);

        await context.GleMerchant.AddRangeAsync(new List<GleMerchantEntity>
        {
            new()
            {
                Id = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid().ToString(),
                CreatedDate = DateTime.Now,
                GleUserId = "7639103x",
                GleLoginId = "1230831",
                GleLoginId2 = "3245111",
                ParentGleUserId = null,
                GleRegistrationStatus = Constants.GleRegistrationStatus.Success,
                GleRegistrationResponse = "Merchant was registered!",
                ReferenceMmsId = "630800001",
                UserCategoryCode = Constants.GleUserCategoryCode.Chain,
                MerchantId = merchantId,
                GleStoreEntities = new List<GleStoreEntity>
                {
                    new()
                    {
                        GleRegistrationStatus = Constants.GleRegistrationStatus.Failed,
                        GleRegistrationResponse = "Ledger Not Found!"
                    },
                    new()
                    {
                        GleRegistrationStatus = Constants.GleRegistrationStatus.Success,
                        GleRegistrationResponse = "Store was registered!"
                    }
                }
            }
        });

        await context.SaveChangesAsync();
    }

    [Test]
    public async Task GetGleHierarchyByMerchantIdAsync_ShouldReturnHierarchy()
    {
        // Act
        var result = await gleMerchantRepository.GetGleHierarchyByMerchantIdAsync(merchantId);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(Constants.GleUserCategoryCode.Chain, result.UserCategoryCode);
        Assert.AreEqual(merchantId, result.MerchantId);
        Assert.IsNotEmpty(result.GleStoreEntities);
        Assert.AreEqual(2, result.GleStoreEntities.Count);
    }

    [Test]
    public async Task GetGleHierarchyByMerchantIdAsync_WhenNotFound_ShouldReturnNull()
    {
        var result = await gleMerchantRepository.GetGleHierarchyByMerchantIdAsync(Guid.Empty);

        Assert.Null(result);
    }

    [Test]
    public async Task GetGleMerchantByMerchantIdAsync_ShouldReturnMerchant()
    {
        // Act
        var result = await gleMerchantRepository.GetGleHierarchyByMerchantIdAsync(merchantId);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(Constants.GleUserCategoryCode.Chain, result.UserCategoryCode);
        Assert.AreEqual(merchantId, result.MerchantId);
        Assert.IsNotEmpty(result.GleStoreEntities);
    }

    [Test]
    public async Task GetGleMerchantByMerchantIdAsync_WhenNotFound_ShouldReturnNull()
    {
        var result = await gleMerchantRepository.GetGleHierarchyByMerchantIdAsync(Guid.NewGuid());

        Assert.Null(result);
    }

    [Test]
    public async Task AddGle_ValidRequest_ShouldCallBaseAndAddGleMerchant()
    {
        var numberOfGleMerchantsBeforeAdd = context.GleMerchant.Count();

        await gleMerchantRepository.AddGle(new GleMerchantEntity
        {
            GleRegistrationStatus = Constants.GleRegistrationStatus.Failed,
            GleRegistrationResponse = "Phone Number is mandatory!",
            ReferenceMmsId = "9000081",
            UserCategoryCode = Constants.GleUserCategoryCode.MultiStore,
            MerchantId = Guid.NewGuid()
        });

        Assert.AreEqual(numberOfGleMerchantsBeforeAdd + 1, context.GleMerchant.Count());
    }

    [Test]
    public async Task AddGle_InvalidRequest_ShouldThrowServiceException()
    {
        await gleMerchantRepository.Invoking(x => x.AddGle(new GleMerchantEntity()))
            .Should().ThrowAsync<ServiceException>();
    }

    [Test]
    public async Task UpdateGle_ValidRequest_ShouldCallBaseAndUpdateGleMerchant()
    {
        var gleMerchantBeforeUpdate = await gleMerchantRepository.GetGleMerchantByMerchantIdAsync(merchantId);

        const string newValue = "00001";
        var updateDocument = new JsonPatchDocument<GleMerchantEntity>();
        updateDocument.Operations.Add(new Operation<GleMerchantEntity>
        {
            op = "replace",
            path = "GleUserId",
            value = newValue
        });
        await gleMerchantRepository.UpdateGle(gleMerchantBeforeUpdate!.Id, updateDocument);

        var gleMerchantAfterUpdate = await gleMerchantRepository.GetGleMerchantByMerchantIdAsync(merchantId);

        Assert.AreNotEqual(gleMerchantBeforeUpdate.GleUserId, gleMerchantAfterUpdate!.GleUserId);
        Assert.AreEqual(newValue, gleMerchantAfterUpdate.GleUserId);
    }

    [Test]
    public async Task IsMerchantRegisteredInGleAsync_ShouldReturnTrue()
    {
        // Act
        var result = await gleMerchantRepository.IsMerchantRegisteredInGleAsync(merchantId);

        // Assert
        Assert.IsTrue(result);
    }

    [Test]
    public async Task IsMerchantRegisteredInGleAsync_ShouldReturnFalse()
    {
        // Act
        var result = await gleMerchantRepository.IsMerchantRegisteredInGleAsync(Guid.NewGuid());

        // Assert
        Assert.IsFalse(result);
    }
}