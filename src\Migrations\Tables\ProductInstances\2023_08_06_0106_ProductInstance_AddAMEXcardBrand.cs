﻿using FluentMigrator;
namespace Migrations.Tables.ProductInstances;

[Migration(2023_08_06_0106)]
public class ProductInstance_AddAMEXcardBrand : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"DECLARE @egyptUaeIds table(
            id  uniqueidentifier
            )
insert into @egyptUaeIds
SELECT pi.[Id]
FROM ProductInstances pi
INNER JOIN Products p ON p.id = pi.ProductId
WHERE [Metadata] IS NOT NULL
AND p.Type = 'GWAY'
AND ISJSON([Metadata]) > 0
AND (p.Counterparty = 'GEIDEA_EGYPT' OR p.Counterparty = 'GEIDEA_UAE')
AND JSON_QUERY([Metadata], '$.CardBrandProviders') IS NOT NULL
AND JSON_QUERY([Metadata], '$.CardBrandProviders[0]') IS NOT NULL
UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.CardBrandProviders', JSON_QUERY(REPLACE(JSON_QUERY([Metadata], '$.CardBrandProviders'),']',', { ""CardBrand"": ""AMEX_GW"", ""AcquiringProvider"": ""MPGS"", ""ThreeDSecureProvider"": ""MPGS"" } ]'),'$')) 
FROM ProductInstances pi
INNER JOIN @egyptUaeIds e ON e.id = pi.Id
WHERE JSON_QUERY([Metadata], '$.CardBrandProviders') NOT LIKE '%AMEX%'");
    }
}
