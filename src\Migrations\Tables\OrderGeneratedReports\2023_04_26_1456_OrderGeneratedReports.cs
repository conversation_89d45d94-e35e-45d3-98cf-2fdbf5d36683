﻿using FluentMigrator;

namespace Migrations.Tables.OrderGeneratedReports;

[Migration(2023_04_26_1456)]
public class OrderGeneratedReports : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("OrderGeneratedReports")
              .WithColumn("Id").AsInt64().NotNullable().PrimaryKey().Identity()
              .WithColumn("OrderNumber").AsString(255).NotNullable()
              .WithColumn("Counterparty").AsString(255).NotNullable()
              .WithColumn("AcquiringBank").AsString(255).NotNullable()
              .WithColumn("ReportName").AsString(255).NotNullable()
              .WithColumn("CreationDate").AsDateTime2().NotNullable();

        Create.Index("IX_OrderGeneratedReports_ReportOrder").OnTable("OrderGeneratedReports")
            .OnColumn("ReportName").Ascending()
            .OnColumn("Counterparty").Ascending()
            .OnColumn("AcquiringBank").Ascending()
            .OnColumn("OrderNumber").Unique();
    }
}
