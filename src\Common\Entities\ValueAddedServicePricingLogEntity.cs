﻿using Geidea.Utils.DataAccess.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Entities;
public class ValueAddedServicePricingLogEntity : Entity<Guid>
{
    public Guid ProductID { get; set; }

    public Guid MCCID { get; set; }

    public Guid BusinessTypeID { get; set; }

    public Guid VASID { get; set; }

    public decimal SubscriptionFee { get; set; }

    public int FeeType { get; set; }

    public int BillingType { get; set; }

    public int BillingFrequency { get; set; }

    public string? DeletedBy { get; set; }

    public DateTime DeletedDate { get; set; }
}
