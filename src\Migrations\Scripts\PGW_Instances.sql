﻿USE ORDERS
go
create or alter procedure [dbo].CreateGatewayProductInstance @ProductId uniqueidentifier, 
															 @ParentId uniqueidentifier,
															 @StoreId uniqueidentifier,
															 @LegalName nvarchar(255),
															 @LegalNameAr nvarchar(255),
															 @Counterparty nvarchar(255),
															 @IsTest bit
	as
	begin
		DECLARE @Metadata nvarchar(max)

		set @Metadata='{
               "merchantGatewayKey":null,
               "mpgsMerchantId":null,
               "mpgsApiKey":null,
               "gsdkTid":null,
               "gsdkMid":null,
               "gsdkSecretKey":null,
               "cyberSourceApiIdentifier":null,
               "cyberSourceApiKey":null,
               "cyberSourceMerchantId":null,
               "cyberSourceMerchantKeyId":null,
               "cyberSourceOrgUnitId":null,
               "cyberSourceSharedSecretKey":null,
               "defaultPaymentOperation":"Pay",
               "apiPassword":null,
               "stylesheetId":null,
               "virtualTerminalId":null,
               "applePartnerInternalMerchantIdentifier":null,
               "appleCsr":null,
               "appleCertificatePrivateKey":null,
               "applePaymentProcessingCertificate":null,
               "applePaymentProcessingCertificateExpiryDate":null,
               "appleDeveloperId":null,
               "meezaMerchantId":null,
               "isApplePayWebEnabled":false,
               "isApplePayMobileEnabled":false,
               "isApplePayMobileCertificateAvailable":false,
               "merchantName":"' + @LegalName + '",
			   "merchantNameAr":"' + @LegalNameAr + '",
               "merchantWebsite":null,
               "merchantLogoUrl":null,
               "merchantCountry":'
		
		IF @Counterparty = 'GEIDEA_SAUDI'
			set @Metadata = @Metadata + '"SAU"'
		ELSE
			set @Metadata = @Metadata + '"EGY"'
			   
		set @Metadata = @Metadata + ',
               "callbackUrl":null,
               "isTest":'

		IF @IsTest = 0
			set @Metadata = @Metadata + 'false'
		ELSE
			set @Metadata = @Metadata + 'true'
			   
			   
		set @Metadata = @Metadata + ',
               "isTokenizationEnabled":true,
               "allowedInitiatedByValues":[
                  "Internet"
               ],
               "currencies":['
		IF @Counterparty = 'GEIDEA_SAUDI'
			set @Metadata = @Metadata + '"SAR"'
		ELSE
			set @Metadata = @Metadata + '"EGP"'

        set @Metadata = @Metadata +      '],
               "cardBrandProviders":[]
            }'

	 INSERT INTO [PRODUCTS].[dbo].[ProductInstances](ProductId, StoreId, CreatedBy, CreatedDate, ParentId, Metadata, ValidFrom) OUTPUT inserted.Id
	 VALUES(@ProductId, @StoreId, '00000000-0000-0000-0000-000000000000', GETDATE(), @ParentId, @Metadata, GETDATE())

	end


go
create or alter procedure dbo.CreateProductInstance @ProductId uniqueidentifier, 
													@StoreId uniqueidentifier, 
													@LegalName nvarchar(255), 
													@LegalNameAr nvarchar(255), 
													@OrderItemId uniqueidentifier,
													@Counterparty nvarchar(255)
	as
	begin
	 DECLARE @Ids TABLE(ID UNIQUEIDENTIFIER)
	 DECLARE @ParentProductInstanceId UNIQUEIDENTIFIER
	 DECLARE @GatewayProductId UNIQUEIDENTIFIER

	 BEGIN TRAN

	 INSERT INTO [PRODUCTS].[dbo].[ProductInstances](ProductId, StoreId, CreatedBy, CreatedDate, ValidFrom) OUTPUT inserted.Id INTO @Ids
	 VALUES(@ProductId, @StoreId, '00000000-0000-0000-0000-000000000000', GETDATE(), GETDATE())

	 SELECT TOP 1 @ParentProductInstanceId = ID FROM @Ids 
	 DELETE FROM @Ids

	 SELECT TOP 1 @GatewayProductId = pp.PartId 
	 FROM [PRODUCTS].[dbo].[ProductParts] pp join [PRODUCTS].[dbo].[Products] p ON pp.PartId = p.Id 
	 WHERE p.[Type]='GWAY' AND pp.ProductId = @ProductId

	 EXEC CreateGatewayProductInstance @GatewayProductId, @ParentProductInstanceId, @StoreId, @LegalName, @LegalNameAr, @Counterparty, 0
	 EXEC CreateGatewayProductInstance @GatewayProductId, @ParentProductInstanceId, @StoreId, @LegalName, @LegalNameAr, @Counterparty, 1

	 INSERT INTO [ORDERS].[dbo].[OrderItemProductInstances](OrderItemId, ProductInstanceId)
	 VALUES(@OrderItemId, @ParentProductInstanceId)

	 COMMIT TRAN
	end


go
create or alter function dbo.GetMerchantStore(@MerchantId uniqueidentifier)
returns uniqueidentifier
    as
    begin
		declare @storeId uniqueidentifier
		select top 1 @storeId = MerchantId from [MERCHANTS].[dbo].[MerchantHierarchy] where ParentMerchantId = @MerchantId
        return @storeId
    end


go
create or alter function dbo.GetMerchantLegalName(@MerchantId uniqueidentifier)
returns nvarchar(255)
    as
    begin
		declare @legalName nvarchar(255)
		select top 1 @legalName = LegalName from [MERCHANTS].[dbo].[MerchantDetails] where MerchantId = @MerchantId
        return @legalName
    end


go
create or alter function dbo.GetMerchantLegalNameAr(@MerchantId uniqueidentifier)
returns nvarchar(255)
    as
    begin
		declare @legalNameAr nvarchar(255)
		select top 1 @legalNameAr = LegalNameAr from [MERCHANTS].[dbo].[MerchantDetails] where MerchantId = @MerchantId
        return @legalNameAr
    end


go
SET XACT_ABORT ON
--create product instances when there is no instance associated with the order item
DECLARE @BundlesWithBothGatewaysInstancesToCreate CURSOR
DECLARE @OrderItemId UNIQUEIDENTIFIER
DECLARE @ProductId UNIQUEIDENTIFIER
DECLARE @MerchantId UNIQUEIDENTIFIER
DECLARE @StoreId UNIQUEIDENTIFIER
DECLARE @LegalName nvarchar(255)
DECLARE @LegalNameAr nvarchar(255)
DECLARE @Counterparty nvarchar(255)

SET @BundlesWithBothGatewaysInstancesToCreate = CURSOR FOR select oi.Id, oi.ProductId, o.MerchantId, o.Counterparty
from [ORDERS].[dbo].[OrderItem] oi 
join [ORDERS].[dbo].[Order] o on oi.OrderId = o.Id
left join [ORDERS].[dbo].[OrderItemProductInstances] oipi on oi.Id = oipi.OrderItemId
where oi.ProductId in (select Id from [PRODUCTS].[dbo].[Products] where Code in ('WEBSITE_BUILDER_BUNDLE','PAYMENT_GATEWAY_BUNDLE')) 
and oipi.ProductInstanceId is null

OPEN @BundlesWithBothGatewaysInstancesToCreate FETCH NEXT FROM @BundlesWithBothGatewaysInstancesToCreate 
	INTO @OrderItemId,@ProductId,@MerchantId,@Counterparty WHILE @@FETCH_STATUS = 0
BEGIN
	set @StoreId = dbo.GetMerchantStore(@MerchantId)
	set @LegalName = dbo.GetMerchantLegalName(@MerchantId)
	set @LegalNameAr = dbo.GetMerchantLegalNameAr(@MerchantId)

	EXEC [dbo].CreateProductInstance @ProductId, @StoreId, @LegalName, @LegalNameAr, @OrderItemId, @Counterparty

    FETCH NEXT FROM @BundlesWithBothGatewaysInstancesToCreate INTO @OrderItemId,@ProductId,@MerchantId,@Counterparty
END

CLOSE @BundlesWithBothGatewaysInstancesToCreate
DEALLOCATE @BundlesWithBothGatewaysInstancesToCreate

--create child product instances when the parent is created
DECLARE @BundlesWithGatewayInstances CURSOR
DECLARE @ProductInstanceId UNIQUEIDENTIFIER
DECLARE @GatewayProductId UNIQUEIDENTIFIER
DECLARE @InstancesCount int

SET @BundlesWithGatewayInstances = CURSOR FOR select oipi.ProductInstanceId, oi.ProductId, o.MerchantId, o.Counterparty
from [ORDERS].[dbo].[OrderItem] oi 
join [ORDERS].[dbo].[Order] o on oi.OrderId = o.Id
inner join [ORDERS].[dbo].[OrderItemProductInstances] oipi on oi.Id = oipi.OrderItemId
inner join [PRODUCTS].[dbo].[ProductInstances] pi on oipi.ProductInstanceId = pi.Id
where oi.ProductId in (select Id from [PRODUCTS].[dbo].[Products] where Code in ('WEBSITE_BUILDER_BUNDLE','PAYMENT_GATEWAY_BUNDLE')) 
and oipi.ProductInstanceId is not null

OPEN @BundlesWithGatewayInstances FETCH NEXT FROM @BundlesWithGatewayInstances 
	INTO @ProductInstanceId, @ProductId, @MerchantId, @Counterparty WHILE @@FETCH_STATUS = 0
BEGIN
	set @StoreId = dbo.GetMerchantStore(@MerchantId)
	set @LegalName = dbo.GetMerchantLegalName(@MerchantId)
	set @LegalNameAr = dbo.GetMerchantLegalNameAr(@MerchantId)

	select top 1 @GatewayProductId = pp.PartId 
	from [PRODUCTS].[dbo].[ProductParts] pp
	left join [PRODUCTS].[dbo].[Products] p on pp.PartId = p.Id
	where pp.ProductId = @ProductId and p.[Type] = 'GWAY'

	--has live instance?
	select @InstancesCount = count(*) 
	from [PRODUCTS].[dbo].[ProductInstances]
	where ParentId = @ProductInstanceId and ProductId=@GatewayProductId 
	and (JSON_VALUE(Metadata, '$.isTest') = 'false' or JSON_VALUE(Metadata, '$.IsTest') = 'false')

	if @InstancesCount = 0
		EXEC [dbo].CreateGatewayProductInstance @GatewayProductId, @ProductInstanceId, @StoreId, @LegalName, @LegalNameAr, @Counterparty, 0

	--has test instance?
	select @InstancesCount = count(*) 
	from [PRODUCTS].[dbo].[ProductInstances]
	where ParentId = @ProductInstanceId and ProductId=@GatewayProductId 
	and (JSON_VALUE(Metadata, '$.isTest') = 'true' or JSON_VALUE(Metadata, '$.IsTest') = 'true')

	if @InstancesCount = 0
		EXEC [dbo].CreateGatewayProductInstance @GatewayProductId, @ProductInstanceId, @StoreId, @LegalName, @LegalNameAr, @Counterparty, 1

    FETCH NEXT FROM @BundlesWithGatewayInstances INTO @ProductInstanceId, @ProductId, @MerchantId, @Counterparty
END

CLOSE @BundlesWithGatewayInstances
DEALLOCATE @BundlesWithGatewayInstances
