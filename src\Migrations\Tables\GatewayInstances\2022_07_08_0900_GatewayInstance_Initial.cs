﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_07_08_0900)]
public class GatewayInstance_Initial : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("GatewayInstances")
            .WithColumn("ProductInstanceId").AsGuid().PrimaryKey()
            .WithColumn("CompanyId").AsGuid().Nullable()
            .WithColumn("StoreId").AsGuid().Nullable()
            .WithColumn("IsTest").AsBoolean().Nullable()
            .WithColumn("MerchantGatewayKey").AsGuid().Nullable()
            .WithColumn("ShahryCpBnplMerchantCode").AsString(255).Nullable()
            .WithColumn("ShahryCpBnplBranchCode").AsString(255).Nullable();

        Execute.Sql(@"
            ALTER TABLE [dbo].[GatewayInstances] WITH CHECK ADD CONSTRAINT [FK_GatewayInstances_ProductInstanceId_ProductInstances_Id] FOREIGN KEY([ProductInstanceId])
            REFERENCES [dbo].[ProductInstances] ([Id])
            ON DELETE CASCADE
            GO

            ALTER TABLE [dbo].[GatewayInstances] CHECK CONSTRAINT [FK_GatewayInstances_ProductInstanceId_ProductInstances_Id]
            GO");

        Execute.Sql(@"
            INSERT INTO [GatewayInstances](
                 [ProductInstanceId],
                 [CompanyId],
                 [StoreId],
                 [MerchantGatewayKey],
                 [IsTest],
                 [ShahryCpBnplMerchantCode],
                 [ShahryCpBnplBranchCode])
            SELECT
                 pi.Id,
                 pi.CompanyId,
                 pi.StoreId,
                 CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0 and TRY_CONVERT(UNIQUEIDENTIFIER, JSON_VALUE(pi.Metadata, '$.MerchantGatewayKey')) IS NOT NULL THEN JSON_VALUE(pi.Metadata, '$.MerchantGatewayKey') ELSE NULL END,
                 CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0 and TRY_CONVERT(BIT, JSON_VALUE(pi.Metadata, '$.IsTest')) IS NOT NULL THEN JSON_VALUE(pi.Metadata, '$.IsTest') ELSE NULL END,
                 CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0 THEN JSON_VALUE(pi.Metadata, '$.ShahryCpBnplMerchantCode') ELSE NULL END,
                 CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0 THEN JSON_VALUE(pi.Metadata, '$.ShahryCpBnplBranchCode') ELSE NULL END
            FROM ProductInstances pi
                 INNER JOIN Products p on p.Id=pi.ProductId
            WHERE
                 p.Type='GWAY'");

        Execute.Sql(@"
            IF NOT EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'IDX_GatewayInstances_MerchantGatewayKey' AND object_id = OBJECT_ID('[dbo].[GatewayInstances]'))
            BEGIN
                CREATE NONCLUSTERED INDEX [IDX_GatewayInstances_MerchantGatewayKey] ON [dbo].[GatewayInstances] ([MerchantGatewayKey])
            END");

        Execute.Sql(@"
            IF NOT EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'IDX_GatewayInstances_CompanyId_StoreId_IsTest' AND object_id = OBJECT_ID('[dbo].[GatewayInstances]'))
            BEGIN
                CREATE NONCLUSTERED INDEX [IDX_GatewayInstances_CompanyId_StoreId_IsTest] ON [dbo].[GatewayInstances] ([CompanyId], [StoreId], [IsTest])
            END");
    }
}
