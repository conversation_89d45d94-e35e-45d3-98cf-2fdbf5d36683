﻿using AutoMapper;
using Common.Entities;
using Common.Enums;
using Common.Models.Onboarding;
using Common.Repositories;
using Common.Views;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using Common.Data.Extensions;
using System.Threading.Tasks;
using UnitPriceSearchKey = Common.Models.Onboarding.UnitPriceSearchKey;
using System.Linq.Dynamic.Core;

namespace DataAccess.Repositories;
public class OnboardingUnitPriceRepository : AuditableRepository<Guid, UnitPriceEntity>, IOnboardingUnitPriceRepository
{
    private IQueryable<OnboardingUnitPriceListView> OnboardingUnitPriceListQuery;
    public OnboardingUnitPriceRepository(DataContext context, IHttpContextAccessor contextAccessor, IMapper mapper) : base(context, contextAccessor)
    {
        OnboardingUnitPriceListQuery = context.OnboardingUnitPriceList.AsQueryable();
    }
    public async Task<OnboardingUnitPricesListResponse> GetOnboardingUnitPricesList(OnboardingUnitPricesListRequest request)
    {
        OnboardingUnitPriceListQuery = OnboardingUnitPriceListQuery.AsNoTracking();
        OnboardingUnitPriceListQuery = SearchUnitPrices(request, OnboardingUnitPriceListQuery);
        OnboardingUnitPriceListQuery = FilterUnitPrices(request, OnboardingUnitPriceListQuery);

        OnboardingUnitPriceListQuery = SortUnitPrices(request, OnboardingUnitPriceListQuery);

        int totalCount = await OnboardingUnitPriceListQuery.CountAsync();

        var unitPriceList = request.Size > 0
            ? await OnboardingUnitPriceListQuery.Page(request.Page, request.Size).ToArrayAsync()
            : await OnboardingUnitPriceListQuery.ToArrayAsync();

        return new OnboardingUnitPricesListResponse
        {
            TotalCount = totalCount,
            TotalPages = request.Size > 0 ? (int)Math.Ceiling((double)totalCount / request.Size) : 1,  // Calculate total pages only if paginated
            OnboardingUnitPriceLists = unitPriceList
        };
    }

    public static IQueryable<OnboardingUnitPriceListView> SearchUnitPrices(OnboardingUnitPricesListRequest request, IQueryable<OnboardingUnitPriceListView> unitPriceList)
    {
        var SearchTerm = request.SearchTerms.FirstOrDefault(s => !string.IsNullOrEmpty(s.Value));
        if (!string.IsNullOrEmpty(SearchTerm.Value))
        {
            switch (SearchTerm.Key)
            {
                case UnitPriceSearchKey.All:
                    unitPriceList = unitPriceList.Where(s => s.ProductName != null && s.ProductName.Contains(SearchTerm.Value) ||
                                                             s.ProductCode.Contains(SearchTerm.Value) ||
                                                             s.MccName.Contains(SearchTerm.Value) ||
                                                             s.MccCode.Contains(SearchTerm.Value) ||
                                                             s.MccCode.Contains(SearchTerm.Value) ||
                                                             s.BusinessType.Contains(SearchTerm.Value));
                    break;
                case UnitPriceSearchKey.ProductName:
                    unitPriceList = unitPriceList.Where(s => s.ProductName != null && s.ProductName.Contains(SearchTerm.Value));
                    break;
                case UnitPriceSearchKey.ProductCode:
                    unitPriceList = unitPriceList.Where(s => s.ProductCode.Contains(SearchTerm.Value));
                    break;
                case UnitPriceSearchKey.MccName:
                    unitPriceList = unitPriceList.Where(s => s.MccName.Contains(SearchTerm.Value));
                    break;
                case UnitPriceSearchKey.MccCode:
                    unitPriceList = unitPriceList.Where(s => s.MccCode.Contains(SearchTerm.Value));
                    break;
                case UnitPriceSearchKey.BusinessType:
                    unitPriceList = unitPriceList.Where(s => s.BusinessType.Contains(SearchTerm.Value));
                    break;
                default:
                    break;
            }
        }
        return unitPriceList;
    }
    public static IQueryable<OnboardingUnitPriceListView> FilterUnitPrices(OnboardingUnitPricesListRequest request, IQueryable<OnboardingUnitPriceListView> unitPriceList)
    {
        if (request.FilterByProducts != null && request.FilterByProducts.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.FilterByProducts.Contains(s.ProductId));
        }

        if (request.FilterByProductCodes != null && request.FilterByProductCodes.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.FilterByProductCodes.Contains(s.ProductCode));
        }

        if (request.FilterByBusinessType != null && request.FilterByBusinessType.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.FilterByBusinessType.Contains(s.BusinessTypeId));
        }
        if (request.FilterBySalesChannel != null && request.FilterBySalesChannel.Any())
        {
            unitPriceList = unitPriceList.Where(s => s.ProductSalesChannel != null && request.FilterBySalesChannel.Contains(s.ProductSalesChannel));
        }
        if (request.FilterByAvailability != null && request.FilterByAvailability.Any())
        {
            unitPriceList = unitPriceList.Where(s => s.ProductAvailability != null && request.FilterByAvailability.Contains(s.ProductAvailability));
        }
        if (request.FilterByMccType != null && request.FilterByMccType.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.FilterByMccType.Contains(s.MccId));
        }
        if (request.FilterByProductType != null && request.FilterByProductType.Any())
        {
            unitPriceList = unitPriceList.Where(s => s.ProductType != null && request.FilterByProductType.Contains(s.ProductType));
        }
        if (request.FilterByVatType != null && request.FilterByVatType.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.FilterByVatType.Contains(s.VATType));
        }
        if (request.FilterByCategory != null && request.FilterByCategory.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.FilterByCategory.Contains(s.CategoryId));
        }

        return unitPriceList;
    }
    public static IQueryable<OnboardingUnitPriceListView> SortUnitPrices(OnboardingUnitPricesListRequest request, IQueryable<OnboardingUnitPriceListView> unitPriceList)
    {
        SortType orderType;

        if (request.OrderType.ToLower() == SortType.asc.ToString())
            orderType = SortType.asc;
        else if (request.OrderType.ToLower() == SortType.desc.ToString())
            orderType = SortType.desc;
        else
            throw new ArgumentException("Invalid value for OrderType. Allowed values are 'asc' and 'desc'.", nameof(request));


        if (!string.IsNullOrEmpty(request.OrderFieldName))
        {
            unitPriceList = unitPriceList.OrderBy(request.OrderFieldName, orderType);

            // Add secondary sorting for CreatedDate when the primary sort field is not ProductDisplayOrder
            if (!request.OrderFieldName.Equals(nameof(OnboardingUnitPriceListView.ProductDisplayOrder), StringComparison.OrdinalIgnoreCase))
            {
                unitPriceList = ((IOrderedQueryable<OnboardingUnitPriceListView>)unitPriceList).ThenBy(u => u.CreatedDate);
            }
        }
        else
        {
            // If no OrderFieldName is provided, order by CreatedDate
            unitPriceList = unitPriceList.OrderBy(u => u.CreatedDate);
        }

        return unitPriceList;
    }
}
