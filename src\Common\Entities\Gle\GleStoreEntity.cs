﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace Common.Entities.Gle;

public class GleStoreEntity : GleBaseEntity
{
    public Guid GleMerchantId { get; set; }
    [ForeignKey("GleMerchantId")]
    public GleMerchantEntity GleMerchant { get; set; } = null!;

    public Guid StoreId { get; set; }

    public string? UserCategoryCode { get; set; }

    public IReadOnlyCollection<GleTerminalEntity> GleTerminalEntities { get; set; } = null!;
}