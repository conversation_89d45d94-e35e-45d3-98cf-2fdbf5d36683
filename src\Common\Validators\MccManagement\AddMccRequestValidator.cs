﻿using Common.Entities;
using Common.Models.MccManagement;
using Common.Models.NonTransactionalFees;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Validators.MccManagement;
public class AddMccRequestValidator : AbstractValidator<MccRequest>
{
    public AddMccRequestValidator()
    {
        RuleFor(s => s.Code)
            .NotEmpty()
            .Length(1, 6)
            .Custom((code, context) =>
            {
                if (code != null)
                {
                    var cleanedCode = code.Replace(" ", string.Empty);
                    context.InstanceToValidate.Code = cleanedCode;
                }
            });
        RuleFor(s => s.Name)
         .NotEmpty()
         .Length(1, 100);

        RuleFor(s => s.MccCategoryId)
        .NotEmpty();

        RuleFor(s => s.Status)
          .IsInEnum()
          .WithMessage("Status must be either 0 (Inactive) or 1 (Active).");

        RuleFor(s => s.NameAr)
        .NotEmpty()
        .Length(1, 100)
        .WithMessage("MCC Name is required and cannot exceed 100 characters.");
    }
}