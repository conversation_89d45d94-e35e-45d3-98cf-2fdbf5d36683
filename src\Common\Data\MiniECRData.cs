﻿using Common.Validators;
using Geidea.Utils.Exceptions;

namespace Common.Data;

public class MiniEcrData : IData
{
    public string DeviceId { get; set; } = string.Empty;

    public string? Nickname { get; set; }

    public void ValidateAndThrow()
    {
        var dataValidationResult = new MiniEcrDataValidator().Validate(this);
        if (!dataValidationResult.IsValid)
        {
            throw new ValidationException(dataValidationResult);
        }
    }
}
