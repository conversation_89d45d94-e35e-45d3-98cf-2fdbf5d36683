﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_03_05_1030)]
public class ProductInstance_SetSaudiAsMerchantCountryForGatewayInstancesWhereMerchantCountryIsMissing : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
                UPDATE pi
                SET Metadata = JSON_MODIFY(pi.Metadata, '$.MerchantCountry', 'SAU')
                FROM ProductInstances pi
                INNER JOIN Products p ON p.id=pi.ProductId
                WHERE p.Type='GWAY' AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.MerchantCountry') IS NULL");
    }
}
