﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Nullable>enable</Nullable>
		<LangVersion>latest</LangVersion>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<ProjectGuid>{cfdc0f29-6065-4529-bb8d-cc0845c01ce2}</ProjectGuid>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="FluentMigrator" Version="3.3.2" />
		<PackageReference Include="FluentMigrator.Extensions.SqlServer" Version="3.3.2" />
		<PackageReference Include="FluentMigrator.Runner" Version="3.3.2" />
		<PackageReference Include="FluentMigrator.Runner.SqlServer" Version="3.3.2" />
		<PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.3" />
		<PackageReference Include="Microsoft.Extensions.Logging.Configuration" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="6.0.0" />
		<PackageReference Include="Serilog" Version="2.12.0" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="3.1.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Common\Common.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Update="Scripts\AddCNPToProductEntity.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AddPendingComissionPriceTable.sql">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AddPendingUnitPriceTable.sql">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AlterPendingComissionPrice.sql">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AlterPendingUnitPriceTable.sql">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AlterPendingUnitPriceTableAddReviewColumns.sql">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AlterProductCommissionPriceListingView.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<Compile Update="Tables\Products\2024_04_16_0200_UaeAddCUPProductToProductTblBNDL.cs">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Compile>
		<Compile Update="Tables\Products\2024_03_19_0200_UaeAddCUPProductToProductTbl.cs">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<None Update="Scripts\AddNameToCategoryEntity.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AddNearpayVendor.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AddNewFiledsToProductEntity.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AddNewFiledsValueAddedServicesTable.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AddProductImageTable.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AddValueAddedServicesTable.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AddVendorForigenKeyInTerminalDataSet.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AddVendorIdTerminalDataset.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AlterValueAddedServicesTable.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\DropTIDsStockViewScript.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\KsaSmartSunmiBundleCreate.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<Compile Update="Tables\Products\2024_04_16_0200_UaeAddCUPProductToProductTblBNDL.cs">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Compile>
		<Compile Update="Tables\Products\2024_03_19_0200_UaeAddCUPProductToProductTbl.cs">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Compile>
		<Compile Update="Tables\Products\2024_04_23_0200_UaeAddJCBProductToProductTbl.cs">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Compile>
		<Compile Update="Tables\Products\2024_05_05_0200_UaeAddJCBProductToProductTblBNDL.cs">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Compile>
		<Compile Update="Tables\Products\2024_06_24_0300_UaeAddDiscoverProductToProductTbl.cs">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Compile>
		<Compile Update="Tables\Products\2024_06_24_0345_UaeAddDinersProductToProductTbl.cs">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<None Update="Scripts\EgyptGoAirBundleUpdate.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\CleanupDeprecatedGleStructure.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\GleUpdateHierarchyData.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\MccAddNameAr.sql">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\PercentagePriceUpdate_Saudi.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\SAUDI_TerminalDataSets_Update_ChannelType_ConnectionType.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_AddAmexProduct.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\EgyptTerminalDataSetsNewColumns.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\EgyptTerminalDataSetsUpdateNewColumns.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\GleHistoryLog.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AccessoriesPricesUpdates.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductInstance_AddMpgsHistoryMetadata.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\BreakdownAvailability.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductsUpdate_Geidea_Pos_BundleUAE.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\RemoveMeezaCardSchemeFromUae.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_Go_Smart_With_Bill_Payments_Sales_Chanell_All.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\TerminalDataSetSyncWithOrderMigration_V2.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ChangePartFromProduct_v2.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\GenesisSmart_Bundles_Live.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\PriceUpdates_Egypt_GoSmartBundle.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\PriceUpdates_Egypt_PaymentGatewayBundle.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\EgyptGoSmartBundleCreate.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\EgyptCategoriesUpdate.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\EgyptBundlesUpdate.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_GenesisBundle_Adjust_ReferralChannel_For_Card_Scheme.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_Add_Specific_Products_For_MerchantMigration.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductUpdate_SpectraSp530_ProductCode.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Product_Update_GenesisBundle_Add_Gcc_Card_Scheme.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductsUpdate_Go_Smart_Bundle_SAUDI.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductsUpdate_Go_Smart_Bundle_SAUDI_2.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductsUpdate_Payment_Gateway_Bundle.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_GenesisSmart_Bundles.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_Update_Sarie_Prices.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_Add_Pay_By_Link_Bundle.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductUpdate_PriceUpdate_Payment_Gateway_Bundle_UAE.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductsUpdate_Payment_Gateway_Bundle_UAE.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_v11.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductUpdateProcedure_v8.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_AddOldGeideaBundles.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\PriceUpdates_Saudi_GenesisBundle.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\MarkAmexSchemeAsObsolete.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_Go_Smart_With_Bill_Payments_Bundle.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\TIDsStockViewScript.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\UAEInitialSeed.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\EgyptInitialSeed.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\InitialSeed.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\InitialSeed_AfterRecreate.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\BundlePricesUpdates.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\PriceUpdates_Egypt_PaymentGateway_RentalPeriod.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\PriceUpdates_Egypt_RemoveCpPrices.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\PriceUpdates_Add_HelpRequired_Bundles.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductInstances_EposTicketId_NonClustered_Index.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductsUpdate_ProCategory.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\PriceUpdate_Release_4_4.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_GeideaGo_Bundles.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_Make_Code_Unique.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\PriceUpdates.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_Naming_Updates.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_v10.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_v2.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_v3.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_v4.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_v5.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_v6.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_v7.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_v8.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_v9.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductUpdateProcedure.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductInstances_ProviderBankDefault.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductUpdateProcedure_v2.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductUpdateProcedure_v3.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductUpdateProcedure_v5.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductUpdateProcedure_v4.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductUpdateProcedure_v7.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductUpdateProcedure_v6.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\EgyptProductsChannelUpdate.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Product_add_missuse_and_sarie_fees.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ShopUpdates.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Products_Go_Smart_With_Bill_Payments_Disable_In_Shop.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\EgyptGoAirBundleUpdateOrder_Parts.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\Create_TerminalId_Sequence.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\CreateIndexOnProductInstances.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AlterValueAddedServices.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AddNonTransactionalFeesTable.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\CreateMccCategoryTable.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\CreateMccTable.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AddNewBusinessTypeTable.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AddNewUnitPriceTable.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\PendingUnitPriceList.sql">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\UnitPriceList.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\UnitPriceLogsTable.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ValueAddedServicePricingTable.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AlterTableBusinessType.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ValueAddedServicePricingLogs.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductCommissionPriceTable.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ValueAddedServiceList.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AlterUnitPriceList.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AlterProductCommissionPriceListingView.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AlterValueAddedServicesList.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\NonTransactionalPriceTable.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\NonTransactionalFeesPriceListView.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\NonTransactionalPriceLog.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductCategoryView.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\OnboardingUnitPriceList.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AlterUnitPriceListView.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\AddCommissionFeesTable.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductCommissionPriceLog.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Scripts\ProductCommissionPriceListingView.sql">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>
