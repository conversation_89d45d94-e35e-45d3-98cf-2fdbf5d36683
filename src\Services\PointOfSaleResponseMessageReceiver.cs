﻿using System;
using Common.Data;
using Common.Models;
using Common.Repositories;
using Common.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Geidea.Utils.Counterparty.Providers;

namespace Services;

public sealed class PointOfSaleResponseMessageReceiver : IDisposable
{
    private readonly IServiceScopeFactory scopeFactory;
    private readonly IPointOfSaleResponseMessageClient messageClient;
    private readonly ILogger<PointOfSaleResponseMessageReceiver> logger;

    public PointOfSaleResponseMessageReceiver(IServiceScopeFactory scopeFactory,
        IPointOfSaleResponseMessageClient messageClient,
        ILogger<PointOfSaleResponseMessageReceiver> logger)
    {
        this.scopeFactory = scopeFactory;
        this.messageClient = messageClient;
        this.logger = logger;
    }
    public void StartReceiving()
    {
        messageClient.OnPointOfSaleResponseMessageReceived += OnGsdkPointOfSaleResponseMessageReceived;
        messageClient.Connect();
    }

    private async void OnGsdkPointOfSaleResponseMessageReceived(object? sender, PointOfSaleResponseMessageReceivedEventArgs args)
    {
        try
        {
            logger.LogInformation("Start adding GSDK secret key to {@productInstanceId}", args.Message.ProductInstanceId);
            var pointOfSaleResponse = args.Message;

            using var scope = scopeFactory.CreateScope();

            var counterpartyProvider = scope.ServiceProvider.GetRequiredService<ICounterpartyProvider>();
            counterpartyProvider.SetCode(args.Counterparty);

            var productInstanceRepository = scope.ServiceProvider.GetRequiredService<IProductInstanceRepository>();
            var productInstance = await productInstanceRepository.GetByIdAsync(pointOfSaleResponse.ProductInstanceId);

            if (productInstance.Data is GatewayData gatewayData)
            {
                gatewayData.GsdkSecretKey = pointOfSaleResponse.SecretKey;
                productInstance.Data = gatewayData;
            }
            else if (productInstance.Data is TerminalData terminalData)
            {
                terminalData.GSDKKey = pointOfSaleResponse.SecretKey;
                productInstance.Data = terminalData;
            }
            else
            {
                logger.LogError("Expected gateway or terminal product instance.");
                return;
            }

            productInstanceRepository.Update(productInstance);
            await productInstanceRepository.SaveChangesAsync();

            var productChangeSenderService = scope.ServiceProvider.GetRequiredService<IProductChangeSenderService>();
            productChangeSenderService.SendUpdatedEvent(productInstance);

            logger.LogInformation("GSDK secret key was added to {@productInstanceId}", args.Message.ProductInstanceId);
        }
        catch (Exception e)
        {
            logger.LogError("OnGsdkPointOfSaleResponseMessageReceived throw error while trying to add GSDK secret key {@ex}", e);
        }
    }
    public void Dispose()
    {
        messageClient.OnPointOfSaleResponseMessageReceived -= OnGsdkPointOfSaleResponseMessageReceived;
    }
}
