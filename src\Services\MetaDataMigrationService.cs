﻿using Common.Data;
using Common.Entities;
using Common.Repositories;
using Common.Services;
using Geidea.ProductService.Models;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace Services;
public class MetaDataMigrationService : IMetaDataMigrationService
{
    private readonly ILogger<MetaDataMigrationService> logger;
    private readonly IMetaDataMigrationFilesRepository metaDataMigrationFilesRepository;
    private readonly IProductInstanceRepository productInstanceRepository;

    public MetaDataMigrationService(
        ILogger<MetaDataMigrationService> logger,
        IMetaDataMigrationFilesRepository metaDataMigrationFilesRepository,
        IProductInstanceRepository productInstanceRepository)
    {
        this.logger = logger;
        this.metaDataMigrationFilesRepository = metaDataMigrationFilesRepository;
        this.productInstanceRepository = productInstanceRepository;
    }

    public async Task MigrateMetaDataScripts()
    {
        try
        {
            List<MetaDataMigrationFiles> metadataScriptsToBeMigrated = await GetScriptsToBeMigrated();

            await UpdateAllEleigibeScriptsToPendingStatus(metadataScriptsToBeMigrated);

            foreach (MetaDataMigrationFiles metaDataFile in metadataScriptsToBeMigrated)
            {
                await UpdateScriptInDataBase(metaDataFile, MetadataStatus.InProgress.ToString());

                logger.LogInformation("Start migrate {MigrationFileName}", metaDataFile.MigrationFileName);

                List<string> eligibalProductInstanceIds;
                try
                {
                    if (metaDataFile.Status.Equals(MetadataStatus.PartiallySucceeded.ToString()))
                    {
                        eligibalProductInstanceIds = metaDataFile.FailedIds!;
                    }
                    else
                    {
                        eligibalProductInstanceIds = await productInstanceRepository.SelectByQueryString(metaDataFile.SelectQuery ?? "");
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Error happened in execute select query for migrating {MigrationFileName} ,\n {message}", metaDataFile.MigrationFileName, ex.Message);
                    await UpdateScriptInDataBase(metaDataFile, MetadataStatus.Failed.ToString());
                    continue;
                }

                var failedIds = await Migrate(metaDataFile, eligibalProductInstanceIds);
                int eligibleRecordsToBeUpdatedCount = eligibalProductInstanceIds.Count;
                await AddOrUpdateScriptInDatabase(metaDataFile, eligibleRecordsToBeUpdatedCount, failedIds);

                logger.LogInformation("update metadata migration files {MigrationFileName} success", metaDataFile.MigrationFileName);
            }
        }
        catch (Exception ex)
        {
            logger.LogError("update metadata migration files failed,\n {Message}", ex.Message);
        }
    }

    private async Task UpdateAllEleigibeScriptsToPendingStatus(List<MetaDataMigrationFiles> metadataScriptsToBeMigrated)
    {
        foreach (var metaDataFile in metadataScriptsToBeMigrated)
        {
            await UpdateScriptInDataBase(metaDataFile, MetadataStatus.Pending.ToString());
        }
    }

    private async Task<List<string>> Migrate(MetaDataMigrationFiles metaDataFile, List<string> eligibalProductInstanceIds)
    {
        List<string> failedProductInstanceIds = new List<string>();
        int nSize = 100;

        for (int i = 0; i < eligibalProductInstanceIds.Count; i += nSize)
        {
            List<string> smallList = new List<string>();
            string fullUpdatequery = string.Empty;

            try
            {
                smallList = eligibalProductInstanceIds.GetRange(i, Math.Min(nSize, eligibalProductInstanceIds.Count - i));
                fullUpdatequery = metaDataFile.UpdateQuery + " AND pi.Id IN ('" + string.Join("','", smallList.ToArray()) + "');";
                await productInstanceRepository.UpdateByQueryString(fullUpdatequery);
            }
            catch (SqlException sqlException)
            {
                if (sqlException.Number == -2)
                {
                    logger.LogError("TimeOutException happened in migrating {MigrationFileName} and will try again, for these productinstance ids {smallList},\n {message}", metaDataFile.MigrationFileName, smallList, sqlException.Message);
                    try
                    {
                        await productInstanceRepository.UpdateByQueryString(fullUpdatequery);
                    }
                    catch (Exception ex)
                    {
                        failedProductInstanceIds.AddRange(smallList);
                        logger.LogError("Error happened in migrating {MigrationFileName} for second time, for these productinstance ids {smallList},\n {message}", metaDataFile.MigrationFileName, smallList, ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                failedProductInstanceIds.AddRange(smallList);
                logger.LogError("Error happened in migrating {MigrationFileName}, for these productinstance ids {smallList},\n {message}", metaDataFile.MigrationFileName, smallList, ex.Message);
            }
        }

        return failedProductInstanceIds;
    }

    private async Task AddOrUpdateScriptInDatabase(MetaDataMigrationFiles metaDataFile, int eligibleRecordsToBeUpdatedCount, List<string> failedIds)
    {
        var failedRecordsCount = failedIds.Count;
        string status;
        if (failedRecordsCount == 0)
        {
            status = MetadataStatus.Succeeded.ToString();
        }
        else if (failedRecordsCount < eligibleRecordsToBeUpdatedCount)
        {
            status = MetadataStatus.PartiallySucceeded.ToString();
        }
        else
        {
            status = MetadataStatus.Failed.ToString();
        }

        await UpdateScriptInDataBase(metaDataFile, status, failedIds);
    }

    private async Task UpdateScriptInDataBase(MetaDataMigrationFiles metaDataFile, string status, List<string>? failedIds = null)
    {
        try
        {
            var newMigrationEntity = new MetaDataMigrationFilesEntity
            {
                MigrationFileName = metaDataFile.MigrationFileName,
                SelectQuery = metaDataFile.SelectQuery,
                UpdateQuery = metaDataFile.UpdateQuery,
                CreatedDate = DateTime.UtcNow,
                Status = status,
                FailedIds = failedIds != null ? JsonSerializer.Serialize(failedIds) : string.Empty
            };

            if (metaDataFile.Id != null)
            {
                newMigrationEntity.CreatedDate = metaDataFile.CreatedDate;
                logger.LogInformation("Update old migration script status in database {MigrationFileName}", metaDataFile.MigrationFileName);
                await metaDataMigrationFilesRepository.UpdateMigrationFile(metaDataFile.Id.Value, newMigrationEntity);
            }
            else
            {
                logger.LogInformation("Insert New migration script in database {MigrationFileName}", metaDataFile.MigrationFileName);
                await metaDataMigrationFilesRepository.InsertAsync(newMigrationEntity);
            }

            //update the Id with the new id added
            metaDataFile.Id = newMigrationEntity.Id;
        }
        catch (Exception ex)
        {
            logger.LogError("Error happened in inserting/updating metadata migration script in database {MigrationFileName}, \n {Message}", metaDataFile.MigrationFileName, ex.Message);
        }
    }

    private async Task<List<MetaDataMigrationFiles>> GetScriptsToBeMigrated()
    {
        List<MetaDataMigrationFiles> metaDataMigrationFilesList = MetaDataMigrationFilesList.getMetaDataMigrationFilesList();

        var metaDataAllMigrationFiles = await metaDataMigrationFilesRepository.GetAllMigrationFiles();
        List<MetaDataMigrationFiles> dataBaseAllMetaDataMigrations = new();
        foreach (MetaDataMigrationFilesEntity metaDataFinishedMigrationFile in metaDataAllMigrationFiles)
        {
            dataBaseAllMetaDataMigrations.Add(new MetaDataMigrationFiles
            {
                Id = metaDataFinishedMigrationFile.Id,
                MigrationFileName = metaDataFinishedMigrationFile.MigrationFileName,
                UpdateQuery = metaDataFinishedMigrationFile.UpdateQuery,
                SelectQuery = metaDataFinishedMigrationFile.SelectQuery,
                CreatedDate = metaDataFinishedMigrationFile.CreatedDate,
                Status = metaDataFinishedMigrationFile.Status!,
                FailedIds = !string.IsNullOrEmpty(metaDataFinishedMigrationFile.FailedIds) ? JsonSerializer.Deserialize<List<string>>(metaDataFinishedMigrationFile.FailedIds!) : null
            });
        }
        List<MetaDataMigrationFiles> newMetaDataMigrationFiles = metaDataMigrationFilesList.Where(a => !dataBaseAllMetaDataMigrations.Any(b => a.MigrationFileName == b.MigrationFileName)).ToList();
        newMetaDataMigrationFiles.AddRange(dataBaseAllMetaDataMigrations.Where(m => m.Status.Equals(MetadataStatus.Failed.ToString())
                                                                                || m.Status.Equals(MetadataStatus.PartiallySucceeded.ToString())).ToList());
        return newMetaDataMigrationFiles;
    }
}
