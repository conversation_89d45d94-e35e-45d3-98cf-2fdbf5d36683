
CREATE TABLE NonTransactionalFeesPrice (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    ProductID UNIQUEIDENTIFIER NOT NULL,
    MCCID UNIQUEIDENTIFIER NOT NULL,
    BusinessTypeID UNIQUEIDENTIFIER NOT NULL,
    NonTransFeeID UNIQUEIDENTIFIER NOT NULL,
    FeeType INT NOT NULL,  
    FeeValue DECIMAL(9, 2) NOT NULL,  
    BillingType INT NOT NULL, 
    BillingFrequency INT NOT NULL,  
    CreatedBy VARCHAR(255) NOT NULL,
    CreatedDate DATETIME NOT NULL,
    UpdatedBy VARCHAR(255),
    UpdatedDate DATETIME,

    -- Adding Unique Constraint
    CONSTRAINT UQ_Product_MCC_BusinessType_NonTransactionalFees UNIQUE (ProductID, MCCID, BusinessTypeID, NonTransFeeID),

    FOREIGN KEY (ProductID) REFERENCES Products(Id),
    <PERSON>OREI<PERSON><PERSON> KEY (MCCID) REFERENCES MCC(Id),
    FOR<PERSON><PERSON><PERSON> KEY (BusinessTypeID) REFERENCES BusinessTypes(Id),
    FOREIG<PERSON> KEY (NonTransFeeID) REFERENCES NonTransactionalFees(Id)
);
