﻿using System.Collections.Generic;
using Common.Data.ProductType;
using Common.Services;
using FluentAssertions;
using GeideaPaymentGateway.Utils.RabbitMQ;
using Messaging;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NSubstitute;
using Xunit;

namespace Services.Test;

public class ProductInstancePublisherSelectorTests
{
    private readonly ProductInstancePublisherSelector publisherSelector;
    IOptionsMonitor<RabbitMqConfig> optionsMonitor;
    ILogger<TerminalConfigurationPublisher> terminalLogger;
    ILogger<MeezaConfigurationPublisher> meezaLogger;
    ILogger<GatewayConfigurationPublisher> gwayLogger;
    IHttpContextAccessor contextAccessor;
    IConnectionFactory factory;

    public ProductInstancePublisherSelectorTests()
    {
        optionsMonitor = Substitute.For<IOptionsMonitor<RabbitMqConfig>>();
        contextAccessor = Substitute.For<IHttpContextAccessor>();
        factory = Substitute.For<IConnectionFactory>();
        terminalLogger = Substitute.For<ILogger<TerminalConfigurationPublisher>>();
        meezaLogger = Substitute.For<ILogger<MeezaConfigurationPublisher>>();
        gwayLogger = Substitute.For<ILogger<GatewayConfigurationPublisher>>();

        List<ProductTypes> GwayProductType = new List<ProductTypes> { ProductTypes.GWAY };
        List<ProductTypes> TerminalProductType = new List<ProductTypes> { ProductTypes.TERMINAL, ProductTypes.M_POS };

        var gatewayPublisher = new Mock<IProductInstancePublisher>();
        gatewayPublisher.Setup(x => x.ProductTypes).Returns(GwayProductType);

        var gatewayMeezaPublisher = new Mock<IGatewayProductInstancePublisher>();
        gatewayMeezaPublisher.Setup(x => x.ProductTypes).Returns(GwayProductType);

        var terminalPublisher = new Mock<IProductInstancePublisher>();
        terminalPublisher.Setup(x => x.ProductTypes).Returns(TerminalProductType);
        publisherSelector = new ProductInstancePublisherSelector(new List<IProductInstancePublisher> { gatewayPublisher.Object, terminalPublisher.Object });
    }

    [Theory]
    [InlineData(ProductTypes.TERMINAL)]
    [InlineData(ProductTypes.M_POS)]
    [InlineData(ProductTypes.GWAY)]
    public void TestExistingPublisher(ProductTypes type)
    {
        var publisher = publisherSelector.GetProductInstancePublisher(type);
        publisher.ProductTypes.Should().Contain(type);
    }

    [Fact]
    public void TestTerminalPublisher()
    {
        var terminalConfigurationPublisher = new TerminalConfigurationPublisher(optionsMonitor, terminalLogger, contextAccessor, factory);
        var productTypes = terminalConfigurationPublisher.ProductTypes;

        var exepctedProductTypes = new List<ProductTypes> { ProductTypes.TERMINAL, ProductTypes.M_POS };
        productTypes.Should().BeEquivalentTo(exepctedProductTypes);
    }

    [Fact]
    public void TestGwayPublisher()
    {
        var gwayConfigurationPublisher = new GatewayConfigurationPublisher(optionsMonitor, gwayLogger, contextAccessor, factory);
        var productTypes = gwayConfigurationPublisher.ProductTypes;
        productTypes.Should().Contain(ProductTypes.GWAY);
    }

    [Fact]
    public void TestMeezaPublisher()
    {
        var meezaConfigurationPublisher = new MeezaConfigurationPublisher(optionsMonitor, meezaLogger, contextAccessor, factory);
        var productTypes = meezaConfigurationPublisher.ProductTypes;
        productTypes.Should().Contain(ProductTypes.MEEZA);
    }

    [Fact]
    public void TestNotExistingPublisher()
    {
        var publisher = publisherSelector.GetProductInstancePublisher(ProductTypes.BUNDLE);
        publisher.Should().BeNull();
    }
}
