﻿EXEC NewProductVersion 'GO_A920_V3','GO_A920_V4', 1
EXEC NewProductVersion 'GO_MPOS_V3','GO_MPOS_V4', 1

DECLARE @MPOS_v4 UNIQUEIDENTIFIER
SELECT TOP 1 @MPOS_v4 = ID FROM Products where Code='GO_MPOS_V4' 

DECLARE @A920_v4 UNIQUEIDENTIFIER
SELECT TOP 1 @A920_v4 = ID FROM Products where Code='GO_A920_V4' 

DECLARE @AMEX_POS UNIQUEIDENTIFIER
SELECT TOP 1 @AMEX_POS = ID FROM Products where Code='AMEX_POS' 

INSERT INTO ProductParts(ProductId, PartId) VALUES(@MPOS_v4, @AMEX_POS)
INSERT INTO ProductParts(ProductId, PartId) VALUES(@A920_v4, @AMEX_POS)