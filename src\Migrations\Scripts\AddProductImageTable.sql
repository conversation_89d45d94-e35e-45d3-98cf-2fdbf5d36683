IF NOT EXISTS (
    SELECT 1
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_NAME = 'ProductImages' AND TABLE_SCHEMA = 'dbo'
)
BEGIN
    CREATE TABLE ProductImages
    (
        Id UNIQUEIDENTIFIER PRIMARY KEY, -- Inherits the Id field from AuditableEntity
        Language NVARCHAR(255) NULL,     -- Language of the image, nullable
        ImageId UNIQUEIDENTIFIER NOT NULL, -- Unique identifier for the image, assumed to be a GUID
        ProductId UNIQUEIDENTIFIER NOT NULL, -- Foreign key to the Product entity
        DisplayOrder INT NULL,            -- Optional field to specify the display order of images
        CreatedBy NVARCHAR(255) NOT NULL, -- Created by user identifier
        CreatedDate DATETIME2 NOT NULL,   -- Creation date and time
        UpdatedBy NVARCHAR(255) NULL,     -- Updated by user identifier, nullable
        UpdatedDate DATETIME2 NULL,       -- Date and time of last update, nullable
        -- Foreign key constraint to ensure ProductId references an existing Product entity
        CONSTRAINT FK_ProductImages_Products FOREIGN KEY (ProductId)
        REFERENCES Products(Id)
        ON DELETE CASCADE -- Specifies that if the referenced product is deleted, the image should also be deleted
    );
END;
