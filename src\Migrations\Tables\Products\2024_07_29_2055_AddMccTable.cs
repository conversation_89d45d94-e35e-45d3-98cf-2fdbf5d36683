﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.Products;
[Migration(2024_07_29_1207)]
public class AddMccTable : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "CreateMccTable.sql"));
    }
}

[Migration(2025_01_19_1658)]
public class AlterMccTable_AddNameAr : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "MccAddNameAr.sql"));
    }
}