﻿using AutoMapper;
using Common.Entities;
using Common.Repositories;
using Microsoft.Extensions.Logging;
using Moq;
using ProductService;
using System;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using FluentAssertions;
using Common.Models.CategoryRequests;
using Geidea.Utils.Exceptions;
using System.Net;
using Common;
using Microsoft.EntityFrameworkCore;
using DataAccess;
using DataAccess.Repositories;
using Geidea.Utils.Counterparty.Providers;
using Microsoft.AspNetCore.Http;
using Services.Test.Helpers;

namespace Services.Test.CategoryServiceTests;

public class CreateTests
{
    private readonly ICategoryRepository categoryRepository;
    private readonly Mock<ILogger<CategoryService>> logger = new();
    private readonly Mock<IHttpContextAccessor> httpContext = new();
    private readonly Mock<IProductRepository> productRepository = new();
    private readonly CategoryService categoryService;
    private readonly DataContext context;
    private readonly CategoryEntity category = new()
    {
        Code = "TEST"
    };

    public CreateTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
            .UseInMemoryDatabase(databaseName: "CategoryCreateTests" + Guid.NewGuid().ToString())
            .Options;

        context = new DataContext(options, new CounterpartyProvider());
        categoryRepository = new CategoryRepository(context, httpContext.Object);
        categoryRepository.Save(category);
        categoryRepository.SaveChanges();

        categoryService = new CategoryService(categoryRepository, logger.Object, mapper, productRepository.Object);
    }

    [Fact]
    public async Task ExistingCode()
    {
        await categoryService
            .Invoking(x => x.CreateAsync(new CategoryRequest { Code = "TEST" }))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.InvalidCategoryCode.Code);
    }

    [Fact]
    public async Task NotExistingParent()
    {
        await categoryService
            .Invoking(x => x.CreateAsync(new CategoryRequest { ParentId = Guid.NewGuid() }))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
                        x.ProblemDetails.Type == Errors.CategoryNotFound.Code);
    }

    [Fact]
    public async Task ExistingParent()
    {
        var categoryRequest = new CategoryRequest { Code = "TEST_PARENT", ParentId = category.Id };
        await categoryService.CreateAsync(categoryRequest);

        var created = context.Categories.Local.FirstOrDefault(c => c.Code == categoryRequest.Code);
        created.Should().NotBeNull();
        created.Should().BeEquivalentTo(categoryRequest);
        created.SalesChannel.Should().BeNull();
    }

    [Fact]
    public async Task NoParent()
    {
        var categoryRequest = new CategoryRequest { Code = "TEST_NO_PARENT" };
        await categoryService.CreateAsync(categoryRequest);

        var created = context.Categories.Local.FirstOrDefault(c => c.Code == categoryRequest.Code);
        created.Should().NotBeNull();
        created.Should().BeEquivalentTo(categoryRequest);
        created.SalesChannel.Should().BeNull();
    }

    [Fact]
    public async Task InvalidAvailability()
    {
        await categoryService
            .Invoking(x => x.CreateAsync(new CategoryRequest { Code = "NEW_CODE", SalesChannel = "Something else" }))
            .Should().ThrowAsync<ValidationException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        TestsHelper.HasErrorCode(x, Errors.InvalidSalesChannel.Code));
    }

    [Fact]
    public async Task WithAvailability()
    {
        var categoryRequest = new CategoryRequest { Code = "NEW_CODE_2", SalesChannel = Constants.SalesChannel.Shop };
        await categoryService.CreateAsync(categoryRequest);

        var created = await categoryRepository.FirstOrDefaultAsync(c => c.Code == categoryRequest.Code);
        created.Should().NotBeNull();
        created.Should().BeEquivalentTo(categoryRequest);
    }

    [Fact]
    public async Task CreateAsync_WhenRequestContainsValidFlow_ShouldAddFlow()
    {
        var categoryRequest = new CategoryRequest { Code = "NEW_CODE_3", Flow = Constants.Flow.HelpRequired };
        await categoryService.CreateAsync(categoryRequest);

        var created = await categoryRepository.FirstOrDefaultAsync(c => c.Code == categoryRequest.Code);
        created.Should().NotBeNull();
        created.Should().BeEquivalentTo(categoryRequest);
    }
}
