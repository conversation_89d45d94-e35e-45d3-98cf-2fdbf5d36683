﻿BEGIN

 DECLARE @missuseFee<PERSON>ursor CURSOR 
 DECLARE @sarieFee<PERSON>ursor CURSOR 
 DECLARE @productId UNIQUEIDENTIFIER
 
-- Add missused fees
 SET @missuseFeeCursor = CURSOR FOR SELECT Id FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_SAUDI' AND Code IN ('GO_AIR', 'GO_SMART')

 OPEN @missuseFeeCursor FETCH NEXT FROM @missuseFeeCursor INTO @ProductId WHILE @@FETCH_STATUS = 0
 BEGIN
     INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, PerItemPrice, [Priority], ValidFrom, CreatedBy, CreatedDate, Currency, ProductId)
	 VALUES (NULL, 'PENALTY_CHARGE', 0, 20000, 1, GETUTCDATE(), '00000000-0000-0000-0000-000000000000', GETUTCDATE(), 'SAR', @productId)

	 FETCH NEXT FROM @missuseFeeCursor INTO @ProductId
 END
 
 CLOSE @missuseFeeCursor
 DEALLOCATE @missuseFeeCursor

 -- Add sarie fees
 SET @sarieFeeCursor = CURSOR FOR SELECT Id FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_SAUDI' AND Code IN ('GO_AIR', 'GO_SMART', 'PRO_SMART', 'PAYMENT_GATEWAY_BUNDLE', 'WEBSITE_BUILDER_BUNDLE')

 OPEN @sarieFeeCursor FETCH NEXT FROM @sarieFeeCursor INTO @ProductId WHILE @@FETCH_STATUS = 0
 BEGIN
     INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, PerItemPrice, [Priority], ValidFrom, CreatedBy, CreatedDate, Currency, ProductId)
	 VALUES (NULL, 'SARIE_CHARGE', 0, 700, 1, GETUTCDATE(), '00000000-0000-0000-0000-000000000000', GETUTCDATE(), 'SAR', @productId)

	 FETCH NEXT FROM @sarieFeeCursor INTO @ProductId
 END
 
 CLOSE @sarieFeeCursor
 DEALLOCATE @sarieFeeCursor

 END