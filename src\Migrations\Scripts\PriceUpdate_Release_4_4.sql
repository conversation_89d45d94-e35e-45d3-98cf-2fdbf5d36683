﻿DECLARE @ProductId UNIQUEIDENTIFIER

--IPad Stand
EXEC NewProductVersion_v2 'IPAD_STAND', 1, 'IPAD_STAND', 2, 1
SELECT TOP 1 @ProductId = ID FROM Products where Code='IPAD_STAND' and Version = 2
UPDATE Prices SET PerItemPrice = 27000 where ProductId = @ProductId

--Pro Cash Drawer
EXEC NewProductVersion_v2 'PRO_CASH_DRAWER', 1, 'PRO_CASH_DRAWER', 2, 1
SELECT TOP 1 @ProductId = ID FROM Products where Code='PRO_CASH_DRAWER' and Version = 2
UPDATE Prices SET PerItemPrice = 37300 where ProductId = @ProductId

--Pro Scanner
EXEC NewProductVersion_v2 'PRO_SCANNER', 1, 'PRO_SCANNER', 2, 1
SELECT TOP 1 @ProductId = ID FROM Products where Code='PRO_SCANNER' and Version = 2
UPDATE Prices SET PerItemPrice = 20100 where ProductId = @ProductId

--Pro Printer
EXEC NewProductVersion_v2 'PRO_PRINTER', 1, 'PRO_PRINTER', 2, 1
SELECT TOP 1 @ProductId = ID FROM Products where Code='PRO_PRINTER' and Version = 2
UPDATE Prices SET PerItemPrice = 33500 where ProductId = @ProductId

--Pro Screen Lite
EXEC NewProductVersion_v2 'PRO_SCREEN_LITE', 1, 'PRO_SCREEN_LITE', 2, 1
SELECT TOP 1 @ProductId = ID FROM Products where Code='PRO_SCREEN_LITE' and Version = 2
UPDATE Prices SET PerItemPrice = 155700 where ProductId = @ProductId

--Pro Screen PRO_SCREEN 3271
EXEC NewProductVersion_v2 'PRO_SCREEN', 1, 'PRO_SCREEN', 2, 1
SELECT TOP 1 @ProductId = ID FROM Products where Code='PRO_SCREEN' and Version = 2
UPDATE Prices SET PerItemPrice = 327100 where ProductId = @ProductId
