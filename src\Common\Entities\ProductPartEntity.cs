﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Common.Entities;

public class ProductPartEntity
{
    public Guid ProductId { get; set; }
    public Guid PartId { get; set; }
    public int Quantity { get; set; } = 1;

    [ForeignKey("ProductId")]
    public ProductEntity Product { get; set; } = null!;
    [ForeignKey("PartId")]
    public ProductEntity Part { get; set; } = null!;
}
