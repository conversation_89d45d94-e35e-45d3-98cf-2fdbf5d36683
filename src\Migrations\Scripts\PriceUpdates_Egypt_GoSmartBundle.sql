﻿BEGIN TRANSACTION;

DECLARE @GoFamilyCategoryId UNIQUEIDENTIFIER
SELECT TOP 1 @GoFamilyCategoryId = ID FROM [PRODUCTS].[dbo].[Category] WHERE Code = 'GO_FAMILY' AND Counterparty = 'GEIDEA_EGYPT'

DECLARE @GoSmartId UNIQUEIDENTIFIER
SELECT TOP 1 @GoSmartId = ID FROM [PRODUCTS].[dbo].[Products] WHERE Code = 'GO_SMART' AND Availability = 'Live' AND Counterparty = 'GEIDEA_EGYPT'
AND [Id] in (SELECT ProductId FROM [PRODUCTS].[dbo].[ProductCategories] pc where pc.[CategoryId] = @GoFamilyCategoryId)

DECLARE @MasterCardId UNIQUEIDENTIFIER
SELECT TOP 1 @MasterCardId = ID FROM [PRODUCTS].[dbo].[Products] WHERE Code = 'MC' AND Availability = 'Live' AND Counterparty = 'GEIDEA_EGYPT'
AND [Id] in (SELECT PartId FROM [PRODUCTS].[dbo].[ProductParts] where ProductId = @GoSmartId)

DECLARE @VisaId UNIQUEIDENTIFIER
SELECT TOP 1 @VisaId = ID FROM [PRODUCTS].[dbo].[Products] WHERE Code = 'VISA' AND Availability = 'Live' AND Counterparty = 'GEIDEA_EGYPT'
AND [Id] in (SELECT PartId FROM [PRODUCTS].[dbo].[ProductParts] where ProductId = @GoSmartId)

DECLARE @MeezaId UNIQUEIDENTIFIER
SELECT TOP 1 @MeezaId = ID FROM [PRODUCTS].[dbo].[Products] WHERE Code = 'MEEZA' AND Availability = 'Live' AND Counterparty = 'GEIDEA_EGYPT'
AND [Id] in (SELECT PartId FROM [PRODUCTS].[dbo].[ProductParts] where ProductId = @GoSmartId)

DECLARE @GoSmartPriceId UNIQUEIDENTIFIER
SELECT TOP 1 @GoSmartPriceId = ID FROM [PRODUCTS].[dbo].[Prices] WHERE ChargeType = 'RECCURRING_CHARGE' AND DeletedFlag = 0 AND [ProductId] = @GoSmartId

DECLARE @MasterCardPriceId UNIQUEIDENTIFIER
SELECT TOP 1 @MasterCardPriceId = ID FROM [PRODUCTS].[dbo].[Prices] WHERE ChargeType = 'PURCHASE_CNP' AND DeletedFlag = 0 AND [ProductId] = @MasterCardId

DECLARE @VisaPriceId UNIQUEIDENTIFIER
SELECT TOP 1 @VisaPriceId = ID FROM [PRODUCTS].[dbo].[Prices] WHERE ChargeType = 'PURCHASE_CNP' AND DeletedFlag = 0 AND [ProductId] = @VisaId

DECLARE @MeezaPriceId UNIQUEIDENTIFIER
SELECT TOP 1 @MeezaPriceId = ID FROM [PRODUCTS].[dbo].[Prices] WHERE ChargeType = 'PURCHASE_CNP' AND DeletedFlag = 0 AND [ProductId] = @MeezaId

UPDATE [PRODUCTS].[dbo].[Prices] SET PerItemPrice = NULL, RentalPeriod = NULL WHERE Id = @GoSmartPriceId 
UPDATE [PRODUCTS].[dbo].[Prices] SET PercentagePrice = NULL WHERE Id = @MasterCardPriceId 
UPDATE [PRODUCTS].[dbo].[Prices] SET PercentagePrice = NULL WHERE Id = @VisaPriceId 
UPDATE [PRODUCTS].[dbo].[Prices] SET PercentagePrice = NULL WHERE Id = @MeezaPriceId 

COMMIT;