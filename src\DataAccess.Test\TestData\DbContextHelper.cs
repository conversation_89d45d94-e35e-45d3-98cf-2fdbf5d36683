﻿using Geidea.Utils.Counterparty.Providers;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using System;

namespace DataAccess.Test.TestData;

public static class DbContextHelper
{
    public static DataContext CreateInMemoryDatabase(ICounterpartyProvider counterparty, string dbName = null)
    {
        var dbOptions = new DbContextOptionsBuilder<DataContext>()
            .UseInMemoryDatabase(dbName ?? Guid.NewGuid().ToString())
            .ConfigureWarnings(x => x.Ignore(InMemoryEventId.TransactionIgnoredWarning))
            .Options;

        var dataContext = new DataContext(dbOptions, counterparty);
        dataContext.Database.EnsureCreated();

        return dataContext;
    }
}
