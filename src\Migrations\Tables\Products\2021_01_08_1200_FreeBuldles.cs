﻿using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_01_08_1200)]
public class FreeBuldles : ForwardOnlyMigration
{
    public override void Up()
    {
        Update.Table("Products").Set(new { Code = "MC_ONLINE", Version = "0" }).Where(new { Code = "MC_ONLINE" });
        Update.Table("Products").Set(new { Code = "MC_ONLINE", Version = "1" }).Where(new { Code = "MC_ONLINE_V1" });

        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "ProductUpdateProcedure_v2.sql"));

        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "Products_v6.sql"));
    }
}
