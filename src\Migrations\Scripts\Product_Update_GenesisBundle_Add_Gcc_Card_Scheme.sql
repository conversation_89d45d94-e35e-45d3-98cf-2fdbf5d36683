﻿USE PRODUCTS;
GO
DECLARE @ProductId UNIQUEIDENTIFIER
DECLARE @GoFamily UNIQUEIDENTIFIER
DECLARE @GCC_NET_GENESIS UNIQUEIDENTIFIER

DECLARE @SCHEME VARCHAR(10) SET @SCHEME = 'SCHEME'
DECLARE @ChargeType VARCHAR(20) SET @ChargeType = 'PURCHASE_CP'
DECLARE @CounterParty VARCHAR(20) SET @CounterParty = 'GEIDEA_SAUDI' 
DECLARE @ChargeFrequency VARCHAR(10) SET @ChargeFrequency = 'ONE_OFF'
DECLARE @Flow VARCHAR(10) SET @Flow= 'Normal'

BEGIN TRAN

SELECT TOP 1 @GoFamily = ID FROM Category where Code = 'GO_FAMILY' and Counterparty = 'GEIDEA_SAUDI';

EXEC NewProductVersion_v2 @ProductCode = 'GENESIS_SMART', @ProductVersion = 1, @NewProductCode = 'GENESIS_SMART', @NewProductVersion = 2, @MarkObsolete = 1, @Counterparty = 'GEIDEA_SAUDI'

--SELECT new productId
SELECT TOP 1 @ProductId = ID FROM Products where Code = 'GENESIS_SMART' and [Version] = 2 and CounterParty = 'GEIDEA_SAUDI'

--Set Sabb Referral Channel for Genesis_Smart bundle
UPDATE Products SET [ReferralChannel] = 'SABB' WHERE Id = @ProductId

--Delete copied ProductCategories mappings for new product
DELETE FROM ProductCategories where ProductId = @ProductId 

--Insert ProductCategories mapping for new product
INSERT INTO ProductCategories(ProductId, CategoryId) VALUES (@ProductId, @GoFamily)

--Add new GCC_NET card scheme
INSERT INTO [PRODUCTS].[dbo].[Products](Availability, Code, Type, Description, CreatedBy, CreatedDate, ValidFrom, [Version], Counterparty, Flow)
VALUES('Live','GCC_NET_GENESIS',@SCHEME, 'GCC_NET_GENESIS','n/a', GETUTCDATE(),GETUTCDATE(), 0, @CounterParty,@Flow)

--Select GCCNET card scheme
SELECT TOP 1 @GCC_NET_GENESIS = ID FROM Products where Code = 'GCC_NET_GENESIS' and [Version] = 0 and CounterParty = @CounterParty

--Set Sabb Referral Channel for GCC_NET card scheme
UPDATE Products SET [ReferralChannel] = 'SABB' WHERE Id = @GCC_NET_GENESIS

--Insert GCC_NET card scheme in Prices
INSERT INTO [PRODUCTS].[dbo].[Prices] ([ChargeFrequency],[ChargeType],[ExemptFromVAT],[ProductId],[PercentagePrice],[Priority],[ValidFrom],[DeletedFlag],[CreatedBy],[CreatedDate],[UpdatedBy],[Currency])
VALUES (@ChargeFrequency, @ChargeType, 0, @GCC_NET_GENESIS, 150, 1, GETUTCDATE(), 0, '00000000-0000-0000-0000-000000000000', GETUTCDATE(), '00000000-0000-0000-0000-000000000000', 'SAR')

--Insert GCCNET card scheme in ProductParts 
INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId, Quantity) VALUES(@ProductId, @GCC_NET_GENESIS, 1)

COMMIT TRAN