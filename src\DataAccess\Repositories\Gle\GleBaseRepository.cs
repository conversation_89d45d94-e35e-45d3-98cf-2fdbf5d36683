﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Threading.Tasks;
using Common;
using Common.Models.Gle;
using Common.Repositories.Gle;
using Geidea.Utils.DataAccess.Entities;
using Geidea.Utils.DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DataAccess.Repositories.Gle;

public class GleBaseRepository<T> : AuditableRepository<Guid, T>, IGleBaseRepository<T>
    where T : AuditableEntity<Guid>, new()
{
    private readonly ILogger<GleBaseRepository<T>> logger;


    public GleBaseRepository(DbContext context,
        IHttpContextAccessor contextAccessor,
        ILogger<GleBaseRepository<T>> logger)
        : base(context, contextAccessor)
    {
        this.logger = logger;
    }

    public async Task AddGle(T entityToBeAdded)
    {
        try
        {
            base.Add(entityToBeAdded);
            await context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            logger.LogError("Invalid create GLE request. The error was {ex}", ex);
            throw new ServiceException(HttpStatusCode.BadRequest, ex.Message, Errors.InvalidCreateRequest.Code);
        }
    }

    public async Task UpdateGle(Guid gleId, JsonPatchDocument<T> updateDocument)
    {
        var gleEntity = await SingleOrDefaultAsync(x => x.Id == gleId);

        if (gleEntity == null)
        {
            logger.LogError("GLE entity not found for id: {id}", gleId);
            throw new ServiceException(HttpStatusCode.NotFound);
        }

        try
        {
            updateDocument.ApplyTo(gleEntity);
        }
        catch (Exception ex)
        {
            logger.LogError("Invalid patch GLE request.");
            throw new ServiceException(HttpStatusCode.BadRequest, ex.Message, Errors.InvalidPatchRequest.Code);
        }

        base.Update(gleEntity);
        await context.SaveChangesAsync();
    }

    [ExcludeFromCodeCoverage]
    public async Task<IReadOnlyCollection<GleRegistrationStatusInfo>> GetGleRegistrationStatusHistoryAsync(GleHistoryLogRequest gleHistoryLogRequest)
    {
        await using var command = context.Database.GetDbConnection().CreateCommand();
        command.CommandText = "[dbo].[GleHistoryLog]";
        command.CommandType = CommandType.StoredProcedure;

        CreateGuidParameterForStoredProcedure(command, "@MerchantId", gleHistoryLogRequest.MerchantId);
        CreateGuidParameterForStoredProcedure(command, "@OrderId", gleHistoryLogRequest.OrderId);
        CreateGuidParameterForStoredProcedure(command, "@ParentMerchantId", gleHistoryLogRequest.ParentMerchantId);

        if (command.Connection!.State == ConnectionState.Closed)
        {
            await command.Connection.OpenAsync();
        }

        var resultDbSet = await command.ExecuteReaderAsync();
        return GleRegistrationStatusInfoFromDbSet(resultDbSet);
    }

    [ExcludeFromCodeCoverage]
    private static void CreateGuidParameterForStoredProcedure(DbCommand command, string paramName, Guid? paramValue)
    {
        var parameter = command.CreateParameter();
        parameter.ParameterName = paramName;
        parameter.DbType = DbType.Guid;
        parameter.Value = paramValue ?? Convert.DBNull;
        command.Parameters.Add(parameter);
    }

    [ExcludeFromCodeCoverage]
    private static List<GleRegistrationStatusInfo> GleRegistrationStatusInfoFromDbSet(IDataReader resultDbSet)
    {
        var gleStatusHistory = new List<GleRegistrationStatusInfo>();
        var gleInstanceIdIndex = resultDbSet.GetOrdinal("Id");
        var gleStatusIndex = resultDbSet.GetOrdinal("RegistrationStatus");
        var gleResponseIndex = resultDbSet.GetOrdinal("RegistrationResponse");
        var updatedByIndex = resultDbSet.GetOrdinal("SubmittedBy");
        var gleStatusStartDateIndex = resultDbSet.GetOrdinal("RequestDate");
        var gleRegistrationType = resultDbSet.GetOrdinal("RegistrationType");
        var referenceMmsIdIndex = resultDbSet.GetOrdinal("ReferenceMMSId");

        while (resultDbSet.Read())
        {
            gleStatusHistory.Add(new GleRegistrationStatusInfo
            {
                GleInstanceId = resultDbSet.GetGuid(gleInstanceIdIndex),
                ReferenceMMSId = !resultDbSet.IsDBNull(referenceMmsIdIndex) ? resultDbSet.GetString(referenceMmsIdIndex) : string.Empty,
                RegistrationType = resultDbSet.GetString(gleRegistrationType),
                RegistrationStatus = resultDbSet.GetString(gleStatusIndex),
                RegistrationResponse = !resultDbSet.IsDBNull(gleResponseIndex) ? resultDbSet.GetString(gleResponseIndex) : string.Empty,
                SubmittedBy = !resultDbSet.IsDBNull(updatedByIndex) ? resultDbSet.GetString(updatedByIndex) : string.Empty,
                RequestDate = DateTime.SpecifyKind(resultDbSet.GetDateTime(gleStatusStartDateIndex), DateTimeKind.Utc)
            });
        }

        return gleStatusHistory;
    }
}