﻿using Common.Entities;
using Common.Models;
using DataAccess.Repositories;
using DataAccess.Test.TestData;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Moq;
using System;
using System.Threading.Tasks;
using AutoMapper;
using Geidea.Utils.Counterparty.Providers;
using ProductService;
using Xunit;

namespace DataAccess.Test;

public class PriceRepositoryTests
{
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly PriceRepository priceRepository;

    public PriceRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<DataContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        var context = new DataContext(options, new CounterpartyProvider());
        priceRepository = new PriceRepository(context, httpContext.Object);

        context.Add(new PriceEntity
        {
            ProductId = Guid.NewGuid(),
            ChargeFrequency = "test val",
            ChargeType = "test val",
            MaxPrice = int.MaxValue,
            Threshold = int.MaxValue,
            PerItemPrice = int.MaxValue,
            ExemptFromVAT = false,
            CreatedBy = "test val",
            CreatedDate = DateTime.UtcNow,
            Currency = "test val",
            Group = "test val",
            PercentagePrice = int.MaxValue,
            Priority = 0,
            RentalPeriod = int.MaxValue,
            ThresholdType = "Threshold type"
        });
        context.SaveChanges();
    }

    [Fact]
    public async Task HardDelete()
    {
        var price = new PriceEntity
        {
            ProductId = Guid.NewGuid()
        };
        priceRepository.Save(price);
        await priceRepository.SaveChangesAsync();

        await priceRepository.HardDeleteAsync(price);

        var retrievedPrice = await priceRepository.FirstOrDefaultAsync(c => c.Id == price.Id);
        retrievedPrice.Should().BeNull();
    }

    [ClassData(typeof(FindPriceData))]
    [Theory]
    public async Task Find(PriceEntity priceEntity, FindPriceRequest findPriceRequest)
    {
        priceRepository.Save(priceEntity);
        await priceRepository.SaveChangesAsync();

        var prices = await priceRepository.FindAsync(findPriceRequest, false);
        prices.Should().HaveCount(1);
        prices[0].Should().BeEquivalentTo(priceEntity);

        await priceRepository.HardDeleteAsync(priceEntity);
    }
}
