﻿using Common.Models.PendingUnitPrice;
using Common.Models.UnitPrice;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Services;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;

namespace ProductService.Controllers;
[Route("api/v1/[controller]")]
[ApiController]
public class PendingUnitPriceController : ControllerBase
{
    private readonly IpendingUnitPriceService pendingunitPriceService;
    public PendingUnitPriceController(IpendingUnitPriceService pendingunitPriceService)
    {
        this.pendingunitPriceService = pendingunitPriceService;
    }

    /// <summary>
    /// Creates pending unit prices based on the provided request data.
    /// </summary>
    /// <param name="request">The request object containing details for the unit price creation.</param>
    /// <returns>
    /// Returns an HTTP 200 OK response with the created unit prices,
    /// a 400 Bad Request response if the input is invalid,
    /// or a 500 Internal Server Error response if something goes wrong.
    /// </returns>
    [HttpPost("HandleCreateAction")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> HandleCreateAction([FromBody] UnitPriceCreateRequest request)
    {
        var result = await pendingunitPriceService.CreateAsync(request);
        return Ok(result);
    }
    /// <summary>
    /// Retrieves a list of pending unit prices based on the specified criteria in the request.
    /// </summary>
    /// <param name="request">The request object containing filters and parameters for fetching the pending unit prices.</param>
    /// <returns>
    /// Returns an HTTP 200 OK response with the list of pending unit prices,
    /// a 400 Bad Request response if the input is invalid,
    /// or a 500 Internal Server Error response if something goes wrong.
    /// </returns>
    [HttpPost("GetPendingUnitPricesList")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetUnitPricesList(PendingUnitPricesListRequest request)
    {
        var result = await pendingunitPriceService.GetPendingUnitPricesList(request);
        return Ok(result);
    }
    /// <summary>
    /// Create or Update unit prices based on products, MCCs, and business types.
    /// </summary>
    /// <returns>Returns the created or updated unit prices.</returns>
    [HttpPost("HandleBulkEditAction")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> BulkEdit([FromBody] UnitPriceCreateRequest request)
    {
        var result = await pendingunitPriceService.BulkEdit(request);
        return Ok(result);
    }
    /// <summary>
    /// Delete unit prices by a list of IDs.
    /// </summary>
    /// <param name="request">request that contains delete action details.</param>
    /// <returns>Returns a status indicating success or failure.</returns>
    [HttpDelete("HandleBulkDeleteAction")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> HandleBulkDeleteAction(UnitPriceBulkDeleteRequest request)
    {
        var Result = await pendingunitPriceService.DeleteUnitPricesAsync(request);

        return Ok(Result);
    }
    [HttpPut("HandleUpdateAction/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> HandleUpdateAction(Guid id, [FromBody] UnitPriceUpdateRequest request)
    {
        await pendingunitPriceService.UpdateAsync(id, request);
        return Ok();
    }
    /// <summary>
    /// Review (Approve/Reject) pending unit price requests.
    /// </summary>
    /// <param name="request">Request containing the list of unit price IDs to be Reviewed (Approve/Reject).</param>
    /// <returns>Returns the review result containing reviewed and failed unit prices.</returns>
    [HttpPost("Review")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> Review([FromBody] PendingUnitPriceReviewRequest request)
    {
        var result = await pendingunitPriceService.ReviewAsync(request);
        return Ok(result);
    }
}
