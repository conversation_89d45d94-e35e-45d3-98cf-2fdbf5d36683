﻿using Common.Options;
using Geidea.Messages.Apex;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using System;
using System.Configuration;
using System.IO;
using System.Reflection;

namespace ProductService.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddApplicationOptions(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddOptions<ExceptionOptions>().Configure(o =>
        {
            configuration.GetSection("ServiceExceptions").Bind(o);
        });

        services.Configure<SoftposEgyptFeatureToggle>(configuration.GetSection("SoftposEgyptFeatureToggle"));

        return services;
    }
    public static IServiceCollection AddSwagger(this IServiceCollection services, IConfiguration configuration)
    {
        ApplicationOptions opt = new ApplicationOptions();

        configuration.GetSection("Application").Bind(opt);
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo { Title = opt.Name, Version = opt.Version });
            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            c.IncludeXmlComments(xmlPath);
        });

        return services;
    }
}
