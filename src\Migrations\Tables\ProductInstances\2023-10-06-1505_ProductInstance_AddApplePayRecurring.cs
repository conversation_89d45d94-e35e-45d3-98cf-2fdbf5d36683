﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;
[Migration(2023_10_06_1505)]
public class ProductInstance_AddApplePayRecurring : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
             USE [PRODUCTS]
             GO
             
             Update PRI set Metadata = JSON_MODIFY(PRI.Metadata,'$.IsApplePayWebRecurringEnabled', CAST(0 as BIT))
             from [PRODUCTS].[dbo].[ProductInstances] PRI
             join [PRODUCTS].[dbo].Products P
             on PRI.ProductId = P.Id
             WHERE [Metadata] IS NOT NULL
             AND p.Type = 'GWAY'
             AND ISJSON([Metadata]) > 0
        ");

        Execute.Sql(@"
             USE [PRODUCTS]
             GO
             
             Update PRI set Metadata = JSON_MODIFY(PRI.Metadata,'$.IsApplePayMobileRecurringEnabled', CAST(0 as BIT))
             from [PRODUCTS].[dbo].[ProductInstances] PRI
             join [PRODUCTS].[dbo].Products P
             on PRI.ProductId = P.Id
             WHERE [Metadata] IS NOT NULL
             AND p.Type = 'GWAY'
             AND ISJSON([Metadata]) > 0
        ");
    }
}
