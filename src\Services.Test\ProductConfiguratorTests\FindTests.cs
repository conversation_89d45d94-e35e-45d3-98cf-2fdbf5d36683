﻿using System;
using System.Threading.Tasks;
using AutoMapper;
using Common.Entities;
using Common.Models;
using Common.Models.ProductInstance;
using Common.Repositories;
using Common.Services;
using DataAccess;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using ProductService;
using Services.Settings;
using Xunit;


namespace Services.Test.ProductConfiguratorTests;

public class FindTests
{
    private readonly Mock<ILogger<ProductConfiguratorService>> logger = new Mock<ILogger<ProductConfiguratorService>>();
    private readonly Mock<ILogger<ProductInstanceRepository>> repoLogger = new Mock<ILogger<ProductInstanceRepository>>();
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly Mock<IProductService> productService = new Mock<IProductService>();
    private readonly Mock<ICounterpartyProvider> counterpartyProvider = new Mock<ICounterpartyProvider>();
    private readonly Mock<IProductChangeSenderService> productSender = new Mock<IProductChangeSenderService>();
    private readonly Mock<IOptionsMonitor<MeezaSettings>> meezaSettings = new Mock<IOptionsMonitor<MeezaSettings>>();
    private readonly Mock<IOptionsMonitor<CurrencySettings>> currencySettings = new Mock<IOptionsMonitor<CurrencySettings>>();
    private readonly Mock<IProductRepository> productRepository = new Mock<IProductRepository>();
    private readonly Mock<IOptionsMonitor<MpgsAccountsSettings>> mpgsAccountsSettings = new Mock<IOptionsMonitor<MpgsAccountsSettings>>();

    private DataContext context;
    private ProductConfiguratorService productConfiguratorService;

    internal void Setup()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
           .UseInMemoryDatabase(databaseName: "ProductInstanceFindTests" + Guid.NewGuid().ToString())
           .Options;

        context = new DataContext(options, new CounterpartyProvider());
        var productInstanceRepo = new ProductInstanceRepository(context, httpContext.Object, repoLogger.Object, mapper);
        productConfiguratorService = new ProductConfiguratorService(
            meezaSettings.Object,
            logger.Object,
            mapper,
            productService.Object,
            productInstanceRepo,
            productRepository.Object,
            counterpartyProvider.Object,
            productSender.Object,
            currencySettings.Object, mpgsAccountsSettings.Object);
    }

    [Fact]
    public async Task InvalidRequest_Type()
    {
        Setup();

        await productConfiguratorService
            .Invoking(x => x.FindAsync(new FindProductInstanceRequest { Types = new[] { "test" } }))
            .Should().ThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task InvalidRequest_Take()
    {
        Setup();

        await productConfiguratorService
            .Invoking(x => x.FindAsync(new FindProductInstanceRequest { Take = -10 }))
            .Should().ThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task Find()
    {
        Setup();
        context.ProductInstances.Add(new ProductInstanceEntity
        {
            Product = new ProductEntity
            {
                Type = "SCHEME",
                Code = "MC"
            }
        });
        context.ProductInstances.Add(new ProductInstanceEntity
        {
            Product = new ProductEntity
            {
                Type = "SCHEME",
                Code = "VISA"
            }
        });
        context.SaveChanges();

        var instances = await productConfiguratorService.FindAsync(new FindProductInstanceRequest { Types = new[] { "SCHEME" } });
        instances.Should().NotBeNullOrEmpty();
        instances.Should().HaveCount(2);
    }

    [Fact]
    public async Task FindByIds()
    {
        Setup();
        var instance1 = new ProductInstanceEntity
        {
            Product = new ProductEntity
            {
                Type = "SCHEME",
                Code = "MC"
            }
        };
        var instance2 = new ProductInstanceEntity
        {
            Product = new ProductEntity
            {
                Type = "SCHEME",
                Code = "VISA"
            }
        };
        context.ProductInstances.Add(instance1);
        context.ProductInstances.Add(instance2);
        context.SaveChanges();

        var instances = await productConfiguratorService.FindByIdsAsync(new IdsRequest { Ids = new[] { instance1.Id, instance2.Id } });
        instances.Should().NotBeNullOrEmpty();
        instances.Should().HaveCount(2);
    }
}
