﻿using AutoMapper;
using Common.Entities;
using Common.Models;
using Common.Models.ProductCommissionPrice;
using Common.Models.UnitPrice;
using Common.Repositories;
using Common.Views;
using DataAccess.Repositories;
using Geidea.Utils.Counterparty.Providers;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace DataAccess.Test;
public class ProductCommissionPriceRepositoryTests
{
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly DataContext context;
    private readonly ProductCommissionPriceRepository commissionPriceRepository;

    public ProductCommissionPriceRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<DataContext>()
                          .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                          .Options;
        context = new DataContext(options, new CounterpartyProvider());
        commissionPriceRepository = new ProductCommissionPriceRepository(context, httpContext.Object);
    }
    [Fact]
    public void AddRange_Should_Add_Multiple_Entities()
    {
        // Arrange
        var CommissionPrices = new List<ProductCommissionPriceEntity>
        {
            new ProductCommissionPriceEntity { Id = Guid.NewGuid(), ProductID = Guid.NewGuid(), MCCID = Guid.NewGuid(), BusinessTypeID = Guid.NewGuid(),
                CommissionFeeID = Guid.NewGuid(), FeeType = Common.Enums.ProductCommisssionPrices.FeeType.Flat, FeeValue = 100, BillingType = Common.Enums.ProductCommisssionPrices.CommisssionPricesBillingType.PrePaid,
                BillingFrequency = Common.Enums.ProductCommisssionPrices.PriceBillingFrequency.Annual},

             new ProductCommissionPriceEntity { Id = Guid.NewGuid(), ProductID = Guid.NewGuid(), MCCID = Guid.NewGuid(), BusinessTypeID = Guid.NewGuid(),
                CommissionFeeID = Guid.NewGuid(), FeeType = Common.Enums.ProductCommisssionPrices.FeeType.Percentage, FeeValue = 10, BillingType = Common.Enums.ProductCommisssionPrices.CommisssionPricesBillingType.PostPaid,
                BillingFrequency = Common.Enums.ProductCommisssionPrices.PriceBillingFrequency.Monthly},
        };
        //Act 
        commissionPriceRepository.AddRange(CommissionPrices);
        commissionPriceRepository.SaveChanges();
        //Assert
        var result = context.Set<ProductCommissionPriceEntity>().ToList();
        Assert.Equal(2, result.Count);
        Assert.Contains(result, pc => pc.FeeValue == 10);
        Assert.Contains(result, pc => pc.FeeValue == 100);
    }
    [Fact]
    public async Task SaveCommissionPricesAsync_ShouldSaveData_InDatabaseSuccessfully()
    {
        // Arrange
        var CommissionPrices = new List<ProductCommissionPriceEntity>
        {
            new ProductCommissionPriceEntity { Id = Guid.NewGuid(), ProductID = Guid.NewGuid(), MCCID = Guid.NewGuid(), BusinessTypeID = Guid.NewGuid(),
                CommissionFeeID = Guid.NewGuid(), FeeType = Common.Enums.ProductCommisssionPrices.FeeType.Flat, FeeValue = 100, BillingType = Common.Enums.ProductCommisssionPrices.CommisssionPricesBillingType.PrePaid,
                BillingFrequency = Common.Enums.ProductCommisssionPrices.PriceBillingFrequency.Annual},

             new ProductCommissionPriceEntity { Id = Guid.NewGuid(), ProductID = Guid.NewGuid(), MCCID = Guid.NewGuid(), BusinessTypeID = Guid.NewGuid(),
                CommissionFeeID = Guid.NewGuid(), FeeType = Common.Enums.ProductCommisssionPrices.FeeType.Percentage, FeeValue = 10, BillingType = Common.Enums.ProductCommisssionPrices.CommisssionPricesBillingType.PostPaid,
                BillingFrequency = Common.Enums.ProductCommisssionPrices.PriceBillingFrequency.Monthly},
        };
        //Act 
        await commissionPriceRepository.SaveCommissionPricesAsync(CommissionPrices);
        //Assert
        var result = context.Set<ProductCommissionPriceEntity>().ToList();
        Assert.Equal(2, result.Count);
        Assert.Contains(result, pc => pc.FeeValue == 10);
        Assert.Contains(result, pc => pc.FeeValue == 100);
    }
    [Fact]
    public async Task GetExistCommissionPrices_ShouldReturnMatchedRecords()
    {
        // Arrange
        var CommissionPrice = new ProductCommissionPriceEntity
        {
            Id = Guid.NewGuid(),
            ProductID = Guid.NewGuid(),
            MCCID = Guid.NewGuid(),
            BusinessTypeID = Guid.NewGuid(),
            CommissionFeeID = Guid.NewGuid(),
            FeeType = Common.Enums.ProductCommisssionPrices.FeeType.Flat,
            FeeValue = 100,
            BillingType = Common.Enums.ProductCommisssionPrices.CommisssionPricesBillingType.PrePaid,
            BillingFrequency = Common.Enums.ProductCommisssionPrices.PriceBillingFrequency.Annual,
        };
        commissionPriceRepository.Add(CommissionPrice);
        commissionPriceRepository.SaveChanges();
        //Act
        var result = await commissionPriceRepository.GetExistCommissionPrices(pc =>
           pc.ProductID == CommissionPrice.ProductID &&
           pc.MCCID == CommissionPrice.MCCID &&
           pc.BusinessTypeID == CommissionPrice.BusinessTypeID && pc.CommissionFeeID == CommissionPrice.CommissionFeeID);
        //Assert
        Assert.NotNull(result);
        Assert.Single(result);
    }
    private void GetproductCommissionPriceSample()
    {
        context.ProductCommissionPriceListView.AddRange(
            new ProductCommissionPriceListView
            {
                Id = Guid.NewGuid(),
                ProductName = "Product1",
                FeeValue = 50,
                BusinessType = "BusinessType1",
                BusinessTypeCode = "Code1",
                MccCategory = "MccCategory1",
                ProductCode = "ProductCode1",
                MccCode = "MccCode1",
                MccName = "MccName1",
                CommissionFeeCode = "CommissionCode1",
                FeeName = "CommissionFeeName1"
            },
            new ProductCommissionPriceListView
            {
                Id = Guid.NewGuid(),
                ProductName = "Product2",
                FeeValue = 20,
                BusinessType = "BusinessType2",
                BusinessTypeCode = "Code1",
                MccCategory = "MccCategory2",
                ProductCode = "ProductCode2",
                MccCode = "MccCode2",
                MccName = "MccName2",
                CommissionFeeCode = "CommissionCode2",
                FeeName = "CommissionFeeName2"
            }
        );
        context.SaveChanges();
    }

    [Fact]
    public async Task GetProductCommissionPriceList_ShouldReturnData_WhenRequestIsValid()
    {
        //Arrange
        ProductCommissionPriceListRequest request = new ProductCommissionPriceListRequest
        {
            Page = 1,
            Size = 10
        };
        GetproductCommissionPriceSample();
        //Act
        var result = await commissionPriceRepository.GetProductCommissionPriceList(request);
        //Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.TotalCount);
        Assert.Equal(1, result.TotalPages);
        Assert.Equal(2, result.CommissionPriceList.Length);
    }
}
