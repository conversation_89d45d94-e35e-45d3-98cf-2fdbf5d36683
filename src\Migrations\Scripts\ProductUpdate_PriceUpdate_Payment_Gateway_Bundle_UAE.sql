﻿USE PRODUCTS;
GO
DECLARE @ProductId UNIQUEIDENTIFIER
DECLARE @PayFamily UNIQUEIDENTIFIER

BEGIN TRAN

SELECT TOP 1 @PayFamily = ID FROM Category where Code = 'PAY_FAMILY' and Counterparty = 'GEIDEA_UAE';

EXEC NewProductVersion_v2 @ProductCode = 'PAYMENT_GATEWAY_BUNDLE', @ProductVersion = 2, @NewProductCode = 'PAYMENT_GATEWAY_BUNDLE', @NewProductVersion = 3, @MarkObsolete = 1, @Counterparty = 'GEIDEA_UAE'

--SELECT new productId
SELECT TOP 1 @ProductId = ID FROM Products where Code = 'PAYMENT_GATEWAY_BUNDLE' and [Version] = 3 and CounterParty = 'GEIDEA_UAE'

--Delete copied ProductCategories mappings for new product
DELETE FROM ProductCategories where ProductId = @ProductId 

--Insert ProductCategories mapping for new product
INSERT INTO ProductCategories(ProductId, CategoryId) VALUES (@ProductId, @PayFamily)

--Update MEEZA_GW scheme to Obosolete
UPDATE Products SET [Availability] = 'Obsolete' WHERE Code IN ('MEEZA_GW') and CounterParty = 'GEIDEA_UAE'

--Delete MEEZA_GW mapping to new product
DELETE from ProductParts where ProductId = @ProductId and PartId IN
    (Select ID FROM Products where Code IN ('MEEZA_GW','VISA_GW','MC_GW') and CounterParty = 'GEIDEA_UAE')

--UPDATE Price for new Payment Gateway Bundle
UPDATE Prices SET PerItemPrice=100 WHERE ProductId = @ProductId

--Create new VISA_GW and MC_GW without _CP prices
DECLARE @VISAProductId UNIQUEIDENTIFIER

EXEC NewProductVersion_v2 @ProductCode = 'VISA_GW', @ProductVersion = 0, @NewProductCode = 'VISA_GW', @NewProductVersion = 1, @MarkObsolete = 1, @Counterparty = 'GEIDEA_UAE'

--SELECT new Visa_GWProductId
SELECT TOP 1 @VISAProductId = ID FROM Products where Code = 'VISA_GW' and [Version] = 1 and CounterParty = 'GEIDEA_UAE'

DECLARE @MCProductId UNIQUEIDENTIFIER

EXEC NewProductVersion_v2 @ProductCode = 'MC_GW', @ProductVersion = 0, @NewProductCode = 'MC_GW', @NewProductVersion = 1, @MarkObsolete = 1, @Counterparty = 'GEIDEA_UAE'

--SELECT new MC_GWProductId
SELECT TOP 1 @MCProductId = ID FROM Products where Code = 'MC_GW' and [Version] = 1 and CounterParty = 'GEIDEA_UAE'

--REMOVE _CP Prices from new products
UPDATE Prices SET DeletedFlag = 1 WHERE ProductId IN (@VISAProductId,@MCProductId) AND ChargeType IN ('REFUND_CP', 'PURCHASE_CP')

--Update prices _CNP
UPDATE [PRODUCTS].[dbo].[Prices] SET PerItemPrice = 0, PercentagePrice = 0 
WHERE ChargeType IN ('REFUND_CNP', 'PURCHASE_CNP') AND DeletedFlag = 0 AND [ProductId] in ( @MCProductId, @VISAProductId)

--ADD new Product Parts
INSERT INTO ProductParts (ProductId, PartId, Quantity) VALUES (@ProductId, @VISAProductId, 1), (@ProductId, @MCProductId, 1)

COMMIT TRAN
