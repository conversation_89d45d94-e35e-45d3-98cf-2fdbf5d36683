using Common;
using Common.Entities;
using Common.Models;
using Common.Options;
using Common.Repositories;
using Geidea.Messages.Merchant;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Net;
using System.Reflection.Metadata.Ecma335;
using System.Threading.Tasks;
using static Common.Constants;
using Common.Data.Extensions;
using Common.Enums;
using EFCore.BulkExtensions;

using DocumentFormat.OpenXml.Office2010.Excel;
using Irony.Parsing;

namespace DataAccess.Repositories;

[ExcludeFromCodeCoverage]
public class ProductRepository : AuditableRepository<Guid, ProductEntity>, IProductRepository
{
    private readonly IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;
    private readonly ICounterpartyProvider counterpartyProvider;

    public ProductRepository(DbContext context, IHttpContextAccessor contextAccessor, IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle, ICounterpartyProvider counterpartyProvider) : base(context, contextAccessor)
    {
        this.softposEgyptFeatureToggle = softposEgyptFeatureToggle;
        this.counterpartyProvider = counterpartyProvider;
    }
    public async Task<bool> IsProductUsedAsync(Guid productId)
    {
        return await context.Set<ProductEntity>()
                             .AnyAsync(p => p.Id == productId);
    }
    public IQueryable<ProductEntity> Query()
    {
        return context.Set<ProductEntity>().AsQueryable();
    }
    public async Task<ProductEntity?> GetByIdAsync(Guid productId)
    {
        return await context.Set<ProductEntity>().FindAsync(productId);
    }
    public async Task HardDeleteProductAsync(ProductEntity product)
    {
        context.Set<PriceEntity>().RemoveRange(context.Set<PriceEntity>().Where(a => a.ProductId == product.Id));
        context.Set<ProductPartEntity>().RemoveRange(context.Set<ProductPartEntity>().Where(a => a.ProductId == product.Id || a.PartId == product.Id));
        context.Set<ProductEntity>().Remove(product);

        await context.SaveChangesAsync();
    }

    public async Task<ProductEntity[]> FindAsync(FindProductRequest request, bool attach)
    {
        var query = context.Set<ProductEntity>().AsNoTracking().AsQueryable();

        query = ApplySingleValueFilter(request, attach, query);
        query = ApplyListFilter(request, query);

		// Apply split‐query to prevent huge single‐SQL joins
		query = query.AsSplitQuery();

        return await query
            .Include(a => a.Prices)
            .Include(a => a.Parts)
                .ThenInclude(a => a.Part)
                    .ThenInclude(a => a.Prices)
            .Include(a => a.ProductCategories)
                .ThenInclude(a => a.Category)
            .ToArrayAsync();

    }

    private IQueryable<T> ApplySingleValueFilter<T>(FindProductRequest request, bool attach, IQueryable<T> query) where T : ProductEntity
    {
        if (!attach)
            query = query.AsNoTracking();

        if (request.OnlyValid)
            query = query.Where(a =>
                (a.ValidFrom == null || DateTime.UtcNow >= a.ValidFrom) &&
                (a.ValidTo == null || a.ValidTo > DateTime.UtcNow) &&
                a.Availability != Constants.Availability.Obsolete);

        if (request.Id.HasValue)
            query = query.Where(a => a.Id == request.Id);

        if (!string.IsNullOrWhiteSpace(request.Availability))
            query = query.Where(a => a.Availability == request.Availability);

        if (!string.IsNullOrWhiteSpace(request.Flow))
            query = query.Where(a => a.Flow == request.Flow);

        if (!string.IsNullOrWhiteSpace(request.SalesChannel))
            query = query.Where(a => a.SalesChannel == request.SalesChannel);

        if (!string.IsNullOrWhiteSpace(request.Code))
            query = query.Where(a => a.Code == request.Code);

        if (!string.IsNullOrWhiteSpace(request.Description))
            query = query.Where(a => a.Description != null && (a.Description == request.Description || a.Description.Contains(request.Description)));

        if (!string.IsNullOrWhiteSpace(request.Type))
            query = query.Where(a => a.Type == request.Type);

        if (request.CategoryId.HasValue)
        {
            var productIds = context.Set<ProductCategoriesEntity>().Where(x => request.CategoryId == x.CategoryId).Select(x => x.ProductId);
            query = query.Where(x => productIds.Contains(x.Id));
        }

        query = ApplySoftposFeatureToggleFilter(query);

        return query;
    }


    private IQueryable<T> ApplySoftposFeatureToggleFilter<T>(IQueryable<T> query) where T : ProductEntity
    {
        if (!softposEgyptFeatureToggle.Value.EnableSoftposEgypt && counterpartyProvider.GetCode() == CounterParty.Egypt)
        {
            query = query.Where(x => x.Code != ProductCodes.GoAir && x.Code != ProductCodes.Softpos);
        }

        return query;
    }

    private static IQueryable<T> ApplyListFilter<T>(FindProductRequest request, IQueryable<T> query) where T : ProductEntity
    {
        if (request.ProductCodes?.Any() == true)
        {
            query = query.Where(a => request.ProductCodes.Any(referral => referral == a.Code));
        }
        if (request.ProductIds?.Any() == true)
        {
            query = query.Where(a => request.ProductIds.Any(referral => referral == a.Id));
        }

        if (request.ReferralChannels?.Any() == true)
        {
            query = query.Where(a => request.ReferralChannels.Any(referral => referral == a.ReferralChannel));
        }

        return query;
    }


    public async Task<ProductEntity[]> FindByIdsAsync(IdsRequest request)
    {
        var query = context.Set<ProductEntity>().Where(p => request.Ids.Contains(p.Id)).AsQueryable();
        var result = await query.Include(a => a.Prices)
                          .Include(a => a.Parts)
                              .ThenInclude(a => a.Part)
                                  .ThenInclude(a => a.Prices)
                          .Include(a => a.ProductCategories)
                              .ThenInclude(a => a.Category)
                          .AsNoTracking().ToArrayAsync();
        return result;
    }

    public bool ExistsAsync(params Guid[] ids)
    {
        return ids.All(id => context.Set<ProductEntity>().AsNoTracking().Any(f => f.Id == id));
    }

    public async Task<bool> IsPartOfAsync(Guid partId, Guid productId)
    {
        return await context.Set<ProductPartEntity>().AsNoTracking().AnyAsync(a => a.PartId == partId && a.ProductId == productId);
    }

    public async Task BindPartAsync(Guid partId, Guid productId, int quantity)
    {
        await context.Set<ProductPartEntity>().AddAsync(new ProductPartEntity
        {
            PartId = partId,
            ProductId = productId,
            Quantity = quantity
        });

        await context.SaveChangesAsync();
    }

    public async Task UnbindPartAsync(Guid partId, Guid productId)
    {
        context.Set<ProductPartEntity>().RemoveRange(context.Set<ProductPartEntity>().Where(a => a.PartId == partId && a.ProductId == productId));
        await context.SaveChangesAsync();
    }

    public async Task<ProductEntity[]> GetParentProducts(Guid partId)
    {
        return await context.Set<ProductPartEntity>()
            .Where(p => p.PartId == partId)
            .Include(p => p.Product)
            .Select(p => p.Product)
            .AsNoTracking()
            .ToArrayAsync();
    }

    public async Task AddCategoryAsync(Guid productId, Guid categoryId)
    {
        await context.Set<ProductCategoriesEntity>().AddAsync(new ProductCategoriesEntity
        {
            CategoryId = categoryId,
            ProductId = productId
        });

        await context.SaveChangesAsync();
    }

    public async Task<bool> HasCategoryAsync(Guid productId, Guid categoryId)
    {
        return await context.Set<ProductCategoriesEntity>().AsNoTracking().AnyAsync(a => a.CategoryId == categoryId && a.ProductId == productId);
    }

    public async Task DeleteCategoryAsync(Guid productId, Guid categoryId)
    {
        context.Set<ProductCategoriesEntity>().RemoveRange(
            context.Set<ProductCategoriesEntity>().Where(a => a.CategoryId == categoryId && a.ProductId == productId));
        await context.SaveChangesAsync();
    }

    public Guid[] GetRelatedProductsIds(ProductCodesRequest productCodesRequest)
    {
        var productIds = new List<Guid>();

        productIds.AddRange(context.Set<ProductEntity>().Where(x =>
                productCodesRequest.ProductCodes.Contains(x.Code))
            .Select(x => x.Id));

        productIds.AddRange(context.Set<ProductPartEntity>()
            .Where(x => productIds.Contains(x.PartId))
            .Select(x => x.ProductId));

        return productIds.ToArray();
    }

    public async Task<List<ProductShortResponse>> GetProductIdsFromCodes(ProductCodesRequest productCodesRequest)
    {
        return await context.Set<ProductEntity>()
            .Where(p => productCodesRequest.ProductCodes.ToList().Contains(p.Code) && p.Availability == Availability.Live)
            .Select(p => new ProductShortResponse { ProductId = p.Id, Code = p.Code }).AsNoTracking().ToListAsync();
    }

    public async Task<bool> ProductsContainBillPaymentServiceOrBundleAsync(IdsRequest productIds)
    {
        return await context.Set<ProductEntity>()
            .AsQueryable()
            .AsNoTracking()
            .Where(p => productIds.Ids.Contains(p.Id))
            .AnyAsync(x => x.Code == ProductCodes.BillPayment
                      || x.Code == ProductCodes.GoSmartWithBP);
    }

    public async Task<BillPaymentServiceAndBundleFlags> ProductsContainBillPaymentTypeAsync(IdsRequest productIds)
    {
        var listOfCodes = await context.Set<ProductEntity>()
            .AsQueryable()
            .AsNoTracking()
            .Where(p => productIds.Ids.Contains(p.Id))
            .GroupBy(x => x.Code)
            .Select(x => x.First().Code)
            .ToListAsync();

        var bpServiceFlag = listOfCodes.Any(x => x == ProductCodes.BillPayment);
        var bpBundleFlag = listOfCodes.Any(x => x == ProductCodes.GoSmartWithBP);

        return new BillPaymentServiceAndBundleFlags
        {
            HasBillPaymentService = bpServiceFlag,
            HasBillPaymentBundle = bpBundleFlag
        };
    }

    public async Task<ProductEntity[]> FindWithInfoAsync(FindProductRequest request, bool attach)
    {
        request.Page = Math.Max(request.Page, 1);
        request.Size = Math.Max(request.Size, 1);

        var query = context.Set<ProductEntity>().AsQueryable();

        query = ApplySingleValueFilter(request, attach, query);
        query = ApplyListFilter(request, query);
        query = query.Skip((request.Page - 1) * request.Size)
                .Take(request.Size);

        return await query
                .Include(a => a.Prices)
                .Include(a => a.Parts)
                    .ThenInclude(a => a.Part)
                        .ThenInclude(a => a.Prices)
                .Include(a => a.ProductCategories)
                    .ThenInclude(a => a.Category)
                .ToArrayAsync();
    }
    public async Task<ProductListResponse> GetProductsList(GetProductsListRequest request)
    {
        var Products = GetAllProducts();
        Products = SearchProducts(request, Products);
        Products = FilterProducts(request, Products);
        SortProducts(request, ref Products);

        int totalCount = await Products.CountAsync();
        int totalPages = (int)Math.Ceiling((double)totalCount / request.Size);
        Products = Products.Skip((request.Page - 1) * request.Size)
                           .Take(request.Size);

        var productsArray = await Products.ToArrayAsync();
        return new ProductListResponse
        {
            Products = productsArray,
            TotalCount = totalCount,
            TotalPages = totalPages
        };
    }
    public IQueryable<GetProductsListResponse> GetAllProducts()
    {
        var Products = from p in context.Set<ProductEntity>().AsNoTracking()

                       join pc in context.Set<ProductCategoriesEntity>().AsNoTracking()
                       on p.Id equals pc.ProductId
                       into productCategories
                       from pc in productCategories.DefaultIfEmpty()

                       join c in context.Set<CategoryEntity>().AsNoTracking()
                       on pc.CategoryId equals c.Id
                       into categories
                       from c in categories.DefaultIfEmpty()

                       group c by new { p.Id, p.CreatedDate, p.Code, p.Name, p.Subname, p.SalesChannel }
                       into g

                       select new GetProductsListResponse()
                       {
                           Id = g.Key.Id,
                           CreatedDate = g.Key.CreatedDate,
                           Code = g.Key.Code,
                           Name = g.Key.Name,
                           Subname = g.Key.Subname,
                           SalesChannel = g.Key.SalesChannel,
                           CategoryName = g.Select(c => c.Name).FirstOrDefault()
                       };

        return Products;
    }
    public static IQueryable<GetProductsListResponse> SearchProducts(GetProductsListRequest request, IQueryable<GetProductsListResponse> Products)
    {
        var searchTerms = request.SearchTerms.Where(kv => !string.IsNullOrEmpty(kv.Value)).ToList();

        if (searchTerms.Count == 0)
            return Products;

        var query = Products;

        foreach (var term in searchTerms)
        {
            string searchTerm = term.Value;
            ProductSearchKey searchKey = term.Key;

            switch (searchKey)
            {
                case ProductSearchKey.SearchByAll:
                    query = query.Where(s =>
                        (s.Name != null && s.Name.Contains(searchTerm)) ||
                        (s.Subname != null && s.Subname.Contains(searchTerm)) ||
                        (s.Code != null && s.Code.Contains(searchTerm)));
                    break;

                case ProductSearchKey.Name:
                    query = query.Where(s => s.Name != null && s.Name.Contains(searchTerm));
                    break;

                case ProductSearchKey.Subname:
                    query = query.Where(s => s.Subname != null && s.Subname.Contains(searchTerm));
                    break;

                case ProductSearchKey.Code:
                    query = query.Where(s => s.Code != null && s.Code.Contains(searchTerm));
                    break;
            }
        }

        return query;
    }

    public static IQueryable<GetProductsListResponse> FilterProducts(GetProductsListRequest request, IQueryable<GetProductsListResponse> Products)
    {
        Products = FilterProductsByCategory(request, Products);
        Products = FilterProductsBySalesChannel(request, Products);

        return Products;
    }
    public static IQueryable<GetProductsListResponse> FilterProductsByCategory(GetProductsListRequest request, IQueryable<GetProductsListResponse> Products)
    {
        var filterByCategory = request.FilterByCategory;
        if (filterByCategory == null || !filterByCategory.Any())
        {
            return Products;
        }

        return Products.Where(p => filterByCategory.Contains(p.CategoryName ?? ""));
    }
    public static IQueryable<GetProductsListResponse> FilterProductsBySalesChannel(GetProductsListRequest request, IQueryable<GetProductsListResponse> products)
    {
        var filterBySalesChannel = request.FilterBySalesChannel;

        if (filterBySalesChannel == null || !filterBySalesChannel.Any())
        {
            return products;
        }

        if (filterBySalesChannel.Contains("None"))
        {
            return products.Where(p => p.SalesChannel == null || filterBySalesChannel.Contains(p.SalesChannel ?? ""));
        }

        return products.Where(p => filterBySalesChannel.Contains(p.SalesChannel ?? ""));
    }
    public static IQueryable<GetProductsListResponse> SortProducts(GetProductsListRequest request, ref IQueryable<GetProductsListResponse> products)
    {
        SortType orderType;

        if (request.OrderType.ToLower() == SortType.asc.ToString())
            orderType = SortType.asc;
        else if (request.OrderType.ToLower() == SortType.desc.ToString())
            orderType = SortType.desc;
        else
            throw new ArgumentException("Invalid value for OrderType. Allowed values are 'asc' and 'desc'.", nameof(request));

        if (!string.IsNullOrEmpty(request.OrderFieldName))
            products = products.OrderBy(request.OrderFieldName, orderType);

        return products;
    }

    [ExcludeFromCodeCoverage]
    public async Task<TerminalDetails> GetProductsDetails(string TID)
    {
        var terminalDataSetEntityQuery = context.Set<TerminalDataSetEntity>().AsQueryable();
        var terminalDetailsEntityQuery = context.Set<TerminalDetailsEntity>().AsQueryable();

        var result = await (from td in terminalDataSetEntityQuery
                            join tdd in terminalDetailsEntityQuery on td.TID equals tdd.TID
                            where td.TID == TID
                            select new
                            {
                                td.TID,
                                td.MID,
                                tdd.Make,
                                tdd.Model,
                                tdd.SerialNumber,
                                tdd.Family
                            }).FirstOrDefaultAsync();

        if (result == null)
            throw new ServiceException(HttpStatusCode.NotFound, Errors.MIDNotFound).WithArguments(TID);

        var terminalDetails = new TerminalDetails
        {
            MID = result?.MID,
            TID = result?.TID,
            Family = result?.Family,
            Make = result?.Make,
            Model = result?.Model,
        };

        return terminalDetails;
    }

    [ExcludeFromCodeCoverage]
    public async Task<List<ProductOnBusiness>> GetProductsOnBusinessByMID(string MID)
    {
        var terminalDataSetEntityQuery = context.Set<TerminalDataSetEntity>().AsQueryable();
        var terminalDetailsEntityQuery = context.Set<TerminalDetailsEntity>().AsQueryable();
        var productList = await (from td in terminalDataSetEntityQuery
                                 join tdd in terminalDetailsEntityQuery on td.TID equals tdd.TID
                                 where td.MID == MID
                                 select new ProductOnBusiness
                                 {
                                     Family = tdd.Family,
                                     Make = tdd.Make,
                                     Model = tdd.Model,
                                     SerialNumber = tdd.SerialNumber,
                                     TID = td.TID
                                 }).ToListAsync();

        if (productList == null || productList.Count == 0)
            throw new ServiceException(HttpStatusCode.NotFound, Errors.MIDNotFound).WithArguments(MID);

        return productList;
    }

    [ExcludeFromCodeCoverage]
    public async Task<ProductsOnAccount> GetProductsOnAccountAsync(string accountId)
    {
        var terminalDataSetEntityQuery = context.Set<TerminalDataSetEntity>().AsQueryable();
        var terminalDetailsEntityQuery = context.Set<TerminalDetailsEntity>().AsQueryable();

        var result = await (from td in terminalDataSetEntityQuery
                            from tdd in terminalDetailsEntityQuery
                            .Where(tdd => td.TID == tdd.TID && td.MID == accountId)
                            .DefaultIfEmpty()
                            select new
                            {
                                td.MID,
                                td.TID,
                                tdd.Family,
                                tdd.Make,
                                tdd.Model,
                                tdd.SerialNumber,
                            }).FirstOrDefaultAsync();
        if (result == null)
            throw new ServiceException(HttpStatusCode.NotFound, Errors.AccountIDNotFound).WithArguments(accountId);
        var productsDetails = new ProductsOnAccount
        {
            MID = result?.MID,
            TID = result?.TID,
            Family = result?.Family,
            Make = result?.Make,
            Model = result?.Model,
            SerialNumber = result?.SerialNumber,
        };
        return productsDetails;
    }

    [ExcludeFromCodeCoverage]
    public async Task<TerminalDataSetEntity> GetTerminalDataSet(string MID)
    {
        var terminalDataSetEntityQuery = await context.Set<TerminalDataSetEntity>().AsQueryable().Where(
            x => x.MID == MID).FirstOrDefaultAsync();

        if (terminalDataSetEntityQuery == null)
            throw new ServiceException(HttpStatusCode.NotFound, Errors.MIDNotFound).WithArguments(MID);

        return terminalDataSetEntityQuery;
    }

    [ExcludeFromCodeCoverage]
    public async Task<List<ProductsOnAccount>> GetMultipleProductsOnAccountAsync(string accountId)
    {
        var terminalDataSetEntityQuery = context.Set<TerminalDataSetEntity>().AsQueryable();
        var terminalDetailsEntityQuery = context.Set<TerminalDetailsEntity>().AsQueryable();

        var result = await (from td in terminalDataSetEntityQuery
                            join tdd in terminalDetailsEntityQuery on td.TID equals tdd.TID
                            into joined
                            from tdd in joined.DefaultIfEmpty()
                            where td.MID == accountId || td.OrderNumber == accountId
                            select new
                            {
                                td.MID,
                                td.TID,
                                td.FullTID,
                                tdd.Family,
                                tdd.Make,
                                tdd.Model,
                                tdd.SerialNumber,
                                td.OrderNumber,
                            }).ToListAsync();
        if (result == null)
            throw new ServiceException(HttpStatusCode.NotFound, Errors.AccountIDNotFound).WithArguments(accountId);

        var productsDetails = new List<ProductsOnAccount>();
        foreach (var p in result)
        {
            productsDetails.Add(new ProductsOnAccount()
            {
                MID = p?.MID,
                TID = p?.TID,
                FullTID = p?.FullTID,
                Family = p?.Family,
                Make = p?.Make,
                Model = p?.Model,
                SerialNumber = p?.SerialNumber,
                OrderNumber = p?.OrderNumber,
            });

        }
        return productsDetails;
    }

    public async Task<ProductDetailsResponse?> GetProductDetails(Guid Id)
    {
        var Product = await context.Set<ProductEntity>()
                             .AsQueryable().AsNoTracking().Where(p => p.Id == Id)
                             .Include(p => p.ProductCategories)
                                .ThenInclude(c => c.Category)
                             .Include(i => i.Images).AsNoTracking()
                             .Select(p => new ProductDetailsResponse()
                             {
                                 Code = p.Code,
                                 Name = p.Name,
                                 NameAr = p.NameAr,
                                 Subname = p.Subname,
                                 SubnameAr = p.SubnameAr,
                                 Description = p.Description,
                                 DescriptionAr = p.DescriptionAr,
                                 ProductDisplayOrder = p.DisplayOrder,
                                 SalesChannel = p.SalesChannel,
                                 ProductLink = p.ProductLink,
                                 IsCNP = p.IsCNP,
                                 CategoryId = p.ProductCategories.Select(c => c.CategoryId).FirstOrDefault(),
                                 CategoryName = p.ProductCategories.Select(pc => pc.Category.Name).FirstOrDefault(),
                                 ProductImages = p.Images.Select(pi => new ProductImagesResponse()
                                 {
                                     ImageId = pi.ImageId,
                                     DisplayOrder = pi.DisplayOrder,
                                     Language = pi.Language,
                                 }).ToList(),
                             }).SingleOrDefaultAsync();
        return Product;
    }

    public async Task<List<BasicProductsInfo>> GetProductsNamesAsync()
    {
        return await context.Set<ProductEntity>()
        .AsNoTracking()
        .Select(p => new BasicProductsInfo
        {
            Id = p.Id,
            Name = p.Name
        })
        .ToListAsync();
    }
    public async Task DeleteProductImages(Guid productId)
    {
        await context.Set<ProductImage>()
                     .Where(pi => pi.ProductId == productId)
                     .BatchDeleteAsync();
        await context.SaveChangesAsync();
    }
    public async Task<List<ProductDetailsResponse>> GetProductsListByChannel(bool isCNP)
    {

        var productList = await context.Set<ProductEntity>()
                             .AsQueryable().AsNoTracking().Where(p => p.IsCNP == isCNP && p.Availability == "Live")
                             .Include(i => i.Images.Where(im => im.DisplayOrder != null && im.DisplayOrder == 1 && im.Language != null && im.Language == "EN")).AsNoTracking()
                             .Select(p => new ProductDetailsResponse()
                             {
                                 Code = p.Code,
                                 Name = p.Name,
                                 NameAr = p.NameAr,
                                 Subname = p.Subname,
                                 SubnameAr = p.SubnameAr,
                                 Description = p.Description,
                                 DescriptionAr = p.DescriptionAr,
                                 SalesChannel = p.SalesChannel,
                                 ProductLink = p.ProductLink,
                                 IsCNP = p.IsCNP,
                                 ProductType = p.Type,
                                 ProductImages = p.Images.Select(pi => new ProductImagesResponse()
                                 {
                                     ImageId = pi.ImageId,
                                     DisplayOrder = pi.DisplayOrder,
                                     Language = pi.Language,
                                 }).ToList(),
                             }).ToListAsync();
        return productList;
    }

}
