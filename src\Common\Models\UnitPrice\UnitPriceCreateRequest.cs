﻿using Common.Enums.UnitPrice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.UnitPrice;
public class UnitPriceCreateRequest
{
    public List<Guid> ProductIDs { get; set; } = new List<Guid>();

    public List<Guid> MCCIDs { get; set; } = new List<Guid>();

    public List<Guid> BusinessTypeIDs { get; set; } = new List<Guid>();

    public decimal UnitPrice { get; set; }

    public decimal VATRate { get; set; }

    public VatType VATType { get; set; }

    public BillingType BillingType { get; set; }

    public BillingFrequency BillingFrequency { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}
