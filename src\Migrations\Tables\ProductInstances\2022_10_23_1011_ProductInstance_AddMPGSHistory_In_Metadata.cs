﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_10_23_1011)]
public class ProductInstance_AddMpgsHistory_In_Metadata : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"GO
create or alter function AddMPGSAccountCreationDateAndHistory
(
 @OldMetaData nvarchar(MAX) , @CreationDate Datetime2
)
RETURNS nvarchar(MAX)
    AS
    BEGIN
​
	Declare @UpdatedMetaData Nvarchar(MAX) ;
​
	WITH AddCreatedDataToEachAccountQuery AS (
	    SELECT JSON_MODIFY(t.[value], '$.CreatedDate', convert( nvarchar, @CreationDate) ) UpdatedMpgsAccount
	    FROM OPENJSON((SELECT JSON_QUERY(@OldMetaData, 'lax $.MpgsAccounts'))) t
	), UpdateMpgsAccountsQuery AS (
	    SELECT CONCAT('[',STRING_AGG(UpdatedMpgsAccount, ','),']') UpdatedMpgsAccounts
	    FROM AddCreatedDataToEachAccountQuery
	)
​
	SELECT @UpdatedMetaData = JSON_MODIFY(JSON_MODIFY(@OldMetaData,'$.MpgsHistory', JSON_QUERY(N'[]')), '$.MpgsAccounts', JSON_QUERY(UpdatedMpgsAccounts))
	FROM UpdateMpgsAccountsQuery 
	​
	Return @UpdatedMetaData​​
END

");
        Execute.Sql(@"UPDATE [PRODUCTS].[dbo].[ProductInstances] SET [Metadata] = [dbo].[AddMPGSAccountCreationDateAndHistory](Metadata , pi.CreatedDate)
	            FROM ProductInstances pi
                INNER JOIN Products p ON p.id = pi.ProductId
                WHERE [Metadata] IS NOT NULL
                AND p.Type = 'GWAY'
                AND ISJSON([Metadata]) > 0");

    }
}
