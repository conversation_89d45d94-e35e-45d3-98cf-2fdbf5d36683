﻿using FluentMigrator;

namespace Migrations.Tables.Products;

[Migration(2021_06_08_0920)]
public class EgyptBundlesUpdates_RemoveServicesFromPaymentGateway : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"DELETE FROM ProductParts WHERE
                            ProductId IN (SELECT Id FROM Products WHERE Code = 'PAYMENT_GATEWAY_BUNDLE' AND 
                                          Availability = 'Live' AND Counterparty = 'GEIDEA_EGYPT') AND
                            PartId IN (SELECT Id FROM Products WHERE Type = 'SERVICES'
                                       AND Availability = 'Live' AND Counterparty = 'GEIDEA_EGYPT')");
    }
}
