using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Common;
using Common.Data;
using Common.Data.ProductType;
using Common.Entities;
using Common.Models;
using Common.Models.ProductInstance;
using Common.Repositories;
using Common.Services;
using FluentAssertions;
using Geidea.ProductService.Models;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Models;
using Models.Rules.AuthenticationRules;
using Models.Rules.Common;
using Moq;
using Services.Settings;
using Xunit;
using static Common.Constants.MerchantCountries;
using static Common.Data.Helpers.DataDefaultValuesHelper;
using static Geidea.Utils.Common.Constants;
using GatewayData = Common.Data.GatewayData;
using MpgsMsoProviders = Geidea.Utils.Common.Constants.MpgsMsoProviders;


namespace Services.Test;

public class ProductConfiguratorServiceTests
{
    private readonly Mock<IProductInstanceRepository> productInstanceRepository;
    private readonly Mock<IProductRepository> productRepository;
    private readonly Mock<IProductInstancePublisher> productInstancePublisher;
    private readonly Mock<IProductService> productService;
    private readonly Mock<IMapper> mapper;
    private readonly Mock<ICounterpartyProvider> counterpartyProvider = new();
    private readonly Mock<IProductChangeSenderService> productSender;
    private readonly Mock<IOptionsMonitor<MeezaSettings>> meezaSettings = new();
    private readonly Mock<IOptionsMonitor<CurrencySettings>> currencySettings = new();
    private readonly Mock<IOptionsMonitor<MpgsAccountsSettings>> mpgsAccountsSettings = new();

    private readonly ProductConfiguratorService productConfiguratorService;

    private static readonly Guid ProductInstanceId = Guid.NewGuid();

    private static readonly Guid ProductInstanceCompanyId = Guid.NewGuid();

    private static readonly Guid GatewayCompanyId = Guid.NewGuid();
    private readonly List<Guid?> GatewayComapnyIdResponse = new List<Guid?>() { GatewayCompanyId };

    private readonly ProductInstanceResponse productInstanceResponse = new() { ProductInstanceId = ProductInstanceId };

    private readonly Guid merchantId = Guid.NewGuid();

    private static readonly ProductEntity ProductEntity = new()
    {
        Type = ProductTypes.GWAY.ToString()
    };

    private readonly ProductInstanceEntity productInstance = new()
    {
        Id = ProductInstanceId,
        Product = ProductEntity,
        Metadata = "{\"MerchantGatewayKey\":\"MerchantGatewayKey\"}",
        CompanyId = ProductInstanceCompanyId
    };

    private readonly UpdateProductInstanceRequest updateProductInstanceRequest = new()
    {
        Data = new GatewayData
        {
            MerchantGatewayKey = "MerchantGatewayKey"
        }
    };

    private readonly ProductInstanceEntity schemeProductInstance = new()
    {
        Id = Guid.NewGuid(),
        ParentId = Guid.NewGuid(),
        Product = new ProductEntity
        {
            Type = ProductTypes.SCHEME.ToString()
        },
        Metadata = "{\"MerchantGatewayKey\":\"MerchantGatewayKey\"}"
    };

    private static readonly GatewayData GatewayData = new()
    {
        MerchantGatewayKey = "cdc37be7-ed01-47a4-af06-a4c8c0d103b2",
        MpgsAccounts = new List<MpgsAccount>
        {
            new MpgsAccount
            {
                MpgsMerchantId = "TESTENDAVA",
                MpgsApiKey = "ec05ce4c82047a25b0e2bdfca68cf8e5"
            }
        },
        MpgsHistory = new List<MpgsAccountHistory>
        {
            new MpgsAccountHistory
            {
             BankProvider="NBE",
             Operation=MpgsHistoryOperations.Update,
             DeactivatedDate= DateTime.UtcNow,
           CreatedDate=DateTime.UtcNow
            }
        },
        GsdkTid = "FED1",
        GsdkMid = "FED1",
        GsdkSecretKey = "PD2JFIO3AWEAH5FIYLASS9KK3QHGKAQBPPLE",
        CyberSourceMerchantId = "geidea_test",
        CyberSourceMerchantKeyId = "5cc64896-68d2-429b-9861-b3c46d61febd",
        CyberSourceSharedSecretKey = "VFSU7YYbRyTNfZvtFnaAXlZ69fuZq6LaNZkJbxN34ng",
        DefaultPaymentOperation = "Pay",
        ApiPassword = "TEST_1234567890",
        IsTest = true,
        ApplePartnerInternalMerchantIdentifier = "dd9a6b47-c1da-4cfc-8929-5daca1f9932",
        MerchantName = "Common merchant EN",
        MerchantNameAr = "Common merchant AR",
        MerchantWebsite = "Apple",
        Mcc = "1234",
        IsTokenizationEnabled = true,
        AllowedInitiatedByValues = new List<string> { Constants.InitiatedByInternet
        },
        MinAmount = 10,
        MaxAmount = 10000,
        IsValuBnplEnabled = true,
        ValuVendorId = "123",
        ValuProductId = "123",
        ValuStoreId = "123",
        IsCvvRequiredForTokenPayments = false,
        Is3dsRequiredForTokenPayments = false,
        IsShahryCpBnplEnabled = true,
        ShahryCpBnplMerchantCode = "123456",
        ShahryCpBnplBranchCode = "654321",
        IsSouhoolaCnpBnplEnabled = true,
        SouhoolaMerchantNationalId = "123",
        SouhoolaMerchantPhoneNumber = "234",
        SouhoolaAccessKey = "345",
        IsShahryCnpBnplEnabled = true,
        ShahryCnpBnplMerchantCode = "112233",
        ShahryCnpBnplBranchCode = "445566",
        IsSouhoolaCpBnplEnabled = true,
        IsSouhoolaCpMerchantRegistered = false,
        SouhoolaCpBnplNationalId = "12345678912345",
        SouhoolaCpBnplGlobalId = "11756",
        SouhoolaCpBnplUserName = "test",
        SouhoolaCpBnplPassword = "test",
        SouhoolaCnpUserName = "test",
        SouhoolaCnpPassword = "test",
        IsCustomerBillingAddressMandatory = true,
        IsCustomerEmailMandatory = true,
        IsCustomerShippingAddressMandatory = true,
        AllowCashOnDeliveryValu = false,
        AllowDownPaymentValu = false,
        AllowCashOnDeliverySouhoola = false,
        AllowCashOnDeliveryShahry = false,
        EnableSubMerchantInformation = true,
        IsBankInstallmentsCnpEnabled = true,
        IsRecurringPaymentsEnabled = false,
        IsRecurringPaylinksEnabled = false,
        IsSmartRoutingEnabled = true,
        IsExcessiveCaptureEnabled = false,
        GooglePayMerchantId = "123",
        IsGooglePayEnabled = true,
        IsTamaraEnabled = true,
        TamaraApiToken = "123",
        TamaraPublicKey = "123",
        StcPayMerchantId = "123",
        IsStcPayEnabled = true,
        AddOnFeesPbl = new AddOnFees()
        {
            IsEnabled = true,
            Fees = new CafData()
            {
                Label = "AddOnFee",
                Value = 10,
                ValueType = AmountType.Percentage
            }
        },
        ExtraChargesPbl = new ExtraCharges()
        {
            IsEnabled = true,
            Charges = new CafData()
            {
                Label = "ExtraCharges",
                Value = 10,
                ValueType = AmountType.Amount
            }
        },
        SubMerchantInformation = new SubMerchantInformation()
        {
            City = "City",
            Country = "Country",
            Governorate = "Governorate",
            StreetAndNumber = "StreetAndNumber",
            ZipCode = "ZipCode",
            Email = "<EMAIL>",
            PhoneNumber = "+************",
            CountryPrefix = "+20"
        },
        TenantCode = "0.0000012",
        MadaAccounts = new List<MadaAccount>()
        {
            new MadaAccount
            {
                MadaApiKey = "MadaApiKey",
                MadaMerchantId = "MadaMerchantId",
                CardInfo = new List<CardInfo>
                {
                    new CardInfo
                    {
                        CardBrand = "VISA",
                        PrimaryGatewayId = "PrimaryGatewayId"
                    }
                },
                AcquirerMid = "AcquirerMerchId"
            }
        },
        IsSamsungPayWebEnabled = false,
        IsPartialCaptureEnabled = true,
        IsCaptureEnabled = true,
        IsPartialRefundEnabled = true,
        IsUpdateAuthorizationPercentageEnabled = false,
        IsUpdateAuthorizationEnabled = false,
        UpdateAuthorizationPercentage = 0,
        IsGenerateAndUseNetworkTokenEnabled = false,
        IsUseNetworkTokenEnabled = false,
        IsSendNetworkTokenToMerchantEnabled = false,
        NetworkTokenCallBackUrl = "https://some-dummy-redirect-url.com",
        NetworkTokenEncryptionKey = "a50e8a13-7dc8-4ce8-99f8-c92a08bc8e19"
    };

    private readonly CreateProductInstanceRequest createProductInstanceRequest = new()
    {
        AgreementId = Guid.NewGuid(),
        Data = GatewayData
    };

    private static readonly Guid StoreIdGuid = Guid.NewGuid();
    private static readonly ProductInstanceEntity StoreWithProducts = new()
    {
        Id = Guid.NewGuid(),
        Product = new ProductEntity
        {
            Id = Guid.NewGuid(),
            Type = "TERMINAL"
        },
        Data = new TerminalData
        {
            TId = "TestTid",
        },
        StoreId = StoreIdGuid
    };

    private readonly StoreProductInstanceResponse storeProductResponse = new()
    {
        Data = StoreWithProducts.Data.As<TerminalData>(),
        Product = new ProductShortResponse
        {
            ProductId = StoreWithProducts.Product.Id
        },
    };

    private static AttemptedAuthenticationRule AttemptedAuthenticationRule => new()
    {
        CardTypes = new[]
        {
            "ALL"
        },
        Threshold = 0
    };
    private static readonly SubMerchantMigrationInfoProductInstance subMerchantMigrationRequest = new()
    {

        Mcc = "TestMcc1",
        Mso = "TestMso1",
        CompanyId = ProductInstanceCompanyId
    };


    public ProductConfiguratorServiceTests()
    {

        var publisherSelector = new Mock<IProductInstancePublisherSelector>();
        var logger = new Mock<ILogger<ProductConfiguratorService>>();

        productService = new Mock<IProductService>();
        productInstanceRepository = new Mock<IProductInstanceRepository>();
        productRepository = new Mock<IProductRepository>();
        productInstancePublisher = new Mock<IProductInstancePublisher>();
        mapper = new Mock<IMapper>();
        productSender = new Mock<IProductChangeSenderService>();

        meezaSettings.Setup(x => x.CurrentValue).Returns(new MeezaSettings
        {
            MerchantPrefix = "675"
        });

        currencySettings.Setup(x => x.CurrentValue).Returns(new CurrencySettings
        {
            SupportedCurrencies = new string[] { "SAR", "EGP", "AED", "USD", "EUR", "GBP", "BHD", "KWD", "OMR", "QAR" }
        });
        mpgsAccountsSettings.Setup(x => x.CurrentValue).Returns(new MpgsAccountsSettings
        {
            CleanHistoryPeriod = new TimeSpan(10, 0, 0)
        });
        productService
            .Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), false))
            .Returns(Task.FromResult(new[] { new Product { Type = ProductTypes.GWAY.ToString() } }));

        productInstanceRepository
            .Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<bool>()))
            .Returns(Task.FromResult(productInstance));

        productInstanceRepository
            .Setup(x => x.GetByIdWithProductAndChildrenAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(productInstance));

        productInstanceRepository
            .Setup(x => x.FindAsync(It.IsAny<FindProductInstanceRequest>(), false, It.IsAny<bool>()))
            .Returns(Task.FromResult(new List<ProductInstanceEntity> { new ProductInstanceEntity(), new ProductInstanceEntity() }));

        productInstanceRepository
            .Setup(x => x.GetByIdAsync(schemeProductInstance.Id, It.IsAny<bool>()))
            .Returns(Task.FromResult(schemeProductInstance));

        productInstanceRepository
            .Setup(x => x.GetByIdAsync(schemeProductInstance.Id, It.IsAny<bool>()))
            .Returns(Task.FromResult(schemeProductInstance));



        productInstanceRepository
            .Setup(x => x.GetProductInstancesForStore(StoreIdGuid))
            .Returns(Task.FromResult(new List<ProductInstanceEntity> { StoreWithProducts }));

        productInstanceRepository
            .Setup(x => x.SearchProductInstanceBaseAsync(It.IsAny<SearchProductInstanceBaseRequest>()))
            .Returns(Task.FromResult(new[] { productInstance }));

        mapper.Setup(x => x.Map<ProductInstanceEntity>(createProductInstanceRequest))
            .Returns(productInstance);

        mapper.Setup(x => x.Map<UpdateProductInstanceRequest>(productInstance))
            .Returns(updateProductInstanceRequest);

        productInstanceRepository
             .Setup(x => x.GetProductInstanceByCompanyIdAsync(It.IsAny<Guid>(), It.IsAny<bool>()))
            .Returns(Task.FromResult(new List<ProductInstanceEntity> { productInstance }));

        productInstanceRepository
           .Setup(x => x.FindByIdsAsync(It.IsAny<IdsRequest>(), true))
           .Returns(Task.FromResult(new List<ProductInstanceEntity> { productInstance }));
        productInstanceRepository
           .Setup(x => x.FindAsync(It.IsAny<FindProductInstanceRequest>(), false, It.IsAny<bool>()))
           .Returns(Task.FromResult(new List<ProductInstanceEntity> { productInstance }));

        productInstanceRepository
            .Setup(x => x.SearchGatewayConfigurations(It.IsAny<SearchGatewayConfigurationsRequest>()))
            .Returns(Task.FromResult(new SearchGatewayConfigurationsResponse()));

        mapper.Setup(x => x.Map<UpdateProductInstanceRequest>(schemeProductInstance))
            .Returns(updateProductInstanceRequest);

        mapper.Setup(x => x.Map<ProductInstanceResponse>(productInstance))
            .Returns(productInstanceResponse);

        mapper.Setup(x => x.Map<StoreProductInstanceResponse>(StoreWithProducts))
            .Returns(storeProductResponse);

        mapper.Setup(x => x.Map<ProductShortResponse>(StoreWithProducts.Product))
            .Returns(storeProductResponse.Product);

        mapper.Setup(x => x.Map<ProductInstanceResponse[]>(It.IsAny<ProductInstanceEntity[]>()))
            .Returns(new[] { productInstanceResponse });

        productInstanceRepository
          .Setup(x => x.FindGatewayCompanyIds(It.IsAny<FindGatewayCompanyIds>(), false, It.IsAny<bool>()))
          .Returns(Task.FromResult(GatewayComapnyIdResponse));


        publisherSelector.Setup(x => x
                .GetProductInstancePublisher(ProductTypes.GWAY))
            .Returns(productInstancePublisher.Object);

        productConfiguratorService = new ProductConfiguratorService(
            meezaSettings.Object,
            logger.Object,
            mapper.Object,
            productService.Object,
            productInstanceRepository.Object,
            productRepository.Object,
            counterpartyProvider.Object,
            productSender.Object,
            currencySettings.Object, mpgsAccountsSettings.Object);
    }

    [Fact]
    public async Task CreateShouldCreateProductInstanceAndPublishToRabbitMq()
    {
        mapper.Setup(x => x.Map<ProductInstanceResponse>(It.IsAny<ProductInstanceEntity>()))
            .Returns(new ProductInstanceResponse
            {
                AgreementId = createProductInstanceRequest.AgreementId,
                Data = createProductInstanceRequest.Data
            });
        mapper.Setup(x => x.Map<ProductEntity>(It.IsAny<Product>()))
            .Returns(ProductEntity);

        var result = await productConfiguratorService.CreateAsync(createProductInstanceRequest);

        Assert.Equal(createProductInstanceRequest.AgreementId, result.AgreementId);
        Assert.Equal(createProductInstanceRequest.Data, result.Data);

        productInstanceRepository.Verify(x => x.Save(productInstance), Times.Once);
        productSender.Verify(x => x.SendCreatedEvent(productInstance), Times.Once);
    }

    [Fact]
    public async Task BulkCreateShouldCreateProductInstanceAndPublishToRabbitMq()
    {
        mapper.Setup(x => x.Map<ProductInstanceResponse>(It.IsAny<ProductInstanceEntity>()))
            .Returns(new ProductInstanceResponse
            {
                AgreementId = createProductInstanceRequest.AgreementId,
                Data = createProductInstanceRequest.Data
            });
        mapper.Setup(x => x.Map<ProductEntity>(It.IsAny<Product>()))
            .Returns(ProductEntity);

        var bulkProductInstanceRequest = new List<CreateProductInstanceRequest>
        {
            createProductInstanceRequest
        };

        var result = await productConfiguratorService.BulkCreateAsync(bulkProductInstanceRequest);

        Assert.Equal(createProductInstanceRequest.AgreementId, result[0].AgreementId);
        Assert.Equal(createProductInstanceRequest.Data, result[0].Data);

        productInstanceRepository.Verify(x => x.Save(productInstance), Times.Once);
        productSender.Verify(x => x.SendCreatedEvent(productInstance), Times.Once);
    }

    [Theory]
    [InlineData(CounterpartyEgypt, MpgsMsoProviders.Bm)]
    [InlineData(CounterpartySaudi, MpgsMsoProviders.Geidea)]
    [InlineData(CounterpartyUae, MpgsMsoProviders.Magnati)]
    public async Task CreateShouldSetDefaultValueForMpgsMsoProvider(string counterparty, string msoProvider)
    {
        mapper.Setup(x => x.Map<ProductInstanceResponse>(It.IsAny<ProductInstanceEntity>()))
            .Returns(new ProductInstanceResponse
            {
                AgreementId = createProductInstanceRequest.AgreementId,
                Data = createProductInstanceRequest.Data
            });
        mapper.Setup(x => x.Map<ProductEntity>(It.IsAny<Product>()))
            .Returns(ProductEntity);

        counterpartyProvider.Setup(x => x.GetCode()).Returns(counterparty);

        await productConfiguratorService.CreateAsync(createProductInstanceRequest);

        productInstanceRepository.Verify(x => x.Save(It.Is<ProductInstanceEntity>(i => ((GatewayData)i.Data)!.MpgsMsoProvider == msoProvider)), Times.Once);
    }

    [Theory]
    [InlineData(Egy, CounterpartyEgypt, false)]
    [InlineData(Sau, CounterpartySaudi, false)]
    [InlineData(Are, CounterpartyUae, false)]
    public async Task CreateShouldSetDefaultIs3dsRequiredForTokenPaymentsWhenMerchantCountryIsPassed(string merchantCountry, string counterparty, bool is3dsRequiredForTokenPayments)
    {
        GatewayData.Is3dsRequiredForTokenPayments = null;
        GatewayData.MerchantCountry = merchantCountry;
        createProductInstanceRequest.Data = GatewayData;

        var gatewayDataFromMapper = SetDefaultValues(GatewayData) as GatewayData;

        mapper.Setup(x => x.Map<ProductInstanceEntity>(It.IsAny<CreateProductInstanceRequest>()))
            .Returns(new ProductInstanceEntity
            {
                Data = gatewayDataFromMapper
            });

        mapper.Setup(x => x.Map<ProductInstanceResponse>(It.IsAny<ProductInstanceEntity>()))
            .Returns(new ProductInstanceResponse
            {
                AgreementId = createProductInstanceRequest.AgreementId,
                Data = createProductInstanceRequest.Data
            });

        mapper.Setup(x => x.Map<ProductEntity>(It.IsAny<Product>()))
            .Returns(ProductEntity);

        counterpartyProvider.Setup(x => x.GetCode()).Returns(counterparty);

        await productConfiguratorService.CreateAsync(createProductInstanceRequest);

        productInstanceRepository.Verify(x => x.Save(It.Is<ProductInstanceEntity>(i =>
            ((GatewayData)i.Data)!.Is3dsRequiredForTokenPayments == is3dsRequiredForTokenPayments)), Times.Once);
    }

    [Theory]
    [InlineData(Egy, CounterpartyEgypt, false)]
    [InlineData(Sau, CounterpartySaudi, false)]
    [InlineData(Are, CounterpartyUae, false)]
    public async Task CreateShouldSetDefaultIsCvvRequiredForTokenPaymentsWhenMerchantCountryIsPassed(string merchantCountry, string counterparty, bool isCvvRequiredForTokenPayments)
    {
        GatewayData.IsCvvRequiredForTokenPayments = null;
        GatewayData.MerchantCountry = merchantCountry;
        createProductInstanceRequest.Data = GatewayData;

        var gatewayDataFromMapper = SetDefaultValues(GatewayData) as GatewayData;

        mapper.Setup(x => x.Map<ProductInstanceEntity>(It.IsAny<CreateProductInstanceRequest>()))
            .Returns(new ProductInstanceEntity
            {
                Data = gatewayDataFromMapper
            });

        mapper.Setup(x => x.Map<ProductInstanceResponse>(It.IsAny<ProductInstanceEntity>()))
            .Returns(new ProductInstanceResponse
            {
                AgreementId = createProductInstanceRequest.AgreementId,
                Data = createProductInstanceRequest.Data
            });

        mapper.Setup(x => x.Map<ProductEntity>(It.IsAny<Product>()))
            .Returns(ProductEntity);

        counterpartyProvider.Setup(x => x.GetCode()).Returns(counterparty);

        await productConfiguratorService.CreateAsync(createProductInstanceRequest);

        productInstanceRepository.Verify(x => x.Save(It.Is<ProductInstanceEntity>(i =>
            ((GatewayData)i.Data)!.IsCvvRequiredForTokenPayments == isCvvRequiredForTokenPayments)), Times.Once);
    }

    [Theory]
    [InlineData(CounterpartyEgypt, false)]
    [InlineData(CounterpartySaudi, false)]
    [InlineData(CounterpartyUae, false)]
    public async Task CreateShouldSetDefaultIs3dsRequiredForTokenPaymentsWhenMerchantCountryIsNotPassed(string counterparty, bool is3dsRequiredForTokenPayments)
    {
        GatewayData.Is3dsRequiredForTokenPayments = null;
        createProductInstanceRequest.Data = GatewayData;

        mapper.Setup(x => x.Map<ProductInstanceResponse>(It.IsAny<ProductInstanceEntity>()))
            .Returns(new ProductInstanceResponse
            {
                AgreementId = createProductInstanceRequest.AgreementId,
                Data = createProductInstanceRequest.Data
            });

        mapper.Setup(x => x.Map<ProductEntity>(It.IsAny<Product>()))
            .Returns(ProductEntity);

        counterpartyProvider.Setup(x => x.GetCode()).Returns(counterparty);

        await productConfiguratorService.CreateAsync(createProductInstanceRequest);

        productInstanceRepository.Verify(x => x.Save(It.Is<ProductInstanceEntity>(i =>
            ((GatewayData)i.Data)!.Is3dsRequiredForTokenPayments == is3dsRequiredForTokenPayments)), Times.Once);
    }

    [Theory]
    [InlineData(CounterpartyEgypt, false)]
    [InlineData(CounterpartySaudi, false)]
    [InlineData(CounterpartyUae, false)]
    public async Task CreateShouldSetDefaultIsCvvRequiredForTokenPaymentsWhenMerchantCountryIsNotPassed(string counterparty, bool isCvvRequiredForTokenPayments)
    {
        GatewayData.IsCvvRequiredForTokenPayments = null;
        createProductInstanceRequest.Data = GatewayData;

        mapper.Setup(x => x.Map<ProductInstanceResponse>(It.IsAny<ProductInstanceEntity>()))
            .Returns(new ProductInstanceResponse
            {
                AgreementId = createProductInstanceRequest.AgreementId,
                Data = createProductInstanceRequest.Data
            });

        mapper.Setup(x => x.Map<ProductEntity>(It.IsAny<Product>()))
            .Returns(ProductEntity);

        counterpartyProvider.Setup(x => x.GetCode()).Returns(counterparty);

        await productConfiguratorService.CreateAsync(createProductInstanceRequest);

        productInstanceRepository.Verify(x => x.Save(It.Is<ProductInstanceEntity>(i =>
            ((GatewayData)i.Data)!.IsCvvRequiredForTokenPayments == isCvvRequiredForTokenPayments)), Times.Once);
    }

    [Theory]
    [InlineData(Sau, CounterpartySaudi)]
    [InlineData(Are, CounterpartyUae)]
    public async Task CreateShouldNotSetDefaultAuthenticationRuleWhenMerchantCountryIsNotEgypt(string merchantCountry, string counterparty)
    {
        GatewayData.MerchantCountry = merchantCountry;
        GatewayData.Rules.RuleAttemptedAuth = null;
        GatewayData.Rules.RuleNotEnrolled = null;
        createProductInstanceRequest.Data = GatewayData;

        var gatewayDataFromMapper = SetDefaultValues(GatewayData) as GatewayData;

        mapper.Setup(x => x.Map<ProductInstanceEntity>(It.IsAny<CreateProductInstanceRequest>()))
            .Returns(new ProductInstanceEntity
            {
                Data = gatewayDataFromMapper
            });

        mapper.Setup(x => x.Map<ProductInstanceResponse>(It.IsAny<ProductInstanceEntity>()))
            .Returns(new ProductInstanceResponse
            {
                AgreementId = createProductInstanceRequest.AgreementId,
                Data = createProductInstanceRequest.Data
            });

        mapper.Setup(x => x.Map<ProductEntity>(It.IsAny<Product>()))
            .Returns(ProductEntity);

        counterpartyProvider.Setup(x => x.GetCode()).Returns(counterparty);

        await productConfiguratorService.CreateAsync(createProductInstanceRequest);

        productInstanceRepository.Verify(x => x.Save(It.Is<ProductInstanceEntity>(i =>
            ((GatewayData)i.Data)!.Rules.RuleAttemptedAuth == null &&
            ((GatewayData)i.Data)!.Rules.RuleNotEnrolled == null)), Times.Once);
    }

    [Theory]
    [InlineData(Egy, CounterpartyEgypt)]
    public async Task CreateShouldSetDefaultAuthenticationRuleWhenMerchantCountryIsEgypt(string merchantCountry, string counterparty)
    {
        GatewayData.MerchantCountry = merchantCountry;
        createProductInstanceRequest.Data = GatewayData;

        var gatewayDataFromMapper = SetDefaultValues(GatewayData) as GatewayData;

        mapper.Setup(x => x.Map<ProductInstanceEntity>(It.IsAny<CreateProductInstanceRequest>()))
            .Returns(new ProductInstanceEntity
            {
                Data = gatewayDataFromMapper
            });

        mapper.Setup(x => x.Map<ProductInstanceResponse>(It.IsAny<ProductInstanceEntity>()))
            .Returns(new ProductInstanceResponse
            {
                AgreementId = createProductInstanceRequest.AgreementId,
                Data = createProductInstanceRequest.Data
            });

        mapper.Setup(x => x.Map<ProductEntity>(It.IsAny<Product>()))
            .Returns(ProductEntity);

        counterpartyProvider.Setup(x => x.GetCode()).Returns(counterparty);

        await productConfiguratorService.CreateAsync(createProductInstanceRequest);

        productInstanceRepository.Verify(x => x.Save(It.Is<ProductInstanceEntity>(i =>
            ((GatewayData)i.Data)!.Rules.RuleAttemptedAuth.CardTypes[0] == AttemptedAuthenticationRule.CardTypes[0] &&
            ((GatewayData)i.Data)!.Rules.RuleAttemptedAuth.Threshold == AttemptedAuthenticationRule.Threshold &&
            ((GatewayData)i.Data)!.Rules.RuleNotEnrolled == null)), Times.Once);
    }

    [Fact]
    public async Task UpdateShouldUpdateProductInstanceAndPublishToRabbitMq()
    {
        const string jsonData = "{\"MerchantGatewayKey\":\"MerchantGatewayKey\"";
        var instanceId = Guid.NewGuid();
        var patchDocument = new JsonPatchDocument<UpdateProductInstanceRequest>();
        patchDocument.Replace(x => x.Data, jsonData);

        mapper.Setup(x => x.Map<ProductInstanceResponse>(It.IsAny<ProductInstanceEntity>()))
            .Returns(new ProductInstanceResponse
            {
                Data = jsonData
            });

        var result = await productConfiguratorService.PatchAsync(instanceId, patchDocument);

        Assert.Equal(jsonData, result.Data);

        productInstanceRepository.Verify(x => x.Update(productInstance), Times.Once);
        productSender.Verify(x => x.SendUpdatedEvent(productInstance), Times.Once);
    }

    [Fact]
    public async Task UpdateNonGatewayInstanceWithParentGatewayShouldPublishToRabbitMq()
    {
        const string jsonData = "{\"MerchantGatewayKey\":\"MerchantGatewayKey\"";

        var patchDocument = new JsonPatchDocument<UpdateProductInstanceRequest>();
        patchDocument.Replace(x => x.Data, jsonData);

        mapper.Setup(x => x.Map<ProductInstanceResponse>(schemeProductInstance))
            .Returns(new ProductInstanceResponse
            {
                Data = jsonData
            });

        var result = await productConfiguratorService.PatchAsync(schemeProductInstance.Id, patchDocument);

        Assert.Equal(jsonData, result.Data);

        productSender.Verify(x => x.SendUpdatedEvent(productInstance), Times.Once);
    }

    [Fact]
    public async Task UpdateTerminalProductInstancesMeta_Should_ReturnGuidList()
    {
        var terminalDataRequest = new List<UpdateProductInstanceMetaRequest> {
            new UpdateProductInstanceMetaRequest
            {
                ProductInstanceId = new Guid("********-0000-0000-0000-********0001") ,
                LegalName = "Name",
                LegalNameAr = "NameAr",
                MIDMerchantReference = "************",
                TId = "********",
                FullTId = "********",
                Trsm = "212121",
                ProviderBank = "NBE_BANK",
                TradingCurrency = "EGP"
            },
            new UpdateProductInstanceMetaRequest { ProductInstanceId = new Guid("********-0000-0000-0000-********0002") }
        };


        productInstanceRepository
            .Setup(x => x.FindByIdsAsync(It.IsAny<IdsRequest>(), true)).Returns(Task.FromResult(new List<ProductInstanceEntity> {
                new ProductInstanceEntity {
                    Id = new Guid("********-0000-0000-0000-********0001"),
                    Product = ProductEntity,
                    Metadata = "{\"TerminalDataKey\":\"TerminalDataKey\"}",
                    CompanyId = ProductInstanceCompanyId
                }
            }));

        var result = await productConfiguratorService.UpdateTerminalProductInstancesMeta(terminalDataRequest);

        productInstanceRepository.Verify(x => x.FindByIdsAsync(It.IsAny<IdsRequest>(), true), Times.Once);
        productSender.Verify(x => x.SendUpdatedEvent(It.IsAny<ProductInstanceEntity>()), Times.Once);
        result.Should().HaveCount(1);

        productSender.Setup(x => x.SendUpdatedEvent(It.IsAny<ProductInstanceEntity>())).Throws<Exception>();
        var result2 = await productConfiguratorService.UpdateTerminalProductInstancesMeta(terminalDataRequest);

        productInstanceRepository.Verify(x => x.FindByIdsAsync(It.IsAny<IdsRequest>(), true), Times.AtLeast(2));
        result2.Should().HaveCount(1);
        productSender.Verify(x => x.SendUpdatedEvent(It.IsAny<ProductInstanceEntity>()), Times.AtLeast(2));
    }

    [Fact]
    public async Task FindShouldShouldReturnCorrectInstances()
    {
        var findProductInstanceRequest = new FindProductInstanceRequest();
        var instances = new[] { new ProductInstanceWithParentResponse(), new ProductInstanceWithParentResponse() };
        mapper.Setup(x => x.Map<ProductInstanceWithParentResponse[]>(It.IsAny<List<ProductInstanceEntity>>()))
            .Returns(instances);

        var result = await productConfiguratorService.FindAsync(findProductInstanceRequest);

        Assert.Equal(instances.Length, result.Length);

        productInstanceRepository.Verify(x => x.FindAsync(findProductInstanceRequest, false, false), Times.Once);
    }
    [Fact]
    public async Task FindGatewayCompanyIdsShouldShouldReturnCorrectInstances()
    {
        var result = await productConfiguratorService.FindGatewayProductCompanyIds();

        Assert.Equal(GatewayComapnyIdResponse, result);
    }
    [Fact]
    public async Task FindByIdShouldShouldReturnCorrectInstance()
    {
        var result = await productConfiguratorService.FindByIdAsync(ProductInstanceId);

        Assert.Equal(productInstanceResponse, result);

        productInstanceRepository.Verify(x => x.GetByIdAsync(ProductInstanceId, false), Times.Once);
    }

    [Fact]
    public async Task GetProductsForStore_ShouldReturn_Product()
    {
        var result = await productConfiguratorService.GetProductInstancesForStore(StoreIdGuid);

        result.Count.Should().Be(1);
        result[0].Data.As<TerminalData>().TId.Should().BeEquivalentTo("TestTid");
        result[0].Product.ProductId.Should().Be(StoreWithProducts.Product.Id);

        productInstanceRepository.Verify(x => x.GetProductInstancesForStore(StoreIdGuid), Times.Once);
    }

    [Fact]
    public async Task SearchProductInstanceBaseAsync_WhenDataIsValid_ShouldReturnProductInstance()
    {
        var request = new SearchProductInstanceBaseRequest { Types = new[] { "GWAY", "TERMINAL" }, StoreIds = new[] { Guid.NewGuid() } };

        var result = await productConfiguratorService.SearchProductInstanceBaseAsync(request);

        result.Length.Should().Be(1);
        result[0].Should().BeEquivalentTo(productInstanceResponse);

        productInstanceRepository.Verify(x => x.SearchProductInstanceBaseAsync(request), Times.Once);
    }

    [Fact]
    public async Task UpdateProductInstancesStoreNameForMerchantShouldUpdateStoreNameAndPublishToRabbitMq()
    {
        var request = new MerchantStoreNameProductInstanceRequest()
        {
            StoreName = "TestNameFreelancer",
            ProductInstances = new List<Guid>() { ProductInstanceId }
        };

        await productConfiguratorService.UpdateProductInstancesStoreNameForMerchant(request);

        productInstanceRepository.Verify(
            x => x.FindByIdsAsync(It.IsAny<IdsRequest>(), true), Times.Once);

        productInstanceRepository.Verify(x => x.Update(productInstance), Times.Once);
        productSender.Verify(x => x.SendUpdatedEvent(productInstance), Times.Once);
    }

    [Fact]
    public async Task UpdateProductInstancesStoreNameForMerchantShouldReturnTaskCompletedIfProductInstancesNotFound()
    {
        var request = new MerchantStoreNameProductInstanceRequest()
        {
            StoreName = "TestNameFreelancer",
            ProductInstances = new List<Guid>() { Guid.NewGuid() }
        };
        productInstanceRepository
            .Setup(x => x.FindByIdsAsync(It.IsAny<IdsRequest>(), true))
            .Returns(Task.FromResult(new List<ProductInstanceEntity> { }));

        await productConfiguratorService.UpdateProductInstancesStoreNameForMerchant(request);

        productInstanceRepository.Verify(
            x => x.FindByIdsAsync(It.IsAny<IdsRequest>(), true), Times.Once);

        productInstanceRepository.Verify(x => x.Update(productInstance), Times.Never);
        productSender.Verify(x => x.SendUpdatedEvent(productInstance), Times.Never);
    }

    [Fact]
    public async Task UpdateProductInstancesStoreNameForMerchantShouldThrowExceptionIfInvalidRequest()
    {
        var request = new MerchantStoreNameProductInstanceRequest()
        {
            StoreName = "TestStoreFutureWorks"
        };

        await productConfiguratorService
            .Invoking(x => x.UpdateProductInstancesStoreNameForMerchant(request))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.UpdateMerchantProductInstanceStoreNameBadRequest.Code);
    }

    [Fact]
    public async Task SearchGatewayConfigurationsCallsCorrectRepoMethodAndReturnsSuccessfully()
    {
        var request = new SearchGatewayConfigurationsRequest();
        var result = await productConfiguratorService.SearchGatewayConfigurations(request);

        Assert.NotNull(result);

        productInstanceRepository.Verify(x => x.SearchGatewayConfigurations(request), Times.Once);
    }

    [Fact]
    public async Task CreateMerchantMigrationDefaultProductInstancesAsync_ShoudThrowServiceException_WhenProductPartIsNull()
    {
        var request = new MerchantMigrationProductInstanceRequest();
        var findResult = new Product[] { new Product() { Type = ProductTypes.GWAY.ToString(), Parts = null } };

        productService
            .Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), false))
            .Returns(Task.FromResult(findResult));

        await productConfiguratorService
            .Invoking(x => x.CreateMerchantMigrationDefaultProductInstancesAsync(request))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest && x.ProblemDetails.Type == Errors.ProductNotFound.Code);
    }

    [Fact]
    public async Task CreateMerchantMigrationDefaultProductInstancesAsync_ShoudThrowServiceException_WhenTryingToAddANewProductInstance()
    {
        var request = new MerchantMigrationProductInstanceRequest();
        var findResult = new Product[] { new Product() { Type = ProductTypes.GWAY.ToString() } };

        productService
            .Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), false))
            .Returns(Task.FromResult(findResult));

        await productConfiguratorService
            .Invoking(x => x.CreateMerchantMigrationDefaultProductInstancesAsync(request))
            .Should().ThrowAsync<NullReferenceException>();
    }

    [Fact]
    public async Task CreateMerchantMigrationDefaultProductInstancesAsync_ShoudThrowServiceException_WhenRequstIsNull()
    {
        var productId = Guid.NewGuid();
        var companyId = Guid.NewGuid();
        var storeId = Guid.NewGuid();
        var prodInstanceId = Guid.NewGuid();

        var request = new MerchantMigrationProductInstanceRequest()
        {
            ProductId = productId,
            StoreId = storeId,
            CompanyId = companyId,
            FullTid = "FullTid",
            Mid = "Mid",
            Tid = "Tid",
            Trsm = "Trsm",
            AcquiringLedger = "AcquiringLedger",
            DefaultOperation = "DefaultOperation",
            LegalNameAr = "LegalNameAr",
            MerchantName = "MerchantName",
            MpgsApiKey = "MpgsApiKey",
            MpgsMerchantId = "MpgsMerchantId",
            ProductType = ProductTypes.TERMINAL.ToString()
        };

        var findResult = new Product[] {
            new Product()
            {
                Id = productId,
                Availability = Constants.Availability.Live,
                Flow = Constants.Flow.Normal,
                SalesChannel="Store",
                Code = "Code",
                Version=0,
                Type = ProductTypes.TERMINAL.ToString(),
                Description ="Description",
                DisplayOrder=0,
                ValidFrom = DateTime.Now,
                ValidTo =  DateTime.Now.AddDays(1),
                Prices = new List<Price>(){
                    new Price()
                    {
                        Id = Guid.NewGuid(),
                        ChargeFrequency = "ChargeFrequency",
                        ChargeType = "ChargeType",
                        CreatedBy = String.Empty,
                        CreatedDateUtc = DateTime.Now,
                        Currency = "Currency",
                        DeletedFlag = false,
                        ExemptFromVAT = false,
                        Group = "Group",
                        MaxPrice= 10,
                        PercentagePrice = 2,
                        PerItemPrice = 10,
                        Priority = 1,
                        ProductId = productId,
                        RentalPeriod = 10,
                        Threshold = 1,
                        ThresholdType = string.Empty,
                        ValidFrom = DateTime.Now,
                        ValidTo = DateTime.Now.AddDays(1),
                        Product = new Product()
                    }
                },
                Parts = new List<ProductPart>()
                {
                    new ProductPart()
                    {
                        ProductId = productId,
                        Quantity = 1,
                        PartId = Guid.NewGuid(),
                        Part = new Product(),
                        Product = new Product()
                    }
                },
                ProductCategories = new List<ProductCategories>()
                {
                    new ProductCategories()
                    {
                        CategoryId = Guid.NewGuid(),
                        ProductId = productId,
                        Category = new Category(),
                        Product = new Product()
                    }
                },
                Counterparty = "SA",
                CRMProductId =  "CRMProductId",
                QuickOnboarding = false,
                CreatedBy = String.Empty,
                CreatedDateUtc = DateTime.Now,
                ReferralChannel = "migration",
                CommisionFees=null,
                KnowMoreURL = null,
                CategoryName = null,
                DeliveryFees=0,
                SetupFees=0
            }
        };

        var prodInstanceEntityRequestMapped = new ProductInstanceEntity()
        {
            Id = prodInstanceId,
            ProductId = productId,
            StoreId = Guid.NewGuid(),
            CompanyId = Guid.NewGuid(),
            ValidFrom = DateTime.UtcNow,
            ValidTo = DateTime.UtcNow.AddDays(1),
            Data = new TerminalData()
        };

        mapper.SetupSequence(x => x.Map<ProductInstanceEntity>(It.IsAny<CreateProductInstanceRequest>()))
            .Returns(prodInstanceEntityRequestMapped).Throws<Exception>();

        mapper.Setup(x => x.Map<FindProductInstanceRequest>(It.IsAny<DeleteProductInstanceRequest>()))
            .Returns(new FindProductInstanceRequest()
            {
                StoreId = storeId,
                CompanyId = companyId,
                ProductId = productId,
                ProductInstanceId = new Guid[] { prodInstanceId }
            });

        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), false))
            .Returns(Task.FromResult(findResult));

        productInstanceRepository.Setup(x => x.FindAsync(It.IsAny<FindProductInstanceRequest>(), false, It.IsAny<bool>()))
            .Returns(Task.FromResult(new List<ProductInstanceEntity>()));

        productInstanceRepository.Setup(x => x.Save(It.IsAny<ProductInstanceEntity>()));

        await productConfiguratorService
            .Invoking(x => x.CreateMerchantMigrationDefaultProductInstancesAsync(request))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.InternalServerError && x.ProblemDetails.Type == Errors.UnexpectedError.Code);
    }

    [Fact]
    public async Task CreateMerchantMigrationDefaultProductInstancesAsync_ShoudNotThrowServiceException_WhenRequstIsValid()
    {
        var productId = Guid.NewGuid();
        var companyId = Guid.NewGuid();
        var storeId = Guid.NewGuid();
        var prodInstanceId = Guid.NewGuid();

        var request = new MerchantMigrationProductInstanceRequest()
        {
            ProductId = productId,
            StoreId = storeId,
            CompanyId = companyId,
            FullTid = "FullTid",
            Mid = "Mid",
            Tid = "Tid",
            Trsm = "Trsm",
            AcquiringLedger = "AcquiringLedger",
            DefaultOperation = "DefaultOperation",
            LegalNameAr = "LegalNameAr",
            MerchantName = "MerchantName",
            MpgsApiKey = "MpgsApiKey",
            MpgsMerchantId = "MpgsMerchantId",
            ProductType = ProductTypes.TERMINAL.ToString()
        };

        var findResult = new Product[] {
            new Product()
            {
                Id = productId,
                Availability = Constants.Availability.Live,
                Flow = Constants.Flow.Normal,
                SalesChannel="Store",
                Code = "Code",
                Version=0,
                Type = ProductTypes.TERMINAL.ToString(),
                Description ="Description",
                DisplayOrder=0,
                ValidFrom = DateTime.Now,
                ValidTo =  DateTime.Now.AddDays(1),
                Prices = new List<Price>(){
                    new Price()
                    {
                        Id = Guid.NewGuid(),
                        ChargeFrequency = "ChargeFrequency",
                        ChargeType = "ChargeType",
                        CreatedBy = String.Empty,
                        CreatedDateUtc = DateTime.Now,
                        Currency = "Currency",
                        DeletedFlag = false,
                        ExemptFromVAT = false,
                        Group = "Group",
                        MaxPrice= 10,
                        PercentagePrice = 2,
                        PerItemPrice = 10,
                        Priority = 1,
                        ProductId = productId,
                        RentalPeriod = 10,
                        Threshold = 1,
                        ThresholdType = string.Empty,
                        ValidFrom = DateTime.Now,
                        ValidTo = DateTime.Now.AddDays(1),
                        Product = new Product()
                    }
                },
                Parts = new List<ProductPart>()
                {
                    new ProductPart()
                    {
                        ProductId = productId,
                        Quantity = 1,
                        PartId = Guid.NewGuid(),
                        Part = new Product(),
                        Product = new Product()
                    }
                },
                ProductCategories = new List<ProductCategories>()
                {
                    new ProductCategories()
                    {
                        CategoryId = Guid.NewGuid(),
                        ProductId = productId,
                        Category = new Category(),
                        Product = new Product()
                    }
                },
                Counterparty = "SA",
                CRMProductId =  "CRMProductId",
                QuickOnboarding = false,
                CreatedBy = String.Empty,
                CreatedDateUtc = DateTime.Now,
                ReferralChannel = "migration"
            }
        };

        var prodInstanceEntityRequestMapped = new ProductInstanceEntity()
        {
            Id = prodInstanceId,
            ProductId = productId,
            StoreId = Guid.NewGuid(),
            CompanyId = Guid.NewGuid(),
            ValidFrom = DateTime.UtcNow,
            ValidTo = DateTime.UtcNow.AddDays(1),
            Data = new TerminalData()
        };

        var findByIds = new ProductEntity[] { new ProductEntity() };

        mapper.Setup(x => x.Map<ProductInstanceEntity>(It.IsAny<CreateProductInstanceRequest>()))
            .Returns(prodInstanceEntityRequestMapped);

        mapper.Setup(x => x.Map<FindProductInstanceRequest>(It.IsAny<DeleteProductInstanceRequest>()))
            .Returns(new FindProductInstanceRequest()
            {
                StoreId = storeId,
                CompanyId = companyId,
                ProductId = productId,
                ProductInstanceId = new Guid[] { prodInstanceId }
            });

        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), false))
            .Returns(Task.FromResult(findResult));

        productSender.Setup(x => x.SendCreatedEvent(It.IsAny<ProductInstanceEntity>())).Returns(Task.CompletedTask);

        productRepository.Setup(x => x.FindByIdsAsync(It.IsAny<IdsRequest>()))
            .Returns(Task.FromResult(findByIds));

        productInstanceRepository.Setup(x => x.Save(It.IsAny<ProductInstanceEntity>()));

        var result = await productConfiguratorService.CreateMerchantMigrationDefaultProductInstancesAsync(request);

        Assert.Equal(prodInstanceId, result);
    }
    [Fact]
    public async Task CleanupExpiredMpgsHistoryReturnsTheCorrectResult()
    {
        await productConfiguratorService.CleanExpiredMPGSHistory();
        productInstanceRepository.Verify(x => x.CleanExpiredMPGSHistory(It.IsAny<DateTimeOffset>()), Times.Once);
    }
    [Fact]
    public void GetMpgsHistoryExpirationDateReturnsTheCorrectResult()
    {
        DateTime timeComponent = new DateTime(2007, 6, 1, 7, 55, 0);

        DateTimeOffset utcTime = new DateTimeOffset(timeComponent, new TimeSpan(-5, 0, 0));
        var expirationPeriod = mpgsAccountsSettings.Object.CurrentValue.CleanHistoryPeriod;


        var expiryDateTimeOffset = productConfiguratorService.GetMpgsHistoryExpirationDate(utcTime, expirationPeriod);
        Assert.Equal(new DateTimeOffset(2007, 06, 01, 17, 55, 00, new TimeSpan(-5, 0, 0)), expiryDateTimeOffset);

    }
    [Fact]
    public async Task IsSubscribedToPaymentGatewayCallsRepository()
    {
        var result = await productConfiguratorService.IsSubscribedToPaymentGateway(merchantId);

        productInstanceRepository.Verify(x => x.IsSubscribedToPaymentGateway(merchantId), Times.Once);
    }
    [Fact]
    public async Task IsSubscribedToPaymentGateway_ShouldThrowException_WhenInvalidInput()
    {
        await Assert.ThrowsAsync<ServiceException>(() => productConfiguratorService.IsSubscribedToPaymentGateway(Guid.Empty));
    }
    [Fact]
    public async Task UpdateProductInstancesSubMerchantInformationShouldUpdatesubMerchantAndPublishToRabbitMq()
    {
        var subMerchantProductInstanceRequest = new List<SubMerchantMigrationInfoProductInstance>
        {
            subMerchantMigrationRequest
        };
        await productConfiguratorService.UpdateProductInstancesMsoSubMerchantInfo(subMerchantProductInstanceRequest);
        productInstanceRepository.Verify(x => x.SendCompanyIdsToStoredProcedure(subMerchantProductInstanceRequest), Times.AtLeast(1));
    }

}
