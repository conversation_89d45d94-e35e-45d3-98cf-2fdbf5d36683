﻿using Common.Models.UnitPrice;
using Common.Views;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services;
public interface IUnitPriceService
{
    Task<UnitPriceResponse> CreateAsync(UnitPriceCreateRequest unitPriceCreateRequest);
    Task<UnitPriceDetails> GetUnitPriceAsync(Guid mccId, Guid productId, Guid businessTypeId);
    Task<UnitPriceDetails> UpdateAsync(Guid id, UnitPriceUpdateRequest updateRequest);
    Task DeleteUnitPricesAsync(List<Guid> ids, string deletedBy);
    Task<UnitPricesListResponse> GetUnitPricesList(UnitPricesListRequest request);
    Task<UnitPricesDetails> GetUnitPriceByIdAsync(Guid id);
    Task<List<ProductCategoryView>> ProductCategoryListAsync(Guid mccId, Guid businessTypeId);
    Task<UnitPriceResponse> CreateOrUpdateAsync(UnitPriceCreateRequest request);
    Task<List<UnitPriceDetails>> GetUnitPricesByIdsAsync(List<Guid> ids);
}
