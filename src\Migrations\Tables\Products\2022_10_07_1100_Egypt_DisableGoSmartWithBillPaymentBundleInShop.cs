﻿using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.Products;

[Migration(2022_10_07_1100)]
public class Egypt_DisableGoSmartWithBillPaymentBundleInShop : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
                   AppDomain.CurrentDomain.BaseDirectory +
                   Path.Combine("Scripts", "Products_Go_Smart_With_Bill_Payments_Disable_In_Shop.sql"));
    }
}