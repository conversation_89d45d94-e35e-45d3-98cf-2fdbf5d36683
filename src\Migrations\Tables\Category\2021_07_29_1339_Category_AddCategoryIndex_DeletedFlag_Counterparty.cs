﻿using FluentMigrator;

namespace Migrations.Tables.Category;

[Migration(2021_07_29_1339)]
public class Category_AddCategoryIndex_DeletedFlag_Counterparty : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(
            $@"IF NOT EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'IDX_Category_DeletedFlag_Counterparty' AND object_id = OBJECT_ID('[dbo].[Category]'))
                    BEGIN
                    CREATE NONCLUSTERED INDEX [IDX_Category_DeletedFlag_Counterparty]
                    ON [dbo].[Category] ([DeletedFlag],[Counterparty])
                    INCLUDE ([CreatedDate])
                    END");
    }
}
