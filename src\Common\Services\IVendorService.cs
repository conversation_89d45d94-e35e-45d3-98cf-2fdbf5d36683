﻿using Common.Models.Vendor;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services;
public interface IVendorService
{
    Task<VendorResponse> CreateAsync(VendorRequest request);
    Task<VendorResponse?> GetByIdAsync(Guid id);
    Task<List<VendorResponse>?> GetAsync(string? terminalType = null);
    Task<VendorResponse?> SetDefaultVendorAsync(Guid vendorId);
    Task<VendorResponse?> GetDefaultVendorAsync();
    Task<VendorSearchResponse> AdvancedSearch(VendorSearchRequest searchRequest);
}
