﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_04_12_1545)]
public class ProductInstance_UpdateShahryConfigurations : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"UPDATE pi
            SET Metadata = JSON_MODIFY(pi.Metadata, '$.ShahryCpBnplMerchantCode', JSON_VALUE(pi.Metadata, '$.ShahryOfflineMerchantCode'))
            FROM ProductInstances pi
            INNER JOIN Products p ON p.id=pi.ProductId
            WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.ShahryOfflineMerchantCode') is not null

            UPDATE pi
            SET Metadata = JSON_MODIFY(pi.Metadata, '$.ShahryOfflineMerchantCode', null)
            FROM ProductInstances pi
            INNER JOIN Products p ON p.id=pi.ProductId
            WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.ShahryOfflineMerchantCode') is not null

            UPDATE pi
            SET Metadata = JSON_MODIFY(pi.Metadata, '$.ShahryCpBnplBranchCode', JSON_VALUE(pi.Metadata, '$.ShahryOfflineBranchCode'))
            FROM ProductInstances pi
            INNER JOIN Products p ON p.id=pi.ProductId
            WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.ShahryOfflineBranchCode') is not null

            UPDATE pi
            SET Metadata = JSON_MODIFY(pi.Metadata, '$.ShahryOfflineBranchCode', null)
            FROM ProductInstances pi
            INNER JOIN Products p ON p.id=pi.ProductId
            WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.ShahryOfflineBranchCode') is not null

            UPDATE pi
            SET Metadata = JSON_MODIFY(pi.Metadata, '$.ShahryCnpBnplMerchantCode', JSON_VALUE(pi.Metadata, '$.ShahryOnlineMerchantCode'))
            FROM ProductInstances pi
            INNER JOIN Products p ON p.id=pi.ProductId
            WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.ShahryOnlineMerchantCode') is not null

            UPDATE pi
            SET Metadata = JSON_MODIFY(pi.Metadata, '$.ShahryOnlineMerchantCode', null)
            FROM ProductInstances pi
            INNER JOIN Products p ON p.id=pi.ProductId
            WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.ShahryOnlineMerchantCode') is not null

            UPDATE pi
            SET Metadata = JSON_MODIFY(pi.Metadata, '$.ShahryCnpBnplBranchCode', JSON_VALUE(pi.Metadata, '$.ShahryOnlineBranchCode'))
            FROM ProductInstances pi
            INNER JOIN Products p ON p.id=pi.ProductId
            WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.ShahryOnlineBranchCode') is not null

            UPDATE pi
            SET Metadata = JSON_MODIFY(pi.Metadata, '$.ShahryOnlineBranchCode', null)
            FROM ProductInstances pi
            INNER JOIN Products p ON p.id=pi.ProductId
            WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.ShahryOnlineBranchCode') is not null
            
            UPDATE pi
            SET Metadata = JSON_MODIFY(pi.Metadata, '$.IsShahryCnpBnplEnabled', JSON_VALUE(pi.Metadata, '$.IsShahryOnlineBnplEnabled'))
            FROM ProductInstances pi
            INNER JOIN Products p ON p.id=pi.ProductId
            WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.IsShahryOnlineBnplEnabled') is not null

            UPDATE pi
            SET Metadata = JSON_MODIFY(pi.Metadata, '$.IsShahryOnlineBnplEnabled', null)
            FROM ProductInstances pi
            INNER JOIN Products p ON p.id=pi.ProductId
            WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.IsShahryOnlineBnplEnabled') is not null

            UPDATE pi
            SET Metadata = JSON_MODIFY(pi.Metadata, '$.IsShahryCpBnplEnabled', JSON_VALUE(pi.Metadata, '$.IsShahryOfflineBnplEnabled'))
            FROM ProductInstances pi
            INNER JOIN Products p ON p.id=pi.ProductId
            WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.IsShahryOfflineBnplEnabled') is not null

            UPDATE pi
            SET Metadata = JSON_MODIFY(pi.Metadata, '$.IsShahryOfflineBnplEnabled', null)
            FROM ProductInstances pi
            INNER JOIN Products p ON p.id=pi.ProductId
            WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.IsShahryOfflineBnplEnabled') is not null
            ");
    }
}
