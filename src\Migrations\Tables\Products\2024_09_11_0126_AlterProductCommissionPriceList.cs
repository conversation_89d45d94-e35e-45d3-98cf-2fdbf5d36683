﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.Products;
[Migration(2024_09_11_0126)]
public class AlterProductCommissionPriceList : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "AlterProductCommissionPriceListingView.sql")
            );
    }
}
[Migration(2024_09_20_0547)]
public class AlterProductCommissionPriceList_AddBusinessTypeCodeToDTO : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "AlterProductCommissionPriceListingView.sql")
            );
    }
}