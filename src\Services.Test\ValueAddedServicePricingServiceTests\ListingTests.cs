﻿using AutoMapper;
using Common.Entities;
using Common.Enums.UnitPrice;
using Common.Enums;
using Common.Models.UnitPrice;
using Common.Models.ValueAddedSerivcePricing;
using Common.Repositories;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Xunit;
using Common.Enums.ProductCommisssionPrices;

namespace Services.Test.ValueAddedServicePricingServiceTests;
public class ListingTests
{
    private readonly Mock<ILogger<ValueAddedServicePricingService>> loggerMock;
    private readonly Mock<IValueAddedServicePricingRepository> valueAddedServicePricingRepositoryMock;
    private readonly Mock<IMapper> mapperMock;
    private readonly ValueAddedServicePricingService valueAddedServicePricingService;

    public ListingTests()
    {
        loggerMock = new Mock<ILogger<ValueAddedServicePricingService>>();
        valueAddedServicePricingRepositoryMock = new Mock<IValueAddedServicePricingRepository>();
        mapperMock = new Mock<IMapper>();
        valueAddedServicePricingService = new ValueAddedServicePricingService(loggerMock.Object, valueAddedServicePricingRepositoryMock.Object, mapperMock.Object);
    }
    [Fact]
    public async Task GetAddOnsPriceList_ShouldReturnData_WhenRequestIsValid()
    {
        //Arrange
        ValueAddedServicePriceListRequest request = new ValueAddedServicePriceListRequest();
        var ExpectedResponse = new ValueAddedServicePriceListResponse();
        //Mock
        valueAddedServicePricingRepositoryMock.Setup(s => s.GetValueAddedServicePricesList(request))
                                  .ReturnsAsync(ExpectedResponse);
        //Act
        var result = await valueAddedServicePricingService.GetValueAddedServicesPricesList(request);
        //Assert
        Assert.Equal(ExpectedResponse, result);
        valueAddedServicePricingRepositoryMock.Verify(repo => repo.GetValueAddedServicePricesList(request), Times.Once);
    }
    [Fact]
    public async Task GetUnitPriceList_ShouldReturnException_WhenRequestIsInvalid()
    {
        //Arrange
        ValueAddedServicePriceListRequest request = null;
        //Mock
        valueAddedServicePricingRepositoryMock.Setup(s => s.GetValueAddedServicePricesList(request))
                                  .ThrowsAsync(new ServiceException(HttpStatusCode.BadRequest));
        //Act
        var exception = await Assert.ThrowsAsync<ServiceException>(() => valueAddedServicePricingService.GetValueAddedServicesPricesList(request));
        //Assert
        Assert.IsType<ServiceException>(exception);
        Assert.Equal(HttpStatusCode.BadRequest, exception.StatusCode);
    }
    [Fact]
    public async Task GetUnitPriceList_ShouldReturnException_WhenInternalServerError()
    {
        //Arrange
        ValueAddedServicePriceListRequest request = new ValueAddedServicePriceListRequest();
        //Mock
        valueAddedServicePricingRepositoryMock.Setup(s => s.GetValueAddedServicePricesList(request))
                                  .ThrowsAsync(new ServiceException(HttpStatusCode.InternalServerError));
        //Act
        var exception = await Assert.ThrowsAsync<ServiceException>(() => valueAddedServicePricingService.GetValueAddedServicesPricesList(request));
        //Assert
        Assert.IsType<ServiceException>(exception);
        Assert.Equal(HttpStatusCode.InternalServerError, exception.StatusCode);
    }
    [Fact]
    public async Task GetAddOnsPriceByIdAsync_ShouldReturnAddOnsPrice_WhenPriceExists()
    {
        // Arrange
        var id = Guid.NewGuid();
        var expectedEntity = new ValueAddedServicePricingEntity
        {
            Id = id,
            ProductID = Guid.NewGuid(),
            MCCID = Guid.NewGuid(),
            BusinessTypeID = Guid.NewGuid(),
            VASID = Guid.NewGuid(),
            SubscriptionFee = 150,
            FeeType = VatType.Flat,
            BillingType = BillingType.PostPaid,
            BillingFrequency = PriceBillingFrequency.Monthly
        };
        var expectedResponse = new ValueAddedServicesPricingDetails
        {
            Id = id,
            ProductID = expectedEntity.ProductID,
            MCCID = expectedEntity.MCCID,
            BusinessTypeID = expectedEntity.BusinessTypeID,
            VASID = expectedEntity.VASID,
            SubscriptionFee = expectedEntity.SubscriptionFee,
            FeeType = expectedEntity.FeeType,
            BillingType = expectedEntity.BillingType,
            BillingFrequency = expectedEntity.BillingFrequency
        };

        valueAddedServicePricingRepositoryMock.Setup(repo => repo.GetAddOnsPriceByIdAsync(id))
                                              .ReturnsAsync(expectedEntity);
        mapperMock.Setup(m => m.Map<ValueAddedServicesPricingDetails>(expectedEntity))
                  .Returns(expectedResponse);

        // Act
        var result = await valueAddedServicePricingService.GetAddOnsPriceByIdAsync(id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedResponse, result);
        valueAddedServicePricingRepositoryMock.Verify(repo => repo.GetAddOnsPriceByIdAsync(id), Times.Once);
    }
}
