﻿using Common.Data.ProductType;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Common;

[ExcludeFromCodeCoverage]
public static class Errors
{
    private const string App = "PRODSVC_";

    public static (string Message, string Code) InvalidIdentifier => ("Invalid identifier", App + "InvalidIdentifier");
    public static (string Message, string Code) InvalidBindAttempt => ("Cannot bind part to same part", App + "InvalidBindAttempt");
    public static (string Message, string Code) ProductNotFound => ("Product was not found", App + "ProductNotFound");
    public static (string Message, string Code) CodeAlreadyExist => ("Product with the same code already exists", App + "DuplicateCode");
    public static (string Message, string Code) InvalidType => ($"Invalid product type. Type must be one of {string.Join(",", Enum.GetNames(typeof(ProductTypes)))}", App + "InvalidType");
    public static (string Message, string Code) PriceNotFound => ("Price was not found", App + "PriceNotFound");
    public static (string Message, string Code) ParentProductInstanceNotFound => ("Parent product instance not found.", App + "ParentProductInstanceNotFound");
    public static (string Message, string Code) UnexpectedError => ("Unexpected error.", App + "UnexpectedError");
    public static (string Message, string Code) ProductInstanceNotFound => ("Product instance not found.", App + "ProductInstanceNotFound");
    public static (string Message, string Code) InvalidDeleteParameters => ("At least one delete parameter must be present.", App + "InvalidDeleteParameters");
    public static (string Message, string Code) InvalidPatchRequest => ("Invalid patch request.", App + "InvalidPatchRequest");
    public static (string Message, string Code) InvalidCreateRequest => ("Invalid create GLE request.", App + "InvalidCreateRequest");
    public static (string Message, string Code) InvalidCategoryCode => ("Category code must be unique.", App + "InvalidCategoryCode");
    public static (string Message, string Code) CategoryNotFound => ("Category not found.", App + "CategoryNotFound");
    public static (string Message, string Code) InvalidUpdateRequest => ("Cannot update a product that is being used.", App + "InvalidUpdateRequest");
    public static (string Message, string Code) InvalidProductPriceRequest => ("Cannot create or update a price of a product that is used by a product instance.", App + "InvalidProductPriceRequest");
    public static (string Message, string Code) OppositeBindingAlreadyExists => ("Cannot bound part to product because they are already linked in the opposite direction.", App + "OppositeBindingAlreadyExists");
    public static (string Message, string Code) ExpectedGateway => ("Expected gateway product instance.", App + "ExpectedGateway");
    public static (string Message, string Code) ExpectedMeeza => ("Expected meeza product instance.", App + "ExpectedMeeza");
    public static (string Message, string Code) ExpectedTerminal => ("Expected terminal product instance.", App + "ExpectedTerminal");
    public static (string Message, string Code) InvalidQuantity => ("Quantity must be greater than 0.", App + "InvalidQuantity");
    public static (string Message, string Code) BindingAlreadyExists => ("Cannot bound part to product because they are already binded.", App + "BindingAlreadyExists");
    public static (string Message, string Code) ProductInUse => ("Cannot delete product that has configurations.", App + "ProductInUse");
    public static (string Message, string Code) ParentConfigurationNotFound => ("Parent configuration not found.", App + "ParentConfigurationNotFound");
    public static (string Message, string Code) InvalidCodeAndVersion => ("Combination of code and version already exists in the datebase.", App + "InvalidCodeAndVersion");
    public static (string Message, string Code) InvalidSalesChannel => ("Sales channel must be one of the allowed options.", App + "InvalidSalesChannel");
    public static (string Message, string Code) InvalidFlow => ("Flow must be one of the allowed options.", App + "InvalidFlow");
    public static (string Message, string Code) TerminalDataSetNotFound => ("Terminal data set was not found", App + "TerminalDataNotFound");
    public static (string Message, string Code) GetAvailableTerminalDataSetCountBadRequest => ("Acquiring ledger must contain a value.", App + "GetAvailableTerminalDataSetCountBadRequest");
    public static (string Message, string Code) UpdateMerchantProductInstanceStoreNameBadRequest => ("Update Merchant ProductInstance StoreName contains an invalid request.", App + "UpdateMerchantProductInstanceStoreNameBadRequest");
    public static (string Message, string Code) StoreAlreadyHasPayByLinkBundleBadRequest => ("Store already has a pay by link bundle defined.", App + "UpdateStorePayByLinkBundleBadRequest");
    public static (string Message, string Code) StoreAlreadyHasPaymentGatewayBundleBadRequest => ("Store already has a payment gateway bundle defined.", App + "UpdateStorePaymentGatewayBundleBadRequest");
    public static (string Message, string Code) StoreAlreadyHasPaymentGatewaysBadRequest => ("Store already has 2 payment gateways defined.", App + "UpdateStorePaymentGatewayBadRequest");
    public static (string Message, string Code) InvalidProductInstanceId => ("Invalid product instance ID.", App + "InvalidProductInstanceId");
    public static (string Message, string Code) InvalidStoreId => ("Invalid store ID.", App + "InvalidStoreId");
    public static (string Message, string Code) InvalidMerchantId => ("Invalid merchant ID.", App + "InvalidMerchantId");
    public static (string Message, string Code) InvalidOrderId => ("Invalid order ID.", App + "InvalidOrderId");
    public static (string Message, string Code) InvalidGleInstanceId => ("Invalid GLE instance ID.", App + "InvalidGleInstanceId");
    public static (string Message, string Code) UserCategoryCodeMandatory => ("User Category Code is mandatory", App + "UserCategoryCodeMandatory");
    public static (string Message, string Code) ParentGleUserIdMandatory => ("Parent GLE User Id is mandatory", App + "ParentGleUserIdMandatory");
    public static (string Message, string Code) ReferenceMMSIdMandatory => ("ReferenceMMSId is mandatory", App + "ReferenceMMSIdMandatory");
    public static (string Message, string Code) GleInstanceNotFound => ("GLE instance not found.", App + "GleInstanceNotFound");
    public static (string Message, string Code) InvalidGleInstanceStatus => ("Invalid GLE instance status.", App + "InvalidGleInstanceStatus");
    public static (string Message, string Code) InvalidGleInstanceType => ("Invalid GLE instance type. Allowed instance type: merchant, store, terminal.", App + "InvalidGleInstanceType");
    public static (string Message, string Code) InvalidAutoGeneratedAcquirer => ("Invalid acquiring bank, it must be one of the following (NBE_BANK, ALX_BANK)", App + "InvalidAutoGeneratedAcquirer");
    public static (string Message, string Code) ReachedMaxMID => ("MID number has been reached to the Max!", App + "ReachedMaxMID");
    public static (string Message, string Code) MIDMustBeNumbers => ("Terminal MID must be Number for NBE Bank!", App + "MIDMustBeNumbers");
    public static (string Message, string Code) NBEMIDFormat => ("Terminal MID must be 12 characters start with '001770' then 6 numbers for NBE Bank!", App + "NBEMIDFormat");
    public static (string Message, string Code) MpgsHistorySpCleanupError => ("Mpgs History cleanup General Error.", App + "MpgsHistoryError");
    public static (string Message, string Code) InvalidGleStatus => ("Invalid GLE status.", App + "InvalidGleStatus");
    public static (string Message, string Code) InvalidGleUserCategoryCode => ("Invalid GLE user category code.", App + "InvalidGleUserCategoryCode");
    public static (string Message, string Code) InvalidGleTerminalTid => ("Invalid GLE terminal TID.", App + "InvalidGleTerminalTid");
    public static (string Message, string Code) InvalidGleResponse => ("Invalid GLE response.", App + "InvalidGleResponse");
    public static (string Message, string Code) InvalidGleMerchantId => ("Invalid GLE merchant ID.", App + "InvalidGleMerchantId");
    public static (string Message, string Code) InvalidGleTerminalId => ("Invalid GLE terminal ID.", App + "InvalidGleTerminalId");
    public static (string Message, string Code) InvalidGleRegistrationType => ("Invalid GLE registration type. Allowed types: merchant, store, terminal.", App + "InvalidGleRegistrationType");
    public static (string Message, string Code) ALXMIDFormat => ("Terminal MID must be 8 characters start with 'RGF' then 5 numbers for ALX Bank!", App + "ALXMIDFormat");
    public static (string Message, string Code) VendorExistBefore => ("There is another vendor with the same name and terminal type!", App + "VendorExistBefore");
    public static (string Message, string Code) VendorNotFound => ("There is no vendor with the sent id!", App + "VendorNotFound");
    public static (string Message, string Code) InvalidSetDefaultVendorRequest => ("Invalid set default vendor Request", App + "InvalidSetDefaultVendorRequest");
    public static (string Message, string Code) InvalidGetProductsListRequest => ("Invalid Get Products List Request", App + "InvalidGetProductsListRequest");
    public static (string Message, string Code) InternalServerError => ("Internal Server Error!", App + "InternalServerError");
    public static (string Message, string Code) ErrorInTerminalId => ("Error in Generating Terminal ID Generation", App + "ErrorInTerminalId");
    public static (string Message, string Code) TIDNotFound => (" TID not found.", App + "TIDNotFound");
    public static (string Message, string Code) MIDNotFound => (" MID not found.", App + "MIDNotFound");
    public static (string Message, string Code) AccountIDNotFound => (" AccountID not found.", App + "AccountIDNotFound");
    public static (string Message, string Code) InvalidCommissionFeeId => ("Invalid Commission ID.", App + "InvalidCommissionFeeId");
    public static (string Message, string Code) InvalidNonTransactionalFeesId => ("Invalid NonTransactionalFees ID.", App + "InvalidNonTransactionalFeesId");
    public static (string Message, string Code) VasIdAlreadyExist => ("This VAS ID belongs to another VAS", App + "DuplicateVASId");
    public static (string Message, string Code) ValueAddedServicesIdNotFound => ("Product was not found", App + "ProductNotFound");
    public static (string Message, string Code) PendingUnitPriceNotFound => ("Pending unit price was not found", App + "PendingUnitPriceNotFound");
    public static (string Message, string Code) NotAuthorizedToReviewPendingUnitPrice => ("The user who created the prices can not be the one who approve/reject", App + "NotAuthorizedToReviewPendingUnitPrice");
    public static (string Message, string Code) PendingUnitPriceApprovalFailure => ("Something went wrong while approving ubit price", App + "PendingUnitPriceApprovalFailure");
}