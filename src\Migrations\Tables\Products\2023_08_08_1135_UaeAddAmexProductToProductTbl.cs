﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.Products;
[Migration(2023_08_08_1135)]
public class UaeAddAmexProductToProductTbl : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
DECLARE @AmexProductId_UAE AS uniqueidentifier=NEWID();
 
--add new product with type AMEX_GW to UAE

INSERT INTO [dbo].[Products]
([Id]
,[Availability]
,[Code]
,[Type]
,[ValidFrom]
,[CreatedBy]
,[CreatedDate]
,[UpdatedBy]
,[Version]
,[Counterparty],
[DisplayOrder],
[SalesChannel])
VALUES
(@AmexProductId_UAE
,'Live'
,'AMEX_GW'
,'SCHEME'
,GETUTCDATE()
,'00000000-0000-0000-0000-000000000000'
,GETUTCDATE()
,'00000000-0000-0000-0000-000000000000'
,0
,'GEIDEA_UAE',NULL,NULL)

--add new Amex_EGYPT product as part of BUNDLE

INSERT INTO ProductParts (ProductId, PartId, Quantity) VALUES
((SELECT TOP 1 Id FROM Products WHERE type='BUNDLE' AND Counterparty='GEIDEA_UAE' AND Availability='Live' AND Code='PAYMENT_GATEWAY_BUNDLE'), @AmexProductId_UAE, 1)

");
    }
}
