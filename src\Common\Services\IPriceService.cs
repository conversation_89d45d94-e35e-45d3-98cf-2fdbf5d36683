﻿using Common.Entities;
using Common.Models;
using Microsoft.AspNetCore.JsonPatch;
using System;
using System.Threading.Tasks;

namespace Common.Services;

public interface IPriceService
{
    Task<Price> CreateAsync(PriceRequest request);
    Task<Price[]> FindAsync(FindPriceRequest request, bool attach);
    Task<Price> PatchAsync(Guid priceId, JsonPatchDocument<PriceRequest> patchPrice);
    Task DeleteAsync(Guid priceId);
}
