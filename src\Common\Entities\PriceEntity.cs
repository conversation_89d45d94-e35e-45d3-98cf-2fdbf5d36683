﻿using Geidea.Utils.DataAccess.Entities;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Common.Entities;

public class PriceEntity : AuditableEntity<Guid>
{
    public string? ChargeFrequency { get; set; }
    public string ChargeType { get; set; } = string.Empty;
    public bool ExemptFromVAT { get; set; }
    public Guid ProductId { get; set; }

    [ForeignKey("ProductId")]
    public ProductEntity Product { get; set; } = null!;
    public int? PerItemPrice { get; set; }
    public int? PercentagePrice { get; set; }
    public int? Threshold { get; set; }
    /// <summary>
    /// Used for grouping prices such as "Rental", "Lease", "Purchase" prices
    /// </summary>
    public string? Group { get; set; }

    /// <summary>
    /// Could be LT,LTE,GT,GTE
    /// </summary>
    public string? ThresholdType { get; set; }

    /// <summary>
    /// Priority of price rules when Threshold is not null. 
    /// Evaluation goes from high to low and last rule that evaluates to TRUE wins.
    /// </summary>
    public int Priority { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public bool DeletedFlag { get; set; }
    public string? Currency { get; set; }
    public int? RentalPeriod { get; set; }
    public int? MaxPrice { get; set; }
}
