﻿using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.RegularExpressions;

namespace EncryptSamsungToken;

public static class Program
{
    private static readonly IConfiguration Config = new ConfigurationBuilder()
        .AddJsonFile(GetRootPath("appsettings.json"))
        .Build();

    static async Task Main()
    {
        var token = new
        {
            amount = "1000",
            currency_code = "USD",
            utc = "1490266732173",
            eci_indicator = "5",
            tokenPAN = "1234567890123456",
            tokenPanExpiration = "0420",
            cryptogram = "AK+zkbPMCORcABCD3AGRAoACFA=="
        };

        string jsonString = JsonConvert.SerializeObject(token);
        var samsungCertificate = Config.GetSection("SamsungPayCertificate").Value;
        var flow = "web"; // or "in-App"

        if (flow != "web")
        {
            var subject = $"CN=507fc666-fae2-46e5-8370-3bade8e5c1c5, O=GEIDEA, C=AE, ST=DUBAI, L=DUBAI, OU=GEIDEA, E=";

            using (RSACryptoServiceProvider rsa = new RSACryptoServiceProvider(2048))
            {
                var certificateRequest = new CertificateRequest(subject, rsa, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);

                byte[] csrBytes = certificateRequest.CreateSigningRequest();

                string base64CSR = Convert.ToBase64String(csrBytes);

                X509Certificate2 cert = certificateRequest.CreateSelfSigned(DateTimeOffset.Now, DateTimeOffset.Now.AddYears(1));

                byte[] certificateBytes = cert.Export(X509ContentType.Cert);

                var certificate = new X509Certificate2(certificateBytes).GetRSAPublicKey();

                //change Padding to Pkcs1
                var encryptedData = certificate!.Encrypt(Encoding.UTF8.GetBytes(jsonString!), RSAEncryptionPadding.OaepSHA256);

                string encryptedString = Convert.ToBase64String(encryptedData);

                Console.WriteLine("Token is : " + encryptedString);

                string privatekey = Convert.ToBase64String(rsa.ExportRSAPrivateKey());

                var patchDocument = new JsonPatchDocument<UpdateProductInstanceRequest>();
                patchDocument.Operations.AddRange(new List<Operation<UpdateProductInstanceRequest>>()
            {
               new Operation<UpdateProductInstanceRequest>() {
                op = OperationType.Replace.ToString(),
                path = $"data/samsungPayCertificatePrivateKey",
                value = privatekey
            },
            new Operation<UpdateProductInstanceRequest>
            {
                op = OperationType.Replace.ToString(),
                path = $"data/samsungPayCsr",
                value = base64CSR
            }});

                var productInstanceId = Guid.Parse("be836e44-aed2-4978-4e3b-08dacbb46ada");
                var baseUrl = "ProductServiceBaseUrl";
                string url = $"{baseUrl}/{productInstanceId}";
                var body = new StringContent(JsonConvert.SerializeObject(patchDocument), Encoding.UTF8, "application/json");

                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Add("X-CounterpartyCode", "GEIDEA_EGYPT");

                    var response = await client.PatchAsync(url, body);

                    if (!response.IsSuccessStatusCode)
                    {
                        Console.WriteLine("Patch process failed");
                    }
                }
            }
        }
        else
        {
            var certificateBytes = Convert.FromBase64String(samsungCertificate!);

            var certificate = new X509Certificate2(certificateBytes).GetRSAPublicKey();

            //replace the padding with pkcs1
            var encryptedData = certificate!.Encrypt(Encoding.UTF8.GetBytes(jsonString!), RSAEncryptionPadding.OaepSHA256);

            string encryptedString = Convert.ToBase64String(encryptedData);

            Console.WriteLine("Token is : " + encryptedString);
        }
    }

    private static string GetRootPath(string rootFilename)
    {
        var rootDir = Path.GetDirectoryName(AppDomain.CurrentDomain.BaseDirectory);
        if (rootDir == null)
            return string.Empty;

        var pathRegex = new Regex(@"(?<!fil)[A-Za-z]:\\+[\S\s]*?(?=\\+bin)");
        var appRoot = pathRegex.Match(rootDir).Value;
        var path = Path.Combine(appRoot, rootFilename);

        return path;
    }
}