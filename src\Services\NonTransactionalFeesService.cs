﻿using AutoMapper;
using Common;
using Common.Entities;
using Common.Models.businessType;
using Common.Models.CommissionFees;
using Common.Models.NonTransactionalFees;
using Common.Repositories;
using Common.Services;
using DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
namespace Services;
public class NonTransactionalFeesService : INonTransactionalFeesService
{
    #region Fields
    private readonly ILogger<NonTransactionalFeesService> logger;
    private readonly INonTransactionalFeesRepository nonTransactionalFeesRepository;
    private readonly IMapper mapper;
    #endregion

    #region Constructor
    public NonTransactionalFeesService(ILogger<NonTransactionalFeesService> logger, INonTransactionalFeesRepository nonTransactionalFeesRepository, IMapper mapper)
    {
        this.logger = logger;
        this.nonTransactionalFeesRepository = nonTransactionalFeesRepository;
        this.mapper = mapper;
    }
    #endregion

    #region ToggleStatus
    public async Task<bool> ToggleStatus(Guid NonTransactionalFeesId, bool Status)
    {
        var result = false;
        if (NonTransactionalFeesId == Guid.Empty)
        {
            logger.LogError("Invalid NonTransactionalFees Id .");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidIdentifier);
        }

        var NonTransactionalFeesObj = await nonTransactionalFeesRepository.GetByIdAsync(NonTransactionalFeesId);
        if (NonTransactionalFeesObj == null)
            return result;

        NonTransactionalFeesObj.Status = Status ? Common.Enums.Status.Active : Common.Enums.Status.Inactive;
        nonTransactionalFeesRepository.Update(NonTransactionalFeesObj);
        await nonTransactionalFeesRepository.SaveChangesAsync();
        return true;
    }
    #endregion

    #region GetNonTransactionalFeesDetails
    public async Task<NonTransactionalFeesDetailsResponse> GetNonTransactionalFeesDetails(Guid Id)
    {
        if (Id == Guid.Empty)
        {
            logger.LogError("Invalid NonTransactionalFees Id.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidIdentifier);
        }

        var NonTransactionalFeesObj = await nonTransactionalFeesRepository.GetByIdAsync(Id);
        if (NonTransactionalFeesObj == null)
        {
            logger.LogError("NonTransactional Fees with Id '{Id}' not found.", Id);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.InvalidNonTransactionalFeesId);
        }

        return mapper.Map<NonTransactionalFeesDetailsResponse>(NonTransactionalFeesObj);
    }
    #endregion

    #region GetNonTransactionalFeesList
    public async Task<GetNonTransactionalFeesListResponse> GetNonTransactionalFeesList(GetNonTransactionalFeesListRequest request)
    {
        if (request == null)
        {
            logger.LogError("Invalid Get NonTransactional Fees  List request.");
            throw new ServiceException(HttpStatusCode.BadRequest, "Invalid Get NonTransactional Fees List request.");
        }
        try
        {
            return await nonTransactionalFeesRepository.GetNonTransactionalFeesList(request);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "something has went wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, "something has went wrong!");
        }
    }
    #endregion

    #region Create

    public async Task<NonTransactionalFeesEntity> CreateAsync(NonTransactionalFeesRequest request)
    {
        var ExistedCode = await nonTransactionalFeesRepository.ExistsAsync(n => n.Code == request.Code);
        if (ExistedCode)
        {
            logger.LogError("CreateAsync: Invalid non transaction fee, Code '{code}' already exists", request.Code);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.CodeAlreadyExist);
        }
        try
        {
            var NonTransactionalFee = mapper.Map<NonTransactionalFeesEntity>(request);
            nonTransactionalFeesRepository.Add(NonTransactionalFee);
            await nonTransactionalFeesRepository.SaveChangesAsync();

            logger.LogInformation("A new transactional commission fee with Id '{Id}' and Name '{Name}'", NonTransactionalFee.Id, NonTransactionalFee.Name);
            return NonTransactionalFee;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Something has gone wrong");
            throw new ServiceException(HttpStatusCode.InternalServerError, $"Error while adding a non transactional fee: {ex.Message}");
        }
    }
    #endregion

    #region UpdateAsync
    public async Task<NonTransactionalFeesEntity> UpdateAsync(Guid id, NonTransactionalFeesRequest request)
    {
        var ExistedFee = await GetExistingFeeAsync(id, request);
        await ValidateRequestCodeAsync(ExistedFee, request);

        mapper.Map(request, ExistedFee);
        nonTransactionalFeesRepository.Update(ExistedFee);
        await nonTransactionalFeesRepository.SaveChangesAsync();

        logger.LogInformation("Updated non-transactional Fees with id '{Id}' and Code '{Code}'.", ExistedFee.Id, ExistedFee.Code);
        return ExistedFee;
    }
    #endregion

    #region Helpers
    public async Task<NonTransactionalFeesEntity> GetExistingFeeAsync(Guid id, NonTransactionalFeesRequest request)
    {
        var ExistedFee = await nonTransactionalFeesRepository.GetByIdAsync(id);
        if (ExistedFee == null)
        {
            logger.LogWarning("Non-Transactional Fee with id '{id}' not found.", id);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductNotFound);
        }
        return ExistedFee;
    }
    public async Task ValidateRequestCodeAsync(NonTransactionalFeesEntity ExistedFee, NonTransactionalFeesRequest request)
    {
        bool ExistedCode = await nonTransactionalFeesRepository.AnyAsync(s => s.Code == request.Code);
        if (ExistedFee.Code != request.Code && ExistedCode)
        {
            logger.LogError("A Non-Transactional Fee with code '{Code}' already exists.", request.Code);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.CodeAlreadyExist);
        }
    }
    #endregion
    #region Get NonTransactional Fee Names and Ids
    public async Task<List<BasicNonTransactionalFeeInfo>> GetNonTransactionalFeesNamesAsync()
    {
        try
        {
            var getNonTransactionalFeesNames = await nonTransactionalFeesRepository.GetNonTransactionalFeesNamesAsync();
            return getNonTransactionalFeesNames;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while retrieving product IDs and names.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An error occurred while retrieving product data.");
        }
    }
    #endregion
}

