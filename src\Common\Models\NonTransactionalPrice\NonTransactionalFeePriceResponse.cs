﻿using Common.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.NonTransactionalPrice;
public class NonTransactionalFeePriceResponse
{
    public List<NonTransactionalPriceEntity> NewNonTransactionalPriceList { get; set; } = new List<NonTransactionalPriceEntity>();
    public List<NonTransactionalPriceEntity> NewExistedNonTransactionalPriceList { get; set; } = new List<NonTransactionalPriceEntity>();
    public List<NonTransactionalPriceEntity>? OldExistedNonTransactionalPriceList { get; set; } = new List<NonTransactionalPriceEntity>();
}
