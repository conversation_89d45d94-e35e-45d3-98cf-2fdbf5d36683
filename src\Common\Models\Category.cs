﻿using System;
using System.Collections.Generic;

namespace Common.Models;

public class Category
{
    public string Code { get; set; } = null!;
    public int Type { get; set; }
    public bool DeletedFlag { get; set; } = false;
    public int? DisplayOrder { get; set; }

    public Guid? ParentId { get; set; }
    public Category? Parent { get; set; }
    public string? SalesChannel { get; set; }
    public string? Flow { get; set; }

    public List<Category> Subcategories { get; set; } = new List<Category>();

    public string? Counterparty { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedDateUtc { get; set; }
    public string? UpdatedBy { get; set; }
    public DateTime? UpdatedDateUtc { get; set; }
    public Guid Id { get; set; }
}
