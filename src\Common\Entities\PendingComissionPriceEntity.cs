﻿using Common.Enums.ProductCommisssionPrices;
using Common.Enums.UnitPrice;
using Geidea.Utils.DataAccess.Entities;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Common.Entities;
public class PendingComissionPriceEntity : AuditableEntity<Guid>
{
    public Guid? ProductCommissionPriceId { get; set; }
    [ForeignKey(nameof(ProductCommissionPriceId))]
    public ProductCommissionPriceEntity ProductCommissionPriceEntity { get; set; } = null!;
    public Guid ProductId { get; set; }
    public ProductEntity Product { get; set; } = null!;
    public Guid MccId { get; set; }
    public Mcc Mcc { get; set; } = null!;
    public Guid BusinessTypeId { get; set; }
    public BusinessTypeEntity BusinessType { get; set; } = null!;
    public Guid CommissionFeesId { get; set; }

    [ForeignKey(nameof(CommissionFeesId))]
    public CommissionFeesEntity CommissionFeesEntity { get; set; } = null!;
    public decimal? FeeValue { get; set; }
    public VatType VATType { get; set; }
    public BillingType BillingType { get; set; }
    public PriceBillingFrequency BillingFrequency { get; set; }
    public decimal? NewFeeValue { get; set; }
    public ActionTypesEnum ActionType { get; set; }
    public StatusEnum Status { get; set; }
    public Guid? ReviewedBy { get; set; }
    public DateTime? ReviewedDate { get; set; }
}
