﻿BEGIN TRANSACTION;

-- Update Go Air (SoftPOS) Bundle Display Order With respect to Other Bundles

update [PRODUCTS].[dbo].[Products] set DisplayOrder = 1 where CounterParty ='GEIDEA_EGYPT' and availability ='Live' and Type ='BUNDLE' and Code ='GO_SMART'
update [PRODUCTS].[dbo].[Products] set DisplayOrder = 2 where CounterParty ='GEIDEA_EGYPT' and availability ='Live' and Type ='BUNDLE' and Code ='GO_SMART_BP'
update [PRODUCTS].[dbo].[Products] set DisplayOrder = 3 where CounterParty ='GEIDEA_EGYPT' and availability ='Live' and Type ='BUNDLE' and Code ='GO_AIR'
update [PRODUCTS].[dbo].[Products] set DisplayOrder = 4 where CounterParty ='GEIDEA_EGYPT' and availability ='Live' and Type ='BUNDLE' and Code ='PAY_BY_LINK_BUNDLE'

-- Go Air Bundle Id 
DECLARE @ProductBundleId  UNIQUEIDENTIFIER
SELECT TOP 1 @ProductBundleId = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_EGYPT' AND Code = 'GO_AIR'

-- Card schemes
DECLARE @MeezaScheme UNIQUEIDENTIFIER
SELECT TOP 1 @MeezaScheme = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_EGYPT' AND Code = 'MEEZA'

DECLARE @VisaScheme UNIQUEIDENTIFIER
SELECT TOP 1 @VisaScheme = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_EGYPT' AND Code = 'VISA'

DECLARE @MasterCardScheme UNIQUEIDENTIFIER
SELECT TOP 1 @MasterCardScheme = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_EGYPT' AND Code = 'MC'

-- Associations
INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@ProductBundleId, @MeezaScheme)
INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@ProductBundleId, @VisaScheme)
INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@ProductBundleId, @MasterCardScheme)

COMMIT;