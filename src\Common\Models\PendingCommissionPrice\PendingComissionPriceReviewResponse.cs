﻿using System;
using System.Collections.Generic;

namespace Common.Models.PendingCommissionPrice;
public class PendingComissionPriceReviewResponse
{
    public List<Guid> ReviewedComissionPrices { get; set; } = new();
    public List<FailedComissionPrice> FailedComissionPrices { get; set; } = new();
}

public class FailedComissionPrice
{
    public Guid Id { get; set; }
    public string? ErrorCode { get; set; }
}