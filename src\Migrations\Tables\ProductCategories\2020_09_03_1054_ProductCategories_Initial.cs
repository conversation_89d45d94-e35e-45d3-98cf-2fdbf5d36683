﻿using FluentMigrator;

namespace Migrations.Tables.ProductCategories;

[Migration(2020_09_03_1054)]
public class ProductCategories_Initial : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("ProductCategories")
          .WithColumn("ProductId").AsGuid().NotNullable().PrimaryKey().ForeignKey("Products", "Id")
          .WithColumn("CategoryId").AsGuid().NotNullable().PrimaryKey().ForeignKey("Category", "Id")
          ;
    }
}
