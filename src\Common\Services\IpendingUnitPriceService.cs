﻿using Common.Models.PendingUnitPrice;
using Common.Models.UnitPrice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Services;
public interface IpendingUnitPriceService
{
    Task<UnitPriceCreateResponse> CreateAsync(UnitPriceCreateRequest unitPriceCreateRequest);
    Task UpdateAsync(Guid id, UnitPriceUpdateRequest updateRequest);
    Task<PendingUnitPricesListResponse> GetPendingUnitPricesList(PendingUnitPricesListRequest request);
    Task<UnitPriceBulkEditResponse> BulkEdit(UnitPriceCreateRequest request);
    Task<UnitPriceBulkDeleteResponse> DeleteUnitPricesAsync(UnitPriceBulkDeleteRequest request);
    Task<PendingUnitPriceReviewResponse> ReviewAsync(PendingUnitPriceReviewRequest request);
}
