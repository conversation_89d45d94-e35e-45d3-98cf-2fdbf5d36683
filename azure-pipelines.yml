trigger:
- dev
- test
- release/*
- uae-dev
- uae-test

pool:
  name: 'GD-Azure' 

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'
  build.MajorVersion: 1
  build.MinorVersion: 2

name: $(build.MajorVersion).$(build.MinorVersion).$(rev:r)

resources:
  repositories:
    - repository: devops
      type: git
      name: GeideaPaymentGateway/DevOps.Scripts

steps:
- checkout: self
  persistCredentials: true

- template: config-service.yml@devops

- task: NuGetAuthenticate@0
- task: NuGetToolInstaller@1

- task: Bash@3
  displayName: 'Check if the the source branch is allowed for the PR'
  inputs:
   targetType: 'inline'
   script: |
     echo
     echo 'The only and only source branch allowed for merging into test is the DEV branch!!!'
     echo
     exit 1
  condition: and(ne(variables['System.PullRequest.SourceBranch'], 'refs/heads/dev'), contains(variables['System.PullRequest.TargetBranch'], 'refs/heads/test'))


- task: UseDotNet@2
  displayName: 'Use .Net Core sdk 3.x'
  inputs:
    packageType: 'sdk'
    version: 3.x

- task: UseDotNet@2
  displayName: 'Use .Net SDK 6.0.x'
  inputs:
    packageType: 'sdk'
    version: 6.0.x
    installationPath: $(Agent.TempDirectory)/dotnet

- task: DotNetCoreCLI@2
  displayName: Restore
  inputs:
    command: 'restore'
    projects: '**/*.sln'
    feedsToUse: 'config'
    nugetConfigPath: '$(Build.SourcesDirectory)/src/nuget.config'
    noCache: true

- task: DotNetCoreCLI@2
  displayName: Build
  inputs:
    command: build
    projects: |
        **/*.sln
    arguments: --configuration $(BuildConfiguration) --no-restore -p:Version=$(Build.BuildNumber)

- task: DotNetCoreCLI@2
  displayName: Run unit tests
  inputs:
    command: test
    projects: |
        **/*.Test*.csproj
    arguments: --configuration $(BuildConfiguration) --no-build /p:CollectCoverage=true /p:CoverletOutputFormat=opencover /p:IncludeTestAssembly=false
    
- task: PowerShell@2
  displayName: Git Changes
  name: gitChanges
  inputs:
    targetType: 'inline'
    script: |
        $editedFiles = (git diff HEAD~1 HEAD --name-only)
        $editedFiles | ForEach-Object {
            Write-Host $_;
            if($_ -like "*/models/*") {
                Write-Host Changes found in $_;
                Write-Host "##vso[task.setvariable variable=packagePublish;isOutput=true]true";
                break;
            }
        }
  condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'), eq(variables['Build.SourceBranchName'], 'dev'))

- task: DotNetCoreCLI@2
  displayName: 'Pack models'
  inputs:
    command: 'pack'
    packagesToPack: '**/*Models.csproj'
    nobuild: true
    versioningScheme: 'byBuildNumber'

- task: DotNetCoreCLI@2
  displayName: 'Publish NuGet package'
  inputs:
    command: 'push'
    packagesToPush: '$(Build.ArtifactStagingDirectory)/*.nupkg'
    nuGetFeedType: 'internal'
    publishVstsFeed: 'f50ad5dc-f078-4d5c-a6d3-d384c0414d73'
  condition: and(succeeded(), eq(variables['gitChanges.packagePublish'], 'true'))

- task: DotNetCoreCLI@2
  displayName: 'Prepare docked image'
  inputs:
    command: 'publish'
    publishWebProjects: true
    arguments: '--configuration Release -p:Version=$(Build.BuildNumber) --output "$(build.artifactstagingdirectory)" --runtime=linux-musl-x64 --source "https://pkgs.dev.azure.com/GeideaPaymentGateway/_packaging/f50ad5dc-f078-4d5c-a6d3-d384c0414d73/nuget/v3/index.json"'
    zipAfterPublish: false

- task: Docker@2
  displayName: 'Build docker image'
  inputs:
    containerRegistry: geidea
    command: build
    buildContext: '$(build.artifactstagingdirectory)/ProductService'
    repository: product-service
    tags: |
     $(Build.SourceBranchName)-$(Build.BuildNumber)

- task: Docker@2
  displayName: 'Build docker image'
  inputs:
    containerRegistry: geidea
    command: build
    buildContext: '$(build.artifactstagingdirectory)/ProductService'
    repository: uaeproduct-service
    tags: |
     dev-$(Build.BuildNumber)
- task: Docker@2
  displayName: 'Build docker image'
  inputs:
    containerRegistry: geidea
    command: build
    buildContext: '$(build.artifactstagingdirectory)/ProductService'
    repository: uaeproduct-service
    tags: |
     test-$(Build.BuildNumber)

- task: Docker@2
  displayName: 'Push docker image'
  inputs:
    containerRegistry: geidea
    command: push
    repository: product-service
    tags: |
     $(Build.SourceBranchName)-$(Build.BuildNumber)
  condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))

- task: Docker@2
  displayName: 'Push docker image'
  inputs:
    containerRegistry: geidea
    command: push
    repository: uaeproduct-service
    tags: |
     dev-$(Build.BuildNumber)
  condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))

- task: Docker@2
  displayName: 'Push docker image'
  inputs:
    containerRegistry: geidea
    command: push
    repository: uaeproduct-service
    tags: |
     test-$(Build.BuildNumber)
  condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))

- task: GitTag@5
  displayName: 'Tag sources with the build number'
  inputs:
    workingdir: '$(SYSTEM.DEFAULTWORKINGDIRECTORY)'
    tag: '$(Build.BuildNumber)'
    tagMessage: '$(Build.BuildNumber)'
    useLightweightTags: true
  condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))