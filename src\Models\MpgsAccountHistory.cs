﻿using Geidea.ProductService.Models;
using Models.Rules.Common;
using System;
using System.Collections.Generic;
using System.Text;

namespace Geidea.ProductService.Models;
public class MpgsAccountHistory : MpgsAccount
{
    public MpgsAccountHistory()
    {
        BankProvider = string.Empty;
    }
    public string BankProvider { get; set; }
    public string? Operation { get; set; }
    public DateTimeOffset DeactivatedDate { get; set; }

}


