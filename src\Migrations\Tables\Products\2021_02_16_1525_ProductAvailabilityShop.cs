﻿using Common;
using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_02_16_1525)]
public class ProductAvailabilityShop : ForwardOnlyMigration
{
    public override void Up()
    {
        Alter.Table("Products").AddColumn("Flow").AsString(255).NotNullable().WithDefaultValue(Constants.Flow.Normal);
        Alter.Table("Products").AddColumn("SalesChannel").AsString(255).NotNullable().WithDefaultValue(Constants.SalesChannel.All);

        Execute.Sql(@"ALTER TABLE [dbo].[Products] DROP  CONSTRAINT [DF_Products_Availability]
                            ALTER TABLE [dbo].[Products] ADD  CONSTRAINT [DF_Products_Availability]  DEFAULT (N'Live') FOR [Availability]");

        Execute.Script(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Scripts", "BreakdownAvailability.sql"));
    }
}
