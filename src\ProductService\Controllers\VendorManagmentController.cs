﻿using Common.Models.Vendor;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace ProductService.Controllers;

[Route("api/v1/[controller]")]
[ApiController]
public class VendorManagmentController : ControllerBase
{
    private readonly IVendorService vendorService;

    public VendorManagmentController(IVendorService vendorService)
    {
        this.vendorService = vendorService;
    }

    [HttpPost]
    [Produces("application/json")]
    [ProducesResponseType(typeof(VendorResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Create([FromBody] VendorRequest request)
    {
        var result = await vendorService.CreateAsync(request);

        return Ok(result);
    }

    [HttpGet("{id}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(VendorResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetByIdAsync(Guid id)
    {
        var vendor = await vendorService.GetByIdAsync(id);

        if (vendor == null)
            return NotFound();

        return Ok(vendor);
    }

    [HttpGet]
    [Produces("application/json")]
    [ProducesResponseType(typeof(VendorResponse[]), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetAll([FromQuery] string? terminalType = null)
    {
        var vendors = await vendorService.GetAsync(terminalType);

        if (vendors == null)
            return NotFound();

        return Ok(vendors);
    }

    /// <summary>
    /// Returns all Vendors with info matching the search criteria.
    /// </summary>
    /// <response code="200">Search was successfull</response>
    [HttpPost("advancedSearch")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(VendorResponse[]), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> AdvancedSearch([FromBody] VendorSearchRequest searchRequest)
    {
        var result = await vendorService.AdvancedSearch(searchRequest);

        if (result == null)
            return NotFound();

        return Ok(result);
    }

    [HttpPatch("makeDefault/{vendorId}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Patch(Guid vendorId)
    {
        var response = await vendorService.SetDefaultVendorAsync(vendorId);
        return Ok(response);
    }

    [HttpGet("default")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(VendorResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetDefaultVendor()
    {
        var defaultVendor = await vendorService.GetDefaultVendorAsync();
        return Ok(defaultVendor);
    }
}
