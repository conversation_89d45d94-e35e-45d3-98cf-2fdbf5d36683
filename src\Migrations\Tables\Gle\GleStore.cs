﻿using FluentMigrator;

namespace Migrations.Tables.Gle;

[Migration(2022_01_11_1110)]
public class GleStore : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("GleStore")
            .WithColumn("Id").AsGuid().PrimaryKey().WithDefaultValue(SystemMethods.NewGuid)
            .WithColumn("GleUserId").AsString(250).Nullable()
            .WithColumn("GleLoginId").AsString(250).Nullable()
            .WithColumn("GleLoginId2").AsString(250).Nullable()
            .WithColumn("ParentGleUserId").AsString(250).Nullable()
            .WithColumn("GleRegistrationStatus").AsString(250).NotNullable()
            .WithColumn("GleRegistrationResponse").AsString(int.MaxValue).NotNullable()
            .WithColumn("GleMerchantId").AsGuid().NotNullable().ForeignKey("GleMerchant", "Id")
            .WithColumn("StoreId").AsGuid().NotNullable()
            .WithColumn("ReferenceMmsId").AsString(250).Nullable()
            .WithColumn("CreatedBy").AsString(150).NotNullable()
            .WithColumn("CreatedDate").AsDateTime2().NotNullable()
            .WithColumn("UpdatedBy").AsString(150).Nullable()
            .WithColumn("UpdatedDate").AsDateTime2().Nullable();

        Execute.Sql(@"CREATE UNIQUE INDEX GleStore_StoreId_UniqueIndex ON [dbo].[GleStore] ([StoreId]);");

        if (!Schema.Schema("History").Exists())
        {
            Create.Schema("History");
        }

        Execute.Sql(@"ALTER TABLE [dbo].[GleStore] ADD
              [StartDate] DATETIME2 GENERATED ALWAYS AS ROW START HIDDEN CONSTRAINT DF_GleStore_StartDate DEFAULT SYSUTCDATETIME(),
              [EndDate] DATETIME2 GENERATED ALWAYS AS ROW END HIDDEN CONSTRAINT DF_GleStore_EndDate DEFAULT CONVERT(DATETIME2, '9999-12-31 23:59:59'),
              PERIOD FOR SYSTEM_TIME(StartDate, EndDate);");

        Execute.Sql("ALTER TABLE [dbo].[GleStore] SET (SYSTEM_VERSIONING = ON (HISTORY_TABLE = [History].[GleStore]));");
    }
}

[Migration(2023_02_09_1210)]
public class GleStoreAddUserCategoryCodeNullable : ForwardOnlyMigration
{
    public override void Up()
    {
        Alter.Table("GleStore").AddColumn("UserCategoryCode").AsString(256).Nullable();
    }
}