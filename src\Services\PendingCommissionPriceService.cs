﻿using Common.Models.PendingCommissionPrice;
using Common.Models.ProductCommissionPrice;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Common.Enums.UnitPrice;
using System.Threading.Tasks;
using Common.Repositories;
using Common.Entities;
using Common.Services;
using System.Linq;
using AutoMapper;
using System.Net;
using Common;
using System;

namespace Services;
public class PendingCommissionPriceService : IPendingCommissionPriceService
{
    #region Fields
    private readonly ILogger<PendingCommissionPriceService> logger;
    private readonly IProductCommissionPriceRepository productCommissionPriceRepository;
    private readonly IPendingCommissionPriceRepository pendingCommissionPriceRepository;

    private readonly IMapper mapper;
    #endregion

    #region Constructor
    public PendingCommissionPriceService(ILogger<PendingCommissionPriceService> logger, IProductCommissionPriceRepository productCommissionPriceRepository, IMapper mapper,
        IPendingCommissionPriceRepository pendingCommissionPriceRepository)
    {
        this.logger = logger;
        this.productCommissionPriceRepository = productCommissionPriceRepository;
        this.mapper = mapper;
        this.pendingCommissionPriceRepository = pendingCommissionPriceRepository;
    }
    #endregion

    #region Create
    public async Task<CommissionPriceCreateResponse> CreateAsync(ProductCommissionPriceCreateRequest request)
    {
        var (commissionPrices, existingCommissionPrices, existingPendingCommissionPrices) = await ProcessPendingComissionPricesAsync(request);

        if (commissionPrices?.Any() == true)
            await SavePendingCommissionPricesAsync(commissionPrices);

        return new CommissionPriceCreateResponse
        {
            HasPendingCommissionPricesCreated = commissionPrices?.Any() == true,
            HasExistingCommissionPrices = existingCommissionPrices?.Any() == true,
            HasExistingPendingCommissionPrices = existingPendingCommissionPrices?.Any() == true,
        };
    }
    private async Task<(List<PendingComissionPriceEntity> unitPrices, List<ProductCommissionPriceEntity> existingUnitPrices, List<PendingComissionPriceEntity> existingPendingUnitPrices)>
     ProcessPendingComissionPricesAsync(ProductCommissionPriceCreateRequest request)
    {
        var pendingComissionPricesToCreate = new List<PendingComissionPriceEntity>();
        var existingComissionPricesList = new List<ProductCommissionPriceEntity>();
        var existingPendingComissionPriceList = new List<PendingComissionPriceEntity>();

        var allExistedCommissionPrices = await FetchExistedCommissionPrices(request);
        var allExistedPendingCommissionPrices = await FetchExistedPendingCommissionPrices(request);

        foreach (var productId in request.ProductIds)
        {
            foreach (var mccId in request.MccIds)
            {
                foreach (var businessTypeId in request.BusinessTypeIds)
                {
                    foreach (var commissionFeeId in request.CommissionFeeIds)
                    {
                        var existingCommissionPrice = allExistedCommissionPrices.FirstOrDefault(up =>
                                            up.ProductID == productId && up.MCCID == mccId && up.BusinessTypeID == businessTypeId && up.CommissionFeeID == commissionFeeId);

                        var existingPendingCommissionPrice = allExistedPendingCommissionPrices.FirstOrDefault(up =>
                                                up.ProductId == productId && up.MccId == mccId && up.BusinessTypeId == businessTypeId && up.CommissionFeesId == commissionFeeId &&
                                                up.Status == ((int)StatusEnum.Pending));

                        if (existingCommissionPrice == null)
                        {
                            if (existingPendingCommissionPrice == null)
                            {
                                var pendingCommissionPrice = CreateNewPendingComissionPrices(request, productId, mccId, businessTypeId, commissionFeeId);
                                pendingComissionPricesToCreate.Add(pendingCommissionPrice);
                            }
                            else
                            {
                                existingPendingComissionPriceList.Add(existingPendingCommissionPrice);
                            }
                        }
                        else
                        {
                            existingComissionPricesList.Add(existingCommissionPrice);

                            if (existingPendingCommissionPrice != null)
                                existingPendingComissionPriceList.Add(existingPendingCommissionPrice);
                            else
                            {
                                var pendingCommissionPrice = CreateNewPendingComissionPrices(request, productId, mccId, businessTypeId, commissionFeeId);
                                pendingComissionPricesToCreate.Add(pendingCommissionPrice);
                            }
                        }
                    }
                }
            }
        }
        return (pendingComissionPricesToCreate, existingComissionPricesList, existingPendingComissionPriceList);
    }
    #endregion

    #region Bulk Edit
    public async Task<ComissionPriceBulkEditResponse> BulkEdit(ProductCommissionPriceCreateRequest request)
    {
        var (pendingComissionPricesToCreate, existingComissionPricesList, existingPendingComissionPricesList) = await ProcessComissionPriceBulkEdit(request);

        if (pendingComissionPricesToCreate?.Any() == true)
            await SavePendingCommissionPricesAsync(pendingComissionPricesToCreate);

        return new ComissionPriceBulkEditResponse
        {
            HasPendingCommissionPricesCreated = pendingComissionPricesToCreate?.Any() == true,
            HasExistingPendingCommissionPrices = existingPendingComissionPricesList?.Any() == true,
            HasExistingCommissionPrices = existingComissionPricesList?.Any() == true,
        };
    }
    public async Task<(List<PendingComissionPriceEntity> commissionPrices, List<ProductCommissionPriceEntity> existinCommissionPrices,
    List<PendingComissionPriceEntity> existingPendingComissionPrices)> ProcessComissionPriceBulkEdit(ProductCommissionPriceCreateRequest request)
    {
        var pendingComissionPricesToCreate = new List<PendingComissionPriceEntity>();
        var existingComissionPricesList = new List<ProductCommissionPriceEntity>();
        var existingPendingComissionPricesList = new List<PendingComissionPriceEntity>();

        var allExistedComissionPricesList = await FetchExistedCommissionPrices(request);
        var allExistedPendingComissionPricesList = await FetchExistedPendingCommissionPrices(request);

        foreach (var productId in request.ProductIds)
        {
            foreach (var mccId in request.MccIds)
            {
                foreach (var businessTypeId in request.BusinessTypeIds)
                {
                    foreach (var commissionFeeId in request.CommissionFeeIds)
                    {
                        var existingComissionPrice = allExistedComissionPricesList.FirstOrDefault(up =>
                        up.ProductID == productId && up.MCCID == mccId && up.BusinessTypeID == businessTypeId && up.CommissionFeeID == commissionFeeId);

                        var existingPendingComissionPrice = allExistedPendingComissionPricesList.FirstOrDefault(up =>
                           up.ProductId == productId && up.MccId == mccId && up.BusinessTypeId == businessTypeId && up.CommissionFeesId == commissionFeeId);

                        if (existingComissionPrice == null)
                        {
                            if (existingPendingComissionPrice != null)
                                existingPendingComissionPricesList.Add(existingPendingComissionPrice);
                            else
                            {
                                var pendingComissionPrice = CreateNewPendingComissionPrices(request, productId, mccId, businessTypeId, commissionFeeId);
                                pendingComissionPrice.ActionType = ActionTypesEnum.Add;
                                pendingComissionPricesToCreate.Add(pendingComissionPrice);
                            }
                        }
                        else
                        {
                            existingComissionPricesList.Add(existingComissionPrice);

                            if (existingPendingComissionPrice != null)
                                existingPendingComissionPricesList.Add(existingPendingComissionPrice);
                            else
                            {
                                var pendingComissionPrice = HandleBulkUpdateAction(existingComissionPrice, request);
                                pendingComissionPricesToCreate.Add(pendingComissionPrice);
                            }
                        }
                    }
                }
            }
        }
        return (pendingComissionPricesToCreate, existingComissionPricesList, existingPendingComissionPricesList);
    }
    private PendingComissionPriceEntity HandleBulkUpdateAction(ProductCommissionPriceEntity existingProductCommissionPrice, ProductCommissionPriceCreateRequest request)
    {
        var pendingCommissionPrice = mapper.Map<PendingComissionPriceEntity>(existingProductCommissionPrice);
        pendingCommissionPrice.ProductCommissionPriceId = existingProductCommissionPrice.Id;
        pendingCommissionPrice.ActionType = ActionTypesEnum.Edit;
        pendingCommissionPrice.NewFeeValue = request.FeeValue;
        pendingCommissionPrice.CreatedBy = request.CreatedBy;

        return pendingCommissionPrice;
    }
    #endregion

    #region Update
    public async Task UpdateAsync(Guid id, ProductComissionPriceUpdateRequest request)
    {
        var existingCommissionPrice = await productCommissionPriceRepository.GetByIdAsync(id);
        if (existingCommissionPrice == null)
        {
            logger.LogWarning("No unit price found with Id {Id}.", id);
            throw new ServiceException(HttpStatusCode.NotFound, "Unit price not found.");
        }
        else
        {
            var pendingCommissionPrice = await pendingCommissionPriceRepository.FirstOrDefaultAsync(x => x.ProductCommissionPriceId == id && x.Status == StatusEnum.Pending);
            if (pendingCommissionPrice == null)
            {
                await HandleUpdateAction(existingCommissionPrice, request);
            }
            else
            {
                logger.LogWarning("No commission price found with Id {Id}.", id);
                throw new ServiceException(HttpStatusCode.BadRequest, "pending request already existed for this commission price record.");
            }
        }
    }
    private async Task HandleUpdateAction(ProductCommissionPriceEntity existingComissionPrice, ProductComissionPriceUpdateRequest request)
    {
        var pendingCommissionPrice = new PendingComissionPriceEntity();

        pendingCommissionPrice.ProductCommissionPriceId = existingComissionPrice.Id;
        pendingCommissionPrice.FeeValue = existingComissionPrice.FeeValue;
        pendingCommissionPrice.ProductId = existingComissionPrice.ProductID;
        pendingCommissionPrice.MccId = existingComissionPrice.MCCID;
        pendingCommissionPrice.BusinessTypeId = existingComissionPrice.BusinessTypeID;
        pendingCommissionPrice.CommissionFeesId = existingComissionPrice.CommissionFeeID;
        pendingCommissionPrice.ActionType = ActionTypesEnum.Edit;
        pendingCommissionPrice.NewFeeValue = request.FeeValue;
        pendingCommissionPrice.VATType = (VatType)request.FeeType;
        pendingCommissionPrice.BillingType = (BillingType)request.BillingType;
        pendingCommissionPrice.BillingFrequency = request.BillingFrequency;
        pendingCommissionPrice.CreatedBy = request.CreatedBy;

        pendingCommissionPriceRepository.Add(pendingCommissionPrice);
        await pendingCommissionPriceRepository.SaveChangesAsync();
    }
    #endregion

    #region Delete
    public async Task<(List<PendingComissionPriceEntity> comissionPrices, List<PendingComissionPriceEntity> existingPendingComissionPrices)> ProcessgComissionPriceBulkDelete(List<Guid> comissionPricesIds, string UserId)
    {
        var pendingComissionPricesToCreate = new List<PendingComissionPriceEntity>();
        var existingPendingComissionPricesList = new List<PendingComissionPriceEntity>();

        var existingComissionPricesList = await productCommissionPriceRepository.WhereAsync(u => comissionPricesIds.Contains(u.Id));
        foreach (var comissionPrice in existingComissionPricesList)
        {
            var pendingUnitPrice = await pendingCommissionPriceRepository.FirstOrDefaultAsync(up => up.ProductCommissionPriceId == comissionPrice.Id && up.Status == StatusEnum.Pending);
            if (pendingUnitPrice == null)
            {
                var pendingComissionPriceToCreate = mapper.Map<PendingComissionPriceEntity>(comissionPrice);
                pendingComissionPriceToCreate.ProductCommissionPriceId = comissionPrice.Id;
                pendingComissionPriceToCreate.ActionType = ActionTypesEnum.Delete;
                pendingComissionPriceToCreate.Status = StatusEnum.Pending;
                pendingComissionPriceToCreate.CreatedBy = UserId;
                pendingComissionPricesToCreate.Add(pendingComissionPriceToCreate);
            }
            else
            {
                existingPendingComissionPricesList.Add(pendingUnitPrice);
            }
        }

        return (pendingComissionPricesToCreate, existingPendingComissionPricesList);
    }
    public async Task<CommissionPriceBulkDeleteResponse> DeleteComissionPricesAsync(ComissionPriceBulkDeleteRequest request)
    {
        var (pendingComissionPricesToCreate, existingPendingComissionPricesList) = await ProcessgComissionPriceBulkDelete(request.CommissionPricesIds, request.UserId);

        if (pendingComissionPricesToCreate?.Any() == true)
            await SavePendingCommissionPricesAsync(pendingComissionPricesToCreate);

        return new CommissionPriceBulkDeleteResponse
        {
            HasPendingCommissionPricesCreated = pendingComissionPricesToCreate?.Any() == true,
            HasExistingPendingCommissionPrices = existingPendingComissionPricesList?.Any() == true,
        };
    }
    #endregion

    #region Review Process
    private async Task<(PendingComissionPriceEntity? pendingComissionPriceEntity, string? errorCode)> ValidateReviewProcessAsync(Guid id, Guid? createdBy)
    {
        var pendingComissionPrice = await pendingCommissionPriceRepository.FirstOrDefaultAsync(x => x.Id == id);

        if (pendingComissionPrice == null)
            return (null, Errors.PendingUnitPriceNotFound.Code);

        if (!IsAuthorizedToReviewPendingRequest(pendingComissionPrice, createdBy))
            return (null, Errors.NotAuthorizedToReviewPendingUnitPrice.Code);

        return (pendingComissionPrice, null);
    }
    public async Task<PendingComissionPriceReviewResponse> ReviewAsync(PendingComissionPriceReviewRequest request)
    {
        var response = request.Status switch
        {
            StatusEnum.Approved => await ApproveAsync(request),
            StatusEnum.Rejected => await RejectAsync(request),
            _ => new PendingComissionPriceReviewResponse()
        };

        return response;
    }
    private async Task<PendingComissionPriceReviewResponse> RejectAsync(PendingComissionPriceReviewRequest request)
    {
        var response = new PendingComissionPriceReviewResponse();

        foreach (var id in request.Ids)
        {
            var (pendingComissionPrice, errorCode) = await ValidateReviewProcessAsync(id, request.CreatedBy);
            if (errorCode != null || pendingComissionPrice == null)
            {
                response.FailedComissionPrices.Add(new FailedComissionPrice { Id = id, ErrorCode = errorCode });
                continue;
            }

            var pendingComissionPriceRequest = await pendingCommissionPriceRepository.FirstOrDefaultAsync(x => x.Id == id);

            UpdatePendingComissionPrice(pendingComissionPriceRequest, request);
            await pendingCommissionPriceRepository.SaveChangesAsync();
            response.ReviewedComissionPrices.Add(id);
        }

        return response;
    }
    private async Task<PendingComissionPriceReviewResponse> ApproveAsync(PendingComissionPriceReviewRequest request)
    {
        var response = new PendingComissionPriceReviewResponse();

        foreach (var id in request.Ids)
        {
            var (pendingComissionPrice, errorCode) = await ValidateReviewProcessAsync(id, request.CreatedBy);
            if (errorCode != null || pendingComissionPrice == null)
            {
                response.FailedComissionPrices.Add(new FailedComissionPrice { Id = id, ErrorCode = errorCode });
                continue;
            }

            bool approvalResponse = pendingComissionPrice.ActionType switch
            {
                ActionTypesEnum.Add => await HandleAddActionApproval(pendingComissionPrice, request),
                ActionTypesEnum.Edit => await HandleEditActionApproval(pendingComissionPrice, request),
                ActionTypesEnum.Delete => await HandleDeleteActionApproval(pendingComissionPrice, request),
                _ => false
            };

            if (!approvalResponse)
            {
                response.FailedComissionPrices.Add(new FailedComissionPrice { Id = id, ErrorCode = Errors.PendingUnitPriceApprovalFailure.Code });
                continue;
            }
            response.ReviewedComissionPrices.Add(id);
        }

        return response;
    }
    private bool IsAuthorizedToReviewPendingRequest(PendingComissionPriceEntity pendingComissionPriceEntity, Guid? CreatedBy)
    {
        return pendingComissionPriceEntity.CreatedBy != CreatedBy.ToString();
    }
    private async Task<bool> HandleAddActionApproval(PendingComissionPriceEntity pendingComissionPrice, PendingComissionPriceReviewRequest request)
    {
        // Get SQL Server execution strategy
        var strategy = pendingCommissionPriceRepository.CreateExecutionStrategy();

        return await strategy.ExecuteAsync(async () =>
        {
            using var transaction = await pendingCommissionPriceRepository.BeginTransactionAsync();
            try
            {
                // Share transaction with both repositories
                productCommissionPriceRepository.UseTransaction(transaction);
                pendingCommissionPriceRepository.UseTransaction(transaction);

                // Map and add new Unit Price record
                var unitPrice = mapper.Map<ProductCommissionPriceEntity>(pendingComissionPrice);
                unitPrice.CommissionFeeID = pendingComissionPrice.CommissionFeesId;
                unitPrice.FeeType = (Common.Enums.ProductCommisssionPrices.FeeType)pendingComissionPrice.VATType;
                productCommissionPriceRepository.Add(unitPrice);
                await productCommissionPriceRepository.SaveChangesAsync();

                // Update pending request record - Approve & set unit price id
                pendingComissionPrice.ProductCommissionPriceId = unitPrice.Id;
                UpdatePendingComissionPrice(pendingComissionPrice, request);
                await pendingCommissionPriceRepository.SaveChangesAsync();

                // Commit transaction
                pendingCommissionPriceRepository.CommitTransaction();
                return true;
            }
            catch (Exception ex)
            {
                transaction.Dispose(); // Ensures rollback
                logger.LogError(ex, "HandleAddActionApproval: An error occured while approving pending request with Id '{Id}'", pendingComissionPrice.Id);
                return false;
            }
        });
    }

    private async Task<bool> HandleEditActionApproval(PendingComissionPriceEntity pendingUnitPrice, PendingComissionPriceReviewRequest request)
    {
        // Get SQL Server execution strategy
        var strategy = pendingCommissionPriceRepository.CreateExecutionStrategy();

        return await strategy.ExecuteAsync(async () =>
        {
            using var transaction = await pendingCommissionPriceRepository.BeginTransactionAsync();
            try
            {
                // Share transaction with both repositories
                productCommissionPriceRepository.UseTransaction(transaction);
                pendingCommissionPriceRepository.UseTransaction(transaction);

                UpdatePendingComissionPrice(pendingUnitPrice, request);
                await pendingCommissionPriceRepository.SaveChangesAsync();

                await UpdateUnitPrice(pendingUnitPrice, request);
                await productCommissionPriceRepository.SaveChangesAsync();

                pendingCommissionPriceRepository.CommitTransaction();
                return true;
            }
            catch (Exception ex)
            {
                transaction.Dispose();
                logger.LogError(ex, "HandleEditActionApproval: An error occured while approving pending request with Id '{Id}'", pendingUnitPrice.Id);
                return false;
            }
        });
    }
    private async Task<bool> HandleDeleteActionApproval(PendingComissionPriceEntity pendingComissionPrice, PendingComissionPriceReviewRequest request)
    {
        // Get SQL Server execution strategy
        var strategy = pendingCommissionPriceRepository.CreateExecutionStrategy();

        return await strategy.ExecuteAsync(async () =>
        {
            using var transaction = await pendingCommissionPriceRepository.BeginTransactionAsync();
            try
            {
                // Share transaction with both repositories
                productCommissionPriceRepository.UseTransaction(transaction);
                pendingCommissionPriceRepository.UseTransaction(transaction);

                UpdatePendingComissionPrice(pendingComissionPrice, request);
                await pendingCommissionPriceRepository.SaveChangesAsync();

                await DeleteCommissionPrice(pendingComissionPrice, request);
                await productCommissionPriceRepository.SaveChangesAsync();

                pendingCommissionPriceRepository.CommitTransaction();
                return true;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "HandleDeleteActionApproval: An error occured while approving pending request with Id '{Id}'", pendingComissionPrice.Id);
                transaction.Dispose();
                return false;
            }
        });
    }
    private void UpdatePendingComissionPrice(PendingComissionPriceEntity pendingComissionPriceEntity, PendingComissionPriceReviewRequest request)
    {
        pendingComissionPriceEntity.Status = request.Status;
        pendingComissionPriceEntity.ReviewedBy = request.CreatedBy;
        pendingComissionPriceEntity.ReviewedDate = DateTime.UtcNow;

        pendingCommissionPriceRepository.Update(pendingComissionPriceEntity);
    }
    private async Task DeleteCommissionPrice(PendingComissionPriceEntity pendingComissionPrice, PendingComissionPriceReviewRequest request)
    {
        var existingCommissionPrice = await productCommissionPriceRepository.FirstOrDefaultAsync(x => x.Id == pendingComissionPrice.ProductCommissionPriceId);

        if (existingCommissionPrice != null)
        {
            productCommissionPriceRepository.Delete(existingCommissionPrice);

            var log = mapper.Map<ProductCommissionPriceLogEntity>(existingCommissionPrice);
            log.DeletedBy = request.CreatedBy.ToString();
            log.DeletedDate = DateTime.UtcNow;

            var logsList = new List<ProductCommissionPriceLogEntity>();
            logsList.Add(log);

            await productCommissionPriceRepository.AddLogsAsync(logsList);
        }
    }
    private async Task UpdateUnitPrice(PendingComissionPriceEntity pendingComissionPriceEntity, PendingComissionPriceReviewRequest request)
    {
        var existingUnitPrice = await productCommissionPriceRepository.FirstOrDefaultAsync(x => x.Id == pendingComissionPriceEntity.ProductCommissionPriceId);
        // Update only the allowed properties
        if (existingUnitPrice != null)
        {
            existingUnitPrice.FeeValue = pendingComissionPriceEntity.NewFeeValue ?? 0;
            existingUnitPrice.FeeType = (Common.Enums.ProductCommisssionPrices.FeeType)pendingComissionPriceEntity.VATType;
            existingUnitPrice.BillingType = (Common.Enums.ProductCommisssionPrices.CommisssionPricesBillingType)pendingComissionPriceEntity.BillingType;
            existingUnitPrice.BillingFrequency = pendingComissionPriceEntity.BillingFrequency;

            productCommissionPriceRepository.Update(existingUnitPrice);
        }
    }

    #endregion

    #region Helpers
    public PendingComissionPriceEntity CreateNewPendingComissionPrices(ProductCommissionPriceCreateRequest request, Guid productId, Guid mccId, Guid businessTypeId, Guid commissionFeeId)
    {
        var comissionPrice = mapper.Map<PendingComissionPriceEntity>(request);
        comissionPrice.ProductId = productId;
        comissionPrice.MccId = mccId;
        comissionPrice.BusinessTypeId = businessTypeId;
        comissionPrice.CommissionFeesId = commissionFeeId;
        comissionPrice.VATType = (VatType)request.FeeType;
        comissionPrice.ActionType = ActionTypesEnum.Add;
        comissionPrice.Status = StatusEnum.Pending;
        comissionPrice.CreatedBy = request.CreatedBy;
        return comissionPrice;
    }
    public async Task<List<ProductCommissionPriceEntity>> FetchExistedCommissionPrices(ProductCommissionPriceCreateRequest request)
    {
        var allExistedCommissionPricesList = await productCommissionPriceRepository.GetExistCommissionPrices(up =>
          request.ProductIds.Contains(up.ProductID) &&
          request.MccIds.Contains(up.MCCID) &&
          request.CommissionFeeIds.Contains(up.CommissionFeeID) &&
          request.BusinessTypeIds.Contains(up.BusinessTypeID));

        return allExistedCommissionPricesList;
    }
    public async Task<List<PendingComissionPriceEntity>> FetchExistedPendingCommissionPrices(ProductCommissionPriceCreateRequest request)
    {
        var allExistedCommissionPricesList = await pendingCommissionPriceRepository.GetExistPendingCommissionPrices(up =>
              up.Status == StatusEnum.Pending &&
              request.ProductIds.Contains(up.ProductId) &&
              request.MccIds.Contains(up.MccId) &&
              request.CommissionFeeIds.Contains(up.CommissionFeesId) &&
              request.BusinessTypeIds.Contains(up.BusinessTypeId));

        return allExistedCommissionPricesList;
    }
    public async Task<List<PendingComissionPriceEntity>> FetchExistingPendingCommissionPrices(ProductCommissionPriceCreateRequest request)
    {
        return await pendingCommissionPriceRepository.GetExistPendingCommissionPrices(up =>
            request.ProductIds.Contains(up.ProductId) &&
            request.MccIds.Contains(up.MccId) &&
            request.BusinessTypeIds.Contains(up.BusinessTypeId) &&
            request.CommissionFeeIds.Contains(up.CommissionFeesId) &&
            up.Status == (int)StatusEnum.Pending
        );
    }
    public async Task<List<ProductCommissionPriceEntity>> FetchExistingCommissionPrices(ProductCommissionPriceCreateRequest request)
    {
        return await productCommissionPriceRepository.GetExistCommissionPrices(up =>
            request.ProductIds.Contains(up.ProductID) &&
            request.MccIds.Contains(up.MCCID) &&
            request.CommissionFeeIds.Contains(up.CommissionFeeID) &&
            request.BusinessTypeIds.Contains(up.BusinessTypeID)
        );
    }
    public async Task SavePendingCommissionPricesAsync(List<PendingComissionPriceEntity> pendingComissionPrice)
    {
        try
        {
            await pendingCommissionPriceRepository.AddRange(pendingComissionPrice);
        }
        catch (DbUpdateException ex) when (ex.InnerException is SqlException sqlEx && sqlEx.Number == 2627)
        {
            throw new InvalidOperationException("A unique constraint violation occurred when attempting to create Comission prices.", ex);
        }
    }
    #endregion
}