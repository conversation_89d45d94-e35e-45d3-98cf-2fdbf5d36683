﻿using Common.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.ValueAddedServices;
public class GetValueAddedServicesListRequest
{
    [DefaultValue(Constants.ProductsSearchDictionary)]
    public Dictionary<ProductSearchKey, string> SearchTerms { get; set; } = new Dictionary<ProductSearchKey, string>();
    public List<Status>? Status { get; set; }
    public string OrderType { get; set; } = SortType.desc.ToString();
    public string OrderFieldName { get; set; } = "CreatedDate";
    public int Page { get; set; } = 1;
    public int Size { get; set; } = 10;
}
