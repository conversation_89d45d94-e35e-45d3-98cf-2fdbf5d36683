﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_11_22_1110)]
public class ProductInstances_AddIsRefundEnabled : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
UPDATE pi
SET Metadata = JSON_MODIFY(pi.Metadata, '$.IsRefundEnabled', CAST(1 as BIT))
FROM ProductInstances pi
INNER JOIN Products p ON p.id = pi.ProductId
WHERE p.Type = 'GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0
");
    }
}
