﻿USE PRODUCTS
Go
create or alter procedure New<PERSON>roduc<PERSON>V<PERSON>ion @ProductCode nvarchar(255), @NewProductCode nvarchar(255), @MarkObsolete bit
as
BEGIN
 DECLARE @Ids TABLE(ID UNIQUEIDENTIFIER)
 DECLARE @Id UNIQUEIDENTIFIER
 DECLARE @ProductId UNIQUEIDENTIFIER
 DECLARE @NewProductId UNIQUEIDENTIFIER

 --create new product
 INSERT INTO PRODUCTS(Availability, Code, Type, Description, CreatedBy, CreatedDateUtc, ValidFrom, DisplayOrder) OUTPUT inserted.Id INTO @Ids
 SELECT Availability, @NewProductCode, Type, Description, 'n/a', GETUTCDATE(), Valid<PERSON>rom, DisplayOrder FROM Products Where Code = @ProductCode
 
 SELECT TOP 1 @NewProductId = ID FROM @Ids 
 DELETE FROM @Ids

 --mark obsolete
  SELECT TOP 1 @ProductId = Id FROM Products WHERE Code = @ProductCode 
  UPDATE Products SET Availability = 'Obsolete' WHERE Id = @ProductId and @MarkObsolete = 1

 --add categories
 DECLARE @categoryCursor CURSOR

 SET @categoryCursor = CURSOR FOR SELECT CategoryId FROM ProductCategories WHERE ProductId = @ProductId
 
 OPEN @categoryCursor FETCH NEXT FROM @categoryCursor INTO @Id WHILE @@FETCH_STATUS = 0
 BEGIN
     INSERT INTO ProductCategories(ProductId, CategoryId) VALUES(@NewProductId, @Id)
     FETCH NEXT FROM @categoryCursor INTO @Id
 END
 
 CLOSE @categoryCursor
 DEALLOCATE @categoryCursor

 --add prices
  DECLARE @priceCursor CURSOR
  DECLARE @priceId UNIQUEIDENTIFIER

 SET @priceCursor = CURSOR FOR SELECT Id FROM Prices WHERE ProductId = @ProductId
 
 OPEN @priceCursor FETCH NEXT FROM @priceCursor INTO @Id WHILE @@FETCH_STATUS = 0
 BEGIN
     INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, PerItemPrice, PercentagePrice, Threshold, ThresholdType, [Priority], [Group], ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice, ProductId) OUTPUT inserted.Id INTO @Ids
	 SELECT ChargeFrequency, ChargeType, ExemptFromVAT, PerItemPrice, PercentagePrice,Threshold, ThresholdType, [Priority], [Group], ValidFrom, 'n/a', GETUTCDATE(), Currency, RentalPeriod, MaxPrice, @NewProductId FROM Prices WHERE Id = @Id

	 FETCH NEXT FROM @priceCursor INTO @Id
 END
 
 CLOSE @priceCursor
 DEALLOCATE @priceCursor

 --add parts
 DECLARE @partCursor CURSOR

 SET @partCursor = CURSOR FOR SELECT PartId FROM ProductParts WHERE ProductId = @ProductId
 
  OPEN @partCursor FETCH NEXT FROM @partCursor INTO @Id WHILE @@FETCH_STATUS = 0
  BEGIN
       INSERT INTO ProductParts(ProductId, PartId) VALUES(@NewProductId, @Id)
 	   FETCH NEXT FROM @partCursor INTO @Id
  END
  
  CLOSE @partCursor
  DEALLOCATE @partCursor
END

Go
create or alter procedure ChangePartFromProduct @ProductCode nvarchar(255), @OldPartCode nvarchar(255), @NewPartCode nvarchar(255)
as
BEGIN

 declare @productId UNIQUEIDENTIFIER
 declare @partId UNIQUEIDENTIFIER
 declare @oldPartId UNIQUEIDENTIFIER
 select top(1) @productId = Id from Products where Code=@ProductCode
 select top(1) @partId = Id from Products where Code=@NewPartCode
 select top(1) @oldPartId = Id from Products where Code=@OldPartCode
 
 delete from ProductParts where PartId = @oldPartId and ProductId = @productId
 insert into ProductParts(ProductId, PartId) values(@productId, @partId)

END