﻿using Common.Services;
using Microsoft.AspNetCore.Mvc;
using Moq;
using ProductService.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace ProductService.Test;
public class CategoryControllerTests
{
    private Mock<ICategoryService> ProductServiceMock;
    private CategoryController CategoryController;

    public CategoryControllerTests()
    {
        ProductServiceMock = new Mock<ICategoryService>();
        CategoryController = new CategoryController(ProductServiceMock.Object);
    }
    [Fact]
    public async Task GetCategoriesList_ShouldReturn_OkResult()
    {
        var result = await CategoryController.GetCategoriesList();

        var okResult = result as OkObjectResult;

        Assert.Equal(200, okResult.StatusCode);
        Assert.NotNull(okResult);
    }
}
