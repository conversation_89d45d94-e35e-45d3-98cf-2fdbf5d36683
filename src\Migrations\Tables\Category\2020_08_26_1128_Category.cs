﻿using FluentMigrator;

namespace Migrations.Tables.Category;

[Migration(2020_08_26_1128)]
public class Category : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("Category")
          .WithColumn("Id").AsGuid().NotNullable().PrimaryKey().WithDefault(SystemMethods.NewGuid)
          .WithColumn("Code").AsString().NotNullable()
          .WithColumn("Type").AsInt32().NotNullable()
          .WithColumn("ParentId").AsGuid().Nullable().ForeignKey("Category", "Id")
          .WithColumn("DeletedFlag").AsBoolean().NotNullable().WithDefaultValue(false)
          .WithColumn("CreatedBy").AsString(150).NotNullable()
          .WithColumn("CreatedDateUtc").AsDateTime2().NotNullable()
          .WithColumn("UpdatedBy").AsString(150).Nullable()
          .WithColumn("UpdatedDateUtc").AsDateTime2().Nullable();
    }
}
