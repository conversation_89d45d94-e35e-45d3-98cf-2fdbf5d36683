﻿using Common;
using Common.Entities;
using Common.Models.MccCategory;
using Common.Repositories;
using Geidea.Utils.DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Repositories;
public class MccCategoryRepository : AuditableRepository<Guid, MccCategory>, IMccCategoryRepository
{
    public MccCategoryRepository(DbContext context, IHttpContextAccessor contextAccessor) : base(context, contextAccessor)
    {
    }
    public async Task<List<MccCategory>> GetMccCategoriesAsync()
    {
        return await context.Set<MccCategory>()
            .Include(c => c.Mccs)
            .ToListAsync();
    }

    public async Task<List<MccCategoriesListResponse>> GetMccCategoriesList()
    {
        try
        {
            var MccCategoriesList = await context.Set<MccCategory>()
                                       .Select(s => new MccCategoriesListResponse()
                                       {
                                           Id = s.Id,
                                           Name = s.Name
                                       }).ToListAsync();

            return MccCategoriesList;
        }
        catch (Exception ex)
        {
            throw new ServiceException(HttpStatusCode.InternalServerError, ex.Message);
        }

    }
    public async Task<bool> ExistsAsync(Guid categoryId)
    {
        return await context.Set<MccCategory>().AsNoTracking().AnyAsync(x => x.Id == categoryId);
    }
}
