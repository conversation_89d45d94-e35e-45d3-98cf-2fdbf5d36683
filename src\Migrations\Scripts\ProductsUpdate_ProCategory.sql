﻿DECLARE @ProductId UNIQUEIDENTIFIER
DECLARE @ProSmartCategoryId UNIQUEIDENTIFIER

SELECT TOP 1 @ProSmartCategoryId = ID FROM Category where Code = 'PRO_FAMILY'

EXEC NewProductVersion_v2 'PRO_SMART', 5, 'PRO_SMART', 6, 1,'<PERSON><PERSON>EA_SAUDI'
SELECT TOP 1 @ProductId = ID FROM Products where Code='PRO_SMART' and Version = 6
DELETE FROM ProductCategories where ProductId=@ProductId 
INSERT INTO ProductCategories(ProductId, CategoryId) VALUES (@ProductId, @ProSmartCategoryId)
UPDATE Products SET [Availability] = 'Obsolete' WHERE Code IN ('BUSINESS_RETAIL', 'ENTERPRISE_RETAIL', 'BUSINESS_RESTAURANT','ENTERPRISE_RESTAURANT')
