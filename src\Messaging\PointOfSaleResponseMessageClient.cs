﻿using System;
using System.Text;
using System.Text.Json;
using Common.Models;
using Common.Services;
using Geidea.Messages.Base;
using Geidea.Messages.Gsdk;
using Geidea.Utils.Json;
using GeideaPaymentGateway.Utils.RabbitMQ;
using Microsoft.Extensions.Logging;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using Serilog.Context;
using IConnectionFactory = GeideaPaymentGateway.Utils.RabbitMQ.IConnectionFactory;

namespace Messaging;

public sealed class PointOfSaleResponseMessageClient : MessageClient, IPointOfSaleResponseMessageClient
{
    public const string ExchangeName = "Gsdk.Pointofsale.Response";
    public const string QueueName = ExchangeName;
    public const string RoutingKey = "";

    private EventingBasicConsumer? consumer;
    private readonly ILogger<PointOfSaleResponseMessageClient> logger;

    public event EventHandler<PointOfSaleResponseMessageReceivedEventArgs>? OnPointOfSaleResponseMessageReceived;

    public PointOfSaleResponseMessageClient(
        IConnectionFactory connectionFactory,
        ILogger<PointOfSaleResponseMessageClient> logger) : base(connectionFactory)
    {
        this.logger = logger;
    }

    public void Connect()
    {
        SetupConnection();
        SetupChannel();

        consumer = new EventingBasicConsumer(Channel);
        consumer.Received += OnPointOfSaleResponseMessage;

        Channel.BasicConsume(QueueName, true, consumer);
    }

    private void OnPointOfSaleResponseMessage(object? sender, BasicDeliverEventArgs args)
    {
        try
        {
            var json = Encoding.UTF8.GetString(args.Body.ToArray());
            var pointOfSaleResponseMessage = Json.Deserialize<GenericMessage<GsdkPointOfSaleResponse>>(json);

            AddCorrelationIdToLogger(pointOfSaleResponseMessage.Header.CorrelationId);

            logger.LogInformation("Point of sale response message client received message");

            if (pointOfSaleResponseMessage == null)
                throw new JsonException("Invalid JSON encountered, resulting is deserialization result of null");

            OnPointOfSaleResponseMessageReceived?.Invoke(this, new PointOfSaleResponseMessageReceivedEventArgs(pointOfSaleResponseMessage.Data, pointOfSaleResponseMessage.Header.Counterparty));
        }
        catch (Exception ex)
        {
            logger.LogError("Point of sale response message client threw an error while trying to process the message! {@ex}", ex);
        }
    }
    private void SetupChannel()
    {
        Channel.ExchangeDeclare(ExchangeName, ExchangeType.Direct, true);
        Channel.QueueDeclare(QueueName, true, false, false);
        Channel.QueueBind(QueueName, ExchangeName, RoutingKey);
    }

    protected override void Dispose(bool disposing)
    {
        if (consumer != null)
            consumer.Received -= OnPointOfSaleResponseMessage;

        base.Dispose(disposing);
    }

    private static void AddCorrelationIdToLogger(Guid? correlationId)
    {
        if (correlationId == null)
            return;

        LogContext.PushProperty("CorrelationId", correlationId);
    }
}
