﻿using FluentValidation;
using Geidea.ProductService.Models;

namespace Common.Validators;

public class SubMerchantInformationValidator : AbstractValidator<SubMerchantInformation>
{
    public SubMerchantInformationValidator()
    {
        RuleFor(subMerchantInfo => subMerchantInfo.Id)
            .NotEmpty()
            .MaximumLength(255)
            .When(subMerchantInfo => subMerchantInfo.Id is not null);

        RuleFor(subMerchantInfo => subMerchantInfo.Id)
            .NotEmpty()
            .When(subMerchantInfo => !string.IsNullOrWhiteSpace(subMerchantInfo.RegisteredName) || !string.IsNullOrWhiteSpace(subMerchantInfo.TradingName))
            .WithMessage("Id is required when any of RegisteredName or TradingName is provided");

        RuleFor(subMerchantInfo => subMerchantInfo.RegisteredName)
            .NotEmpty()
            .MaximumLength(255)
            .When(subMerchantInfo => subMerchantInfo.RegisteredName is not null);

        RuleFor(subMerchantInfo => subMerchantInfo.TradingName)
            .NotEmpty()
            .MaximumLength(255)
            .When(subMerchantInfo => subMerchantInfo.TradingName is not null);

        RuleFor(subMerchantInfo => subMerchantInfo.Country)
            .NotEmpty()
            .MaximumLength(255)
            .When(subMerchantInfo => subMerchantInfo.Country is not null);

        RuleFor(subMerchantInfo => subMerchantInfo.City)
            .NotEmpty()
            .MaximumLength(255)
            .When(subMerchantInfo => subMerchantInfo.City is not null);

        RuleFor(subMerchantInfo => subMerchantInfo.Governorate)
            .NotEmpty()
            .MaximumLength(255)
            .When(subMerchantInfo => subMerchantInfo.Governorate is not null);

        RuleFor(subMerchantInfo => subMerchantInfo.StreetAndNumber)
            .NotEmpty()
            .MaximumLength(255)
            .When(subMerchantInfo => subMerchantInfo.StreetAndNumber is not null);

        RuleFor(subMerchantInfo => subMerchantInfo.ZipCode)
            .NotEmpty()
            .MaximumLength(255)
            .When(subMerchantInfo => subMerchantInfo.ZipCode is not null);

        RuleFor(subMerchantInfo => subMerchantInfo.PhoneNumber)
            .NotEmpty()
            .MaximumLength(255)
            .When(subMerchantInfo => subMerchantInfo.PhoneNumber is not null);

        RuleFor(subMerchantInfo => subMerchantInfo.Email)
            .NotEmpty()
            .MaximumLength(255)
            .When(subMerchantInfo => !string.IsNullOrEmpty(subMerchantInfo.Email));


        RuleFor(subMerchantInfo => subMerchantInfo.CountryPrefix)
           .NotEmpty()
           .MaximumLength(255)
           .When(subMerchantInfo => subMerchantInfo.CountryPrefix is not null);
    }
}