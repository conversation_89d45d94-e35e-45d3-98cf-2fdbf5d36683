﻿using Common.Data.ProductType;
using Common.Models.ProductInstance;
using FluentValidation;
using System;

namespace Common.Validators;

public class FindProductInstanceValidator : AbstractValidator<FindProductInstanceRequest>
{
    public FindProductInstanceValidator()
    {
        RuleForEach(x => x.Types)
            .IsEnumName(typeof(ProductTypes), caseSensitive: false)
            .WithMessage("Please only use: " + string.Join(",", Enum.GetNames(typeof(ProductTypes))))
            .When(x => x.Types != null);

        RuleFor(x => x.Take).GreaterThan(0).WithMessage("Take should have a value greater than 0");
    }
}
