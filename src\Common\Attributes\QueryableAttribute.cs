﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Common.Attributes;

[AttributeUsage(AttributeTargets.Property)]
public class QueryableAttribute : Attribute
{
    public bool CanSearch { get; set; }
    public bool CanOrder { get; set; }

    public QueryableAttribute(bool canSearch = false, bool canOrder = false)
    {
        CanSearch = canSearch;
        CanOrder = canOrder;
    }
}
