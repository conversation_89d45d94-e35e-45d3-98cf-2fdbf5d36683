﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_10_24_1450)]
public class ProductInstances_AddSouhoolaCpCredentialLogList_To_Metadata : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"

                DECLARE @EmptyList NVARCHAR(256) = N'[]';

                UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.SouhoolaCpCredentialsLog', JSON_Query(@EmptyList))
	            FROM ProductInstances pi
                INNER JOIN Products p ON p.id = pi.ProductId
                WHERE [Metadata] IS NOT NULL
                AND p.Type = 'GWAY' 
                AND ISJSON([Metadata]) > 0 
            ");
    }
}