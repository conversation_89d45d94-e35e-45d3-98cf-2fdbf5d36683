﻿BEGIN TRANSACTION;

DECLARE @GoSmartBpBundleId UNIQUEIDENTIFIER;
DECLARE @GoSmartBillPaymentCode as NVARCHAR(30) = 'GO_SMART_BP';
DECLARE @CounterpartyHeader NVARCHAR(255) = 'GEIDEA_EGYPT';

PRINT('Script start.');

PRINT('Retrieving existing uniqueidentifiers from db')

SELECT TOP 1 @GoSmartBpBundleId = [Id] FROM [PRODUCTS] WHERE [Code] = @goSmartBillPaymentCode AND [Counterparty] = @CounterpartyHeader

PRINT('GO_SMART_BP bundle Id is : ')PRINT(@GoSmartBpBundleId)

UPDATE [PRODUCTS].[dbo].[Products] SET [SalesChannel] = 'All' WHERE [Id] = @GoSmartBpBundleId

PRINT('Updated SalesChannel for GO_SMART_BP bundle')

UPDATE [PRODUCTS].[dbo].[Prices] SET [PerItemPrice] = NULL WHERE [ProductId] = @GoSmartBpBundleId

PRINT('Updated Price for GO_SMART_BP bundle')

COMMIT TRANSACTION