﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2020_10_02_1114)]
public class ProductInstance_Recreate : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("ProductInstances")
          .WithColumn("Id").AsGuid().PrimaryKey().WithDefaultValue(SystemMethods.NewSequentialId)
          .WithColumn("ProductId").AsGuid().NotNullable().ForeignKey("Products", "Id")
          .WithColumn("AgreementId").AsGuid().NotNullable()
          .WithColumn("StoreId").AsGuid().Nullable()
          .WithColumn("ParentId").AsGuid().Nullable().ForeignKey("ProductInstances", "Id")
          .WithColumn("Metadata").AsString(int.MaxValue).Nullable()
          .WithColumn("DeletedFlag").AsBoolean().WithDefaultValue(false)
          .WithColumn("ValidFrom").AsDateTime2().Nullable()
          .WithColumn("ValidTo").AsDateTime2().Nullable()
          .WithColumn("CreatedBy").AsString(250).NotNullable()
          .WithColumn("UpdatedBy").AsString(250).Nullable()
          .WithColumn("CreatedDateUtc").AsDateTime2().NotNullable().WithDefaultValue(SystemMethods.CurrentUTCDateTime)
          .WithColumn("UpdatedDateUtc").AsDateTime2().Nullable();
    }
}
