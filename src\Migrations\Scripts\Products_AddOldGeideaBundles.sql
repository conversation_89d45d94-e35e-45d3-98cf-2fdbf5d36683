DECLARE @ListOfProducts TABLE(ProductName varchar(100))
INSERT INTO @ListOfProducts VALUES
('PAX_A920'),('Spectra_SP530C_GEIDEA'), ('Spectra_T300u'), ('VERIFONE_VX_690'),('VERIFONE_UX_410'),
('VERIFONE_UX_300'),('PAX_IM30'),('Spectra_T300p'),('VERIFONE_VX_672_CTLS'),('VERIFONE_VX_240'),('VERIFONE_VX_675_3G'),
('T1000_NFC_GPRS_DIALUP_TCP'),('VERIFONE_VX_675_GPRS'), ('T1000_3G_NFC_WiFi_TCP'), ('Spectra_SP530C_xPAY'),
('VERIFONE_VX_680'),('VERIFONE_VX_520_CTLS'),('T1000_WiFi'),('mPOS_Model'),('Spectra_T300C_xPAY'),
('T1000_GPRS_TCP_DIALUP'),('SoftPOS'),('PAX_d135')

DECLARE @productName varchar(100)
DECLARE @Ids TABLE(ID UNIQUEIDENTIFIER)
DECLARE @Id UNIQUEIDENTIFIER
DECLARE @IdtoInsertForPrice UNIQUEIDENTIFIER

DECLARE db_cursor_insert CURSOR FOR 
SELECT ProductName 
FROM @ListOfProducts

OPEN db_cursor_insert  
FETCH NEXT FROM db_cursor_insert INTO @productName  

WHILE @@FETCH_STATUS = 0  
BEGIN  

INSERT INTO PRODUCTS ([Availability], Code,	[Type],	[Description],	CreatedBy,	CreatedDate,	ValidFrom,	Counterparty, Flow, SalesChannel)OUTPUT inserted.Id INTO @Ids
VALUES ('Live',	@productName, 'TERMINAL', @productName, '00000000-0000-0000-0000-000000000000', GETUTCDATE(), GETUTCDATE(), 'GEIDEA_SAUDI', 'Normal', NULL)

SELECT TOP 1 @IdtoInsertForPrice = ID FROM @Ids
DELETE FROM @Ids

INSERT INTO PRICES (ChargeFrequency, ChargeType, ExemptFromVAT,	ProductId, PerItemPrice, ValidFrom,	CreatedBy, CreatedDate, Currency)
VALUES ('ONE_OFF', 'RETAIL_PRICE', 0, @IdtoInsertForPrice, 100, GETUTCDATE(), '00000000-0000-0000-0000-000000000000', GETUTCDATE(), 'SAR')

FETCH NEXT FROM db_cursor_insert INTO @productName  
END 

CLOSE db_cursor_insert  
DEALLOCATE db_cursor_insert