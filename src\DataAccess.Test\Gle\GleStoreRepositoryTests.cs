﻿using System;
using System.Collections.Generic;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Common;
using Common.Entities.Gle;
using DataAccess.Repositories.Gle;
using DataAccess.Test.TestData;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;

namespace DataAccess.Test.Gle;

public class GleStoreRepositoryTests
{
    private readonly IHttpContextAccessor contextAccessor = Substitute.For<IHttpContextAccessor>();
    private readonly ILogger<GleStoreRepository> logger = Substitute.For<ILogger<GleStoreRepository>>();
    private readonly ILogger<GleBaseRepository<GleStoreEntity>> baseLogger = Substitute.For<ILogger<GleBaseRepository<GleStoreEntity>>>();
    private DataContext context;
    private GleStoreRepository gleStoreRepository;

    private readonly Guid storeId = Guid.NewGuid();
    private readonly Guid storeIdTwo = Guid.NewGuid();

    [SetUp]
    public async Task SetUp()
    {
        context = DbContextHelper.CreateInMemoryDatabase(Substitute.For<ICounterpartyProvider>());
        gleStoreRepository = new GleStoreRepository(context, contextAccessor, baseLogger, logger);

        await context.AddRangeAsync(new List<GleStoreEntity>
        {
            new()
            {
                Id = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid().ToString(),
                CreatedDate = DateTime.Now,
                GleUserId = "22222",
                GleLoginId = "33254",
                GleLoginId2 = "17999",
                ParentGleUserId = "435645",
                GleRegistrationStatus = Constants.GleRegistrationStatus.Success,
                GleRegistrationResponse = "Store was registered!",
                ReferenceMmsId = null,
                GleMerchant = new GleMerchantEntity{GleRegistrationStatus = Constants.GleRegistrationStatus.Success, GleRegistrationResponse = "Merchant was registered!", UserCategoryCode = Constants.GleUserCategoryCode.Retailer},
                StoreId = storeId,
                UserCategoryCode = null,
                GleTerminalEntities = new List<GleTerminalEntity>(){new(){GleRegistrationStatus = Constants.GleRegistrationStatus.Failed, GleRegistrationResponse = "TID not configured!"}}
            }
        });

        await context.AddRangeAsync(new List<GleStoreEntity>
        {
            new()
            {
                Id = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid().ToString(),
                CreatedDate = DateTime.Now,
                GleUserId = "11111",
                GleLoginId = "45254",
                GleLoginId2 = "18999",
                ParentGleUserId = "575645",
                GleRegistrationStatus = Constants.GleRegistrationStatus.Success,
                GleRegistrationResponse = "Store was registered!",
                ReferenceMmsId = null,
                GleMerchant = new GleMerchantEntity{GleRegistrationStatus = Constants.GleRegistrationStatus.Success, GleRegistrationResponse = "Merchant was registered!", UserCategoryCode = Constants.GleUserCategoryCode.Wholesaler},
                StoreId = storeIdTwo,
                UserCategoryCode = Constants.GleUserCategoryCode.WholesalerStore,
                GleTerminalEntities = new List<GleTerminalEntity>(){new(){GleRegistrationStatus = Constants.GleRegistrationStatus.Failed, GleRegistrationResponse = "TID not configured!"}}
            }
        });

        await context.SaveChangesAsync();
    }

    [Test]
    public async Task GetGleHierarchyByStoreIdAsync_ShouldReturnHierarchy()
    {
        // Act
        var result = await gleStoreRepository.GetGleHierarchyByStoreIdAsync(storeId);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(storeId, result.StoreId);
        Assert.IsNotEmpty(result.GleTerminalEntities);
        Assert.IsNull(result.UserCategoryCode);
    }

    [Test]
    public async Task GetGleHierarchyByStoreIdAsync_ShouldReturnHierarchy_WithValidUserCategoryCode()
    {
        // Act
        var result = await gleStoreRepository.GetGleHierarchyByStoreIdAsync(storeIdTwo);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(storeIdTwo, result.StoreId);
        Assert.IsNotEmpty(result.GleTerminalEntities);
        Assert.AreEqual(Constants.GleUserCategoryCode.WholesalerStore, result.UserCategoryCode);
    }

    [Test]
    public async Task GetGleHierarchyByStoreIdAsync_WhenNotFound_ShouldReturnNull()
    {
        var result = await gleStoreRepository.GetGleHierarchyByStoreIdAsync(Guid.Empty);
        Assert.Null(result);
    }

    [Test]
    public async Task GetGleStoreByStoreIdAsync_ShouldReturnMerchant()
    {
        // Act
        var result = await gleStoreRepository.GetGleStoreByStoreIdAsync(storeId);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(storeId, result.StoreId);
        Assert.IsNull(result.UserCategoryCode);
    }

    [Test]
    public async Task GetGleStoreByStoreIdAsync_WhenNotFound_ShouldReturnNull()
    {
        var result = await gleStoreRepository.GetGleStoreByStoreIdAsync(Guid.NewGuid());
        Assert.Null(result);
    }

    [Test]
    public async Task AddGle_ValidRequest_ShouldCallBaseAndAddGleStore()
    {
        var numberOfGleStoresBeforeAdd = context.GleStore.Count();

        await gleStoreRepository.AddGle(new GleStoreEntity()
        {
            GleRegistrationStatus = Constants.GleRegistrationStatus.Failed,
            GleRegistrationResponse = "Location not found!",
            ReferenceMmsId = "11111111",
            StoreId = Guid.NewGuid()
        });

        Assert.AreEqual(numberOfGleStoresBeforeAdd + 1, context.GleStore.Count());
    }

    [Test]
    public async Task AddGle_InvalidRequest_ShouldThrowServiceException()
    {
        await gleStoreRepository.Invoking(x => x.AddGle(new GleStoreEntity()))
            .Should().ThrowAsync<ServiceException>();
    }

    [Test]
    public async Task UpdateGle_ValidRequest_ShouldCallBaseAndUpdateGleMerchant()
    {
        var gleStoreBeforeUpdate = await gleStoreRepository.GetGleStoreByStoreIdAsync(storeId);

        const string newValue = "24434";
        var updateDocument = new JsonPatchDocument<GleStoreEntity>();
        updateDocument.Operations.Add(new Operation<GleStoreEntity>
        {
            op = "replace",
            path = "GleUserId",
            value = newValue
        });
        await gleStoreRepository.UpdateGle(gleStoreBeforeUpdate!.Id, updateDocument);

        var gleStoreAfterUpdate = await gleStoreRepository.GetGleStoreByStoreIdAsync(storeId);

        Assert.AreNotEqual(gleStoreBeforeUpdate.GleUserId, gleStoreAfterUpdate!.GleUserId);
        Assert.AreEqual(newValue, gleStoreAfterUpdate.GleUserId);
    }
}