﻿using Common.Entities;
using Common.Models.Onboarding;
using Common.Models.UnitPrice;
using Common.Views;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Common.Repositories;
public interface IOnboardingUnitPriceRepository : IRepository<Guid, UnitPriceEntity>
{
    Task<OnboardingUnitPricesListResponse> GetOnboardingUnitPricesList(OnboardingUnitPricesListRequest request);
}
