﻿using Common.Data;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using static Common.Constants;
using CybersourceMsoProviders = Geidea.Utils.Common.Constants.CybersourceMsoProviders;
using MpgsMsoProviders = Geidea.Utils.Common.Constants.MpgsMsoProviders;

namespace Common.Validators;

public class GatewayDataValidator : AbstractValidator<GatewayData>
{
    public GatewayDataValidator()
    {
        var mpgsMsoProviders = new List<string>
            {
                MpgsMsoProviders.Geidea,
                MpgsMsoProviders.Bm,
                MpgsMsoProviders.Nbe,
                MpgsMsoProviders.Ma<PERSON>ti,
                MpgsMsoProviders.Mock,
                MpgsMsoProviders.GeideaKsa,
                MpgsMsoProviders.Ksa,
                MpgsMsoProviders.GeideaUAE,
                MpgsMsoProviders.Ksa,
                MpgsMsoProviders.SAB,
                MpgsMsoProviders.SNB,
                MpgsMsoProviders.ANB,
                MpgsMsoProviders.BSFB
            };
        var cybersourceMsoProvicers = new List<string>
        {
            CybersourceMsoProviders.GEIDEAUAE
        };
        RuleFor(data => data.MpgsMsoProvider)
            .Must(p => mpgsMsoProviders.Any(ep => ep.Equals(p, StringComparison.InvariantCultureIgnoreCase)))
            .When(data => data.MpgsMsoProvider != null)
            .WithMessage("MpgsMsoProvider should be one of these values: " + string.Join(", ", mpgsMsoProviders));

        RuleFor(data => data.CyberSourceAccounts).NotNull();
        RuleFor(data => data.MpgsAccounts).NotNull();

        RuleFor(data => data.CyberSourceMsoProvider)
          .Must(p => cybersourceMsoProvicers.Any(ep => ep.Equals(p, StringComparison.InvariantCultureIgnoreCase)))
          .When(data => data.CyberSourceMsoProvider != null)
          .WithMessage("CyberSourceMsoProvider should be one of these values: " + string.Join(", ", cybersourceMsoProvicers));


        RuleFor(data => data.MpgsAccounts)
            .Must(mpgsAccounts => mpgsAccounts.All(mpgsAccount => mpgsAccount is not null))
            .When(data => data.MpgsAccounts is not null)
            .WithMessage("MPGS accounts should not be null");

        RuleFor(data => data.CyberSourceAccounts)
          .Must(cybersourceAccounts => cybersourceAccounts.All(cybersourceAccount => cybersourceAccount is not null))
          .When(data => data.CyberSourceAccounts is not null)
          .WithMessage("Cybersource Accounts should not be null");

        RuleForEach(gatewayData => gatewayData.CyberSourceAccounts)
            .Where(cybersourceAccount => cybersourceAccount != null)
            .SetValidator(new CybersourceAccountValidator());


        RuleForEach(gatewayData => gatewayData.MpgsAccounts)
            .Where(mpgsAccount => mpgsAccount != null)
            .SetValidator(new MpgsAccountValidator());

        RuleFor(data => data.MinAmount)
            .GreaterThanOrEqualTo(0)
            .When(data => data.MinAmount != null);

        RuleFor(data => data.MaxAmount)
            .GreaterThanOrEqualTo(0)
            .When(data => data.MaxAmount != null);

        RuleFor(data => data.MinAmount)
            .LessThan(data => data.MaxAmount)
            .When(data => data.MinAmount != null && data.MaxAmount != null);

        RuleFor(data => data.ValuVendorId)
            .MaximumLength(255)
            .When(data => !string.IsNullOrEmpty(data.ValuVendorId));

        RuleFor(data => data.ValuStoreId)
            .MaximumLength(255)
            .When(data => !string.IsNullOrEmpty(data.ValuStoreId));

        RuleFor(data => data.ValuProductId)
            .MaximumLength(255)
            .When(data => !string.IsNullOrEmpty(data.ValuProductId));

        RuleFor(data => data.ValuCapLimit)
            .GreaterThanOrEqualTo(1)
            .PrecisionScale(18, 2, true)
            .When(data => data.ValuCapLimit != null);

        RuleFor(data => data.IsValuBnplEnabled)
            .Must(isValuBnplEnabled => !isValuBnplEnabled)
            .When(data => string.IsNullOrEmpty(data.ValuProductId) || string.IsNullOrEmpty(data.ValuStoreId) || string.IsNullOrEmpty(data.ValuVendorId) || data.ValuCapLimit < 1);

        RuleFor(data => data.ShahryCpBnplBranchCode)
            .Length(6)
            .Matches(OnlyAlphanumericRegex)
            .When(data => !string.IsNullOrEmpty(data.ShahryCpBnplBranchCode));

        RuleFor(data => data.ShahryCpBnplMerchantCode)
            .Length(6)
            .Matches(OnlyAlphanumericRegex)
            .When(data => !string.IsNullOrEmpty(data.ShahryCpBnplMerchantCode));

        RuleFor(data => data.IsShahryCpBnplEnabled)
            .Must(isOfflineShahryBnplEnabled => !isOfflineShahryBnplEnabled)
            .When(data => string.IsNullOrEmpty(data.ShahryCpBnplBranchCode) || string.IsNullOrEmpty(data.ShahryCpBnplMerchantCode));

        RuleFor(data => data.SouhoolaMerchantNationalId)
        .MaximumLength(255)
            .Matches(OnlyNumericRegex)
        .When(data => !string.IsNullOrEmpty(data.SouhoolaMerchantNationalId));

        RuleFor(data => data.SouhoolaMerchantPhoneNumber)
            .MaximumLength(255)
            .Matches(OnlyAlphanumericRegex)
            .When(data => !string.IsNullOrEmpty(data.SouhoolaMerchantPhoneNumber));

        RuleFor(data => data.SouhoolaAccessKey)
            .MaximumLength(255)
            .Matches(OnlyAlphanumericRegex)
            .When(data => !string.IsNullOrEmpty(data.SouhoolaAccessKey));

        RuleFor(data => data.IsSouhoolaCnpBnplEnabled)
            .Must(IsSouhoolaCnpBnplEnabled => !IsSouhoolaCnpBnplEnabled)
            .When(data => string.IsNullOrEmpty(data.SouhoolaCnpUserName) || string.IsNullOrEmpty(data.SouhoolaCnpPassword));

        RuleFor(data => data.ShahryCnpBnplBranchCode)
            .Length(6)
            .Matches(OnlyAlphanumericRegex)
            .When(data => !string.IsNullOrEmpty(data.ShahryCnpBnplBranchCode));

        RuleFor(data => data.ShahryCnpBnplMerchantCode)
            .Length(6)
            .Matches(OnlyAlphanumericRegex)
            .When(data => !string.IsNullOrEmpty(data.ShahryCnpBnplMerchantCode));

        RuleFor(data => data.IsShahryCnpBnplEnabled)
            .Must(isOnlineShahryBnplEnabled => !isOnlineShahryBnplEnabled)
            .When(data => string.IsNullOrEmpty(data.ShahryCnpBnplBranchCode) || string.IsNullOrEmpty(data.ShahryCnpBnplMerchantCode));

        RuleFor(data => data.SubMerchantInformation)
            .NotNull()
            .SetValidator(new SubMerchantInformationValidator())
            .WithMessage("SubMerchantInformation should not be null");

        RuleFor(data => data.GooglePayMerchantId)
           .MaximumLength(18)
           .When(data => !string.IsNullOrEmpty(data.GooglePayMerchantId));

        RuleFor(data => data.SouhoolaCpBnplNationalId)
           .Length(14)
           .Matches(OnlyNumericRegex)
           .When(data => !string.IsNullOrEmpty(data.SouhoolaCpBnplNationalId));

        RuleFor(data => data.SouhoolaCpBnplGlobalId)
         .MaximumLength(255)
         .Matches(OnlyAlphanumericRegex)
         .When(data => !string.IsNullOrEmpty(data.SouhoolaCpBnplGlobalId));

        RuleFor(data => data.IsSouhoolaCpBnplEnabled)
            .Must(isSouhoolaCpBnplEnabled => !isSouhoolaCpBnplEnabled)
            .When(data => string.IsNullOrEmpty(data.SouhoolaCpBnplNationalId) || string.IsNullOrEmpty(data.SouhoolaCpBnplGlobalId)
            || string.IsNullOrEmpty(data.SouhoolaCpBnplUserName) || string.IsNullOrEmpty(data.SouhoolaCpBnplPassword));

        RuleFor(data => data.IsSouhoolaCpMerchantRegistered)
            .Must(IsSouhoolaCpMerchantRegistered => !IsSouhoolaCpMerchantRegistered)
            .When(data => string.IsNullOrEmpty(data.SouhoolaCpBnplNationalId) || string.IsNullOrEmpty(data.SouhoolaCpBnplGlobalId)
            || string.IsNullOrEmpty(data.SouhoolaCpBnplUserName) || string.IsNullOrEmpty(data.SouhoolaCpBnplPassword));

        RuleFor(data => data.MadaAccounts).NotNull();

        RuleFor(data => data.MadaAccounts)
            .Must(madaAccounts => madaAccounts.All(madaAccount => madaAccount is not null))
            .When(data => data.MadaAccounts is not null)
            .WithMessage("MADA accounts should not be null");

        RuleForEach(gatewayData => gatewayData.MadaAccounts)
            .SetValidator(new MadaAccountValidator());

        RuleFor(data => data.RefundPeriodDays)
            .NotEmpty()
            .InclusiveBetween(1, 365)
            .When(data => data.IsRefundEnabled && data.IsRefundPeriodEnabled);

        RuleFor(data => data.IsPartialCaptureEnabled)
        .Must(isPartialCaptureEnabled => !isPartialCaptureEnabled)
        .When(data => !data.IsCaptureEnabled)
        .WithErrorCode("PartialCaptureCannotBeEnabledWhenCaptureIsNotEnabled")
        .WithMessage("Partial capture cannot be enabled when capture is not enabled.");

        RuleFor(data => data.PayByGeideaCode.PaymentReferenceExpirationDays)
            .NotEmpty()
            .InclusiveBetween(1, 90)
            .When(data => data.PayByGeideaCode is not null && data.PayByGeideaCode.IsEnabled);

        RuleFor(data => data.PayByGeideaCode.RefundReferenceExpirationDays)
            .NotEmpty()
            .InclusiveBetween(1, 90)
            .When(data => data.PayByGeideaCode is not null && data.PayByGeideaCode.IsEnabled);

        RuleFor(data => data.StcPayMerchantId)
          .MaximumLength(100)
          .When(data => !string.IsNullOrEmpty(data.StcPayMerchantId));

        RuleFor(data => data.Tabby.MerchantCode)
            .NotEmpty()
            .When(data => data.Tabby is not null && data.Tabby.IsTabbyEnabled);

        RuleFor(data => data.Tabby.SecretKey)
            .NotEmpty()
            .When(data => data.Tabby is not null && data.Tabby.IsTabbyEnabled);

        RuleFor(data => data.Tabby.TabbyPublicKey)
            .NotEmpty()
            .When(data => data.Tabby is not null && data.Tabby.IsTabbyEnabled);

        RuleFor(data => data.InstaPay.IpnBank)
            .NotEmpty()
            .When(data => data.InstaPay is not null && data.InstaPay.IsInstaPayEnabled);

        RuleFor(data => data.InstaPay.IpnMerchantId)
            .NotEmpty()
            .When(data => data.InstaPay is not null && data.InstaPay.IsInstaPayEnabled);

        RuleFor(data => data.InstaPay.IpnSharedSecret)
            .NotEmpty()
            .When(data => data.InstaPay is not null && data.InstaPay.IsInstaPayEnabled);
    }
}
