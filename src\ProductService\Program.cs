using Elastic.Apm.Api;
using Geidea.PaymentGateway.ConfigServiceClient;
using Geidea.Utils.Migrations.FluentMigrator;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using ProductService.HostedServices;
using System;
using System.Runtime.InteropServices;
using System.Threading.Tasks;

namespace ProductService;

public static class Program
{
    // The workaround solution to meet integrated security auth for sql (https://github.com/dotnet/SqlClient/issues/1390), will be fixed with .net 6.0.2
    [DllImport("System.Net.Security.Native", EntryPoint = "NetSecurityNative_EnsureGssInitialized")]
    internal static extern int EnsureGssInitialized();

    public static void Main(string[] args)
    {
        // The workaround solution to meet integrated security auth for sql (https://github.com/dotnet/SqlClient/issues/1390), will be fixed with .net 6.0.2
        FixDbConnectivity();

        try
        {
            if (MigrationHelper.Migrate(args, "ProductDatabase:MigrationConnectionString", "ProductDatabase:MigrationAssembly"))
                return;

            Console.WriteLine("Normal app startup, Continue...");
            CreateHostBuilder(args).Build().Run();
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.ToString());
            throw;
        }
    }

    private static void FixDbConnectivity()
    {
        try
        {
            var initResult = EnsureGssInitialized();
            Console.WriteLine($"EnsureGssInitialized completed with result {initResult}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"EnsureGssInitialized failed with {ex.Message}. This can be safely ignored when running under windows or when running in dev and test environments.");
        }
    }

    private static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureWebHostDefaults(webBuilder =>
            {
                webBuilder.UseStartup<Startup>();
                if (!string.IsNullOrEmpty(Environment.GetEnvironmentVariable("IsRunInEnvironments")))
                {
                    webBuilder.UseConfigService();
                }
            })
            .ConfigureServices(s =>
            {
                Task.Run(() =>
                {
                    s.AddHostedService<MetaDataMigrationHostedService>();
                });
            });
}
