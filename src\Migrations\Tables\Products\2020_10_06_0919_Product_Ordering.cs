﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2020_10_06_0919)]
public class Product_Ordering : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"update Products set [Order] = 1 where Code = 'PRO_SMART'
                          update Products set [Order] = 2 where Code in ('BUSINESS_RETAIL', 'BUSINESS_RESTAURANT')
                          update Products set [Order] = 3 where Code in ('ENTERPRISE_RETAIL', 'ENTERPRISE_RESTAURANT')");
    }
}
