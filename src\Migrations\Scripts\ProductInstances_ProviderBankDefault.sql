﻿USE [PRODUCTS]
GO

UPDATE [dbo].[ProductInstances]
SET [Metadata] = JSON_MODIFY([Metadata], N'$.ProviderBank', 'DEFAULT_BANK')
WHERE [Metadata] IS NOT NULL AND [Metadata] != 'null' AND [Metadata] != '{}' AND ISJSON([Metadata]) = 1 
  AND (SELECT p.Type FROM [dbo].[Products] p WHERE p.Id = ProductId) IN ('TERMINAL','M_POS')
  AND ((JSON_VALUE(Metadata, N'$.providerBank') IS NULL AND JSON_VALUE(Metadata, N'$.ProviderBank') IS NULL)
      OR (JSON_VALUE(Metadata, N'$.providerBank') = '' OR JSON_VALUE(Metadata, N'$.ProviderBank') = ''))
 
GO