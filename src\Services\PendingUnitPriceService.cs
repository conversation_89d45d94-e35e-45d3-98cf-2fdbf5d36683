﻿using AutoMapper;
using AutoMapper.Configuration.Annotations;
using Common;
using Common.Entities;
using Common.Enums.UnitPrice;
using Common.Models.PendingUnitPrice;
using Common.Models.UnitPrice;
using Common.Repositories;
using Common.Services;
using DocumentFormat.OpenXml.InkML;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Office2016.Excel;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;
using Geidea.Utils.Exceptions;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace Services;
public class PendingUnitPriceService : IpendingUnitPriceService
{
    #region Fields
    private readonly ILogger<PendingUnitPriceService> logger;
    private readonly IUnitPriceRepository unitPriceRepository;
    private readonly IPendingUnitPriceRepository pendingUnitPriceRepository;

    private readonly IMapper mapper;
    #endregion

    #region Constructor
    public PendingUnitPriceService(ILogger<PendingUnitPriceService> logger, IUnitPriceRepository unitPriceRepository, IMapper mapper,
        IPendingUnitPriceRepository pendingUnitPriceRepository)
    {
        this.logger = logger;
        this.unitPriceRepository = unitPriceRepository;
        this.mapper = mapper;
        this.pendingUnitPriceRepository = pendingUnitPriceRepository;
    }
    #endregion

    #region Create
    private async Task<(List<PendingUnitPriceEntity> unitPrices, List<UnitPriceEntity> existingUnitPrices, List<PendingUnitPriceEntity> existingPendingUnitPrices)>
     ProcessPendingUnitPricesAsync(UnitPriceCreateRequest request)
    {
        var pendingUnitPricesToCreate = new List<PendingUnitPriceEntity>();
        var existingUnitPricesList = new List<UnitPriceEntity>();
        var existingPendingUnitPricesList = new List<PendingUnitPriceEntity>();

        // Fetch existing unit prices and pending unit prices
        var allExistedUnitPrices = await FetchExistingUnitPrices(request);
        var allExistedPendingUnitPrices = await FetchExistingPendingUnitPrices(request);

        foreach (var productId in request.ProductIDs)
        {
            foreach (var mccId in request.MCCIDs)
            {
                foreach (var businessTypeId in request.BusinessTypeIDs)
                {
                    var existingUnitPrice = allExistedUnitPrices.FirstOrDefault(up =>
                                            up.ProductID == productId && up.MCCID == mccId && up.BusinessTypeID == businessTypeId);

                    var existingPendingUnitPrice = allExistedPendingUnitPrices.FirstOrDefault(up =>
                                            up.ProductID == productId && up.MCCID == mccId && up.BusinessTypeID == businessTypeId &&
                                            up.Status == ((int)StatusEnum.Pending));

                    if (existingUnitPrice == null)
                    {
                        if (existingPendingUnitPrice == null)
                        {
                            var pendingUnitPrice = CreateNewPendingUnitPrice(request, productId, mccId, businessTypeId);
                            pendingUnitPricesToCreate.Add(pendingUnitPrice);
                        }
                        else
                        {
                            existingPendingUnitPricesList.Add(existingPendingUnitPrice);
                        }
                    }
                    else
                    {
                        existingUnitPricesList.Add(existingUnitPrice);

                        if (existingPendingUnitPrice != null)
                            existingPendingUnitPricesList.Add(existingPendingUnitPrice);
                        else
                        {
                            var pendingUnitPrice = CreateNewPendingUnitPrice(request, productId, mccId, businessTypeId);
                            pendingUnitPricesToCreate.Add(pendingUnitPrice);
                        }
                    }
                }
            }
        }
        return (pendingUnitPricesToCreate, existingUnitPricesList, existingPendingUnitPricesList);
    }
    public async Task<UnitPriceCreateResponse> CreateAsync(UnitPriceCreateRequest request)
    {
        var (unitPrices, existingUnitPrices, existingPendingUnitPrices) = await ProcessPendingUnitPricesAsync(request);

        if (unitPrices?.Any() == true)
            await SavePendingUnitPricesAsync(unitPrices);

        return new UnitPriceCreateResponse
        {
            HasPendingUnitPricesCreated = unitPrices?.Any() == true,
            HasExistingUnitPrices = existingUnitPrices?.Any() == true,
            HasExistingPendingUnitPrices = existingPendingUnitPrices?.Any() == true,
        };
    }
    #endregion

    #region Edit
    public async Task UpdateAsync(Guid id, UnitPriceUpdateRequest updateRequest)
    {
        var existingUnitPrice = await unitPriceRepository.GetByIdAsync(id);
        if (existingUnitPrice == null)
        {
            logger.LogWarning("No unit price found with Id {Id}.", id);
            throw new ServiceException(HttpStatusCode.NotFound, "Unit price not found.");
        }
        else
        {
            var pendingUnitPrice = await pendingUnitPriceRepository.FirstOrDefaultAsync(x => x.UnitPriceId == id && x.Status == StatusEnum.Pending);
            if (pendingUnitPrice == null)
            {
                await HandleUpdateAction(existingUnitPrice, updateRequest);
            }
            else
            {
                logger.LogWarning("No unit price found with Id {Id}.", id);
                throw new ServiceException(HttpStatusCode.BadRequest, "pending request already existed for this unit price record.");
            }
        }
    }
    private async Task HandleUpdateAction(UnitPriceEntity existingUnitPrice, UnitPriceUpdateRequest request)
    {
        var pendingUnitPrice = new PendingUnitPriceEntity();

        pendingUnitPrice.UnitPriceId = existingUnitPrice.Id;
        pendingUnitPrice.UnitPrice = existingUnitPrice.UnitPrice;
        pendingUnitPrice.ProductID = existingUnitPrice.ProductID;
        pendingUnitPrice.MCCID = existingUnitPrice.MCCID;
        pendingUnitPrice.BusinessTypeID = existingUnitPrice.BusinessTypeID;
        pendingUnitPrice.ActionType = ActionTypesEnum.Edit;
        pendingUnitPrice.NewPrice = request.UnitPrice;
        pendingUnitPrice.VATRate = request.VATRate;
        pendingUnitPrice.VATType = request.VATType;
        pendingUnitPrice.BillingType = request.BillingType;
        pendingUnitPrice.BillingFrequency = request.BillingFrequency;
        pendingUnitPrice.CreatedBy = request.CreatedBy;

        pendingUnitPriceRepository.Add(pendingUnitPrice);
        await pendingUnitPriceRepository.SaveChangesAsync();
    }
    #endregion

    #region List
    public async Task<PendingUnitPricesListResponse> GetPendingUnitPricesList(PendingUnitPricesListRequest request)
    {
        return await pendingUnitPriceRepository.GetPendingUnitPricesList(request);
    }
    #endregion

    #region Bulk Update/Add
    public async Task<(List<PendingUnitPriceEntity> unitPrices, List<UnitPriceEntity> existingUnitPrices,
        List<PendingUnitPriceEntity> existingPendingUnitPrices)> ProcessUnitPriceBulkEdit(UnitPriceCreateRequest request)
    {
        var pendingUnitPricesToCreate = new List<PendingUnitPriceEntity>();
        var existingUnitPricesList = new List<UnitPriceEntity>();
        var existingPendingUnitPricesList = new List<PendingUnitPriceEntity>();

        var allExistedUnitPricesList = await FetchExistedUnitPrices(request);
        var allExistedPendingUnitPricesList = await FetchExistedPendingUnitPrices(request);

        foreach (var productId in request.ProductIDs)
        {
            foreach (var mccId in request.MCCIDs)
            {
                foreach (var businessTypeId in request.BusinessTypeIDs)
                {
                    var existingUnitPrice = allExistedUnitPricesList.FirstOrDefault(up =>
                        up.ProductID == productId && up.MCCID == mccId && up.BusinessTypeID == businessTypeId);

                    var existingPendingUnitPrice = allExistedPendingUnitPricesList.FirstOrDefault(up =>
                       up.ProductID == productId && up.MCCID == mccId && up.BusinessTypeID == businessTypeId);

                    if (existingUnitPrice == null)
                    {
                        if (existingPendingUnitPrice != null)
                            existingPendingUnitPricesList.Add(existingPendingUnitPrice);
                        else
                        {
                            var pendingUnitPrice = CreateNewPendingUnitPrice(request, productId, mccId, businessTypeId);
                            pendingUnitPrice.ActionType = ActionTypesEnum.Add;
                            pendingUnitPricesToCreate.Add(pendingUnitPrice);
                        }
                    }
                    else
                    {
                        existingUnitPricesList.Add(existingUnitPrice);

                        if (existingPendingUnitPrice != null)
                            existingPendingUnitPricesList.Add(existingPendingUnitPrice);
                        else
                        {
                            var pendingUnitPrice = HandleBulkUpdateAction(existingUnitPrice, request);
                            pendingUnitPricesToCreate.Add(pendingUnitPrice);
                        }
                    }
                }
            }
        }
        return (pendingUnitPricesToCreate, existingUnitPricesList, existingPendingUnitPricesList);
    }
    private PendingUnitPriceEntity HandleBulkUpdateAction(UnitPriceEntity existingUnitPrice, UnitPriceCreateRequest request)
    {
        var pendingUnitPrice = mapper.Map<PendingUnitPriceEntity>(existingUnitPrice);
        pendingUnitPrice.UnitPriceId = existingUnitPrice.Id;
        pendingUnitPrice.ActionType = ActionTypesEnum.Edit;
        pendingUnitPrice.NewPrice = request.UnitPrice;
        pendingUnitPrice.CreatedBy = request.CreatedBy;

        return pendingUnitPrice;
    }
    public async Task<UnitPriceBulkEditResponse> BulkEdit(UnitPriceCreateRequest request)
    {
        var (pendingUnitPricesToCreate, existingUnitPrices, existingPendingUnitPrices) = await ProcessUnitPriceBulkEdit(request);

        if (pendingUnitPricesToCreate?.Any() == true)
            await SavePendingUnitPricesAsync(pendingUnitPricesToCreate);

        return new UnitPriceBulkEditResponse
        {
            HasPendingUnitPricesCreated = pendingUnitPricesToCreate?.Any() == true,
            HasExistingPendingUnitPrices = existingPendingUnitPrices?.Any() == true,
            HasExistingUnitPrices = existingUnitPrices?.Any() == true,
        };
    }

    #endregion

    #region Delete
    public async Task<(List<PendingUnitPriceEntity> unitPrices, List<PendingUnitPriceEntity> existingPendingUnitPrices)> ProcessUnitPriceBulkDelete(List<Guid> unitPricesIds, string UserId)
    {
        var pendingUnitPricesToCreate = new List<PendingUnitPriceEntity>();
        var existingPendingUnitPricesList = new List<PendingUnitPriceEntity>();

        var existingUnitPricesList = await unitPriceRepository.WhereAsync(u => unitPricesIds.Contains(u.Id));
        foreach (var unitPrice in existingUnitPricesList)
        {
            var pendingUnitPrice = await pendingUnitPriceRepository.FirstOrDefaultAsync(up => up.UnitPriceId == unitPrice.Id && up.Status == StatusEnum.Pending);
            if (pendingUnitPrice == null)
            {
                var pendingUnitPriceToCreate = mapper.Map<PendingUnitPriceEntity>(unitPrice);
                pendingUnitPriceToCreate.UnitPriceId = unitPrice.Id;
                pendingUnitPriceToCreate.ActionType = ActionTypesEnum.Delete;
                pendingUnitPriceToCreate.Status = StatusEnum.Pending;
                pendingUnitPriceToCreate.CreatedBy = UserId;
                pendingUnitPricesToCreate.Add(pendingUnitPriceToCreate);
            }
            else
            {
                existingPendingUnitPricesList.Add(pendingUnitPrice);
            }
        }

        return (pendingUnitPricesToCreate, existingPendingUnitPricesList);
    }

    public async Task<UnitPriceBulkDeleteResponse> DeleteUnitPricesAsync(UnitPriceBulkDeleteRequest request)
    {
        var (pendingUnitPricesToCreate, existingPendingUnitPricesList) = await ProcessUnitPriceBulkDelete(request.unitPricesIds, request.UserId);

        if (pendingUnitPricesToCreate?.Any() == true)
            await SavePendingUnitPricesAsync(pendingUnitPricesToCreate);

        return new UnitPriceBulkDeleteResponse
        {
            HasPendingUnitPricesCreated = pendingUnitPricesToCreate?.Any() == true,
            HasExistingPendingUnitPrices = existingPendingUnitPricesList?.Any() == true,
        };
    }
    #endregion

    #region Helpers
    public PendingUnitPriceEntity CreateNewPendingUnitPrice(UnitPriceCreateRequest request, Guid productId, Guid mccId, Guid businessTypeId)
    {
        var unitPrice = mapper.Map<PendingUnitPriceEntity>(request);
        unitPrice.ProductID = productId;
        unitPrice.MCCID = mccId;
        unitPrice.BusinessTypeID = businessTypeId;
        unitPrice.ActionType = ActionTypesEnum.Add;
        unitPrice.Status = StatusEnum.Pending;
        unitPrice.CreatedBy = request.CreatedBy;
        return unitPrice;
    }
    public async Task<List<UnitPriceEntity>> FetchExistedUnitPrices(UnitPriceCreateRequest request)
    {
        var allExistedUnitPricesList = await unitPriceRepository.GetExistUnitPrices(up =>
          request.ProductIDs.Contains(up.ProductID) &&
          request.MCCIDs.Contains(up.MCCID) &&
          request.BusinessTypeIDs.Contains(up.BusinessTypeID));

        return allExistedUnitPricesList;
    }
    public async Task<List<PendingUnitPriceEntity>> FetchExistedPendingUnitPrices(UnitPriceCreateRequest request)
    {
        var allExistedUnitPricesList = await pendingUnitPriceRepository.GetExistPendingUnitPrices(up =>
              up.Status == StatusEnum.Pending &&
              request.ProductIDs.Contains(up.ProductID) &&
              request.MCCIDs.Contains(up.MCCID) &&
              request.BusinessTypeIDs.Contains(up.BusinessTypeID));

        return allExistedUnitPricesList;
    }
    public async Task<List<PendingUnitPriceEntity>> FetchExistingPendingUnitPrices(UnitPriceCreateRequest unitPriceCreateRequest)
    {
        return await pendingUnitPriceRepository.GetExistPendingUnitPrices(up =>
            unitPriceCreateRequest.ProductIDs.Contains(up.ProductID) &&
            unitPriceCreateRequest.MCCIDs.Contains(up.MCCID) &&
            unitPriceCreateRequest.BusinessTypeIDs.Contains(up.BusinessTypeID) &&
            up.Status == (int)StatusEnum.Pending
        );
    }
    public async Task<List<UnitPriceEntity>> FetchExistingUnitPrices(UnitPriceCreateRequest unitPriceCreateRequest)
    {
        return await unitPriceRepository.GetExistUnitPrices(up =>
            unitPriceCreateRequest.ProductIDs.Contains(up.ProductID) &&
            unitPriceCreateRequest.MCCIDs.Contains(up.MCCID) &&
            unitPriceCreateRequest.BusinessTypeIDs.Contains(up.BusinessTypeID)
        );
    }
    public async Task SavePendingUnitPricesAsync(List<PendingUnitPriceEntity> unitPrices)
    {
        try
        {
            await pendingUnitPriceRepository.AddRange(unitPrices);
        }
        catch (DbUpdateException ex) when (ex.InnerException is SqlException sqlEx && sqlEx.Number == 2627)
        {
            throw new InvalidOperationException("A unique constraint violation occurred when attempting to create unit prices.", ex);
        }
    }
    #endregion

    #region Review Process
    private async Task<(PendingUnitPriceEntity? pendingUnitPrice, string? errorCode)> ValidateReviewProcessAsync(Guid id, Guid? createdBy)
    {
        var pendingUnitPrice = await pendingUnitPriceRepository.FirstOrDefaultAsync(x => x.Id == id);

        if (pendingUnitPrice == null)
            return (null, Errors.PendingUnitPriceNotFound.Code);

        if (!IsAuthorizedToReviewPendingRequest(pendingUnitPrice, createdBy))
            return (null, Errors.NotAuthorizedToReviewPendingUnitPrice.Code);

        return (pendingUnitPrice, null);
    }
    public async Task<PendingUnitPriceReviewResponse> ReviewAsync(PendingUnitPriceReviewRequest request)
    {
        var response = request.Status switch
        {
            StatusEnum.Approved => await ApproveAsync(request),
            StatusEnum.Rejected => await RejectAsync(request),
            _ => new PendingUnitPriceReviewResponse()
        };

        return response;
    }
    private async Task<PendingUnitPriceReviewResponse> RejectAsync(PendingUnitPriceReviewRequest request)
    {
        var response = new PendingUnitPriceReviewResponse();

        foreach (var id in request.Ids)
        {
            var (pendingUnitPrice, errorCode) = await ValidateReviewProcessAsync(id, request.CreatedBy);
            if (errorCode != null || pendingUnitPrice == null)
            {
                response.FailedUnitPrices.Add(new FailedUnitPrices { Id = id, ErrorCode = errorCode });
                continue;
            }

            var pendingUnitPriceRequest = await pendingUnitPriceRepository.FirstOrDefaultAsync(x => x.Id == id);

            UpdatePendingUnitPrice(pendingUnitPriceRequest, request);
            await pendingUnitPriceRepository.SaveChangesAsync();
            response.ReviewedUnitPrices.Add(id);
        }

        return response;
    }
    private async Task<PendingUnitPriceReviewResponse> ApproveAsync(PendingUnitPriceReviewRequest request)
    {
        var response = new PendingUnitPriceReviewResponse();

        foreach (var id in request.Ids)
        {
            var (pendingUnitPrice, errorCode) = await ValidateReviewProcessAsync(id, request.CreatedBy);
            if (errorCode != null || pendingUnitPrice == null)
            {
                response.FailedUnitPrices.Add(new FailedUnitPrices { Id = id, ErrorCode = errorCode });
                continue;
            }

            bool approvalResponse = pendingUnitPrice.ActionType switch
            {
                ActionTypesEnum.Add => await HandleAddActionApproval(pendingUnitPrice, request),
                ActionTypesEnum.Edit => await HandleEditActionApproval(pendingUnitPrice, request),
                ActionTypesEnum.Delete => await HandleDeleteActionApproval(pendingUnitPrice, request),
                _ => false
            };

            if (!approvalResponse)
            {
                response.FailedUnitPrices.Add(new FailedUnitPrices { Id = id, ErrorCode = Errors.PendingUnitPriceApprovalFailure.Code });
                continue;
            }
            response.ReviewedUnitPrices.Add(id);
        }

        return response;
    }
    private bool IsAuthorizedToReviewPendingRequest(PendingUnitPriceEntity pendingUnitPrice, Guid? CreatedBy)
    {
        return pendingUnitPrice.CreatedBy != CreatedBy.ToString();
    }
    private async Task<bool> HandleAddActionApproval(PendingUnitPriceEntity pendingUnitPrice, PendingUnitPriceReviewRequest request)
    {
        // Get SQL Server execution strategy
        var strategy = pendingUnitPriceRepository.CreateExecutionStrategy();

        return await strategy.ExecuteAsync(async () =>
        {
            using var transaction = await pendingUnitPriceRepository.BeginTransactionAsync();
            try
            {
                // Share transaction with both repositories
                unitPriceRepository.UseTransaction(transaction);
                pendingUnitPriceRepository.UseTransaction(transaction);

                // Map and add new Unit Price record
                var unitPrice = mapper.Map<UnitPriceEntity>(pendingUnitPrice);
                unitPriceRepository.Add(unitPrice);
                await unitPriceRepository.SaveChangesAsync();

                // Update pending request record - Approve & set unit price id
                pendingUnitPrice.UnitPriceId = unitPrice.Id;
                UpdatePendingUnitPrice(pendingUnitPrice, request);
                await pendingUnitPriceRepository.SaveChangesAsync();

                // Commit transaction
                pendingUnitPriceRepository.CommitTransaction();
                return true;
            }
            catch (Exception ex)
            {
                transaction.Dispose(); // Ensures rollback
                logger.LogError(ex, "HandleAddActionApproval: An error occured while approving pending request with Id '{Id}'", pendingUnitPrice.Id);
                return false;
            }
        });
    }
    private async Task<bool> HandleEditActionApproval(PendingUnitPriceEntity pendingUnitPrice, PendingUnitPriceReviewRequest request)
    {
        // Get SQL Server execution strategy
        var strategy = pendingUnitPriceRepository.CreateExecutionStrategy();

        return await strategy.ExecuteAsync(async () =>
        {
            using var transaction = await pendingUnitPriceRepository.BeginTransactionAsync();
            try
            {
                // Share transaction with both repositories
                unitPriceRepository.UseTransaction(transaction);
                pendingUnitPriceRepository.UseTransaction(transaction);

                UpdatePendingUnitPrice(pendingUnitPrice, request);
                await pendingUnitPriceRepository.SaveChangesAsync();

                await UpdateUnitPrice(pendingUnitPrice, request);
                await unitPriceRepository.SaveChangesAsync();

                pendingUnitPriceRepository.CommitTransaction();
                return true;
            }
            catch (Exception ex)
            {
                transaction.Dispose();
                logger.LogError(ex, "HandleEditActionApproval: An error occured while approving pending request with Id '{Id}'", pendingUnitPrice.Id);
                return false;
            }
        });
    }
    private async Task<bool> HandleDeleteActionApproval(PendingUnitPriceEntity pendingUnitPrice, PendingUnitPriceReviewRequest request)
    {
        // Get SQL Server execution strategy
        var strategy = pendingUnitPriceRepository.CreateExecutionStrategy();

        return await strategy.ExecuteAsync(async () =>
        {
            using var transaction = await pendingUnitPriceRepository.BeginTransactionAsync();
            try
            {
                // Share transaction with both repositories
                unitPriceRepository.UseTransaction(transaction);
                pendingUnitPriceRepository.UseTransaction(transaction);

                UpdatePendingUnitPrice(pendingUnitPrice, request);
                await pendingUnitPriceRepository.SaveChangesAsync();

                await DeleteUnitPrice(pendingUnitPrice, request);
                await unitPriceRepository.SaveChangesAsync();

                pendingUnitPriceRepository.CommitTransaction();
                return true;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "HandleDeleteActionApproval: An error occured while approving pending request with Id '{Id}'", pendingUnitPrice.Id);
                transaction.Dispose();
                return false;
            }
        });
    }
    private void UpdatePendingUnitPrice(PendingUnitPriceEntity pendingUnitPrice, PendingUnitPriceReviewRequest request)
    {
        pendingUnitPrice.Status = request.Status;
        pendingUnitPrice.ReviewedBy = request.CreatedBy;
        pendingUnitPrice.ReviewedDate = DateTime.UtcNow;

        pendingUnitPriceRepository.Update(pendingUnitPrice);
    }
    private async Task UpdateUnitPrice(PendingUnitPriceEntity pendingUnitPrice, PendingUnitPriceReviewRequest request)
    {
        var existingUnitPrice = await unitPriceRepository.FirstOrDefaultAsync(x => x.Id == pendingUnitPrice.UnitPriceId);
        // Update only the allowed properties
        if (existingUnitPrice != null)
        {
            existingUnitPrice.UnitPrice = pendingUnitPrice.NewPrice ?? 0;
            existingUnitPrice.VATRate = pendingUnitPrice.VATRate;
            existingUnitPrice.VATType = pendingUnitPrice.VATType;
            existingUnitPrice.BillingType = pendingUnitPrice.BillingType;
            existingUnitPrice.BillingFrequency = pendingUnitPrice.BillingFrequency;

            unitPriceRepository.Update(existingUnitPrice);
        }
    }
    private async Task DeleteUnitPrice(PendingUnitPriceEntity pendingUnitPrice, PendingUnitPriceReviewRequest request)
    {
        var existingUnitPrice = await unitPriceRepository.FirstOrDefaultAsync(x => x.Id == pendingUnitPrice.UnitPriceId);

        if (existingUnitPrice != null)
        {
            unitPriceRepository.Delete(existingUnitPrice);

            var log = mapper.Map<UnitPriceLogsEntity>(existingUnitPrice);
            log.DeletedBy = request.CreatedBy.ToString();
            log.DeletedDate = DateTime.UtcNow;

            var logsList = new List<UnitPriceLogsEntity>();
            logsList.Add(log);

            await unitPriceRepository.AddLogsAsync(logsList);
        }
    }
    #endregion
}
