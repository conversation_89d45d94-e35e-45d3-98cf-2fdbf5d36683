﻿using FluentValidation;
using Geidea.ProductService.Models;

namespace Common.Validators;

public class MpgsAccountValidator : AbstractValidator<MpgsAccount>
{
    public MpgsAccountValidator()
    {
        RuleFor(mpgsAccount => mpgsAccount.MpgsMerchantId)
            .NotEmpty()
            .MaximumLength(255)
            .When(mpgsAccount => mpgsAccount.MpgsMerchantId is not null);

        RuleFor(mpgsAccount => mpgsAccount.MpgsApiKey)
            .NotEmpty()
            .MaximumLength(255)
            .When(mpgsAccount => mpgsAccount.MpgsApiKey is not null);

        RuleFor(mpgsAccount => mpgsAccount.CardBrands).NotNull();

        RuleForEach(mpgsAccount => mpgsAccount.CardBrands)
            .NotEmpty()
            .MaximumLength(255)
            .WithMessage("MPGS card brands cannot be null or empty");
    }
}