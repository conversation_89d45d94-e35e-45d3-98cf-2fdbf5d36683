﻿using Common.Entities;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using Xunit;

namespace DataAccess.Test;

public class MetaDataMigrationFilesRepositoryTests
{
    private readonly DataContext context;
    private readonly MetaDataMigrationFilesRepository metaDataMigrationFilesRepository;

    private readonly MetaDataMigrationFilesEntity metaDataMigrationFiles = new MetaDataMigrationFilesEntity
    {
        Id = Guid.NewGuid(),
        MigrationFileName = "test1",
        UpdateQuery = "UPDATE pi SET Metadata = JSON_MODIFY(pi.Metadata, '$.TestManualMigration', 'true') FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE p.Type = 'GWAY' AND ISJSON(pi.Metadata) = 1 AND Counterparty = 'GEIDEA_EGYPT' ",
        SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE p.Type = 'GWAY' AND ISJSON(pi.Metadata) = 1 AND Counterparty = 'GEIDEA_EGYPT' ",
    };
    public MetaDataMigrationFilesRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<DataContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        context = new DataContext(options, new CounterpartyProvider());
        metaDataMigrationFilesRepository = new MetaDataMigrationFilesRepository(context);


    }

    [Fact]
    public async Task GetFinishedMigrationFiles()
    {
        var response = await metaDataMigrationFilesRepository.GetAllMigrationFiles();

        response.Should().NotBeNull();
    }

    [Fact]
    public async Task InsertAsyncMetaMigrationRepository()
    {
        await metaDataMigrationFilesRepository.InsertAsync(metaDataMigrationFiles);

        var retrievedInstance = await context.MetaDataMigrationFiles.FirstOrDefaultAsync(p => p.Id == metaDataMigrationFiles.Id);

        retrievedInstance.Should().NotBeNull();
    }
}