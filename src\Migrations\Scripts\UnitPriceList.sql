﻿CREATE OR ALTER VIEW UnitPriceList
As
	SELECT 
            u.Id,
			p.Id AS ProductId,
            p.[Name] AS ProductName,
			m.Id AS MccId,
            m.[Name] AS MccName,
			b.Id As BusinessTypeId,
            b.[Name] AS BusinessType,
            u.UnitPrice,
            u.VATRate,
            p.Code AS ProductCode,
            m.Code AS MccCode,
            mc.Name AS MccCategory,
            u.VATType,
            u.BillingType,
            u.BillingFrequency,
            u.CreatedDate
        FROM 
           [dbo].[UnitPrice]  u
            JOIN [dbo].[Products]  p ON u.ProductID = p.Id
            JOIN [dbo].[BusinessTypes] b ON u.BusinessTypeID = b.Id
            JOIN [dbo].[Mcc] m ON u.MCCID = m.Id
            JOIN [dbo].[MccCategory] mc ON m.MccCategoryId = mc.Id
