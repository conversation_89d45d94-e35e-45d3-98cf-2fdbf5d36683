﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_02_11_1730)]
public class ProductInstance_AddIsCvvRequiredForTokenPayments : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
 UPDATE pi
 SET Metadata = JSON_MODIFY(pi.Metadata, '$.IsCvvRequiredForTokenPayments', CAST(1 as BIT))
 FROM ProductInstances pi
 INNER JOIN Products p ON p.id = pi.ProductId
 WHERE p.Type = N'GWAY' AND p.Counterparty = N'GEIDEA_EGYPT' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0
 ");
    }
}
