﻿using System;


namespace Common.Models.NonTransactionalPrice;
public struct NonTransactionalPriceCombinationKey
{
    public Guid ProductId { get; }
    public Guid MccId { get; }
    public Guid BusinessTypeId { get; }
    public Guid NonTransFeeId { get; }

    public NonTransactionalPriceCombinationKey(Guid productId, Guid mccId, Guid businessTypeId, Guid NonTransFeeID)
    {
        ProductId = productId;
        MccId = mccId;
        BusinessTypeId = businessTypeId;
        NonTransFeeId = NonTransFeeID;
    }
    public override bool Equals(object? obj)
    {
        if (obj is NonTransactionalPriceCombinationKey key)
        {
            return ProductId == key.ProductId && MccId == key.MccId && BusinessTypeId == key.BusinessTypeId
                && NonTransFeeId == key.NonTransFeeId;
        }
        return false;
    }
    public override int GetHashCode()
    {
        return HashCode.Combine(ProductId, MccId, BusinessTypeId, NonTransFeeId);
    }
    public static bool operator ==(NonTransactionalPriceCombinationKey left, NonTransactionalPriceCombinationKey right)
    {
        return left.Equals(right);
    }
    public static bool operator !=(NonTransactionalPriceCombinationKey left, NonTransactionalPriceCombinationKey right)
    {
        return !(left == right);
    }
}
