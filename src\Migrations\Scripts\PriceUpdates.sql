﻿DECLARE @bundles TABLE (id uniqueidentifier)

INSERT @bundles SELECT Id FROM Products WHERE Code='GO_AIR'
UPDATE Prices SET PerItemPrice=2900 WHERE ProductId IN (SELECT * FROM @bundles)
DELETE FROM @bundles

INSERT @bundles SELECT Id FROM Products WHERE Code='GO_LITE'
UPDATE Prices SET PerItemPrice=3900 WHERE ProductId IN (SELECT * FROM @bundles)
DELETE FROM @bundles

INSERT @bundles SELECT Id FROM Products WHERE Code='GO_SMART'
UPDATE Prices SET PerItemPrice=13900 WHERE ProductId IN (SELECT * FROM @bundles)
DELETE FROM @bundles

INSERT @bundles SELECT Id FROM Products WHERE Code='BUSINESS'
UPDATE Prices SET PerItemPrice=32900 WHERE ProductId IN (SELECT * FROM @bundles)
DELETE FROM @bundles

INSERT @bundles SELECT Id FROM Products WHERE Code='ENTERPRISE'
UPDATE Prices SET PerItemPrice=49900 WHERE ProductId IN (SELECT * FROM @bundles)
DELETE FROM @bundles

INSERT @bundles SELECT Id FROM Products WHERE Code='WEBSITE_BUILDER'
UPDATE Prices SET PerItemPrice=9900 WHERE ProductId IN (SELECT * FROM @bundles)
DELETE FROM @bundles

INSERT @bundles SELECT Id FROM Products WHERE Code='PAYMENT_GATEWAY'
UPDATE Prices SET PerItemPrice=9900 WHERE ProductId IN (SELECT * FROM @bundles)
DELETE FROM @bundles

--create separate GO_SMART bundle for pro category
DECLARE @proCategory TABLE (id uniqueidentifier)
DECLARE @ProSmartId UNIQUEIDENTIFIER
DECLARE @ProductIds TABLE(ID UNIQUEIDENTIFIER)
DECLARE @RestaurantCategoryId UNIQUEIDENTIFIER
DECLARE @RetailCategoryId UNIQUEIDENTIFIER
DECLARE @SmartPosId UNIQUEIDENTIFIER
DECLARE @GeideaGoAppId UNIQUEIDENTIFIER
DECLARE @Ids TABLE(ID UNIQUEIDENTIFIER)

INSERT @Ids SELECT Id FROM Category WHERE Code='Restaurant'
SELECT TOP 1 @RestaurantCategoryId = ID FROM @Ids 
DELETE FROM @Ids

INSERT @Ids SELECT Id FROM Category WHERE Code='Retail'
SELECT TOP 1 @RetailCategoryId = ID FROM @Ids 
DELETE FROM @Ids

INSERT @Ids SELECT Id FROM Products WHERE Code='SMARTPOS_A920'
SELECT TOP 1 @SmartPosId = ID FROM @Ids 
DELETE FROM @Ids

INSERT @Ids SELECT Id FROM Products WHERE Code='GEIDEA_GO_APP'
SELECT TOP 1 @GeideaGoAppId = ID FROM @Ids 
DELETE FROM @Ids

INSERT @bundles SELECT Id FROM Products WHERE Code='GO_SMART'
DELETE FROM ProductCategories WHERE ProductId IN (SELECT * FROM @bundles) AND CategoryId IN (@RestaurantCategoryId, @RetailCategoryId)

INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
VALUES('Bundle', 'PRO_SMART', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())

SELECT TOP 1 @ProSmartId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@ProSmartId, @RestaurantCategoryId)
INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@ProSmartId, @RetailCategoryId)

INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
VALUES('MONTH', 'RECCURRING_CHARGE', 0, @ProSmartId, 22900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @SmartPosId)
INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @GeideaGoAppId)

DECLARE @schemeCursor CURSOR
DECLARE @id UNIQUEIDENTIFIER

SET @schemeCursor = CURSOR FOR SELECT Id FROM Products WHERE Type = 'SCHEME'

OPEN @schemeCursor FETCH NEXT FROM @schemeCursor INTO @id WHILE @@FETCH_STATUS = 0
BEGIN
    INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @id)
    FETCH NEXT FROM @schemeCursor INTO @id
END

CLOSE @schemeCursor
DEALLOCATE @schemeCursor
