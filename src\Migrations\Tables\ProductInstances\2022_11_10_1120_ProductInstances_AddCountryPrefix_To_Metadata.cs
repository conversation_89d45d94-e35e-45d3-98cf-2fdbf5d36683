﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_11_10_1120)]
public class ProductInstances_AddCountryPrefix_To_Metadata : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
                UPDATE ProductInstances 
                  SET [Metadata] = JSON_MODIFY([Metadata], '$.SubMerchantInformation.CountryPrefix', '+20')
                WHERE  [Metadata] IS NOT NULL  
                  AND ISJSON([Metadata]) > 0
                  AND JSON_QUERY(metadata, '$.SubMerchantInformation') IS NOT NULL
                  AND JSON_VALUE(metadata, '$.SubMerchantInformation.CountryPrefix') IS NULL
                  AND ProductId in (SELECT ID FROM [Products] WHERE Type = 'GWAY' and Counterparty = 'GEIDEA_EGYPT')
            ");
    }
}