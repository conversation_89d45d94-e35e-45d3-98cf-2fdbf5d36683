﻿using FluentMigrator;

namespace Migrations.Tables.Products;
[Migration(2024_03_19_0200)]
public class UaeAddCUPProductToProductTbl : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
DECLARE @CUPProductId_UAE AS uniqueidentifier=NEWID();
 
--add new product with type CUP_GW to UAE

INSERT INTO [dbo].[Products]
([Id]
,[Availability]
,[Code]
,[Type]
,[ValidFrom]
,[CreatedBy]
,[CreatedDate]
,[UpdatedBy]
,[Version]
,[Counterparty],
[DisplayOrder],
[SalesChannel])
VALUES
(@CUPProductId_UAE
,'Live'
,'CUP_GW'
,'SCHEME'
,GETUTCDATE()
,'00000000-0000-0000-0000-000000000000'
,GETUTCDATE()
,'00000000-0000-0000-0000-000000000000'
,0
,'GEIDEA_UAE',NULL,NULL)

--add new CUP_UAE product as part of BUNDLE

INSERT INTO ProductParts (ProductId, PartId, Quantity) VALUES
((SELECT TOP 1 Id FROM Products WHERE type='BUNDLE' AND Counterparty='GEIDEA_UAE' AND Availability='Live' AND Code='PAYMENT_GATEWAY_BUNDLE'), @CUPProductId_UAE, 1)

");
    }
}
