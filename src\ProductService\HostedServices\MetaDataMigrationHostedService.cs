﻿using Common.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace ProductService.HostedServices;

public class MetaDataMigrationHostedService : IHostedService
{
    private readonly IServiceProvider serviceProvider;
    private readonly ILogger<MetaDataMigrationHostedService> logger;
    public MetaDataMigrationHostedService(IServiceProvider serviceProvider, ILogger<MetaDataMigrationHostedService> logger)
    {
        this.serviceProvider = serviceProvider;
        this.logger = logger;
    }
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            using (var scope = serviceProvider.CreateScope())
            {
                var metaDataMigrationService = scope.ServiceProvider.GetRequiredService<IMetaDataMigrationService>();

                logger.LogInformation("MigrationHostedService starting.");

                await metaDataMigrationService.MigrateMetaDataScripts();

                logger.LogInformation("MigrationHostedService finished.");
            }
        }
        catch (Exception ex)
        {
            logger.LogError("update metadata migration files failed,\n {Message}", ex.Message);
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("MigrationHostedService stopped.");
        return Task.CompletedTask;
    }
}