﻿using Common.Enums;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.CommissionFees;
[ExcludeFromCodeCoverage]

public class CommissionFeesListResponse
{
    public Guid Id { get; set; }
    public string? Code { get; set; }
    public string? Name { get; set; }
    public Guid ImageId { get; set; }
    public Status Status { get; set; }
    public DateTime CreatedDate { get; set; }
}
