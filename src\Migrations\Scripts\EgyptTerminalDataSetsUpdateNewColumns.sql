BEGIN TRANSACTION;

--Update Values to New Columns [ChannelType, ConnectionType]
UPDATE tds
SET
    tds.[ChannelType] = 'SOFT_POS',
	tds.[ConnectionType] = 'MPGS'
FROM 
	[PRODUCTS].[dbo].[TerminalDataSets] tds
	JOIN [PRODUCTS].[dbo].[ProductInstances] AS Pis ON Pis.Id = tds.ProductInstanceId
	JOIN [PRODUCTS].[dbo].[Products] AS P ON P.Id = Pis.ProductId
	WHERE P.Counterparty = tds.Counterparty AND P.Counterparty = 'GEIDEA_EGYPT' 
	AND P.[Type] = 'Terminal' AND P.Code = 'SOFT_POS';
GO

UPDATE tds
SET
    tds.[ChannelType] = 'SMART_POS',
	tds.[ConnectionType] = 'H2H'
FROM 
	[PRODUCTS].[dbo].[TerminalDataSets] tds
	JOIN [PRODUCTS].[dbo].[ProductInstances] AS Pis ON Pis.Id = tds.ProductInstanceId
	JOIN [PRODUCTS].[dbo].[Products] AS P ON P.Id = Pis.ProductId
	WHERE P.Counterparty = tds.Counterparty AND P.Counterparty = 'GEIDEA_EGYPT' 
	AND ((P.[Type] = 'Terminal' AND P.Code <> 'SOFT_POS') OR (P.[Type] = 'M_POS'));
GO

COMMIT;