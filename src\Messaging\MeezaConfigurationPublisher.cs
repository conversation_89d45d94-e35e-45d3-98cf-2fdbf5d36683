﻿using System;
using System.Linq;
using System.Text.Json;
using Common;
using Common.Data;
using Common.Data.ProductType;
using Common.Entities;
using Common.Services;
using Geidea.Messages.MeezaConfiguration;
using Geidea.Messages.MeezaConfiguration.Messages;
using GeideaPaymentGateway.Utils.RabbitMQ;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Generic;

namespace Messaging;

public class MeezaConfigurationPublisher : ConfigurationPublisherPublisher, IProductInstancePublisher
{
    private const string MeezaConfigurationCreated = "meeza.configuration.created.event";
    private const string MeezaConfigurationUpdated = "meeza.configuration.updated.event";
    private const string MeezaConfigurationDeleted = "meeza.configuration.deleted.event";

    public List<ProductTypes> ProductTypes => new List<ProductTypes> { Common.Data.ProductType.ProductTypes.MEEZA };

    public MeezaConfigurationPublisher(IOptionsMonitor<RabbitMqConfig> options,
        ILogger<MeezaConfigurationPublisher> logger,
        IHttpContextAccessor contextAccessor,
        IConnectionFactory factory)
        : base(options, logger, contextAccessor, factory, "MeezaConfiguration.Fanout")
    {
    }

    public void PublishCreatedEvent(ProductInstanceEntity productInstance)
    {
        if (!IsConfigValid)
            return;

        var message = new MeezaConfigurationCreatedEvent
        {
            Header = BuildHeader(MeezaConfigurationCreated),
            MeezaConfigurationId = productInstance.Id,
            CompanyId = productInstance.CompanyId ?? default,
            MeezaConfiguration = BuildMeezaConfiguration(productInstance)
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(MeezaConfigurationCreated, messageAsByteArray);
    }

    public void PublishUpdatedEvent(ProductInstanceEntity productInstance)
    {
        if (!IsConfigValid)
            return;

        var message = new MeezaConfigurationUpdatedEvent
        {
            Header = BuildHeader(MeezaConfigurationUpdated),
            MeezaConfigurationId = productInstance.Id,
            CompanyId = productInstance.CompanyId ?? default,
            MeezaConfiguration = BuildMeezaConfiguration(productInstance)
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(MeezaConfigurationUpdated, messageAsByteArray);
    }

    public void PublishDeletedEvent(Guid productInstanceId)
    {
        if (!IsConfigValid)
            return;

        var message = new MeezaConfigurationDeletedEvent()
        {
            Header = BuildHeader(MeezaConfigurationDeleted),
            MeezaConfigurationId = productInstanceId
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(MeezaConfigurationDeleted, messageAsByteArray);
    }

    private MeezaConfiguration BuildMeezaConfiguration(ProductInstanceEntity meezaProductInstance)
    {
        if (!(meezaProductInstance.Data is MeezaData meezaData))
        {
            logger.LogError("Expected meeza product instance.");
            throw new ServiceException(System.Net.HttpStatusCode.BadRequest, Errors.ExpectedMeeza);
        }

        var result = new MeezaConfiguration
        {
            MeezaMerchantId = meezaData.MeezaMerchantId,
            RegistrationStatus = meezaData.RegistrationStatus,
            RegistrationHistory = meezaData.RegistrationHistory.Select(h => new MeezaRegistrationHistory()
            {
                Status = h.Status,
                Reason = h.Reason,
                ChangeDate = h.ChangeDate,
                Initiator = h.Initiator,
                ErrorResponse = h.ErrorResponse,
                Type = h.Type
            }).ToList(),
        };

        return result;
    }
}
