﻿using Common.Models.Apex;
using Common.Options;
using Common.Services;
using Geidea.Utils.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Services;
public class ApexToken : IApexToken
{
    private const string ApexGenerateTokenEndpoint = "generate/token";

    private readonly HttpClient httpClient;
    private readonly ApexSettings apexSettings;
    private readonly ILogger<ApexClient> logger;
    private readonly ExternalApiSettings externalApiSettings;


    public ApexToken(
      HttpClient httpClient,
      IOptionsMonitor<ApexSettings> apexOptions,
      ILogger<ApexClient> logger, IOptionsMonitor<ExternalApiSettings> externalApiOtions)
    {

        HttpClientHandler clientHandler = new HttpClientHandler();
        clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
        httpClient = new HttpClient(clientHandler);
        this.httpClient = httpClient;
        apexSettings = apexOptions.CurrentValue;
        this.logger = logger;
        this.externalApiSettings = externalApiOtions.CurrentValue;
    }

    public async Task<ApexTokenResponse> GetTokenAsync()
    {
        var tokenResponse = new ApexTokenResponse();
        var apexEnvironmentSetting = apexSettings;
        var baseUrl = externalApiSettings.BaseUrl;
        var credentials = $"{apexEnvironmentSetting.clientId}:{apexEnvironmentSetting.clientsecret}";
        var apiKey = Convert.ToBase64String(Encoding.UTF8.GetBytes(credentials));
        var url = $"{baseUrl}/{ApexGenerateTokenEndpoint}";
        var authorizationHeader = $"Basic {apiKey}";
        var contentType = "application/x-www-form-urlencoded";
        var grantType = "client_credentials";

        HttpContent content =
                   new FormUrlEncodedContent(new[] { new KeyValuePair<string, string>("grant_type", grantType) })
                   {
                       Headers = { ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(contentType) }
                   };

        Dictionary<string, string>? headers = new Dictionary<string, string>
                    {
                        { "Authorization",authorizationHeader },
                    };
        try
        {
            httpClient.DefaultRequestHeaders.Add("Authorization", authorizationHeader);
            logger.LogInformation("Apex token setup. {credentials}", credentials);

            var response = await httpClient.PostAsync(url, content);
            var responseData = await response.Content.ReadAsStringAsync();
            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
                tokenResponse = Json.Deserialize<ApexTokenResponse>(responseBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            }
            else
            {
                logger.LogInformation("Error Failed to retrieve token. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, response.ReasonPhrase);
            }
        }
        catch (Exception ex)
        {
            logger.LogCritical("Error Failed to retrieve token. Error was {@responseBody}", ex);


        }

        return tokenResponse;
    }
}
