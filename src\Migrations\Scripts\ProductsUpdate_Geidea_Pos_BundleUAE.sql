﻿USE PRODUCTS; 
GO



DECLARE @CounterParty VARCHAR(10);
DECLARE @GeideaPOS VARCHAR(10);
DECLARE @OMA880 VARCHAR(10);



BEGIN TRANSACTION;



SET @CounterParty ='GEIDEA_UAE'
SET @GeideaPOS = 'GEIDEA_POS'
SET @OMA880='OMA 880'

--Categories
INSERT INTO [PRODUCTS].[dbo].[CATEGORY](Code, Type, CreatedBy, CreatedDate,CounterParty, [DisplayOrder]) 
VALUES('GO_FAMILY', 0, 'n/a', GETUTCDATE(),@CounterParty ,1) 

DECLARE @CategoryId UNIQUEIDENTIFIER;
SELECT TOP 1 @CategoryId = [Id] FROM  [PRODUCTS].[dbo].[CATEGORY] WHERE [Code] = 'GO_FAMILY' AND [Counterparty] = @CounterParty
-- Bundles

INSERT INTO [PRODUCTS].[dbo].[Products](Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty) 
VALUES('Live', @GeideaPOS, 'BUNDLE', GETUTCDATE(), '00000000-0000-0000-0000-000000000000', GETUTCDATE(), 1, 0, @CounterParty)

DECLARE @GeideaPosBundleId UNIQUEIDENTIFIER;
SELECT TOP 1 @GeideaPosBundleId = [Id] FROM  [PRODUCTS].[dbo].[PRODUCTS] WHERE [Code] = @GeideaPOS AND [Counterparty] = @CounterParty


-- Products 

INSERT INTO [PRODUCTS].[dbo].[Products](Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty)
VALUES('Live', @OMA880, 'TERMINAL', GETUTCDATE(), '00000000-0000-0000-0000-000000000000', GETUTCDATE(), 1, 0, @CounterParty)

DECLARE @OMA880Id UNIQUEIDENTIFIER;
SELECT TOP 1 @OMA880Id = [Id] FROM  [PRODUCTS].[dbo].[PRODUCTS] WHERE [Code] = @OMA880 AND [Counterparty] = @CounterParty


-- ProductCategories
INSERT INTO [PRODUCTS].[dbo].[ProductCategories](ProductId, CategoryId) VALUES(@GeideaPosBundleId, @CategoryId)
INSERT INTO [PRODUCTS].[dbo].[ProductCategories](ProductId, CategoryId) VALUES(@OMA880Id,@CategoryId )


-- Product Parts
INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@GeideaPosBundleId, @OMA880Id)

-- Card schemes
DECLARE @MeezaScheme UNIQUEIDENTIFIER
SELECT TOP 1 @MeezaScheme = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = @CounterParty AND Code = 'MEEZA'

DECLARE @VisaScheme UNIQUEIDENTIFIER
SELECT TOP 1 @VisaScheme = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = @CounterParty AND Code = 'VISA'

DECLARE @MasterCardScheme UNIQUEIDENTIFIER
SELECT TOP 1 @MasterCardScheme = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = @CounterParty AND Code = 'MC'

INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@GeideaPosBundleId, @MeezaScheme)
INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@GeideaPosBundleId, @VisaScheme)
INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@GeideaPosBundleId, @MasterCardScheme)


COMMIT;

