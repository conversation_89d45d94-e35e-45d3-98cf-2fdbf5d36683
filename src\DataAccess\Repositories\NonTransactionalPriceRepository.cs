﻿using AutoMapper;
using Common.Entities;
using Common.Enums;
using Common.Models.NonTransactionalPrice;
using Common.Models.ProductCommissionPrice;
using Common.Repositories;
using Common.Views;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Common.Data.Extensions;
using System.Linq.Dynamic.Core;
using EFCore.BulkExtensions;
using DocumentFormat.OpenXml.InkML;
namespace DataAccess.Repositories;
public class NonTransactionalPriceRepository : AuditableRepository<Guid, NonTransactionalPriceEntity>, INonTransactionalPriceRepository
{
    private IQueryable<NonTransactionalFeesPriceListView> NonTransactionalFeesPriceListQuery;
    public NonTransactionalPriceRepository(DataContext context, IHttpContextAccessor contextAccessor)
        : base(context, contextAccessor)
    {
        NonTransactionalFeesPriceListQuery = context.NonTransactionalFeesPriceList.AsQueryable();
    }

    public async Task<int> DeleteBulkAsync(List<Guid> ids)
    {
        var deletedCount = await context.Set<NonTransactionalPriceEntity>()
            .Where(up => ids.Contains(up.Id))
            .BatchDeleteAsync();

        return deletedCount;
    }
    public async Task<NonTransactionalPriceEntity?> GetByIdAsync(Guid id)
    {
        return await context.Set<NonTransactionalPriceEntity>().FirstOrDefaultAsync(a => a.Id == id);
    }

    public async Task<List<NonTransactionalPriceEntity>> GetExistNonTransactionalPrices(Expression<Func<NonTransactionalPriceEntity, bool>> predicate)
    {
        return await context.Set<NonTransactionalPriceEntity>().Where(predicate).ToListAsync();
    }

    public async Task<NonTransactionalPriceEntity?> GetNonTransactionalPriceByIdAsync(Guid id)
    {
        return await context.Set<NonTransactionalPriceEntity>()
           .FirstAsync(up => up.Id == id);
    }
    public async Task<List<NonTransactionalPriceEntity>> GetNonTransactionalPricesByIdsAsync(List<Guid> ids)
    {
        return await context.Set<NonTransactionalPriceEntity>()
            .Where(up => ids.Contains(up.Id))
            .ToListAsync();
    }
    public async Task SaveNonTransactionalPricesAsync(List<NonTransactionalPriceEntity> nonTransactionalPrices)
    {
        AddRange(nonTransactionalPrices);
        await context.SaveChangesAsync();
    }
    public void AddRange(IEnumerable<NonTransactionalPriceEntity> entities)
    {
        context.Set<NonTransactionalPriceEntity>().AddRange(entities);
    }

    public async Task<NonTransactionalFeesPriceListResponse> GetNonTransactionalPriceList(NonTransactionalFeesPriceListRequest request)
    {
        NonTransactionalFeesPriceListQuery = NonTransactionalFeesPriceListQuery.AsNoTracking();
        NonTransactionalFeesPriceListQuery = SearchNonTransactionlPrices(request, NonTransactionalFeesPriceListQuery);
        NonTransactionalFeesPriceListQuery = FilterNonTransactionalPrices(request, NonTransactionalFeesPriceListQuery);
        NonTransactionalFeesPriceListQuery = SortNonTransactionalPrices(request, NonTransactionalFeesPriceListQuery);
        int TotalCount = await NonTransactionalFeesPriceListQuery.CountAsync();

        var nonTransactionalFeesPrice = request.Size > 0
            ? await NonTransactionalFeesPriceListQuery.Page(request.Page, request.Size).ToArrayAsync()
            : await NonTransactionalFeesPriceListQuery.ToArrayAsync();
        return new NonTransactionalFeesPriceListResponse
        {
            TotalCount = TotalCount,
            TotalPages = (int)Math.Ceiling((double)TotalCount / request.Size),
            NonTransactionalPriceList = nonTransactionalFeesPrice
        };
    }

    public async Task AddLogsAsync(List<NonTransactionalPriceLogEntity> logs)
    {

        await context.Set<NonTransactionalPriceLogEntity>().AddRangeAsync(logs);
        await context.SaveChangesAsync();
    }

    #region Private Methods
    private static IQueryable<NonTransactionalFeesPriceListView> SearchNonTransactionlPrices(NonTransactionalFeesPriceListRequest request,
IQueryable<NonTransactionalFeesPriceListView> NonTransactionalPriceList)
    {
        var searchTerm = request.SearchTerms.FirstOrDefault(s => !string.IsNullOrEmpty(s.Value));

        if (!string.IsNullOrEmpty(searchTerm.Value))
        {
            var searchDictionary = new Dictionary<NonTransactionalFeesPriceSearchKey, Func<IQueryable<NonTransactionalFeesPriceListView>, string, IQueryable<NonTransactionalFeesPriceListView>>>
            {
                { NonTransactionalFeesPriceSearchKey.All, (query, value) => query.Where(s => s.ProductName != null && s.ProductName.Contains(value) ||
                                                                              s.ProductCode.Contains(value) ||
                                                                              s.MccName.Contains(value) ||
                                                                              s.NonTransFeeName.Contains(value) ||
                                                                              s.NonTransFeeCode.Contains(value) ||
                                                                              s.MccCode.Contains(value)) },
                { NonTransactionalFeesPriceSearchKey.ProductName, (query, value) => query.Where(s => s.ProductName != null && s.ProductName.Contains(value)) },
                { NonTransactionalFeesPriceSearchKey.ProductCode, (query, value) => query.Where(s => s.ProductCode.Contains(value)) },
                { NonTransactionalFeesPriceSearchKey.MccName, (query, value) => query.Where(s => s.MccName.Contains(value)) },
                { NonTransactionalFeesPriceSearchKey.MccCode, (query, value) => query.Where(s => s.MccCode.Contains(value)) },
                { NonTransactionalFeesPriceSearchKey.FeeName, (query, value) => query.Where(s => s.NonTransFeeName.Contains(value)) },
                { NonTransactionalFeesPriceSearchKey.FeeCode, (query, value) => query.Where(s => s.NonTransFeeCode.Contains(value)) }
            };

            if (searchDictionary.ContainsKey(searchTerm.Key))
            {
                NonTransactionalPriceList = searchDictionary[searchTerm.Key](NonTransactionalPriceList, searchTerm.Value);
            }
        }

        return NonTransactionalPriceList;
    }

    private static IQueryable<NonTransactionalFeesPriceListView> FilterNonTransactionalPrices(NonTransactionalFeesPriceListRequest request,
        IQueryable<NonTransactionalFeesPriceListView> NonTransactionalPriceList)
    {
        if (request.ProductsFilter != null && request.ProductsFilter.Any())
            NonTransactionalPriceList = NonTransactionalPriceList.Where(s => request.ProductsFilter.Contains(s.ProductId));

        if (request.BusinessTypeFilter != null && request.BusinessTypeFilter.Any())
            NonTransactionalPriceList = NonTransactionalPriceList.Where(s => request.BusinessTypeFilter.Contains(s.BusinessTypeId));

        if (request.MccTypeFilter != null && request.MccTypeFilter.Any())
            NonTransactionalPriceList = NonTransactionalPriceList.Where(s => request.MccTypeFilter.Contains(s.MccId));

        if (request.NonTransactionalFeeFilter != null && request.NonTransactionalFeeFilter.Any())
            NonTransactionalPriceList = NonTransactionalPriceList.Where(s => request.NonTransactionalFeeFilter.Contains(s.NonTransFeeId));

        if (request.BillingTypeFilter != null && request.BillingTypeFilter.Any())
            NonTransactionalPriceList = NonTransactionalPriceList.Where(s => request.BillingTypeFilter.Contains(s.BillingType));

        if (request.BillingFrequencyFilter != null && request.BillingFrequencyFilter.Any())
            NonTransactionalPriceList = NonTransactionalPriceList.Where(s => request.BillingFrequencyFilter.Contains(s.BillingFrequency));

        if (request.FilterByFeeStatus != null && request.FilterByFeeStatus.Any())
            NonTransactionalPriceList = NonTransactionalPriceList.Where(s => request.FilterByFeeStatus.Contains(s.FeeStatus));
        return NonTransactionalPriceList;
    }
    private static IQueryable<NonTransactionalFeesPriceListView> SortNonTransactionalPrices(NonTransactionalFeesPriceListRequest request,
      IQueryable<NonTransactionalFeesPriceListView> NonTransactionalPriceList)
    {
        if (Enum.TryParse(request.OrderType, true, out SortType orderType) && !string.IsNullOrEmpty(request.OrderFieldName))
            NonTransactionalPriceList = NonTransactionalPriceList.OrderBy(request.OrderFieldName, orderType);

        return NonTransactionalPriceList;
    }
    #endregion

    public async Task UpdateNonTransactionalPricesAsync(List<NonTransactionalPriceEntity> nonTransPrices)
    {
        context.Set<NonTransactionalPriceEntity>().UpdateRange(nonTransPrices);
        await context.SaveChangesAsync();
    }
}
