﻿using AutoMapper;
using Common;
using Common.Entities;
using Common.Enums;
using Common.Models;
using Common.Models.businessType;
using Common.Models.ValueAddedServices;
using Common.Repositories;
using Common.Services;
using Common.Validators;
using DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Services;

public class ValueAddedServiceService : IValueAddedServiceService
{
    #region Fields

    private readonly ILogger<ValueAddedServiceService> logger;
    private readonly IValueAddedServiceRepository valueAddedServiceRepository;
    private readonly IMapper mapper;

    #endregion

    #region Constructor

    public ValueAddedServiceService(ILogger<ValueAddedServiceService> logger,
                          IValueAddedServiceRepository valueAddedServiceRepository,
                          IMapper mapper
                          )
    {
        this.logger = logger;
        this.valueAddedServiceRepository = valueAddedServiceRepository;
        this.mapper = mapper;
    }

    #endregion

    #region Methods

    #region CreateAsync
    public async Task<ValueAddedServiceResponse> CreateAsync(ValueAddedServiceRequest valueAddedServiceRequest)
    {

        var codeExists = await valueAddedServiceRepository.ExistsAsync(v => v.Code == valueAddedServiceRequest.Code);
        var valueAddedService = mapper.Map<ValueAddedService>(valueAddedServiceRequest);
        if (codeExists)
        {
            logger.LogError("CreateAsync: Invalid VAS ID '{Code}' already exists", valueAddedServiceRequest.Code);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.VasIdAlreadyExist);
        }
        try
        {
            valueAddedServiceRepository.Add(valueAddedService);
            await valueAddedServiceRepository.SaveChangesAsync();

            logger.LogInformation("Added Value Added Service with id '{ValueAddedServiceId}' and Name '{ValueAddedServiceName}'.", valueAddedService.Id, valueAddedService.Name);
            return mapper.Map<ValueAddedServiceResponse>(valueAddedService);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating Value Added Service with Name '{Name}'.", valueAddedServiceRequest.Name);
            throw new ServiceException(HttpStatusCode.BadRequest, $"Error creating Value Added Service: {ex.Message}");
        }
    }
    #endregion

    #region UpdateAsync
    public async Task<ValueAddedServiceResponse> UpdateAsync(Guid id, ValueAddedServiceRequest valueAddedServiceRequest)
    {

        var existingValueAddedService = await valueAddedServiceRepository.GetByIdAsync(id);

        if (existingValueAddedService == null)
        {
            logger.LogWarning("Value Added Service with id '{id}' not found.", id);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ValueAddedServicesIdNotFound);
        }

        if (existingValueAddedService.Code != valueAddedServiceRequest.Code &&
         await valueAddedServiceRepository.AnyAsync(x => x.Code == valueAddedServiceRequest.Code))
        {
            logger.LogError("A value added services with code '{Code}' already exists.", valueAddedServiceRequest.Code);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.VasIdAlreadyExist);
        }

        mapper.Map(valueAddedServiceRequest, existingValueAddedService);

        valueAddedServiceRepository.Update(existingValueAddedService);
        await valueAddedServiceRepository.SaveChangesAsync();

        logger.LogInformation("Updated Value Added Service with id '{ValueAddedServiceId}' and Code '{ValueAddedServiceCode}'.", existingValueAddedService.Id, existingValueAddedService.Code);

        return mapper.Map<ValueAddedServiceResponse>(existingValueAddedService);

    }
    #endregion

    #region GetValueAddedServicesListAsync

    public async Task<ValueAddedServicesListResponse> GetValueAddedServicesListAsync(GetValueAddedServicesListRequest request)
    {
        if (request == null)
        {
            logger.LogError("Invalid Get Value Added Services List request.");
            throw new ServiceException(HttpStatusCode.BadRequest, "Invalid Get Value Added Services List request.");
        }
        try
        {
            return await valueAddedServiceRepository.GetValueAddedServicesList(request);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "something has went wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, "something has went wrong!");
        }
    }
    #endregion

    #region GetValueAddedServicesDetailsAsync
    public async Task<ValueAddedServiceDetailsResponse> GetValueAddedServicesDetailsAsync(Guid id)
    {

        var valueAddedServiceDetails = await valueAddedServiceRepository.GetByIdAsync(id);

        if (valueAddedServiceDetails == null)
        {
            logger.LogError("Value Added Service with id '{id}' not found.", id);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ValueAddedServicesIdNotFound);
        }

        return mapper.Map<ValueAddedServiceDetailsResponse>(valueAddedServiceDetails);

    }


    #endregion

    #region Set Status
    public async Task<bool> SetStatusAsync(Guid id, bool isActive)
    {
        var vas = await valueAddedServiceRepository.GetByIdAsync(id);
        if (vas == null)
        {
            return false;
        }

        vas.Status = isActive ? Status.Active : Status.Inactive;
        valueAddedServiceRepository.Update(vas);
        await valueAddedServiceRepository.SaveChangesAsync();
        return true;
    }
    #endregion

    #endregion
    #region ValueAddedService Names and Ids
    public async Task<List<BasicValueAddedServiceInfo>> GetValueAddedServiceNamesAsync()
    {
        try
        {
            var getValueAddedServiceNames = await valueAddedServiceRepository.GetValueAddedServiceNamesAsync();
            return getValueAddedServiceNames;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while retrieving vas IDs and names.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An error occurred while retrieving product data.");
        }
    }
    #endregion
}
