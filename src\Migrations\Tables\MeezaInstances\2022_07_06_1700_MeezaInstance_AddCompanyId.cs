﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_07_06_1700)]
public class MeezaInstance_AddCompanyId : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
            IF EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'IDX_MeezaInstances_MeezaMerchantId' AND object_id = OBJECT_ID('[dbo].[MeezaInstances]'))
            BEGIN
                DROP INDEX [IDX_MeezaInstances_MeezaMerchantId] ON [dbo].[MeezaInstances]
            END");

        Alter.Table("MeezaInstances").AlterColumn("MeezaMerchantId").AsString(255).Nullable();
        Alter.Table("MeezaInstances").AddColumn("CompanyId").AsGuid().Nullable();

        Execute.Sql(@"
            ALTER TABLE [dbo].[MeezaInstances] DROP CONSTRAINT [FK_MeezaInstances_ProductInstanceId_ProductInstances_Id];
            GO

            ALTER TABLE [dbo].[MeezaInstances] WITH CHECK ADD CONSTRAINT [FK_MeezaInstances_ProductInstanceId_ProductInstances_Id] FOREIGN KEY([ProductInstanceId])
            REFERENCES [dbo].[ProductInstances] ([Id])
            ON DELETE CASCADE
            GO

            ALTER TABLE [dbo].[MeezaInstances] CHECK CONSTRAINT [FK_MeezaInstances_ProductInstanceId_ProductInstances_Id]
            GO");

        Execute.Sql(@"DELETE FROM MeezaInstances");

        Execute.Sql(@"
            INSERT INTO [MeezaInstances](
                [ProductInstanceId],
                [CompanyId],
                [MeezaMerchantId])
            SELECT
                pi.Id,
                pi.CompanyId,
                CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0 THEN JSON_VALUE(pi.Metadata, '$.MeezaMerchantId') ELSE NULL END
            FROM ProductInstances pi
                INNER JOIN Products p on p.Id=pi.ProductId
            WHERE
                p.Type='MEEZA'");

        Execute.Sql(@"
            IF NOT EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'IDX_MeezaInstances_MeezaMerchantId' AND object_id = OBJECT_ID('[dbo].[MeezaInstances]'))
            BEGIN
                CREATE NONCLUSTERED INDEX [IDX_MeezaInstances_MeezaMerchantId] ON [dbo].[MeezaInstances] ([MeezaMerchantId])
            END");

        Execute.Sql(@"
            IF NOT EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'IDX_MeezaInstances_CompanyId' AND object_id = OBJECT_ID('[dbo].[MeezaInstances]'))
            BEGIN
                CREATE NONCLUSTERED INDEX [IDX_MeezaInstances_CompanyId] ON [dbo].[MeezaInstances] ([CompanyId])
            END");
    }
}
