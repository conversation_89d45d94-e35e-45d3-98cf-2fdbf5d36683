﻿using System;
using System.Collections.Generic;
using Common.Models.Gle;
using FluentValidation;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;

namespace Common.Validators.Gle;

public class UpdateGleTerminalValidator : AbstractValidator<JsonPatchDocument<UpdateGleTerminalRequest>>
{
    public UpdateGleTerminalValidator()
    {
        var gleStatusList = new List<string> { Constants.GleRegistrationStatus.Success, Constants.GleRegistrationStatus.Failed };
        RuleFor(x => FindOperation(x, nameof(UpdateGleTerminalRequest.GleRegistrationStatus)))
            .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && gleStatusList.Contains(o?.value?.ToString()!))
            .When(o => FindOperation(o, nameof(UpdateGleTerminalRequest.GleRegistrationStatus)) != null)
            .WithErrorCode(Errors.InvalidGleStatus.Code)
            .WithMessage(Errors.InvalidGleStatus.Message);
    }

    private static Operation<UpdateGleTerminalRequest>? FindOperation(JsonPatchDocument<UpdateGleTerminalRequest> o, string pathName) =>
        o.Operations.Find(x
            => x.path.Contains(pathName, StringComparison.InvariantCultureIgnoreCase));
}