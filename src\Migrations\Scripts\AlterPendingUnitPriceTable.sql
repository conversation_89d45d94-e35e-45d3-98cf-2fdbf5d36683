ALTER TABLE PendingUnitPrice 
ADD CONSTRAINT FK_PendingUnitPrice_UnitPrice 
FOREIGN KEY (UnitPriceId) REFERENCES UnitPrice(Id) ON DELETE CASCADE;

ALTER TABLE PendingUnitPrice 
ADD CONSTRAINT FK_PendingUnitPrice_Product 
FOREIGN KEY (ProductID) REFERENCES Products(Id) ON DELETE CASCADE;

ALTER TABLE PendingUnitPrice 
ADD CONSTRAINT FK_PendingUnitPrice_MCC 
FOREIGN KEY (MCCID) REFERENCES Mcc(Id) ON DELETE CASCADE;

ALTER TABLE PendingUnitPrice 
ADD CONSTRAINT FK_PendingUnitPrice_BusinessType 
FOREIGN KEY (BusinessTypeID) REFERENCES BusinessTypes(Id) ON DELETE CASCADE;
GO
