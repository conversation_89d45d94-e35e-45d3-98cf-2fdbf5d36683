﻿
USE PRODUCTS;
GO
BEGIN TRAN
-- Update PercentagePrice of Mada
  update Price
  set Price.PercentagePrice = 100
  FROM [PRODUCTS].[dbo].[Prices] Price
  inner JOIN [PRODUCTS].[dbo].[Products] Product on Price.ProductId = Product.Id 
  where currency ='SAR' and chargeType='PURCHASE_CNP'  and code='MADA_ONLINE' and Product.Availability ='Live' and Product.counterParty='GEIDEA_SAUDI'

-- Update PercentagePrice of Visa 
  update Price
  set Price.PercentagePrice = 275
  FROM [PRODUCTS].[dbo].[Prices] Price
  inner JOIN [PRODUCTS].[dbo].[Products] Product on Price.ProductId = Product.Id 
  where currency ='SAR' and chargeType='PURCHASE_CNP'  and code='VISA_ONLINE' and Product.Availability ='Live' and Product.counterParty='GEIDEA_SAUDI'

  
-- Update PercentagePrice of MasterCard
  update Price
  set Price.PercentagePrice = 275
  FROM [PRODUCTS].[dbo].[Prices] Price
  inner JOIN [PRODUCTS].[dbo].[Products] Product on Price.ProductId = Product.Id 
  where currency ='SAR' and chargeType='PURCHASE_CNP'  and code='MC_ONLINE' and Product.Availability ='Live' and Product.counterParty='GEIDEA_SAUDI'

-- Update PerItemPrice of Monthly subscription Fees of Payment_Gateway
  update Price
  set Price.PerItemPrice = 9900
  FROM [PRODUCTS].[dbo].[Prices] Price
  inner JOIN [PRODUCTS].[dbo].[Products] Product on Price.ProductId = Product.Id 
  where currency ='SAR' and chargeType='RECCURRING_CHARGE'  and code='PAYMENT_GATEWAY_BUNDLE' and Product.Availability ='Live' and Product.counterParty='GEIDEA_SAUDI'

COMMIT TRAN