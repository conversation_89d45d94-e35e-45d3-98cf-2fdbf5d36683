﻿using System;
using System.Linq;

namespace Geidea.ProductService.Models;

public static class Helpers
{
    public static string? ExtractDomainNameFromURL(string? url)
    {
        if (url == null)
            return null;

        try
        {
            return new Uri(url.Contains("://") ? url : ("http://" + url), UriKind.Absolute).Host;
        }
        catch (Exception)
        {
            return null;
        }
    }

    public static string? FormatMerchantNotificationEmail(string? value)
    {
        var emails = value?.Split(';')
                .Select(email => email.Trim())
                .Where(email => string.IsNullOrWhiteSpace(email) is false)
                .GroupBy(email => email)
                .Select(group => group.Key);

        return emails?.Count() == 0 ? null : emails?.Aggregate((a, b) => $"{a};{b}");
    }
}
