﻿using AutoMapper;
using Common.Entities;
using Common.Repositories;
using Microsoft.Extensions.Logging;
using Moq;
using ProductService;
using System;
using System.Threading.Tasks;
using Xunit;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using System.Net;
using Common;
using Microsoft.EntityFrameworkCore;
using DataAccess;
using DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Geidea.Utils.Counterparty.Providers;
using Common.Options;
using Microsoft.Extensions.Options;
using NSubstitute;

namespace Services.Test.CategoryServiceTests;

public class DeleteTests
{
    private readonly Mock<ILogger<CategoryService>> logger = new Mock<ILogger<CategoryService>>();
    private readonly DataContext context;
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly CategoryService categoryService;
    private readonly IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;
    private readonly ICounterpartyProvider counterpartyProvider;

    public DeleteTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
            .UseInMemoryDatabase(databaseName: "CategoryDeleteTests" + Guid.NewGuid().ToString())
            .Options;

        context = new DataContext(options, new CounterpartyProvider());
        ICategoryRepository categoryRepository = new CategoryRepository(context, httpContext.Object);

        softposEgyptFeatureToggle = Substitute.For<IOptions<SoftposEgyptFeatureToggle>>();
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = true });
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();

        IProductRepository productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);

        categoryService = new CategoryService(categoryRepository, logger.Object, mapper, productRepository);
    }

    [Fact]
    public async Task InexistingId()
    {
        await categoryService
            .Invoking(x => x.DeleteAsync(Guid.NewGuid()))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
                        x.ProblemDetails.Type == Errors.CategoryNotFound.Code);
    }

    [Fact]
    public async Task UsedCategory_SoftDelete()
    {
        var category = new CategoryEntity
        {
            Code = "CATEGORY"
        };
        var product = new ProductEntity
        {
            ProductCategories = new List<ProductCategoriesEntity>
                {
                    new ProductCategoriesEntity
                    {
                        Category = category
                    }
                }
        };
        context.Categories.Add(category);
        context.Products.Add(product);
        context.SaveChanges();

        await categoryService.DeleteAsync(category.Id);
        var retrievedCategory = await context.Categories.FirstOrDefaultAsync(c => c.Id == category.Id);
        retrievedCategory.Should().NotBeNull();
        retrievedCategory.DeletedFlag.Should().BeTrue();
    }

    [Fact]
    public async Task NotUsedCategory_HardDelete()
    {
        var category = new CategoryEntity
        {
            Code = "CATEGORY"
        };
        context.Categories.Add(category);
        context.SaveChanges();

        await categoryService.DeleteAsync(category.Id);
        var retrievedCategory = await context.Categories.FirstOrDefaultAsync(c => c.Id == category.Id);
        retrievedCategory.Should().BeNull();
    }

    [Fact]
    public async Task CategoryWithSubcategories()
    {
        var category = new CategoryEntity
        {
            Code = "CATEGORY",
            Subcategories = new List<CategoryEntity>
                {
                    new CategoryEntity
                    {
                        Code="SUBCATEGORY1"
                    },
                    new CategoryEntity
                    {
                        Code="SUBCATEGORY2"
                    }
                }
        };
        context.Categories.Add(category);
        context.SaveChanges();

        await categoryService.DeleteAsync(category.Id);
        var retrievedCategory = await context.Categories.FirstOrDefaultAsync(c => c.Id == category.Id);
        retrievedCategory.Should().BeNull();

        var retrievedSubCategory1 = await context.Categories.FirstOrDefaultAsync(c => c.Id == category.Subcategories[0].Id);
        retrievedSubCategory1.Should().NotBeNull();
        retrievedSubCategory1.ParentId.Should().BeNull();

        var retrievedSubCategory2 = await context.Categories.FirstOrDefaultAsync(c => c.Id == category.Subcategories[1].Id);
        retrievedSubCategory2.Should().NotBeNull();
        retrievedSubCategory2.ParentId.Should().BeNull();
    }
}
