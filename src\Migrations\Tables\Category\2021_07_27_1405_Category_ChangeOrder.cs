﻿using FluentMigrator;

namespace Migrations.Tables.Category;

[Migration(2021_07_27_1406)]
public class Category_UpdateOrder : ForwardOnlyMigration
{
    public override void Up()
    {
        Update.Table("Category").Set(new { DisplayOrder = "2" }).Where(new { Code = "ONLINE_FAMILY", Counterparty = "GEIDEA_SAUDI" });
        Update.Table("Category").Set(new { DisplayOrder = "3" }).Where(new { Code = "PRO_FAMILY", Counterparty = "GEIDEA_SAUDI" });
    }
}
