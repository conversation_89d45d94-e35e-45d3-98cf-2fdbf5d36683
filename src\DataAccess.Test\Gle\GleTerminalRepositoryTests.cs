﻿using System;
using System.Collections.Generic;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Common;
using Common.Entities.Gle;
using DataAccess.Repositories.Gle;
using DataAccess.Test.TestData;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;

namespace DataAccess.Test.Gle;

public class GleTerminalRepositoryTests
{
    private readonly IHttpContextAccessor contextAccessor = Substitute.For<IHttpContextAccessor>();
    private readonly ILogger<GleTerminalRepository> logger = Substitute.For<ILogger<GleTerminalRepository>>();
    private readonly ILogger<GleBaseRepository<GleTerminalEntity>> baseLogger = Substitute.For<ILogger<GleBaseRepository<GleTerminalEntity>>>();
    private DataContext context;
    private GleTerminalRepository gleTerminalRepository;

    private readonly Guid storeId = Guid.NewGuid();
    private readonly Guid orderId = Guid.NewGuid();
    private readonly Guid productInstanceId = Guid.NewGuid();

    [SetUp]
    public async Task SetUp()
    {
        context = DbContextHelper.CreateInMemoryDatabase(Substitute.For<ICounterpartyProvider>());
        gleTerminalRepository = new GleTerminalRepository(context, contextAccessor, baseLogger, logger);

        await context.GleTerminal.AddRangeAsync(new List<GleTerminalEntity>
        {
            new()
            {
                Id = Guid.NewGuid(),
                GleUserId = "64333",
                GleLoginId = "09999999",
                GleLoginId2 = "23124",
                ParentGleUserId = "657697",
                GleRegistrationStatus = Constants.GleRegistrationStatus.Success,
                GleRegistrationResponse = "Terminal was registered",
                ReferenceMmsId = "87876873",
                ProductInstanceId = productInstanceId,
                OrderId = orderId,
                Tid = "87876873",
                IsTerminalUser = false
            }
        });

        await context.SaveChangesAsync();
    }

    [Test]
    public async Task OrderHasAllInstancesRegisteredToGle_ReturnsTrue()
    {
        var result = await gleTerminalRepository.OrderHasAllInstancesRegisteredToGle(orderId);

        Assert.NotNull(result);
        Assert.IsTrue(result);
    }

    [Test]
    public async Task OrderHasAllInstancesRegisteredToGle_WhenOrderNotFound_ReturnsNull()
    {
        var result = await gleTerminalRepository.OrderHasAllInstancesRegisteredToGle(Guid.NewGuid());

        Assert.IsNull(result);
    }

    [Test]
    public async Task AddGle_ValidRequest_ShouldCallBaseAndAddGleTerminal()
    {
        var numberOfGleTerminalsBeforeAdd = context.GleTerminal.Count();

        await gleTerminalRepository.AddGle(new GleTerminalEntity()
        {
            GleRegistrationStatus = Constants.GleRegistrationStatus.Failed,
            GleRegistrationResponse = "Location not found!",
            ReferenceMmsId = "11111111",
            ProductInstanceId = Guid.NewGuid(),
            OrderId = Guid.NewGuid()
        });

        Assert.AreEqual(numberOfGleTerminalsBeforeAdd + 1, context.GleTerminal.Count());
    }

    [Test]
    public async Task AddGle_InvalidRequest_ShouldThrowServiceException()
    {
        await gleTerminalRepository.Invoking(x => x.AddGle(new GleTerminalEntity()))
            .Should().ThrowAsync<ServiceException>();
    }

    [Test]
    public async Task UpdateGle_ValidRequest_ShouldCallBaseAndUpdateGleMerchant()
    {
        var gleTerminalBeforeUpdate = await gleTerminalRepository.GetGleTerminalByProductInstanceIdAsync(productInstanceId);

        const string newValue = "3644444";
        var updateDocument = new JsonPatchDocument<GleTerminalEntity>();
        updateDocument.Operations.Add(new Operation<GleTerminalEntity>
        {
            op = "replace",
            path = "GleUserId",
            value = newValue
        });
        await gleTerminalRepository.UpdateGle(gleTerminalBeforeUpdate!.Id, updateDocument);

        var gleTerminalAfterUpdate = await gleTerminalRepository.GetGleTerminalByProductInstanceIdAsync(productInstanceId);

        Assert.AreNotEqual(gleTerminalBeforeUpdate.GleUserId, gleTerminalAfterUpdate!.GleUserId);
        Assert.AreEqual(newValue, gleTerminalAfterUpdate.GleUserId);
    }
}