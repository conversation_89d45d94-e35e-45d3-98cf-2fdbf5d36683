﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Nullable>enable</Nullable>
		<LangVersion>latest</LangVersion>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<ProjectGuid>{f45b5285-2f28-4d09-a56a-8a5df68a1ad8}</ProjectGuid>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="12.0.1" />
		<PackageReference Include="ClosedXML" Version="0.102.1" />
		<PackageReference Include="EFCore.BulkExtensions" Version="6.0.2" />		
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.15" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.15" />
		<PackageReference Include="Geidea.Utils.Counterparty" Version="2.0.232" />
		<PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Common\Common.csproj" />
	</ItemGroup>

</Project>
