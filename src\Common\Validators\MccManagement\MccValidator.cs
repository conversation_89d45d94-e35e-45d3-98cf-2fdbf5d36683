﻿using Common.Entities;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Validators.MccManagement;
public class MccValidator : AbstractValidator<Mcc>
{
    public MccValidator()
    {
        RuleFor(r => r.Code).NotEmpty().Length(1, 6);
        RuleFor(r => r.Name).NotEmpty().Length(1, 100);
        RuleFor(a => a.Status)
        .IsInEnum()
        .WithMessage("Status must be either 0 (Inactive) or 1 (Active).");

        RuleFor(s => s.NameAr)
        .NotEmpty()
        .Length(1, 100)
        .WithMessage("MCC Name is required and cannot exceed 100 characters.");
    }
}
