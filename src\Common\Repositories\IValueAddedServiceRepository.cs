﻿using Common.Entities;
using Common.Models.ValueAddedServices;
using Geidea.Utils.DataAccess.Entities;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Common.Repositories;
public interface IValueAddedServiceRepository : IRepository<Guid, ValueAddedService>
{
    Task<ValueAddedService?> GetByIdAsync(Guid id);
    Task<ValueAddedServicesListResponse> GetValueAddedServicesList(GetValueAddedServicesListRequest request);
    Task<List<BasicValueAddedServiceInfo>> GetValueAddedServiceNamesAsync();
}
