﻿USE PRODUCTS
Go
create or alter procedure NewProductVersion_v2 @ProductCode nvarchar(255), @ProductVersion int, @NewProductCode nvarchar(255), @NewProductVersion int, @MarkObsolete bit
as
BEGIN
 DECLARE @Ids TABLE(ID UNIQUEIDENTIFIER)
 DECLARE @Id UNIQUEIDENTIFIER
 DECLARE @ProductId UNIQUEIDENTIFIER
 DECLARE @NewProductId UNIQUEIDENTIFIER

 --create new product
 INSERT INTO PRODUCTS(Availability, Code, Type, Description, CreatedBy, CreatedDateUtc, ValidFrom, DisplayOrder, Version) OUTPUT inserted.Id INTO @Ids
 SELECT Availability, @NewProductCode, Type, Description, 'n/a', GETUTCDATE(), ValidFrom, DisplayOrder, @NewProductVersion FROM Products Where Code = @ProductCode and Version = @ProductVersion
 
 SELECT TOP 1 @NewProductId = ID FROM @Ids 
 DELETE FROM @Ids

 --mark obsolete
  SELECT TOP 1 @ProductId = Id FROM Products WHERE Code = @ProductCode and Version = @ProductVersion
  UPDATE Products SET Availability = 'Obsolete' WHERE Id = @ProductId and @MarkObsolete = 1

 --add categories
 DECLARE @categoryCursor CURSOR

 SET @categoryCursor = CURSOR FOR SELECT CategoryId FROM ProductCategories WHERE ProductId = @ProductId
 
 OPEN @categoryCursor FETCH NEXT FROM @categoryCursor INTO @Id WHILE @@FETCH_STATUS = 0
 BEGIN
     INSERT INTO ProductCategories(ProductId, CategoryId) VALUES(@NewProductId, @Id)
     FETCH NEXT FROM @categoryCursor INTO @Id
 END
 
 CLOSE @categoryCursor
 DEALLOCATE @categoryCursor

 --add prices
  DECLARE @priceCursor CURSOR
  DECLARE @priceId UNIQUEIDENTIFIER

 SET @priceCursor = CURSOR FOR SELECT Id FROM Prices WHERE ProductId = @ProductId
 
 OPEN @priceCursor FETCH NEXT FROM @priceCursor INTO @Id WHILE @@FETCH_STATUS = 0
 BEGIN
     INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, PerItemPrice, PercentagePrice, Threshold, ThresholdType, [Priority], [Group], ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice, ProductId) OUTPUT inserted.Id INTO @Ids
	 SELECT ChargeFrequency, ChargeType, ExemptFromVAT, PerItemPrice, PercentagePrice,Threshold, ThresholdType, [Priority], [Group], ValidFrom, 'n/a', GETUTCDATE(), Currency, RentalPeriod, MaxPrice, @NewProductId FROM Prices WHERE Id = @Id

	 FETCH NEXT FROM @priceCursor INTO @Id
 END
 
 CLOSE @priceCursor
 DEALLOCATE @priceCursor

 --add parts
 DECLARE @partCursor CURSOR

 SET @partCursor = CURSOR FOR SELECT PartId FROM ProductParts WHERE ProductId = @ProductId
 
  OPEN @partCursor FETCH NEXT FROM @partCursor INTO @Id WHILE @@FETCH_STATUS = 0
  BEGIN
       INSERT INTO ProductParts(ProductId, PartId) VALUES(@NewProductId, @Id)
 	   FETCH NEXT FROM @partCursor INTO @Id
  END
  
  CLOSE @partCursor
  DEALLOCATE @partCursor
END