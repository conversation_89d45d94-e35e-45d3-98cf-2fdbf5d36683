﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_08_05_1430)]
public class ProductInstance_Add_isCallbackEmailNotificationEnabled_To_Metadata : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
                UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsCallbackEmailNotificationEnabled', 'false')
	            FROM ProductInstances pi
                INNER JOIN Products p ON p.id = pi.ProductId
                WHERE [Metadata] IS NOT NULL
                AND p.Type = 'GWAY' 
                AND ISJSON([Metadata]) > 0 
            ");
    }
}