﻿using Common.Models.UnitPrice;
using Common.Models.ValueAddedSerivcePricing;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProductService.Controllers;
[Route("api/v1/[controller]")]
[ApiController]
public class ValueAddedServicePricingController : ControllerBase
{
    private readonly IValueAddedServicePricingService valueAddedServicePricingService;
    public ValueAddedServicePricingController(IValueAddedServicePricingService valueAddedServicePricingService)
    {
        this.valueAddedServicePricingService = valueAddedServicePricingService;
    }
    /// <summary>
    /// Create new prices based on products, MCCs,VAS, and business types.
    /// </summary>
    /// <returns>Returns the created unit prices or existing ones if they already exist.</returns>
    [HttpPost("CreateAddonsPrices")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreateAsync([FromBody] ValueAddedServicesPricingCreateRequest request)
    {
        var result = await valueAddedServicePricingService.CreateAsync(request);
        return Ok(result);
    }
    [HttpPut("UpdateAddonsPrice/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> UpdateAsync(Guid id, [FromBody] ValueAddedServicePricingUpdateRequest request)
    {
        var updatedVasPrice = await valueAddedServicePricingService.UpdateAsync(id, request);
        return Ok(updatedVasPrice);
    }
    /// <summary>
    /// Delete addons prices by a list of IDs.
    /// </summary>
    /// <param name="deletedBy"></param>
    /// <param name="ids">List of addons price IDs to delete.</param>
    /// <returns>Returns a status indicating success or failure.</returns>
    [HttpDelete("DeleteAddonsPrices")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DeleteAddonsPricesAsync([FromQuery] string deletedBy, [FromBody] List<Guid> ids)
    {
        await valueAddedServicePricingService.DeleteAddonsPricesAsync(ids, deletedBy);

        return Ok(new { Message = "Unit prices deleted successfully." });
    }
    /// <summary>
    /// Get AddOns(VAS) prices list, based on search, filteration and sorting data in the request.
    /// </summary>
    /// <param name="request">An object that contains search, filter, sort and pagination data.</param>
    /// <returns>
    ///  Returns an <see cref="IActionResult"/> Indicating the result of operation.
    /// Success: returns http 200 ok with the list of addons prices list data.
    /// Bad request: returns http 4oo bad request, with the result indicating the reason.
    /// Failure: returns http 500 InternalServerError, with the result indicating the failure.
    /// </returns>
    [HttpPost("GetAddonsPricesList")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAddOnsPricesList(ValueAddedServicePriceListRequest request)
    {
        var result = await valueAddedServicePricingService.GetValueAddedServicesPricesList(request);
        return Ok(result);
    }
    /// <summary>
    /// Get addons price details by ID.
    /// </summary>
    /// <param name="id">The ID of the addons price.</param>
    /// <returns>
    /// Returns the unit price details if found.
    /// </returns>
    [HttpGet("GetAddonsById/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAddOnsPriceById(Guid id)
    {
        var addOnsPrice = await valueAddedServicePricingService.GetAddOnsPriceByIdAsync(id);
        return Ok(addOnsPrice);
    }
    /// <summary>
    /// Create or Update VAS prices based on request data.
    /// </summary>
    /// <returns>Returns created or updated VAS prices.</returns>
    [HttpPost("CreateOrUpdateAddonsPrices")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreateOrUpdateAsync([FromBody] ValueAddedServicesPricingCreateRequest request)
    {
        var result = await valueAddedServicePricingService.CreateOrUpdateAsync(request);
        return Ok(result);
    }
    [HttpPost("GetValueAddedServicePriceByIds")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetValueAddedServicePriceByIds([FromBody] List<Guid> ids)
    {
        var valueAddedServicePricing = await valueAddedServicePricingService.GetAddonsPricesByIdsAsync(ids);
        return Ok(valueAddedServicePricing);
    }
}
