﻿using Common.Entities;
using Common.Models.CommissionFees;
using Common.Models.NonTransactionalFees;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services;
public interface INonTransactionalFeesService
{
    Task<GetNonTransactionalFeesListResponse> GetNonTransactionalFeesList(GetNonTransactionalFeesListRequest request);
    Task<bool> ToggleStatus(Guid NonTransactionalFeesId, bool Status);
    Task<NonTransactionalFeesDetailsResponse> GetNonTransactionalFeesDetails(Guid Id);
    Task<NonTransactionalFeesEntity> CreateAsync(NonTransactionalFeesRequest request);
    Task<NonTransactionalFeesEntity> UpdateAsync(Guid id, NonTransactionalFeesRequest request);
    Task<List<BasicNonTransactionalFeeInfo>> GetNonTransactionalFeesNamesAsync();
}
