﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models;
[ExcludeFromCodeCoverage]

public class GetProductsListResponse
{
    [JsonProperty("Id")]
    public Guid Id { get; set; }
    [JsonProperty("CreatedDate")]
    public DateTime CreatedDate { get; set; }
    [JsonProperty("Code")]
    public string? Code { get; set; }
    [JsonProperty("Name")]
    public string? Name { get; set; } = null!;
    [JsonProperty("Subname")]// English Name
    public string? Subname { get; set; } = null!;
    [JsonProperty("SalesChannel")]// English Subname
    public string? SalesChannel { get; set; } = null!;
    [JsonProperty("CategoryName")]
    public string? CategoryName { get; set; } = null!;
}
