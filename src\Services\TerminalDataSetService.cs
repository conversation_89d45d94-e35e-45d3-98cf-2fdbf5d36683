﻿using System;
using Common.Models.Search;
using Common.Models.TerminalDataSets;
using Common.Models.Validators;
using Common.Repositories;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using Common;
using System.Net;
using AutoMapper;
using Common.Services.Acquirers;
using System.Linq;
using Geidea.Utils.Counterparty.Providers;
using Common.Entities;
using Models;
using Common.Models;

namespace Services;

public class TerminalDataSetService : ITerminalDataSetService
{
    private readonly ITerminalDataSetRepository _terminalDataSetRepository;
    private readonly ILogger<TerminalDataSetService> _logger;
    private readonly IMapper _mapper;
    private readonly Func<string, IAcquirer> _acquirerProvider;
    private readonly ICounterpartyProvider _counterpartyProvider;


    public TerminalDataSetService(ITerminalDataSetRepository terminalDataSetRepository,
        Func<string, IAcquirer> acquirerProvider,
        ILogger<TerminalDataSetService> logger, IMapper mapper, ICounterpartyProvider counterpartyProvider)
    {
        _terminalDataSetRepository = terminalDataSetRepository;
        _acquirerProvider = acquirerProvider;
        _logger = logger;
        _mapper = mapper;
        _counterpartyProvider = counterpartyProvider;

    }

    public async Task<List<TerminalDataSet>> CreateAsync(List<TerminalDataSet> terminalDataSets)
    {
        var createdTerminalDataSet = await _terminalDataSetRepository.CreateAsync(terminalDataSets);

        return createdTerminalDataSet;
    }

    public async Task<List<TerminalDataSet>> PatchAsync(List<TerminalDataSetPatchRequest> terminalDataSetPatchRequest)
    {
        var response = await _terminalDataSetRepository.PatchAsync(terminalDataSetPatchRequest);

        return response;
    }

    public async Task<SearchResponse<TerminalDataSetSearchResponse>> AdvancedSearchAsync(TerminalDataSetSearchRequest terminalDataSetRequest)
    {
        var validatorResult = await new TerminalDataRequestValidator().ValidateAsync(terminalDataSetRequest);
        if (!validatorResult.IsValid)
        {

            var errorDescription = ErrorMessageCollection.FromValidationResult(validatorResult);
            _logger.LogError("Lead search criteria validation failed: {@errors}", errorDescription);

            throw new ValidationException(validatorResult);
        }

        return await _terminalDataSetRepository.AdvancedSearchAsync(terminalDataSetRequest);
    }

    public async Task<TerminalsCountResponse> GetAvailableTerminalDataSetCountAsync(string acquiringLedger)
    {
        if (string.IsNullOrWhiteSpace(acquiringLedger))
        {
            _logger.LogError("GetAvailableTerminalDataSetCountAsync request does not contain an acquiringLedger");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.GetAvailableTerminalDataSetCountBadRequest);
        }

        var terminalDataSetCount = await _terminalDataSetRepository.GetAvailableTerminalDataSetCountAsync(acquiringLedger);

        return new TerminalsCountResponse { ValidTerminalDataSetCount = terminalDataSetCount };
    }

    public async Task<List<TerminalDataSetValidationRequiredFields>> GetValidationRequiredFieldsAsync(List<TerminalDataSet> terminalDataSet)
    {
        var response = await _terminalDataSetRepository.GetValidationRequiredFieldsAsync(terminalDataSet);
        return response;
    }

    public async Task<TerminalDataSetSearchResponse> GetTerminalDataByIdAsync(Guid terminalDataId)
    {
        var terminalDataSet = await _terminalDataSetRepository.GetTerminalDataByIdAsync(terminalDataId);
        return _mapper.Map<TerminalDataSetSearchResponse>(terminalDataSet);
    }

    public async Task<List<TerminalDataSet>> GetOrderMigrationValidationRequiredFieldsAsync(List<TerminalDataSet> terminalDataSet)
    {
        return await _terminalDataSetRepository.GetOrderMigrationValidationRequiredFieldsAsync(terminalDataSet);
    }

    public async Task<List<TerminalDataSet>> GetTerminalDataByProductInstanceId(Guid[] productInstanceIds)
    {
        var terminalDataSet = await _terminalDataSetRepository.GetTerminalDataByProductInstanceId(productInstanceIds);

        return terminalDataSet;
    }

    public async Task<List<TerminalDataSetsResponse>> GenerateTIDAndMIDAndAddEditTerminalDataSets(TerminalDataSetsRequest terminalDataSets)
    {
        if (_counterpartyProvider.GetCode() == Constants.CounterParty.Uae)
            terminalDataSets.AcquiringLedger = Constants.AcquiringLedger.GeIdea;
        var acquirer = GetAcquirerInstance(terminalDataSets.AcquiringLedger);


        return await _terminalDataSetRepository.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, acquirer);
    }

    public async Task<List<TerminalDataSetsResponse>> GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(TerminalDataSetsMidTidRequest terminalDataSetsMidTid)
    {
        if (terminalDataSetsMidTid == null || terminalDataSetsMidTid.TerminalDataSets == null
          || terminalDataSetsMidTid.TerminalDataSets.Count == 0)
            return new List<TerminalDataSetsResponse>();

        var acquirer = GetAcquirerInstance(terminalDataSetsMidTid.TerminalDataSets.First().AcquiringLedger);

        return await _terminalDataSetRepository.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(terminalDataSetsMidTid, acquirer);
    }

    public async Task<TerminalDataSetsResponse> GenerateTIDAndAddEditTerminalDataSet(TerminalDataSet terminalDataSet)
    {
        if (terminalDataSet == null)
            return new TerminalDataSetsResponse();

        var acquirer = GetAcquirerInstance(terminalDataSet.AcquiringLedger);

        return await _terminalDataSetRepository.GenerateTIDAndAddEditTerminalDataSet(terminalDataSet, acquirer);
    }

    public async Task<string> GenerateMIDAndAddEditTerminalDataSet(TerminalDataSetMidRequest terminalDataSetMidRequest)
    {
        var terminalDataSet = terminalDataSetMidRequest?.TerminalDataSet;

        if (terminalDataSet != null)
        {
            var acquirer = GetAcquirerInstance(terminalDataSet.AcquiringLedger);
            return await _terminalDataSetRepository.GenerateMIDAndAddEditTerminalDataSet(terminalDataSetMidRequest!, acquirer);
        }

        return "";
    }

    public async Task<List<TerminalDataSetsResponse>> AddUpdateTerminalDataSetMcc(TerminalDataSetsMidTidRequest terminalDataSetsRequest)
        => await _terminalDataSetRepository.AddUpdateTerminalDataSetMcc(terminalDataSetsRequest);

    private IAcquirer GetAcquirerInstance(string acquirer)
    {
        return _acquirerProvider(acquirer);
    }

    public async Task<string> GetNewTIDSequence()
    {
        return await _terminalDataSetRepository.GetTerminalIdSequenceAsync();
    }

    public async Task<List<OrderTid>> GetTidByOrderId(List<string?> orderNumber)
    => await _terminalDataSetRepository.GetTidByOrderId(orderNumber);
}
