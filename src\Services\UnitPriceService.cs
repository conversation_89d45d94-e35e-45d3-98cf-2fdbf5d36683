﻿using AutoMapper;
using Common;
using Common.Entities;
using Common.Models.UnitPrice;
using Common.Repositories;
using Common.Services;
using Common.Views;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Services;

public class UnitPriceService : IUnitPriceService
{
    #region Fields
    private readonly ILogger<UnitPriceService> logger;
    private readonly IUnitPriceRepository unitPriceRepository;

    private readonly IMapper mapper;
    #endregion

    #region Constructor
    public UnitPriceService(ILogger<UnitPriceService> logger, IUnitPriceRepository unitPriceRepository, IMapper mapper)
    {
        this.logger = logger;
        this.unitPriceRepository = unitPriceRepository;
        this.mapper = mapper;
    }
    #endregion

    #region Create
    public async Task<UnitPriceResponse> CreateAsync(UnitPriceCreateRequest unitPriceCreateRequest)
    {

        var (unitPrices, existingUnitPrices) = await ProcessUnitPricesAsync(unitPriceCreateRequest);

        await unitPriceRepository.SaveUnitPricesAsync(unitPrices);

        return new UnitPriceResponse
        {
            CreatedUnitPrices = mapper.Map<List<UnitPriceDetails>>(unitPrices),
            NewExistingUnitPrices = mapper.Map<List<UnitPriceDetails>>(existingUnitPrices)
        };
    }

    private async Task<(List<UnitPriceEntity> unitPrices, List<UnitPriceEntity> existingUnitPrices)> ProcessUnitPricesAsync(UnitPriceCreateRequest unitPriceCreateRequest)
    {
        var NewUnitPricesList = new List<UnitPriceEntity>();
        var ExistedUnitPricesList = new List<UnitPriceEntity>();

        var AllExistedUnitPricesList = await unitPriceRepository.GetExistUnitPrices(up =>
                                             unitPriceCreateRequest.ProductIDs.Contains(up.ProductID) &&
                                             unitPriceCreateRequest.MCCIDs.Contains(up.MCCID) &&
                                             unitPriceCreateRequest.BusinessTypeIDs.Contains(up.BusinessTypeID));

        foreach (var productId in unitPriceCreateRequest.ProductIDs)
        {
            foreach (var mccId in unitPriceCreateRequest.MCCIDs)
            {
                foreach (var businessTypeId in unitPriceCreateRequest.BusinessTypeIDs)
                {
                    var existingUnitPrice = AllExistedUnitPricesList.FirstOrDefault(up =>
                                            up.ProductID == productId && up.MCCID == mccId && up.BusinessTypeID == businessTypeId);

                    if (existingUnitPrice == null)
                    {
                        var unitPrice = mapper.Map<UnitPriceEntity>(unitPriceCreateRequest);
                        unitPrice.ProductID = productId;
                        unitPrice.MCCID = mccId;
                        unitPrice.BusinessTypeID = businessTypeId;
                        NewUnitPricesList.Add(unitPrice);
                    }
                    else
                    {
                        ExistedUnitPricesList.Add(existingUnitPrice);
                    }
                }
            }
        }
        return (NewUnitPricesList, ExistedUnitPricesList);
    }
    #endregion

    #region New Method: Get Unit Price
    public async Task<UnitPriceDetails> GetUnitPriceAsync(Guid mccId, Guid productId, Guid businessTypeId)
    {
        var existingUnitPrices = await unitPriceRepository.GetExistUnitPrices(up =>
            up.ProductID == productId &&
            up.MCCID == mccId &&
            up.BusinessTypeID == businessTypeId);

        var unitPrice = existingUnitPrices.FirstOrDefault();

        if (unitPrice == null)
        {
            logger.LogWarning("No unit price found for MCC ID {MCCID}, Product ID {ProductID}, and Business Type ID {BusinessTypeID}.", mccId, productId, businessTypeId);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductNotFound);
        }

        return mapper.Map<UnitPriceDetails>(unitPrice);
    }
    #endregion

    #region Update
    public async Task<UnitPriceDetails> UpdateAsync(Guid id, UnitPriceUpdateRequest updateRequest)
    {
        // Fetch the existing unit price entity
        var existingUnitPrice = await unitPriceRepository.GetByIdAsync(id);

        if (existingUnitPrice == null)
        {
            logger.LogWarning("No unit price found with Id {Id}.", id);
            throw new ServiceException(HttpStatusCode.NotFound, "Unit price not found.");
        }

        // Update only the allowed properties
        existingUnitPrice.UnitPrice = updateRequest.UnitPrice;
        existingUnitPrice.VATRate = updateRequest.VATRate;
        existingUnitPrice.VATType = updateRequest.VATType;
        existingUnitPrice.BillingType = updateRequest.BillingType;
        existingUnitPrice.BillingFrequency = updateRequest.BillingFrequency;

        unitPriceRepository.Update(existingUnitPrice);
        await unitPriceRepository.SaveChangesAsync();

        // Return the updated unit price details
        return mapper.Map<UnitPriceDetails>(existingUnitPrice);
    }
    #endregion

    #region Delete Unit Prices
    public async Task DeleteUnitPricesAsync(List<Guid> ids, string deletedBy)
    {
        if (!ids.Any())
        {
            logger.LogWarning("DeleteUnitPricesAsync: No IDs were provided for deletion.");
            throw new ServiceException(HttpStatusCode.BadRequest, "No IDs provided for deletion.");
        }

        var unitPricesToDelete = await unitPriceRepository.GetUnitPricesByIdsAsync(ids);

        if (!unitPricesToDelete.Any())
        {
            logger.LogWarning("DeleteUnitPricesAsync: No matching records found for the provided IDs.");
            throw new ServiceException(HttpStatusCode.NotFound, "No matching records found for deletion.");
        }

        try
        {
            var logs = unitPricesToDelete.Select(up =>
            {
                var log = mapper.Map<UnitPriceLogsEntity>(up);
                log.DeletedBy = deletedBy;
                log.DeletedDate = DateTime.UtcNow;
                return log;
            }).ToList();

            int affectedRows = await unitPriceRepository.DeleteBulkAsync(ids);
            await unitPriceRepository.AddLogsAsync(logs);

            logger.LogInformation($"DeleteUnitPricesAsync: Successfully deleted {affectedRows} unit prices and logged the actions.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "DeleteUnitPricesAsync: An error occurred while deleting unit prices.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An error occurred while deleting unit prices.");
        }
    }

    #endregion

    #region Listing
    public async Task<UnitPricesListResponse> GetUnitPricesList(UnitPricesListRequest request)
    {
        if (request == null)
        {
            logger.LogError("Invalid Get Unit Price List request.");
            throw new ServiceException(HttpStatusCode.BadRequest, "Invalid Get Unit Price List request.");
        }
        try
        {
            return await unitPriceRepository.GetUnitPricesList(request);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "something has went wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, "something has went wrong!");
        }
    }
    #endregion

    #region View Details
    public async Task<UnitPricesDetails> GetUnitPriceByIdAsync(Guid id)
    {
        var unitPriceEntity = await unitPriceRepository.GetUnitPriceByIdAsync(id);

        if (unitPriceEntity == null)
        {
            logger.LogWarning("No unit price found with Id {Id}.", id);
            throw new ServiceException(HttpStatusCode.NotFound, "Unit price not found.");
        }

        var unitPriceDetails = mapper.Map<UnitPricesDetails>(unitPriceEntity);
        return unitPriceDetails;
    }
    #endregion

    #region Get ProductCategory
    public async Task<List<ProductCategoryView>> ProductCategoryListAsync(Guid mccId, Guid businessTypeId)
    {
        if (mccId == Guid.Empty || businessTypeId == Guid.Empty)
        {
            logger.LogError("Invalid MCCId or BusinessTypeId provided for ProductCategoryListAsync.");
            throw new ServiceException(HttpStatusCode.BadRequest, "Invalid MCCId or BusinessTypeId.");
        }

        var productCategories = await unitPriceRepository.ProductCategoryList(mccId, businessTypeId);

        if (productCategories == null || !productCategories.Any())
        {
            logger.LogWarning("No product categories found for MCCId {MCCId} and BusinessTypeId {BusinessTypeId}.", mccId, businessTypeId);
            throw new ServiceException(HttpStatusCode.NotFound, "No product categories found.");
        }
        return productCategories;
    }
    #endregion

    #region CreateOrUpdate
    public async Task<UnitPriceResponse> CreateOrUpdateAsync(UnitPriceCreateRequest request)
    {
        var (newUnitPrices, oldExistUnitPricesToUpdate, newExistingUnitPricesToUpdate) = await ProcessUnitPricesForCreateOrUpdateAsync(request);

        if (newUnitPrices.Any())
        {
            await unitPriceRepository.SaveUnitPricesAsync(newUnitPrices);
        }

        if (newExistingUnitPricesToUpdate.Any())
        {
            await unitPriceRepository.UpdateUnitPricesAsync(newExistingUnitPricesToUpdate);
        }

        return new UnitPriceResponse
        {
            CreatedUnitPrices = mapper.Map<List<UnitPriceDetails>>(newUnitPrices),
            NewExistingUnitPrices = mapper.Map<List<UnitPriceDetails>>(newExistingUnitPricesToUpdate),
            OldExistingUnitPrices = mapper.Map<List<UnitPriceDetails>>(oldExistUnitPricesToUpdate)
        };
    }

    private async Task<(List<UnitPriceEntity> newUnitPrices,
                        List<UnitPriceEntity> oldExistUnitPricesToUpdate,
                        List<UnitPriceEntity> newExistingUnitPricesToUpdate)>
        ProcessUnitPricesForCreateOrUpdateAsync(UnitPriceCreateRequest request)
    {
        var newUnitPricesList = new List<UnitPriceEntity>();
        var existingUnitPricesToUpdateList = new List<UnitPriceEntity>();
        var oldUnitPricesList = new List<UnitPriceEntity>();

        var allExistedUnitPricesList = await unitPriceRepository.GetExistUnitPrices(up =>
            request.ProductIDs.Contains(up.ProductID) &&
            request.MCCIDs.Contains(up.MCCID) &&
            request.BusinessTypeIDs.Contains(up.BusinessTypeID));

        foreach (var productId in request.ProductIDs)
        {
            foreach (var mccId in request.MCCIDs)
            {
                foreach (var businessTypeId in request.BusinessTypeIDs)
                {
                    var existingUnitPrice = allExistedUnitPricesList.FirstOrDefault(up =>
                        up.ProductID == productId && up.MCCID == mccId && up.BusinessTypeID == businessTypeId);

                    if (existingUnitPrice == null)
                    {
                        var unitPrice = CreateNewUnitPrice(request, productId, mccId, businessTypeId);
                        newUnitPricesList.Add(unitPrice);
                    }
                    else
                    {
                        var oldExistingPrice = JsonConvert.DeserializeObject<UnitPriceEntity>(
                            JsonConvert.SerializeObject(existingUnitPrice));
                        if (oldExistingPrice != null)
                            oldUnitPricesList.Add(oldExistingPrice);

                        UpdateExistingUnitPrice(existingUnitPrice, request);
                        existingUnitPricesToUpdateList.Add(existingUnitPrice);
                    }
                }
            }
        }

        return (newUnitPricesList, oldUnitPricesList, existingUnitPricesToUpdateList);
    }

    private UnitPriceEntity CreateNewUnitPrice(UnitPriceCreateRequest request, Guid productId, Guid mccId, Guid businessTypeId)
    {
        var unitPrice = mapper.Map<UnitPriceEntity>(request);
        unitPrice.ProductID = productId;
        unitPrice.MCCID = mccId;
        unitPrice.BusinessTypeID = businessTypeId;
        return unitPrice;
    }

    private void UpdateExistingUnitPrice(UnitPriceEntity existingUnitPrice, UnitPriceCreateRequest request)
    {
        existingUnitPrice.UnitPrice = request.UnitPrice;
        existingUnitPrice.VATRate = request.VATRate;
        existingUnitPrice.VATType = request.VATType;
        existingUnitPrice.BillingType = request.BillingType;
        existingUnitPrice.BillingFrequency = request.BillingFrequency;
    }
    #endregion
    public async Task<List<UnitPriceDetails>> GetUnitPricesByIdsAsync(List<Guid> ids)
    {
        var unitPriceEntities = await unitPriceRepository.GetUnitPricesByIdsAsync(ids);
        return mapper.Map<List<UnitPriceDetails>>(unitPriceEntities);
    }
}
