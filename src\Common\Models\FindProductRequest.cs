﻿using System;
using System.Collections.Generic;

namespace Common.Models;

public class FindProductRequest
{
    public Guid? Id { get; set; }
    public string? Availability { get; set; }
    public string? Flow { get; set; }
    public string? SalesChannel { get; set; }
    public string? Code { get; set; }
    public string? Type { get; set; }
    public string? Description { get; set; }
    public Guid? CategoryId { get; set; }
    public bool OnlyValid { get; set; } = true;
    public List<string>? ProductCodes { get; set; }
    public List<string>? ReferralChannels { get; set; }
    public List<Guid>? ProductIds { get; set; }
    public int Page { get; set; } = 0;
    public int Size { get; set; } = 10;
}
