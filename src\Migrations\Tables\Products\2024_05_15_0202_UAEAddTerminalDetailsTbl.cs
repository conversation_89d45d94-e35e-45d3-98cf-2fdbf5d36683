﻿using FluentMigrator;

namespace Migrations.Tables.Products;
[Migration(2024_05_16_0202)]
public class UAEAddTerminalDetailsTbl : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"If NOT EXISTS(select top 1 * from sys.Objects where type ='U' and Object_ID = Object_ID(N'[PRODUCTS].dbo.[TerminalDetails]'))
BEGIN
CREATE TABLE dbo.TerminalDetails (
    ID uniqueidentifier PRIMARY KEY,
    TID nvarchar(36) NULL,
    MAKE nvarchar(60) NULL,
    MODEL nvarchar(60) NULL,
    SERIAL<PERSON>MBER nvarchar(100),
    <PERSON>MILY nvarchar(60),
    <PERSON><PERSON>y uniqueidentifier,
    CreatedDate datetime,
    UpdatedBY uniqueidentifier,
    UpdatedDate datetime
);
END
IF NOT EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'IDX_TerminalDetails_TID' AND object_id = OBJECT_ID(N'[dbo].[TerminalDetails]'))
                 BEGIN
                    CREATE NONCLUSTERED INDEX [IDX_TerminalDetails_TID]
                                ON [dbo].[TerminalDetails] ([TID])
                                INCLUDE ([MAKE],[MODEL],[SERIALNUMBER],[FAMILY])
END
            ");
    }
}
