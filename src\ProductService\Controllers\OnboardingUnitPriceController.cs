﻿using Common.Models.Onboarding;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ProductService.Controllers;
[Route("api/v1/[controller]")]
[ApiController]
public class OnboardingUnitPriceController : ControllerBase
{
    private readonly IOnboardingUnitPriceService onboardingUnitPriceService;
    public OnboardingUnitPriceController(IOnboardingUnitPriceService onboardingUnitPriceService)
    {
        this.onboardingUnitPriceService = onboardingUnitPriceService;
    }
    /// <summary>
    /// Get unit prices list, based on search, filteration and sorting data in the request.
    /// </summary>
    /// <param name="request">An object that contains search, filter, sort and pagination data.</param>
    /// <returns>
    ///  Returns an <see cref="IActionResult"/> Indicating the result of operation.
    /// Success: returns http 200 ok with the list of unit prices list data.
    /// Bad request: returns http 4oo bad request, with the result indicating the reason.
    /// Failure: returns http 500 InternalServerError, with the result indicating the failure.
    /// </returns>
    [HttpPost("GetProductUnitPricesList")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetProductUnitPricesList(OnboardingUnitPricesListRequest request)
    {
        var result = await onboardingUnitPriceService.GetOnboardingUnitPricesList(request);
        return Ok(result);
    }
}
