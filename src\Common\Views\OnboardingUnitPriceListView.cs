﻿using Common.Enums.UnitPrice;
using System;

namespace Common.Views;
public class OnboardingUnitPriceListView
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public string? ProductName { get; set; }
    public string? ProductNameAR { get; set; }
    public string? ProductSubname { get; set; }
    public string? ProductSubnameAr { get; set; }
    public string? ProductLink { get; set; }
    public int? ProductDisplayOrder { get; set; }
    public string? ProductDescription { get; set; }
    public string? ProductDescriptionAr { get; set; }
    public string ProductCode { get; set; } = null!;
    public bool? IsCNP { get; set; }
    public string? ProductFlow { get; set; }
    public string? ProductSalesChannel { get; set; }
    public string? ProductAvailability { get; set; }
    public string? ProductType { get; set; }
    public string? ProductReferralChannel { get; set; }
    public bool? ProductQuickOnboarding { get; set; }
    public Guid MccId { get; set; }
    public string MccName { get; set; } = null!;
    public string MccCode { get; set; } = null!;
    public string MccCategory { get; set; } = null!;
    public Guid BusinessTypeId { get; set; }
    public string BusinessType { get; set; } = null!;
    public string? BusinessTypeCode { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal VATRate { get; set; }
    public VatType VATType { get; set; }
    public BillingType BillingType { get; set; }
    public BillingFrequency BillingFrequency { get; set; }
    public DateTime CreatedDate { get; set; }
    public Guid? ImageIdEn { get; set; }
    public Guid? ImageIdAr { get; set; }
    public Guid CategoryId { get; set; }
    public string CategoryName { get; set; } = null!;
    public string CategoryCode { get; set; } = null!;
}
