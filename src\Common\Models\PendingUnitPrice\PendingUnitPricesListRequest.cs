﻿using Common.Enums;
using Common.Enums.UnitPrice;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace Common.Models.PendingUnitPrice;
public class PendingUnitPricesListRequest
{
    [DefaultValue(Constants.UnitPriceSearchDictionary)]
    public Dictionary<PendingUnitPriceSearchKey, string> SearchTerms { get; set; } = new Dictionary<PendingUnitPriceSearchKey, string>();
    public List<Guid>? FilterByProducts { get; set; }
    public List<Guid>? FilterByMccType { get; set; }
    public List<Guid>? FilterByBusinessType { get; set; }
    public List<VatType>? FilterByVatType { get; set; }
    public List<Guid>? CreatedByFilter { get; set; }
    [DefaultValue("desc")]
    public string OrderType { get; set; } = SortType.desc.ToString();
    [DefaultValue("CreatedDate")]
    public string OrderFieldName { get; set; } = "CreatedDate";
    [DefaultValue(1)]
    public int Page { get; set; } = 1;
    [DefaultValue(10)]
    public int Size { get; set; } = 10;
}
public enum PendingUnitPriceSearchKey
{
    All,
    ProductName,
    ProductCode,
    MccName,
    MccCode,
    BusinessType,
    CreatedBy
}