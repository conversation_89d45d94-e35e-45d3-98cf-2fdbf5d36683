﻿using Common.Validators;
using Geidea.Utils.Exceptions;
using MeezaDataModel = Geidea.ProductService.Models.MeezaData;

namespace Common.Data;

public class MeezaData : MeezaDataModel, IData
{
    public void ValidateAndThrow()
    {
        var dataValidationResult = new MeezaDataValidator().Validate(this);
        if (!dataValidationResult.IsValid)
        {
            throw new ValidationException(dataValidationResult);
        }
    }
}
