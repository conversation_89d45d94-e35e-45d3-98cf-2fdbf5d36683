﻿using Common.Entities;
using Common.Repositories;
using DataAccess.Repositories;
using Geidea.Utils.Counterparty.Providers;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace DataAccess.Test;
public class OrderGeneratedReportRepositoryTests
{
    private readonly DataContext context;
    private readonly IOrderGeneratedReportRepository orderReportRepository;

    public OrderGeneratedReportRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<DataContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        var counterPartyProvider = new CounterpartyProvider();
        counterPartyProvider.SetCode("GEIDEA_EGYPT");

        context = new DataContext(options, counterPartyProvider);
        orderReportRepository = new OrderGeneratedReportRepository(context, counterPartyProvider);
    }

    [Fact]
    public async Task AddOrderGeneratedReport()
    {
        await orderReportRepository.AddOrdersReport(new Common.Models.OrderGeneratedReport.OrdersReportRequest
        {
            AcquiringBank = "NBE_BANK",
            OrderNumbers = new List<string> { "EX_255959" },
            ReportName = "Legal File"
        });

        Assert.Equal(1, context.Set<OrderGeneratedReportEntity>().Count());
    }
}