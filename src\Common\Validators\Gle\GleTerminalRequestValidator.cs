﻿using System;
using Common.Models.Gle;
using FluentValidation;

namespace Common.Validators.Gle;

public class GleTerminalRequestValidator : AbstractValidator<GleTerminalRequest>
{
    public GleTerminalRequestValidator()
    {
        Include(new GleBaseValidator());

        RuleFor(x => x.OrderId)
            .NotNull()
            .NotEqual(Guid.Empty)
            .WithErrorCode(Errors.InvalidOrderId.Code)
            .WithMessage(Errors.InvalidOrderId.Message);

        RuleFor(x => x.GleStoreId)
            .NotNull()
            .NotEqual(Guid.Empty)
            .WithErrorCode(Errors.InvalidStoreId.Code)
            .WithMessage(Errors.InvalidStoreId.Message);

        RuleFor(x => x.ProductInstanceId)
            .NotNull()
            .NotEqual(Guid.Empty)
            .WithErrorCode(Errors.InvalidProductInstanceId.Code)
            .WithMessage(Errors.InvalidProductInstanceId.Message);
    }
}