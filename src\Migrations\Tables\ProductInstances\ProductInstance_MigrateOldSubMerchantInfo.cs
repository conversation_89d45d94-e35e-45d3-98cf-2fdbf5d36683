﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.ProductInstances;
[Migration(2023_06_16_1011)]
public class ProductInstance_MigrateOldSubMerchantInfo : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"CREATE TYPE [dbo].[CompanyIdsData] AS TABLE(
	[CmpanyId] [uniqueidentifier] NULL,
	[Mcc] [nvarchar](500) NULL,
	[Mso] [nvarchar](500) NULL
)");

        Execute.Sql(@"create  procedure MigrateOldSubMerchantData
@merchantServiceData dbo.CompanyIdsData READONLY   
as 
Begin 
declare @Metadata nvarchar(max)
declare @ProductInstanceId UNIQUEIDENTIFIER;
declare @json nvarchar(max)
DECLARE @CmpanyId UNIQUEIDENTIFIER;
DECLARE @Mcc VARCHAR(500);
DECLARE @Mso VARCHAR(500);
DECLARE @Count UNIQUEIDENTIFIER;
declare @subMerchantInfo nvarchar(max)

DECLARE my_cursor CURSOR FOR
SELECT CmpanyId, Mcc,Mso FROM @merchantServiceData ;

OPEN my_cursor;
FETCH NEXT FROM my_cursor INTO  @CmpanyId, @Mcc,@Mso;

WHILE @@FETCH_STATUS =0
BEGIN

DECLARE metadata_cursor CURSOR FOR
Select PIR.Id,PIR.Metadata  FROM [PRODUCTS].[dbo].[ProductInstances] PIR
   JOIN [PRODUCTS].[dbo].Products P ON P.Id = PIR.ProductId
   WHERE [Metadata] IS NOT NULL
     AND p.Type = 'GWAY'
     AND ISJSON([Metadata]) > 0
     AND p.Counterparty = 'GEIDEA_EGYPT'
     AND PIR.CompanyId = @CmpanyId
     And PIR.DeletedFlag =0 

OPEN metadata_cursor;

FETCH NEXT FROM metadata_cursor INTO @ProductInstanceId,@Metadata;

WHILE @@FETCH_STATUS = 0
BEGIN

  SET @subMerchantInfo =  JSON_QUERY(@Metadata, '$.SubMerchantInformation')
set @json = N'[{""Mcc"": ""'+COALESCE(@Mcc, '')+'"", ""Mso"": ""'+COALESCE(@Mso, '')+'"",""SubMerchantInfo"": '+@subMerchantInfo+'}]'
update  [PRODUCTS].[dbo].[ProductInstances] SET [Metadata] = JSON_MODIFY(@Metadata, '$.MsoSubMerchantInfo', JSON_QUERY(@json)) where Id= @ProductInstanceId

FETCH NEXT FROM metadata_cursor INTO  @ProductInstanceId,@Metadata;
END;
CLOSE metadata_cursor;
DEALLOCATE metadata_cursor;

   FETCH NEXT FROM my_cursor INTO @CmpanyId, @Mcc,@Mso;
END;
CLOSE my_cursor;
DEALLOCATE my_cursor;

end");
    }

}
