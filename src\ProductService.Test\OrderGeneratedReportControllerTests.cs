﻿using Common.Models.OrderGeneratedReport;
using Common.Services;
using Microsoft.AspNetCore.Mvc;
using Moq;
using ProductService.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace ProductService.Test;
public class OrderGeneratedReportControllerTests
{
    private readonly Mock<IOrderGeneratedReportService> orderReportService;
    private readonly OrderGeneratedReportController controller;

    public OrderGeneratedReportControllerTests()
    {
        orderReportService = new Mock<IOrderGeneratedReportService>();

        controller = new OrderGeneratedReportController(orderReportService.Object);
    }

    [Fact]
    public async Task AddReportOrders_Returns204NoContent()
    {
        var response = await controller.AddOrdersReport(new OrdersReportRequest());

        var okResult = response as NoContentResult;
        Assert.NotNull(okResult);

        orderReportService.Verify(x => x.AddOrdersReport(It.IsAny<OrdersReportRequest>()), Times.Once);
    }

    [Fact]
    public void AddReportOrders_Returns400BadRequest()
    {
        orderReportService.Setup(x => x.AddOrdersReport(It.IsAny<OrdersReportRequest>())).ThrowsAsync(new Exception());

        Assert.ThrowsAsync<Exception>(() => controller.AddOrdersReport(new OrdersReportRequest()));

        orderReportService.Verify(x => x.AddOrdersReport(It.IsAny<OrdersReportRequest>()), Times.Once);
    }
}
