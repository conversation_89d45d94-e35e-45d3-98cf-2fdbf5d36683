﻿DECLARE @AmexIds Table(Id UNIQUEIDENTIFIER)
DECLARE @ProductIds Table(ProductId UNIQUEIDENTIFIER, AmexIds UNIQUEIDENTIFIER)
DECLARE @TempProductTable TABLE(ProductId UNIQUEIDENTIFIER, Code nvarchar(255), Version int)
DECLARE @Id UNIQUEIDENTIFIER
DECLARE @Code nvarchar(255)
DECLARE @Version int
DECLARE @NewVersion int
DECLARE @Counterparty nvarchar(255)
set @Counterparty = 'GEIDEA_SAUDI'
DECLARE @Availability nvarchar(255)
set @Availability = 'Live'

INSERT INTO @AmexIds (Id)
Select Id FROM [PRODUCTS].[dbo].[Products] WHERE (Code = 'AMEX' or Code = 'AMEX_ONLINE' or Code = 'AMEX_POS') and Availability= @Availability and Counterparty= @Counterparty

UPDATE [PRODUCTS].[dbo].[Products]
SET Availability = 'Obsolete'
WHERE Counterparty= @Counterparty and (Code = 'AMEX' or Code = 'AMEX_ONLINE' or Code = 'AMEX_POS')

INSERT INTO @ProductIds (ProductId, AmexIds)
Select ProductId, PartId FROM [PRODUCTS].[dbo].[ProductParts] WHERE PartId in (Select Id from @AmexIds)

INSERT INTO @TempProductTable (ProductId, Code, Version)
Select p.Id, p.Code, p.Version FROM [PRODUCTS].[dbo].[Products] p
INNER JOIN @ProductIds t on p.Id = t.ProductId
Where p.Availability = @Availability

DECLARE @Cursor CURSOR

SET @Cursor = CURSOR FOR SELECT DISTINCT ProductId, Code, Version FROM @TempProductTable
OPEN @Cursor FETCH NEXT FROM @Cursor INTO @Id, @Code, @Version WHILE @@FETCH_STATUS = 0

BEGIN
	set @NewVersion = @Version + 1

	EXEC NewProductVersion_v2 @Code, @Version, @Code, @NewVersion, 1, @Counterparty

	DELETE 
	FROM [PRODUCTS].[dbo].[ProductParts] 
	WHERE PartId IN (select AmexIds FROM @ProductIds) AND ProductId IN (Select Id FROM [PRODUCTS].[dbo].[Products] WHERE code = @Code and version = @NewVersion and Availability = @Availability)
	
	FETCH NEXT From @Cursor INTO @Id, @Code, @Version
END

CLOSE @Cursor
DEALLOCATE @Cursor
