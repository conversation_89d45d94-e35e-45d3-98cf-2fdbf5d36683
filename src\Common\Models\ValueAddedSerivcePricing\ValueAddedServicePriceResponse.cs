﻿using Common.Models.UnitPrice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.ValueAddedSerivcePricing;
public class ValueAddedServicePriceResponse
{
    public List<ValueAddedServicesPricingDetails> CreatedVASPrices { get; set; } = new List<ValueAddedServicesPricingDetails>();
    public List<ValueAddedServicesPricingDetails> NewExistingVASPrices { get; set; } = new List<ValueAddedServicesPricingDetails>();
    public List<ValueAddedServicesPricingDetails> OldExistingVASPrices { get; set; } = new List<ValueAddedServicesPricingDetails>();
}
