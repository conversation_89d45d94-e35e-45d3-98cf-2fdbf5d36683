﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.ProductInstances;
[Migration(2023_01_22_0127)]
public class ProductInstances_DeleteMpgsHistoryExceedsExpiryDate : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"Create or alter FUNCTION DeleteExpiredMPGSHistoryFromMetaData
(
 @OldMetaData nvarchar(MAX) , @ExpirationDate DateTimeOffset
)
RETURNS nvarchar(MAX)
    AS
    BEGIN
​
	Declare @UpdatedMetaData Nvarchar(MAX) ;

       WITH HistoryMoreThanPeriod AS ( SELECt mpgsHistoryData.value as newHistoryMetaData FROM OPENJSON(@OldMetaData,'lax $.MpgsHistory' ) as mpgsHistoryData
	 
		where JSON_VALUE(mpgsHistoryData.value,'$.DeactivatedDate') > @ExpirationDate),
        NewHistoryConnncatinated as (select CONCAT('[',STRING_AGG(newHistoryMetaData, ','),']') as newHistoryJsonMetaData from HistoryMoreThanPeriod)

		SELECT @UpdatedMetaData=JSON_MODIFY(@OldMetaData, '$.MpgsHistory', JSON_Query(newHistoryJsonMetaData)) from NewHistoryConnncatinated;​

	Return @UpdatedMetaData​​
END
");
        Execute.Sql(@"USE [PRODUCTS]
GO
/****** Object:  UserDefinedFunction [dbo].[GetMPGSHistoryDeactivatedDates]    Script Date: 1/22/2023 12:12:59 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER function [dbo].[GetMPGSHistoryDeactivatedDates]
(
    @MPGSHistoryMetaData nvarchar(MAX)
)
returns table
as return
      SELECT * FROM OPENJSON(@MPGSHistoryMetaData)
        WITH (  DeactivatedDate DateTimeOffset(7)) ");
        Execute.Sql(@"USE [PRODUCTS]
GO
USE [PRODUCTS]
GO
/****** Object:  StoredProcedure [dbo].[DeleteExpiredMPGSHistory]   ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
Create OR ALTER       PROCEDURE [dbo].[DeleteExpiredMPGSHistory]
@ExpirationDate DateTimeOffset
AS
BEGIN

 with cte_MPGSHistory as
  (
    select  JSON_QUERY(Metadata,'$.MpgsHistory') as MPGSHistory 
	,PIR.Id as CTEProductInstanceId
	from  [PRODUCTS].[dbo].[ProductInstances] PIR join Products P
	  on P.Id = PIR.ProductId
	  WHERE [Metadata] IS NOT NULL
	   AND p.Type = 'GWAY'
       AND ISJSON([Metadata]) > 0
  ),
  cte_Selected_Ids_ToUpdate as
  (
  select  CTEProductInstanceId  from cte_MPGSHistory H
                cross apply GetMPGSHistoryDeactivatedDates( H.MPGSHistory) C
				where C.DeactivatedDate < @ExpirationDate
				group by CTEProductInstanceId
  )


     UPDATE ProductInstances SET [Metadata] = [dbo].[DeleteExpiredMPGSHistoryFromMetaData](Metadata, @ExpirationDate)
	            FROM ProductInstances 
                WHERE Id in ( select * from cte_Selected_Ids_ToUpdate)	
				
	
END
");

    }
}
