﻿using Common.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.ValueAddedServices;
public class ValueAddedServiceRequestParams
{
    [DefaultValue("")]
    public string? Filter { get; set; }
    [DefaultValue("")]
    public string? SortBy { get; set; }
    [DefaultValue("asc")]
    public string? OrderType { get; set; }
    [DefaultValue(1)]
    public int? PageNumber { get; set; }
    [DefaultValue(10)]
    public int? PageSize { get; set; }
}
