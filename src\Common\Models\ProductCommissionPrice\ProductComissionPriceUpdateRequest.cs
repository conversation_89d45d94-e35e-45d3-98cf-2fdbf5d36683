﻿using Common.Enums.ProductCommisssionPrices;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.ProductCommissionPrice;
public class ProductComissionPriceUpdateRequest
{
    public FeeType FeeType { get; set; }
    public decimal FeeValue { get; set; }
    public CommisssionPricesBillingType BillingType { get; set; }
    public PriceBillingFrequency BillingFrequency { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}
