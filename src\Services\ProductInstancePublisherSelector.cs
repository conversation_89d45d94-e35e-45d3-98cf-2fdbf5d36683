﻿using Common.Data.ProductType;
using Common.Services;
using System.Collections.Generic;
using System.Linq;

namespace Services;

public class ProductInstancePublisherSelector : IProductInstancePublisherSelector
{
    private readonly IEnumerable<IProductInstancePublisher> publishers;

    public ProductInstancePublisherSelector(IEnumerable<IProductInstancePublisher> publishers)
    {
        this.publishers = publishers;
    }

    public IProductInstancePublisher? GetProductInstancePublisher(ProductTypes productType)
    {
        return publishers.FirstOrDefault(p => p.ProductTypes.Contains(productType));
    }
}
