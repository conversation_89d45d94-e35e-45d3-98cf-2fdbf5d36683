﻿using FluentMigrator;

namespace Migrations.Tables.Gle;

[Migration(2022_01_11_1111)]
public class GleTerminal : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("GleTerminal")
            .WithColumn("Id").AsGuid().PrimaryKey().WithDefaultValue(SystemMethods.NewGuid)
            .WithColumn("GleUserId").AsString(250).Nullable()
            .WithColumn("GleLoginId").AsString(250).Nullable()
            .WithColumn("GleLoginId2").AsString(250).Nullable()
            .WithColumn("ParentGleUserId").AsString(250).Nullable()
            .WithColumn("GleRegistrationStatus").AsString(250).NotNullable()
            .WithColumn("GleRegistrationResponse").AsString(int.MaxValue).NotNullable()
            .WithColumn("GleStoreId").AsGuid().NotNullable().ForeignKey("GleStore", "Id")
            .WithColumn("ProductInstanceId").AsGuid().NotNullable().ForeignKey("ProductInstances", "Id")
            .WithColumn("OrderId").AsGuid().NotNullable()
            .WithColumn("TID").AsString(23).Nullable()
            .WithColumn("ReferenceMmsId").AsString(250).Nullable()
            .WithColumn("IsTerminalUser").AsBoolean().NotNullable().WithDefaultValue(false)
            .WithColumn("CreatedBy").AsString(150).NotNullable()
            .WithColumn("CreatedDate").AsDateTime2().NotNullable()
            .WithColumn("UpdatedBy").AsString(150).Nullable()
            .WithColumn("UpdatedDate").AsDateTime2().Nullable();

        Execute.Sql(@"CREATE UNIQUE INDEX GleTerminal_ProductInstanceId_UniqueIndex ON [dbo].[GleTerminal] ([ProductInstanceId], [IsTerminalUser]);");

        if (!Schema.Schema("History").Exists())
        {
            Create.Schema("History");
        }

        Execute.Sql(@"ALTER TABLE [dbo].[GleTerminal] ADD
              [StartDate] DATETIME2 GENERATED ALWAYS AS ROW START HIDDEN CONSTRAINT DF_GleTerminal_StartDate DEFAULT SYSUTCDATETIME(),
              [EndDate] DATETIME2 GENERATED ALWAYS AS ROW END HIDDEN CONSTRAINT DF_GleTerminal_EndDate DEFAULT CONVERT(DATETIME2, '9999-12-31 23:59:59'),
              PERIOD FOR SYSTEM_TIME(StartDate, EndDate);");

        Execute.Sql("ALTER TABLE [dbo].[GleTerminal] SET (SYSTEM_VERSIONING = ON (HISTORY_TABLE = [History].[GleTerminal]));");
    }
}