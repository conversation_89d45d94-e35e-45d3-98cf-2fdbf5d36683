﻿using Common.Models;
using Common.Models.businessType;
using Common.Models.CommissionFees;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Services;
public interface IBusinessTypeService
{
    Task<GetBusinessTypesListResponse> GetBusinessTypesList(GetBusinessTypesListRequest request);
    Task<BusinessTypeResponse> CreateAsync(BusinessTypeRequest request);
    Task<BusinessTypeResponse> UpdateAsync(Guid id, BusinessTypeUpdateRequest businessTypeUpdateRequest);
    Task<BusinessTypeDetailsResponse?> GetBusinessTypeDetails(Guid Id);
    Task<bool> ToggleStatus(Guid Id, bool Status);
    Task<List<BasicBusinessTypesInfo>> GetBusinessTypesNamesAsync();
}
