﻿update [dbo].[Products]
set code='ENTERPRISE1' where id = (select top 1 id from [dbo].[Products] where code = 'ENTERPRISE')
update [dbo].[Products]
set code='ENTERPRISE2' where id = (select top 1 id from [dbo].[Products] where code = 'ENTERPRISE')
update [dbo].[Products]
set code='BUSINESS1' where id = (select top 1 id from [dbo].[Products] where code = 'BUSINESS')
update [dbo].[Products]
set code='BUSINESS2' where id = (select top 1 id from [dbo].[Products] where code = 'BUSINESS')
update [dbo].[Products]
set code='PAYMENT_GATEWAY1' where id = (select top 1 id from [dbo].[Products] where code = 'PAYMENT_GATEWAY')
update [dbo].[Products]
set code='PAYMENT_GATEWAY2' where id = (select top 1 id from [dbo].[Products] where code = 'PAYMENT_GATEWAY')
update [dbo].[Products]
set code='WEBSITE_BUILDER1' where id = (select top 1 id from [dbo].[Products] where code = 'WEBSITE_BUILDER')
update [dbo].[Products]
set code='WEBSITE_BUILDER2' where id = (select top 1 id from [dbo].[Products] where code = 'WEBSITE_BUILDER')

if NOT EXISTS( SELECT * 
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
    WHERE CONSTRAINT_NAME='UQ_Code'  and CONSTRAINT_CATALOG='PRODUCTS')
	begin
		ALTER TABLE [dbo].[Products]
		ADD CONSTRAINT UQ_Code UNIQUE (Code);  
	end