﻿using AutoMapper;
using Common.Models;
using Common.Options;
using Common.Repositories;
using DataAccess;
using DataAccess.Repositories;
using Elastic.Apm.Api;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NSubstitute;
using ProductService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace Services.Test;
public class GetCategoriesListTests
{
    private readonly Mock<ILogger<CategoryService>> logger = new Mock<ILogger<CategoryService>>();
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly CategoryService categoryService;
    private readonly Guid categoryId = Guid.NewGuid();
    private readonly IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;
    private readonly ICounterpartyProvider counterpartyProvider;

    public GetCategoriesListTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);
        var options = new DbContextOptionsBuilder<DataContext>()
           .UseInMemoryDatabase(databaseName: "CategoryGetCategoriesListTests" + Guid.NewGuid().ToString())
           .Options;
        var context = new DataContext(options, new CounterpartyProvider());
        softposEgyptFeatureToggle = Substitute.For<IOptions<SoftposEgyptFeatureToggle>>();
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = true });
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();

        ICategoryRepository categoryRepository = new CategoryRepository(context, httpContext.Object);
        IProductRepository productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);

        categoryService = new CategoryService(categoryRepository, logger.Object, mapper, productRepository);
    }
    [Fact]
    public async Task GetCategoriesList_ShouldCallRepository_ReturnCategoriesLost()
    {
        var CategoriesList = await categoryService.GetCategoriesList();
        CategoriesList.Should().NotBeNull();
    }
}
