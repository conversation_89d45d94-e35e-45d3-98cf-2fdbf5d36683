using Common.Data;
using Common.Entities;
using Common.Repositories;
using Common.Services;
using DataAccess.Repositories;
using Elasticsearch.Net;
using Geidea.ProductService.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace ProductService.Test;

public class MetaDataMigrationHostedTests
{
    private readonly ServiceCollection services;
    private readonly Mock<IMetaDataMigrationFilesRepository> metaDataMigrationFilesRepository;
    private readonly Mock<IProductInstanceRepository> productInstanceRepository;
    private readonly ServiceProvider serviceProvider;
    private readonly MetaDataMigrationFilesEntity metaDataMigrationFile = new MetaDataMigrationFilesEntity
    {
        Id = Guid.NewGuid(),
        MigrationFileName = "test1",
        UpdateQuery = "UPDATE pi SET Metadata = JSON_MODIFY(pi.Metadata, '$.TestManualMigration', 'true') FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE p.Type = 'GWAY' AND ISJSON(pi.Metadata) = 1 AND Counterparty = 'GEIDEA_EGYPT' ",
        SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE p.Type = 'GWAY' AND ISJSON(pi.Metadata) = 1 AND Counterparty = 'GEIDEA_EGYPT' ",
        CreatedDate = DateTime.UtcNow,
        Status = MetadataStatus.Failed.ToString()
    };
    private readonly List<MetaDataMigrationFilesEntity> testData = new List<MetaDataMigrationFilesEntity>
    {
        new MetaDataMigrationFilesEntity { Id = Guid.NewGuid(), Status = MetadataStatus.Failed.ToString() },
        new MetaDataMigrationFilesEntity { Id = Guid.NewGuid(), Status = MetadataStatus.PartiallySucceeded.ToString() },
        new MetaDataMigrationFilesEntity { Id = Guid.NewGuid(), Status = MetadataStatus.Succeeded.ToString() },
    };

    public MetaDataMigrationHostedTests()
    {
        services = new ServiceCollection();

        services.AddTransient<IMetaDataMigrationFilesRepository, MetaDataMigrationFilesRepository>();
        services.AddTransient<IProductInstanceRepository, ProductInstanceRepository>();
        services.AddTransient<IMetaDataMigrationService, MetaDataMigrationService>();

        metaDataMigrationFilesRepository = new Mock<IMetaDataMigrationFilesRepository>();
        metaDataMigrationFilesRepository.Setup(x => x.GetAllMigrationFiles()).ReturnsAsync(new List<MetaDataMigrationFilesEntity>());
        metaDataMigrationFilesRepository.Setup(x => x.GetFailedAndPartiallyFailedMigrationFiles()).ReturnsAsync(testData);
        metaDataMigrationFilesRepository.Object.InsertAsync(metaDataMigrationFile);

        productInstanceRepository = new Mock<IProductInstanceRepository>();
        productInstanceRepository.Setup(x => x.SelectByQueryString(It.IsAny<string>())).ReturnsAsync(new List<string>());
        productInstanceRepository.Setup(x => x.UpdateByQueryString(It.IsAny<string>())).ReturnsAsync(true);

        services.AddScoped(_ => metaDataMigrationFilesRepository.Object);
        services.AddScoped(_ => productInstanceRepository.Object);

        serviceProvider = services.BuildServiceProvider();

    }

    [Fact]
    public async Task StartAsync_getMetaDataMigrationFilesList_Success()
    {
        using var scope = serviceProvider.CreateScope();
        var hostedService = new MetaDataMigrationService(new Mock<ILogger<MetaDataMigrationService>>().Object, metaDataMigrationFilesRepository.Object, productInstanceRepository.Object);
        await hostedService.MigrateMetaDataScripts();

        var metaDataMigrationFilesList = MetaDataMigrationFilesList.getMetaDataMigrationFilesList();
        Assert.NotNull(metaDataMigrationFilesList);
    }

    [Fact]
    public async Task StartAsync_GetFinishedMigrationFiles_Success()
    {
        using var scope = serviceProvider.CreateScope();
        var hostedService = new MetaDataMigrationService(new Mock<ILogger<MetaDataMigrationService>>().Object, metaDataMigrationFilesRepository.Object, productInstanceRepository.Object);
        await hostedService.MigrateMetaDataScripts();

        metaDataMigrationFilesRepository.Verify(x => x.GetAllMigrationFiles(), Times.AtLeastOnce);
        metaDataMigrationFilesRepository.Verify(x => x.InsertAsync(It.IsAny<MetaDataMigrationFilesEntity>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task UpdateMigrationStatusALreadyExist()
    {
        var newMetaDataMigrationFile = new MetaDataMigrationFilesEntity
        {
            Id = Guid.NewGuid(),
            MigrationFileName = metaDataMigrationFile.MigrationFileName,
            UpdateQuery = metaDataMigrationFile.UpdateQuery,
            SelectQuery = metaDataMigrationFile.SelectQuery,
            CreatedDate = metaDataMigrationFile.CreatedDate,
            Status = MetadataStatus.Succeeded.ToString()
        };

        await metaDataMigrationFilesRepository.Object.UpdateMigrationFile(metaDataMigrationFile.Id, newMetaDataMigrationFile);
        metaDataMigrationFilesRepository.Verify(x => x.InsertAsync(It.IsAny<MetaDataMigrationFilesEntity>()), Times.Once);
    }

    [Fact]
    public async Task UpdateMigrationStatusNotExist()
    {
        var newMetaDataMigrationFile = new MetaDataMigrationFilesEntity
        {
            Id = Guid.NewGuid(),
            MigrationFileName = metaDataMigrationFile.MigrationFileName,
            UpdateQuery = metaDataMigrationFile.UpdateQuery,
            SelectQuery = metaDataMigrationFile.SelectQuery,
            CreatedDate = metaDataMigrationFile.CreatedDate,
            Status = MetadataStatus.Succeeded.ToString()
        };

        await metaDataMigrationFilesRepository.Object.UpdateMigrationFile(Guid.NewGuid(), newMetaDataMigrationFile);
        metaDataMigrationFilesRepository.Verify(x => x.InsertAsync(It.IsAny<MetaDataMigrationFilesEntity>()), Times.Once);
    }

    [Fact]
    public async Task GetFailedAndPartiallyFailedMigrationFiles()
    {
        var result = await metaDataMigrationFilesRepository.Object.GetFailedAndPartiallyFailedMigrationFiles();

        Assert.NotNull(result);
        Assert.All(result, file => Assert.True(file.Status == MetadataStatus.Failed.ToString() || file.Status == MetadataStatus.PartiallySucceeded.ToString()
            || file.Status == MetadataStatus.Succeeded.ToString()));
    }
}
