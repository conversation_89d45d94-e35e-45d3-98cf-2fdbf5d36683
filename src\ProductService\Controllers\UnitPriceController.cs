﻿using Common.Models.UnitPrice;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Services;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace ProductService.Controllers;
[Route("api/v1/[controller]")]
[ApiController]
public class UnitPriceController : ControllerBase
{
    private readonly IUnitPriceService unitPriceService;
    public UnitPriceController(IUnitPriceService unitPriceService)
    {
        this.unitPriceService = unitPriceService;
    }
    /// <summary>
    /// Create new unit prices based on products, MCCs, and business types.
    /// </summary>
    /// <returns>Returns the created unit prices or existing ones if they already exist.</returns>
    [HttpPost("CreateUnitPrices")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreateAsync([FromBody] UnitPriceCreateRequest request)
    {
        var result = await unitPriceService.CreateAsync(request);
        return Ok(result);
    }
    /// <summary>
    /// Get the unit price based on MCCId, ProductId, and BusinessTypeId.
    /// </summary>
    /// <param name="mccId">MCC ID</param>
    /// <param name="productId">Product ID</param>
    /// <param name="businessTypeId">Business Type ID</param>
    /// <returns>
    /// Returns the unit price details if found.
    /// </returns>
    [HttpGet("GetUnitPrice")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetUnitPrice(Guid mccId, Guid productId, Guid businessTypeId)
    {

        var unitPrice = await unitPriceService.GetUnitPriceAsync(mccId, productId, businessTypeId);
        return Ok(unitPrice);

    }
    [HttpPut("UpdateUnitPrice/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> UpdateUnitPrice(Guid id, [FromBody] UnitPriceUpdateRequest request)
    {

        var updatedUnitPrice = await unitPriceService.UpdateAsync(id, request);
        return Ok(updatedUnitPrice);
    }
    /// <summary>
    /// Get unit prices list, based on search, filteration and sorting data in the request.
    /// </summary>
    /// <param name="request">An object that contains search, filter, sort and pagination data.</param>
    /// <returns>
    ///  Returns an <see cref="IActionResult"/> Indicating the result of operation.
    /// Success: returns http 200 ok with the list of unit prices list data.
    /// Bad request: returns http 4oo bad request, with the result indicating the reason.
    /// Failure: returns http 500 InternalServerError, with the result indicating the failure.
    /// </returns>
    [HttpPost("GetUnitPricesList")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetUnitPricesList(UnitPricesListRequest request)
    {
        var result = await unitPriceService.GetUnitPricesList(request);
        return Ok(result);
    }
    /// <summary>
    /// Delete unit prices by a list of IDs.
    /// </summary>
    /// <param name="deletedBy"></param>
    /// <param name="ids">List of unit price IDs to delete.</param>
    /// <returns>Returns a status indicating success or failure.</returns>
    [HttpDelete("DeleteUnitPrices")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DeleteUnitPricesAsync([FromQuery] string deletedBy, [FromBody] List<Guid> ids)
    {
        await unitPriceService.DeleteUnitPricesAsync(ids, deletedBy);

        return Ok(new { Message = "Unit prices deleted successfully." });
    }
    /// <summary>
    /// Get unit price details by ID.
    /// </summary>
    /// <param name="id">The ID of the unit price.</param>
    /// <returns>
    /// Returns the unit price details if found.
    /// </returns>
    [HttpGet("GetUnitPriceById/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetUnitPriceById(Guid id)
    {
        var unitPrice = await unitPriceService.GetUnitPriceByIdAsync(id);
        return Ok(unitPrice);
    }
    /// <summary>
    /// Get product categories based on MCCId and BusinessTypeId.
    /// </summary>
    /// <returns>
    /// Returns the list of product categories if found.
    /// </returns>
    [HttpPost("GetProductCategories")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetProductCategoriesAsync([FromBody] ProductCategoryRequest request)
    {
        var productCategories = await unitPriceService.ProductCategoryListAsync(request.MccId, request.BusinessTypeId);
        return Ok(productCategories);
    }
    /// <summary>
    /// Create or Update unit prices based on products, MCCs, and business types.
    /// </summary>
    /// <returns>Returns the created or updated unit prices.</returns>
    [HttpPost("CreateOrUpdateUnitPrices")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreateOrUpdateAsync([FromBody] UnitPriceCreateRequest request)
    {
        var result = await unitPriceService.CreateOrUpdateAsync(request);
        return Ok(result);
    }
    [HttpPost("GetUnitPricesByIds")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetUnitPricesByIds([FromBody] List<Guid> ids)
    {
        var unitPrices = await unitPriceService.GetUnitPricesByIdsAsync(ids);
        return Ok(unitPrices);
    }
}

