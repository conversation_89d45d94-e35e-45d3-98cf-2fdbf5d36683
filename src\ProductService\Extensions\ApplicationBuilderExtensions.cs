﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Services;

namespace ProductService.Extensions;

public static class ApplicationBuilderExtensions
{
    public static IApplicationBuilder StartPointOfSaleResponseMessageConsumption(this IApplicationBuilder builder)
    {
        builder.ApplicationServices.GetRequiredService<PointOfSaleResponseMessageReceiver>().StartReceiving();
        return builder;
    }

    public static IApplicationBuilder StartOrderMigrationRespondMessageConsumption(this IApplicationBuilder builder)
    {
        builder.ApplicationServices.GetRequiredService<OrderMigrationSyncTriggerMessageReceiver>().StartReceiving();
        return builder;
    }
}
