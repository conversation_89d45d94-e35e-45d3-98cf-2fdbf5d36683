﻿using Common.Entities;
using Common.Models.Vendor;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Mapping;
public static class VendorMapper
{
    public static VendorEntity Map(VendorRequest vendorRequest)
    {
        return new VendorEntity
        {
            Name = vendorRequest.Name.Trim(),
            TerminalType = vendorRequest.TerminalType,
            Prefix = string.Join(",", vendorRequest.Prefix.Distinct()),
            IsDefault = false,
        };
    }

    public static VendorResponse? Map(VendorEntity? vendorEntity)
    {
        return vendorEntity != null ?
            new VendorResponse
            {
                Id = vendorEntity.Id,
                Name = vendorEntity.Name,
                TerminalType = vendorEntity.TerminalType,
                Prefix = vendorEntity.Prefix.Split(",").ToList(),
                IsDefault = vendorEntity.IsDefault,
            } : null;
    }

    public static List<VendorResponse>? Map(List<VendorEntity> vendorEntities)
    {
        return vendorEntities != null ?
           vendorEntities.Select(v => new VendorResponse
           {
               Id = v.Id,
               Name = v.Name,
               TerminalType = v.TerminalType,
               Prefix = v.Prefix.Split(",").ToList(),
               IsDefault = v.IsDefault,
           }).ToList()
           : null;
    }

    public static VendorSearchResponse Map(List<VendorEntity> vendorEntities, int totalCount)
    {
        var vendorSearchResponse = new VendorSearchResponse();

        if (vendorEntities != null)
        {
            vendorSearchResponse.Records = vendorEntities.Select(v => new VendorResponse
            {
                Id = v.Id,
                Name = v.Name,
                TerminalType = v.TerminalType,
                Prefix = v.Prefix.Split(",").ToList(),
                IsDefault = v.IsDefault,
            }).ToList();

            vendorSearchResponse.ReturnedRecordCount = vendorSearchResponse.Records.Count;
            vendorSearchResponse.TotalRecordCount = totalCount;
        }

        return vendorSearchResponse;
    }
}
