﻿using Common.Enums;
using Common.Enums.ProductCommisssionPrices;
using Common.Enums.UnitPrice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.ValueAddedSerivcePricing;
public class ValueAddedServicesPricingCreateRequest
{
    public List<Guid> ProductIDs { get; set; } = new List<Guid>();

    public List<Guid> MCCIDs { get; set; } = new List<Guid>();

    public List<Guid> BusinessTypeIDs { get; set; } = new List<Guid>();
    public List<Guid> VASIDs { get; set; } = new List<Guid>();

    public decimal SubscriptionFee { get; set; }

    public VatType FeeType { get; set; }

    public BillingType BillingType { get; set; }

    public PriceBillingFrequency BillingFrequency { get; set; }
}
