﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Common;
using Common.Data;
using Common.Entities;
using Common.Models;
using Common.Models.ProductInstance;
using Common.Repositories;
using Common.Services;
using DataAccess;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using ProductService;
using Services.Settings;
using Xunit;
using GatewayData = Common.Data.GatewayData;
using MeezaData = Common.Data.MeezaData;

namespace Services.Test.ProductConfiguratorTests;

public class CreateTests
{
    private readonly Mock<ILogger<ProductConfiguratorService>> logger = new();
    private readonly Mock<ILogger<ProductInstanceRepository>> repoLogger = new();
    private readonly Mock<IHttpContextAccessor> httpContext = new();
    private readonly Mock<IProductService> productService = new();
    private readonly Mock<ICounterpartyProvider> counterpartyProvider = new();
    private readonly Mock<IProductChangeSenderService> productSender = new();
    private readonly Mock<IProductRepository> productRepository = new();

    private readonly Mock<IOptionsMonitor<MeezaSettings>> meezaSettings = new Mock<IOptionsMonitor<MeezaSettings>>();
    private readonly Mock<IOptionsMonitor<CurrencySettings>> currencySettings = new Mock<IOptionsMonitor<CurrencySettings>>();
    private readonly Mock<IOptionsMonitor<MpgsAccountsSettings>> mpgsAccountsSettings = new Mock<IOptionsMonitor<MpgsAccountsSettings>>();
    private readonly DataContext context;
    private readonly ProductInstanceRepository productInstanceRepo;
    private readonly ProductConfiguratorService productConfiguratorService;

    private readonly ProductEntity productEntity = new()
    {
        Availability = "Bundle",
        Code = "TEST valid",
        Description = "Test description",
        Type = "BUNDLE",
        Version = 0
    };

    private readonly ProductEntity terminalEntity = new()
    {
        Availability = "LIVE",
        Code = "SOFT_POS",
        Description = "Test description",
        Type = "TERMINAL",
        Version = 0
    };

    private readonly ProductEntity miniEcrEntity = new()
    {
        Availability = "LIVE",
        Code = "GEIDEA_GO_APP",
        Description = "Test description",
        Type = "MINI_ECR",
        Version = 0
    };

    private readonly ProductEntity meezaEntity = new()
    {
        Id = Guid.NewGuid(),
        Type = "MEEZA",
        Code = "MEEZA_CODE"
    };

    public CreateTests()
    {
        meezaSettings.Setup(x => x.CurrentValue).Returns(new MeezaSettings
        {
            MerchantPrefix = "675"
        });

        currencySettings.Setup(x => x.CurrentValue).Returns(new CurrencySettings
        {
            SupportedCurrencies = new string[] { "SAR", "EGP", "AED", "USD", "EUR", "GBP", "BHD", "KWD", "OMR", "QAR" }
        });


        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
           .UseInMemoryDatabase(databaseName: "ProductInstanceCreateTests" + Guid.NewGuid().ToString())
           .Options;

        context = new DataContext(options, new CounterpartyProvider());
        context.Products.Add(productEntity);
        context.Products.Add(terminalEntity);
        context.Products.Add(miniEcrEntity);
        context.Products.Add(meezaEntity);
        context.SaveChanges();

        counterpartyProvider.Setup(x => x.GetCode()).Returns("GEIDEA_SAUDI");

        productInstanceRepo = new ProductInstanceRepository(context, httpContext.Object, repoLogger.Object, mapper);
        productConfiguratorService = new ProductConfiguratorService(
            meezaSettings.Object,
            logger.Object,
            mapper,
            productService.Object,
            productInstanceRepo,
            productRepository.Object,
            counterpartyProvider.Object,
            productSender.Object,
            currencySettings.Object,
            mpgsAccountsSettings.Object);
    }

    [Fact]
    public async Task EmptyAgreementId()
    {
        var createRequest = new CreateProductInstanceRequest
        {
            AgreementId = Guid.Empty
        };

        await productConfiguratorService
            .Invoking(x => x.CreateAsync(createRequest))
            .Should().ThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task NullAgreementId()
    {
        var createRequest = new CreateProductInstanceRequest
        {
            AgreementId = null
        };

        await productConfiguratorService
            .Invoking(x => x.CreateAsync(createRequest))
            .Should().NotThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task NullChildren()
    {
        var createRequest = new CreateProductInstanceRequest
        {
            AgreementId = Guid.NewGuid(),
            Children = new List<CreateProductInstanceRequest>
                {
                    null
                }
        };

        await productConfiguratorService
            .Invoking(x => x.CreateAsync(createRequest))
            .Should().ThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task ParentNotFound()
    {
        var createRequest = new CreateProductInstanceRequest
        {
            AgreementId = Guid.NewGuid(),
            ParentId = Guid.NewGuid()
        };

        await productConfiguratorService
            .Invoking(x => x.CreateAsync(createRequest))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
                        x.ProblemDetails.Type == Errors.ParentProductInstanceNotFound.Code);
    }

    [Fact]
    public async Task ProductNotFound()
    {
        var createRequest = new CreateProductInstanceRequest
        {
            AgreementId = Guid.NewGuid(),
            ProductId = Guid.NewGuid()
        };

        await productConfiguratorService
            .Invoking(x => x.CreateAsync(createRequest))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
                        x.ProblemDetails.Type == Errors.ProductNotFound.Code);
    }

    [Fact]
    public async Task MiniEcrInvalidData()
    {
        var miniEcrProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "MINI_ECR"
        };
        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), It.IsAny<bool>()))
            .Returns(Task.FromResult(new[] { miniEcrProduct }));

        var createRequest = new CreateProductInstanceRequest
        {
            AgreementId = Guid.NewGuid(),
            ProductId = miniEcrProduct.Id,
            Data = new MiniEcrData { Nickname = "test", DeviceId = null! }
        };

        await productConfiguratorService
            .Invoking(x => x.CreateAsync(createRequest))
            .Should().ThrowAsync<ValidationException>();
    }

    [Fact]
    public async Task InvalidChild()
    {
        var terminalProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "TERMINAL"
        };
        var findRequest = new FindProductRequest
        {
            Id = terminalProduct.Id,
            OnlyValid = false
        };
        productService.Setup(x => x.FindAsync(findRequest, It.IsAny<bool>()))
            .Returns(Task.FromResult(new[] { terminalProduct }));

        var createRequest = new CreateProductInstanceRequest
        {
            AgreementId = Guid.NewGuid(),
            ProductId = terminalProduct.Id,
            Children = new List<CreateProductInstanceRequest>
                {
                    new()
                    {
                        AgreementId = Guid.NewGuid(),
                        ProductId = Guid.NewGuid()
                    }
                }
        };

        await productConfiguratorService
            .Invoking(x => x.CreateAsync(createRequest))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
                        x.ProblemDetails.Type == Errors.ProductNotFound.Code);
    }

    [Fact]
    public async Task CreateWithChildren()
    {
        var parentProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "BUNDLE"
        };
        var gatewayProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "GWAY"
        };

        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), It.IsAny<bool>()))
            .Returns<FindProductRequest, bool>((request, _) => request.Id == parentProduct.Id ?
                Task.FromResult(new[] { parentProduct }) : Task.FromResult(new[] { gatewayProduct }));

        var createRequest = new CreateProductInstanceRequest
        {
            AgreementId = Guid.NewGuid(),
            ProductId = parentProduct.Id,
            Children = new List<CreateProductInstanceRequest>
            {
                new()
                {
                    AgreementId = Guid.NewGuid(),
                    ProductId = gatewayProduct.Id,
                    Data=new GatewayData { MerchantGatewayKey = "gateway key" }
                }
            }
        };

        await productConfiguratorService.CreateAsync(createRequest);

        var created = (await productInstanceRepo.WhereAsync(p => p.ProductId == parentProduct.Id, p => p.Children)).FirstOrDefault();
        created.Should().NotBeNull();
        created?.Children.Should().NotBeNullOrEmpty();
        (created?.Children[0].Data as GatewayData)?.AllowedInitiatedByValues.Should().Contain(Constants.InitiatedByInternet);
        created?.Children[0].ParentId.Should().Be(created.Id);
        created?.Children[0].AgreementId.Should().Be(created.AgreementId);
    }

    [Fact]
    public async Task CreateWithParent()
    {
        var parentProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "BUNDLE"
        };
        var gatewayProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "GWAY"
        };

        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), It.IsAny<bool>()))
            .Returns<FindProductRequest, bool>((request, _) => request.Id == parentProduct.Id ?
                Task.FromResult(new[] { parentProduct }) : Task.FromResult(new[] { gatewayProduct }));

        var parentInstance = new ProductInstanceEntity
        {
            AgreementId = Guid.NewGuid(),
            ProductId = parentProduct.Id,
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid()
        };
        context.ProductInstances.Add(parentInstance);
        context.SaveChanges();

        var createRequest = new CreateProductInstanceRequest
        {
            AgreementId = Guid.NewGuid(),
            ProductId = gatewayProduct.Id,
            Data = new GatewayData { MerchantGatewayKey = "gateway key" },
            ParentId = parentInstance.Id,
            ParentConfigurationId = parentInstance.Id
        };

        await productConfiguratorService.CreateAsync(createRequest);

        var created = (await productInstanceRepo.WhereAsync(p => p.ProductId == createRequest.ProductId, p => p.Children)).FirstOrDefault();
        created.Should().NotBeNull();
        created?.ParentId.Should().Be(parentInstance.Id);
        created?.AgreementId.Should().Be(parentInstance.AgreementId);
        created?.CompanyId.Should().Be(parentInstance.CompanyId);
        created?.StoreId.Should().Be(parentInstance.StoreId);
    }

    [Fact]
    public async Task CreateDefaultInstances_ShouldCreateMeezaInstance()
    {
        var meezaProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "MEEZA"
        };

        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), It.IsAny<bool>()))
            .Returns<FindProductRequest, bool>((_, _) => Task.FromResult(new[] { meezaProduct }));

        var request = new DefaultProductInstanceRequest
        {
            CompanyId = Guid.NewGuid(),
            ProductId = meezaProduct.Id
        };

        var created = await productConfiguratorService.CreateDefaultProductInstancesAsync(request);
        created.Should().HaveCount(1);
        var createdInstances = await productInstanceRepo.WhereAsync(p => p.Id == created[0]);
        createdInstances.Should().HaveCount(1);

        var createdInstance = createdInstances.Single();
        createdInstance.Data.Should().BeOfType<MeezaData>();

        createdInstance.Should().NotBeNull();
        (createdInstance.Data as MeezaData)?.MeezaMerchantId.Should().NotBe(null);
        (createdInstance.Data as MeezaData)?.RegistrationStatus.Should().Be(Constants.MeezaRegistrationStatuses.NotRegistered);
        (createdInstance.Data as MeezaData)?.RegistrationHistory.Should().HaveCount(0);
        createdInstance.StoreId.Should().Be(null);
        createdInstance.CompanyId.Should().Be(request.CompanyId);
        createdInstance.ProductId.Should().Be(request.ProductId);
    }

    [Fact]
    public async Task CreateDefaultProductInstancesChildren_ShouldCreateMeezaInstance()
    {
        var meezaProduct = new Product
        {
            Id = meezaEntity.Id,
            Type = "MEEZA"
        };

        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), It.IsAny<bool>()))
            .Returns<FindProductRequest, bool>((_, _) => Task.FromResult(new[] { meezaProduct }));

        var request = new DefaultProductInstanceRequest
        {
            CompanyId = Guid.NewGuid(),
            ProductId = meezaProduct.Id
        };

        var created = await productConfiguratorService.CreateDefaultProductInstancesChildrenAsync(request);
        created.Should().HaveCount(1);
        var createdInstances = await productInstanceRepo.WhereAsync(p => p.Id == created[0].ParentProductInstanceId);
        createdInstances.Should().HaveCount(1);

        var createdInstance = createdInstances.Single();
        createdInstance.Data.Should().BeOfType<MeezaData>();

        createdInstance.Should().NotBeNull();
        (createdInstance.Data as MeezaData)?.MeezaMerchantId.Should().NotBe(null);
        (createdInstance.Data as MeezaData)?.RegistrationStatus.Should().Be(Constants.MeezaRegistrationStatuses.NotRegistered);
        (createdInstance.Data as MeezaData)?.RegistrationHistory.Should().HaveCount(0);
        createdInstance.StoreId.Should().Be(null);
        createdInstance.CompanyId.Should().Be(request.CompanyId);
        createdInstance.ProductId.Should().Be(request.ProductId);
    }


    [Fact]
    public async Task CreateDefaultInstances_WhenGatewayProduct_ShouldCreateTwoInstances()
    {
        var gatewayProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "GWAY"
        };

        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), It.IsAny<bool>()))
            .Returns<FindProductRequest, bool>((_, _) => Task.FromResult(new[] { gatewayProduct }));

        var request = new DefaultProductInstanceRequest
        {
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            ProductId = gatewayProduct.Id,
            DefaultOperation = "Pay",
            MerchantName = "merchant EN",
            MerchantNameAr = "merchant AR",
            MpgsMerchantId = "merchantId",
            MpgsApiKey = "apiKey"
        };

        var created = await productConfiguratorService.CreateDefaultProductInstancesAsync(request);
        created.Should().HaveCount(2);
        var createdInstances = await productInstanceRepo.WhereAsync(p => p.Id == created[0] || p.Id == created[1]);
        createdInstances.Should().HaveCount(2);

        createdInstances.Select(x => x.Data).Should().AllBeOfType<GatewayData>();
        var testInstance = createdInstances.SingleOrDefault(x => (x.Data as GatewayData)?.IsTest == true);
        testInstance.Should().NotBeNull();
        (testInstance.Data as GatewayData).MerchantName.Should().Be(request.MerchantName);
        (testInstance.Data as GatewayData).MerchantNameAr.Should().Be(request.MerchantNameAr);
        (testInstance.Data as GatewayData).Currencies.Should().BeEquivalentTo(new List<string> { "SAR" });
        (testInstance.Data as GatewayData).MerchantCountry.Should().Be("SAU");
        (testInstance.Data as GatewayData).MpgsAccounts.First().MpgsMerchantId.Should().Be(request.MpgsMerchantId);
        (testInstance.Data as GatewayData).MpgsAccounts.First().MpgsApiKey.Should().Be(request.MpgsApiKey);
        (testInstance.Data as GatewayData).DefaultPaymentOperation.Should().Be(request.DefaultOperation);
        (testInstance.Data as GatewayData).IsRefundEnabled.Should().Be(true);
        testInstance.StoreId.Should().Be(request.StoreId);
        testInstance.CompanyId.Should().Be(request.CompanyId);

        var liveInstance = createdInstances.SingleOrDefault(x => (x.Data as GatewayData)?.IsTest == false);
        liveInstance.Should().NotBeNull();
        (liveInstance.Data as GatewayData).MerchantName.Should().Be(request.MerchantName);
        (liveInstance.Data as GatewayData).MerchantNameAr.Should().Be(request.MerchantNameAr);
        (liveInstance.Data as GatewayData).Currencies.Should().BeEquivalentTo(new List<string> { "SAR" });
        (liveInstance.Data as GatewayData).MerchantCountry.Should().Be("SAU");
        (liveInstance.Data as GatewayData).MpgsAccounts.Count.Should().Be(0);
        (liveInstance.Data as GatewayData).DefaultPaymentOperation.Should().Be("Pay");
        (liveInstance.Data as GatewayData).IsRefundEnabled.Should().Be(true);
        liveInstance.StoreId.Should().Be(request.StoreId);
        liveInstance.CompanyId.Should().Be(request.CompanyId);
    }

    [Fact]
    public async Task CreateDefaultInstances_WhenGatewayProduct_ShouldNotCreateInstancesWhenThereAreExistingForTheSpecifiedStore()
    {
        var companyid = Guid.NewGuid();
        var storeId = Guid.NewGuid();

        var parentProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "BUNDLE"
        };
        var gatewayProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "GWAY"
        };

        var parentProductEntity = new ProductEntity
        {
            Id = parentProduct.Id,
            Type = "BUNDLE"
        };
        var gatewayProductEntity = new ProductEntity
        {
            Id = gatewayProduct.Id,
            Type = "GWAY"
        };

        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), It.IsAny<bool>()))
            .Returns<FindProductRequest, bool>((request, _) => request.Id == parentProduct.Id ?
                Task.FromResult(new[] { parentProduct }) : Task.FromResult(new[] { gatewayProduct }));

        var parentInstance = new ProductInstanceEntity
        {
            Product = parentProductEntity,
            CompanyId = companyid,
            StoreId = storeId
        };

        var gatewayInstance = new ProductInstanceEntity
        {
            Product = gatewayProductEntity,
            CompanyId = companyid,
            StoreId = storeId,
            Parent = parentInstance
        };

        context.ProductInstances.Add(gatewayInstance);

        context.SaveChanges();

        var request = new DefaultProductInstanceRequest
        {
            CompanyId = companyid,
            StoreId = storeId,
            ProductId = gatewayProduct.Id,
        };

        var created = await productConfiguratorService.CreateDefaultProductInstancesAsync(request);
        created.Should().HaveCount(0);
    }

    [Fact]
    public async Task CreateDefaultInstances_WhenBundleWithGatewayProduct_ShouldCreateTwoChildInstances()
    {
        var parentProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "BUNDLE"
        };
        var gatewayProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "GWAY"
        };
        var schemeProduct = new Product
        {
            Id = Guid.NewGuid(),
            Code = "VISA_ONLINE",
            Type = "SCHEME"
        };

        parentProduct.Parts = new List<ProductPart>
            {
                new()
                {
                    Part = gatewayProduct,
                    Product = parentProduct
                },
                new()
                {
                    Part = schemeProduct,
                    Product = parentProduct
                }
            };

        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), It.IsAny<bool>()))
            .Returns<FindProductRequest, bool>((productRequest, _) =>
                productRequest.Id == parentProduct.Id ? Task.FromResult(new[] { parentProduct }) :
                productRequest.Id == schemeProduct.Id ? Task.FromResult(new[] { schemeProduct }) :
                Task.FromResult(new[] { gatewayProduct }));

        var request = new DefaultProductInstanceRequest
        {
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            ProductId = parentProduct.Id,
            MerchantName = "merchant EN",
            MerchantNameAr = "merchant AR",
            DefaultOperation = "Pay"
        };

        var created = await productConfiguratorService.CreateDefaultProductInstancesAsync(request);
        created.Should().HaveCount(1);
        var createdInstance = await productInstanceRepo.FirstOrDefaultAsync(p => p.Id == created[0]);
        createdInstance.Children.Should().HaveCount(2);

        createdInstance.Children.Select(x => x.Data).Should().AllBeOfType<GatewayData>();
        var testInstance = createdInstance.Children.SingleOrDefault(x => (x.Data as GatewayData)?.IsTest == true);
        testInstance.Should().NotBeNull();
        (testInstance.Data as GatewayData).MerchantName.Should().Be(request.MerchantName);
        (testInstance.Data as GatewayData).MerchantNameAr.Should().Be(request.MerchantNameAr);
        (testInstance.Data as GatewayData).Currencies.Should().BeEquivalentTo(new List<string> { "SAR" });
        (testInstance.Data as GatewayData).MerchantCountry.Should().Be("SAU");
        (testInstance.Data as GatewayData).IsRefundEnabled.Should().Be(true);
        testInstance.StoreId.Should().Be(request.StoreId);
        testInstance.CompanyId.Should().Be(request.CompanyId);

        var liveInstance = createdInstance.Children.SingleOrDefault(x => (x.Data as GatewayData)?.IsTest == false);
        liveInstance.Should().NotBeNull();
        (liveInstance.Data as GatewayData).MerchantName.Should().Be(request.MerchantName);
        (liveInstance.Data as GatewayData).MerchantNameAr.Should().Be(request.MerchantNameAr);
        (liveInstance.Data as GatewayData).Currencies.Should().BeEquivalentTo(new List<string> { "SAR" });
        (liveInstance.Data as GatewayData).MerchantCountry.Should().Be("SAU");
        (liveInstance.Data as GatewayData).IsRefundEnabled.Should().Be(true);
        liveInstance.StoreId.Should().Be(request.StoreId);
        liveInstance.CompanyId.Should().Be(request.CompanyId);
    }

    [Fact]
    public async Task CreateDefaultInstances_ShouldCreateBundleInstances()
    {
        var terminalProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "TERMINAL",
        };

        var miniEcrProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "MINI_ECR"
        };

        var bundleProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "BUNDLE",
            Parts = new List<ProductPart>
                {
                    new() {Part = terminalProduct, PartId = Guid.NewGuid()},
                    new() {Part = miniEcrProduct, PartId = Guid.NewGuid()}
                }
        };
        var productList = new[] { bundleProduct, terminalProduct, miniEcrProduct };

        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), It.IsAny<bool>()))
            .Returns(Task.FromResult(productList));

        var request = new DefaultProductInstanceRequest
        {
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            ProductId = bundleProduct.Id,
            MerchantName = "merchant EN",
            MerchantNameAr = "merchant AR",
            MpgsMerchantId = "merchantId",
            MpgsApiKey = "apiKey"
        };

        var created = await productConfiguratorService.CreateDefaultProductInstancesAsync(request);
        created.Should().NotBeEmpty();
    }

    [Fact]
    public async Task CreateDefaultInstances_ShouldCreateMeezaInstances()
    {
        var meezaProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "MEEZA"
        };

        var bundleProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "BUNDLE",
            Parts = new List<ProductPart>
                {
                    new() {Part = meezaProduct, PartId = Guid.NewGuid()}
                }
        };
        var productList = new[] { bundleProduct, meezaProduct };

        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), It.IsAny<bool>()))
            .Returns(Task.FromResult(productList));

        var request = new DefaultProductInstanceRequest
        {
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            ProductId = bundleProduct.Id,
            MerchantName = "merchant EN",
            MerchantNameAr = "merchant AR",
            MpgsMerchantId = "merchantId",
            MpgsApiKey = "apiKey"
        };

        var created = await productConfiguratorService.CreateDefaultProductInstancesAsync(request);
        created.Should().NotBeEmpty();
    }

    [Fact]
    public async Task CreateDefaultInstances_ShouldCreateAccessoriesDefaultProductInstance()
    {
        var accessoriesProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "ACCESSORIES"
        };

        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), It.IsAny<bool>()))
            .Returns<FindProductRequest, bool>((productRequest, _) => productRequest.Id == accessoriesProduct.Id ?
                Task.FromResult(new[] { accessoriesProduct }) : Task.FromResult(Array.Empty<Product>()));

        var request = new DefaultProductInstanceRequest
        {
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            ProductId = accessoriesProduct.Id,
            MerchantName = "merchant EN",
            MerchantNameAr = "merchant AR",
            MpgsMerchantId = "merchantId",
            MpgsApiKey = "apiKey"
        };

        var created = await productConfiguratorService.CreateDefaultProductInstancesAsync(request);
        created.Should().NotBeEmpty();
    }

    [Fact]
    public async Task CreateDefaultInstances_WhenNoProduct_ShouldThrowException()
    {
        var request = new DefaultProductInstanceRequest
        {
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            ProductId = Guid.NewGuid(),
            MerchantName = "merchant EN",
            MerchantNameAr = "merchant AR",
            MpgsMerchantId = "merchantId",
            MpgsApiKey = "apiKey"
        };

        await productConfiguratorService
            .Invoking(x => x.CreateDefaultProductInstancesAsync(request))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task CreateDefaultInstances_WhenException_NoInstanceShouldBeCreated()
    {
        var parentProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "BUNDLE"
        };
        var gatewayProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "GWAY"
        };
        var schemeProduct = new Product
        {
            Id = Guid.NewGuid(),
            Code = "VISA_ONLINE",
            Type = "SCHEME"
        };

        parentProduct.Parts = new List<ProductPart>
            {
                new()
                {
                    Part = gatewayProduct,
                    Product = parentProduct
                },
                new()
                {
                    Part = schemeProduct,
                    Product = parentProduct
                }
            };

        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), It.IsAny<bool>()))
            .Returns<FindProductRequest, bool>((productRequest, _) =>
                productRequest.Id == parentProduct.Id ? Task.FromResult(new[] { parentProduct }) :
                productRequest.Id == schemeProduct.Id ? Task.FromResult(new[] { schemeProduct }) :
                Task.FromResult(Array.Empty<Product>()));

        var request = new DefaultProductInstanceRequest
        {
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            ProductId = parentProduct.Id,
            MerchantName = "merchant EN",
            MerchantNameAr = "merchant AR",
            MpgsMerchantId = "merchantId",
            MpgsApiKey = "apiKey"
        };

        await productConfiguratorService
            .Invoking(x => x.CreateDefaultProductInstancesAsync(request))
            .Should().ThrowAsync<ServiceException>();

        var productInstances = context.ProductInstances.Where(i => i.ProductId == parentProduct.Id || i.ProductId == gatewayProduct.Id);
        productInstances.Should().BeNullOrEmpty();
    }

    [Fact]
    // EMULATE the GO_SMART_BP case
    public async Task CreateDefaultInstance_ShouldAddServices()
    {
        var terminalProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "TERMINAL",
        };

        var serviceProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "SERVICES",
            Code = "BILL_PAYMENT"
        };

        var schemeProduct = new Product
        {
            Id = Guid.NewGuid(),
            Code = "VISA_ONLINE",
            Type = "SCHEME"
        };


        var bundleProduct = new Product
        {
            Id = Guid.NewGuid(),
            Type = "BUNDLE",
            Parts = new List<ProductPart>
                {
                    new() {Part = terminalProduct, PartId = Guid.NewGuid()},
                    new() {Part = serviceProduct, PartId = Guid.NewGuid()},
                    new() {Part = schemeProduct, PartId = Guid.NewGuid()}
                }
        };

        var productList = new[] { bundleProduct, serviceProduct, terminalProduct, schemeProduct };

        productService.Setup(x => x.FindAsync(It.IsAny<FindProductRequest>(), It.IsAny<bool>()))
            .Returns(Task.FromResult(productList));

        var request = new DefaultProductInstanceRequest
        {
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            ProductId = bundleProduct.Id,
            MerchantName = "merchant EN",
            MerchantNameAr = "merchant AR",
            MpgsMerchantId = "merchantId",
            MpgsApiKey = "apiKey"
        };

        var created = await productConfiguratorService.CreateDefaultProductInstancesAsync(request);
        created.Should().NotBeEmpty();
    }


}
