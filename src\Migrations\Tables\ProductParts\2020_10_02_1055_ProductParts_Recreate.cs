﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2020_10_02_1055)]
public class ProductParts_Recreate : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("ProductParts")
          .WithColumn("ProductId").AsGuid().NotNullable().PrimaryKey().ForeignKey("Products", "Id")
          .WithColumn("PartId").AsGuid().NotNullable().PrimaryKey().ForeignKey("Products", "Id")
          .WithColumn("Quantity").AsInt32().NotNullable().WithDefaultValue(1)
          ;
    }
}
