﻿using Swashbuckle.AspNetCore.SwaggerGen;
using Geidea.Utils.Common;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;

namespace ProductService.Filters;

public class CounterpartyHeaderFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        operation.Parameters.Add(new OpenApiParameter
        {
            Name = Constants.CounterpartyHeaderName,
            In = ParameterLocation.Header,
            Required = false,
            Example = new OpenApiString(Constants.CounterpartySaudi)
        });
    }
}
