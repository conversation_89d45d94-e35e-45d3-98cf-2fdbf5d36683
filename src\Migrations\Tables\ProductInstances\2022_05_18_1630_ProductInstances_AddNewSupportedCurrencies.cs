﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_05_18_1630)]
public class ProductInstances_AddNewSupportedCurrencies : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
UPDATE pi
SET Metadata = JSON_MODIFY(pi.Metadata, '$.Currencies', JSON_QUERY('[""SAR"",""EGP"",""AED"",""USD"",""EUR"",""GBP"",""BHD"",""KWD"",""OMR"",""QAR""]'))
FROM ProductInstances pi
INNER JOIN Products p ON p.id = pi.ProductId
WHERE p.Type = 'GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) = 1");
    }
}
