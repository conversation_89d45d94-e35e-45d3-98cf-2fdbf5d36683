﻿using Common.Entities;
using Common.Models;
using System;
using System.Collections;
using System.Collections.Generic;

namespace DataAccess.Test.TestData;

public class FindPriceData : IEnumerable<object[]>
{
    public IEnumerator<object[]> GetEnumerator()
    {
        yield return new object[]
        {
                new PriceEntity
                {
                    ProductId = Guid.NewGuid(),
                    ChargeFrequency = "Recurring",
                    ChargeType = "CNP",
                    MaxPrice = 100,
                    Threshold = 30,
                    PerItemPrice = 40,
                    ExemptFromVAT = true,
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    Currency = "EUR",
                    Group = "Rental",
                    PercentagePrice = 3,
                    Priority = 0,
                    RentalPeriod = 24,
                    ThresholdType = "Type1",
                    ValidFrom = DateTime.UtcNow,
                    ValidTo = DateTime.UtcNow.AddDays(3),
                    DeletedFlag = true
                },
                new FindPriceRequest
                {
                    OnlyValid = false,
                    ThresholdType = "Type1"
                }
        };

        yield return new object[]
        {
                new PriceEntity
                {
                    ProductId = Guid.NewGuid(),
                    ChargeFrequency = "Recurring",
                    ChargeType = "CNP",
                    MaxPrice = 100,
                    Threshold = 30,
                    PerItemPrice = 40,
                    ExemptFromVAT = true,
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    Currency = "EUR",
                    Group = "Rental",
                    PercentagePrice = 3,
                    Priority = 0,
                    RentalPeriod = 24,
                    ThresholdType = "Type2",
                    ValidFrom = DateTime.UtcNow.AddDays(-3),
                    ValidTo = DateTime.UtcNow.AddDays(-1)
                },
                new FindPriceRequest
                {
                    OnlyValid = false,
                    ThresholdType = "Type2"
                }
        };

        yield return new object[]
        {
                new PriceEntity
                {
                    ProductId = Guid.NewGuid(),
                    ChargeFrequency = "Recurring",
                    ChargeType = "CNP",
                    MaxPrice = 100,
                    Threshold = 30,
                    PerItemPrice = 40,
                    ExemptFromVAT = true,
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    Currency = "EUR",
                    Group = "Rental",
                    PercentagePrice = 3,
                    Priority = 0,
                    RentalPeriod = 24,
                    ThresholdType = "Type2"
                },
                new FindPriceRequest
                {
                    ChargeType = "CNP"
                }
        };

        yield return new object[]
        {
                new PriceEntity
                {
                    ProductId = Guid.NewGuid(),
                    ChargeFrequency = "Recurring",
                    ChargeType = "CNP",
                    MaxPrice = 100,
                    Threshold = 30,
                    PerItemPrice = 40,
                    ExemptFromVAT = true,
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    Currency = "EUR",
                    Group = "Rental",
                    PercentagePrice = 3,
                    Priority = 0,
                    RentalPeriod = 24,
                    ThresholdType = "Type2"
                },
                new FindPriceRequest
                {
                    ExemptFromVAT = true
                }
        };

        yield return new object[]
        {
                new PriceEntity
                {
                    ProductId = Guid.NewGuid(),
                    ChargeFrequency = "Recurring",
                    ChargeType = "CNP",
                    MaxPrice = 100,
                    Threshold = 30,
                    PerItemPrice = 40,
                    ExemptFromVAT = true,
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    Currency = "EUR",
                    Group = "Rental",
                    PercentagePrice = 3,
                    Priority = 0,
                    RentalPeriod = 24,
                    ThresholdType = "Type2"
                },
                new FindPriceRequest
                {
                    PercentagePrice = 3
                }
        };

        yield return new object[]
        {
                new PriceEntity
                {
                    ProductId = Guid.NewGuid(),
                    ChargeFrequency = "Recurring",
                    ChargeType = "CNP",
                    MaxPrice = 100,
                    Threshold = 30,
                    PerItemPrice = 40,
                    ExemptFromVAT = true,
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    Currency = "EUR",
                    Group = "Rental",
                    PercentagePrice = 3,
                    Priority = 0,
                    RentalPeriod = 24,
                    ThresholdType = "Type2"
                },
                new FindPriceRequest
                {
                    PerItemPrice = 40
                }
        };

        var productId = Guid.NewGuid();
        yield return new object[]
        {
                new PriceEntity
                {
                    ProductId = productId,
                    ChargeFrequency = "Recurring",
                    ChargeType = "CNP",
                    MaxPrice = 100,
                    Threshold = 30,
                    PerItemPrice = 40,
                    ExemptFromVAT = true,
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    Currency = "EUR",
                    Group = "Rental",
                    PercentagePrice = 3,
                    Priority = 0,
                    RentalPeriod = 24,
                    ThresholdType = "Type2"
                },
                new FindPriceRequest
                {
                    ProductId = productId
                }
        };

        yield return new object[]
        {
                new PriceEntity
                {
                    ProductId = Guid.NewGuid(),
                    ChargeFrequency = "Recurring",
                    ChargeType = "CNP",
                    MaxPrice = 100,
                    Threshold = 30,
                    PerItemPrice = 40,
                    ExemptFromVAT = true,
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    Currency = "EUR",
                    Group = "Rental",
                    PercentagePrice = 3,
                    Priority = 0,
                    RentalPeriod = 24,
                    ThresholdType = "Type2"
                },
                new FindPriceRequest
                {
                    Threshold = 30
                }
        };

        yield return new object[]
        {
                new PriceEntity
                {
                    ProductId = Guid.NewGuid(),
                    ChargeFrequency = "Recurring",
                    ChargeType = "CNP",
                    MaxPrice = 100,
                    Threshold = 30,
                    PerItemPrice = 40,
                    ExemptFromVAT = true,
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    Currency = "EUR",
                    Group = "Rental",
                    PercentagePrice = 3,
                    Priority = 0,
                    RentalPeriod = 24,
                    ThresholdType = "Type2"
                },
                new FindPriceRequest
                {
                    ThresholdType = "Type2"
                }
        };

        yield return new object[]
        {
                new PriceEntity
                {
                    ProductId = Guid.NewGuid(),
                    ChargeFrequency = "Recurring",
                    ChargeType = "CNP",
                    MaxPrice = 100,
                    Threshold = 30,
                    PerItemPrice = 40,
                    ExemptFromVAT = true,
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    Currency = "EUR",
                    Group = "Rental",
                    PercentagePrice = 3,
                    Priority = 0,
                    RentalPeriod = 24,
                    ThresholdType = "Type2"
                },
                new FindPriceRequest
                {
                    Group = "Rental"
                }
        };

        yield return new object[]
        {
                new PriceEntity
                {
                    ProductId = Guid.NewGuid(),
                    ChargeFrequency = "Recurring",
                    ChargeType = "CNP",
                    MaxPrice = 100,
                    Threshold = 30,
                    PerItemPrice = 40,
                    ExemptFromVAT = true,
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    Currency = "EUR",
                    Group = "Rental",
                    PercentagePrice = 3,
                    Priority = 0,
                    RentalPeriod = 24,
                    ThresholdType = "Type2"
                },
                new FindPriceRequest
                {
                    Currency = "EUR"
                }
        };

        yield return new object[]
        {
                new PriceEntity
                {
                    ProductId = productId,
                    ChargeFrequency = "Recurring",
                    ChargeType = "CNP",
                    MaxPrice = 100,
                    Threshold = 30,
                    PerItemPrice = 40,
                    ExemptFromVAT = true,
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    Currency = "EUR",
                    Group = "Rental",
                    PercentagePrice = 3,
                    Priority = 0,
                    RentalPeriod = 24,
                    ThresholdType = "Type2"
                },
                new FindPriceRequest
                {
                    OnlyValid = true,
                    Currency = "EUR",
                    ThresholdType = "Type2",
                    ChargeType = "CNP",
                    ExemptFromVAT = true,
                    PercentagePrice = 3,
                    PerItemPrice = 40,
                    ProductId = productId,
                    Threshold = 30,
                    Group = "Rental"
                }
        };
    }

    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }
}
