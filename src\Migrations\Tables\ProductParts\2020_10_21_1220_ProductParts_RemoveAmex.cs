﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2020_10_21_1220)]
public class ProductParts_RemoveAmex : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"  delete from ProductParts 
                            where ProductId in (select Id from Products where code='PAYMENT_GATEWAY_BUNDLE') 
							and PartId in (select Id from Products where code='AMEX')");
    }
}
