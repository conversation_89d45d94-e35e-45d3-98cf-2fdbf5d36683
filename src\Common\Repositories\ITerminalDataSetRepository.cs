﻿using Common.Entities;
using Common.Models.Search;
using Common.Models.TerminalDataSets;
using Common.Services.Acquirers;
using Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Repositories;

public interface ITerminalDataSetRepository
{
    Task<List<TerminalDataSet>> CreateAsync(List<TerminalDataSet> terminalDataSets);
    Task<List<TerminalDataSet>> PatchAsync(List<TerminalDataSetPatchRequest> terminalDataSetPatchRequest);
    Task<TerminalDataSetEntity> GetTerminalDataByIdAsync(Guid terminalDataId);
    Task<SearchResponse<TerminalDataSetSearchResponse>> AdvancedSearchAsync(TerminalDataSetSearchRequest terminalDataRequest);
    Task<int> GetAvailableTerminalDataSetCountAsync(string acquiringLedger);
    Task<List<TerminalDataSetValidationRequiredFields>> GetValidationRequiredFieldsAsync(List<TerminalDataSet> terminalDataSets);
    Task<List<TerminalDataSet>> GetOrderMigrationValidationRequiredFieldsAsync(List<TerminalDataSet> terminalDataSets);
    void TriggerOrderMigrationSyncAsync();
    Task<List<TerminalDataSet>> GetTerminalDataByProductInstanceId(Guid[] productInstanceIds);
    Task<List<TerminalDataSetsResponse>> GenerateTIDAndMIDAndAddEditTerminalDataSets(TerminalDataSetsRequest terminalDataSets, IAcquirer acquirer);
    Task<List<TerminalDataSetsResponse>> GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(TerminalDataSetsMidTidRequest terminalDataSetsMidTid, IAcquirer acquirer);
    Task<TerminalDataSetsResponse> GenerateTIDAndAddEditTerminalDataSet(TerminalDataSet terminalDataSet, IAcquirer acquirer);
    Task<string> GenerateMIDAndAddEditTerminalDataSet(TerminalDataSetMidRequest terminalDataSetMidRequest, IAcquirer acquirer);
    Task<List<TerminalDataSetsResponse>> AddUpdateTerminalDataSetMcc(TerminalDataSetsMidTidRequest terminalDataSetsRequest);
    Task<string> GetTerminalIdSequenceAsync();
    Task<List<OrderTid>> GetTidByOrderId(List<string?> orderNumber);

}
