﻿using AutoMapper;
using Common;
using Common.Entities;
using Common.Models;
using Common.Models.CategoryRequests;
using Common.Repositories;
using Common.Services;
using Common.Validators;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Services;

public class CategoryService : ICategoryService
{
    private readonly ICategoryRepository categoryRepository;
    private readonly ILogger<CategoryService> logger;
    private readonly IMapper mapper;
    private readonly IProductRepository productRepository;

    public CategoryService(ICategoryRepository categoryRepository,
        ILogger<CategoryService> logger,
        IMapper mapper,
        IProductRepository productRepository)
    {
        this.categoryRepository = categoryRepository;
        this.logger = logger;
        this.mapper = mapper;
        this.productRepository = productRepository;
    }

    public async Task<Category> CreateAsync(CategoryRequest categoryRequest)
    {
        ValidateCategory(categoryRequest);
        await CheckCode(categoryRequest.Code);

        if (categoryRequest.ParentId != null)
            await CheckParentExists((Guid)categoryRequest.ParentId);

        var createdCategory = mapper.Map<CategoryEntity>(categoryRequest);

        categoryRepository.Save(createdCategory);
        await categoryRepository.SaveChangesAsync();

        logger.LogInformation($"Created category with id '{createdCategory.Id}'.");

        return mapper.Map<Category>(createdCategory);
    }

    public async Task DeleteAsync(Guid categoryId)
    {
        var isUsed = (await productRepository.FindAsync(new FindProductRequest() { CategoryId = categoryId, OnlyValid = false }, false)).Any();

        var category = await GetCategoryEntityAsync(categoryId);
        category.Subcategories.ForEach(subCategory => subCategory.ParentId = null);

        if (isUsed)
        {
            logger.LogInformation("Soft deleting category with id {categoryId} as it is being used.", categoryId);
            category.DeletedFlag = true;
            await categoryRepository.SaveChangesAsync();
        }
        else
        {
            logger.LogInformation("Hard deleting category with id {categoryId}", categoryId);
            await categoryRepository.HardDeleteCategory(category);
        }
    }

    public async Task<Category[]> FindAsync(FindCategoryRequest findCategoryRequest)
    {
        var categories = await categoryRepository.FindCategories(findCategoryRequest);

        categories.ToList().ForEach(x => x.Subcategories = x.Subcategories.Where(subcategory => !subcategory.DeletedFlag).ToList());

        return mapper.Map<CategoryEntity[], Category[]>(categories.OrderBy(p => p.DisplayOrder).ThenBy(p => p.Code).ToArray());
    }

    public async Task<Category> PatchAsync(Guid categoryId, JsonPatchDocument<CategoryRequest> patchCategory)
    {
        var category = await GetCategoryEntityAsync(categoryId);
        var categoryUpdate = mapper.Map<CategoryRequest>(category);

        try
        {
            patchCategory.ApplyTo(categoryUpdate);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Invalid patch category request.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidPatchRequest);
        }

        ValidateCategory(categoryUpdate);

        if (categoryUpdate.ParentId != null && category.ParentId != categoryUpdate.ParentId)
            await CheckParentExists((Guid)categoryUpdate.ParentId);

        if (category.Code != categoryUpdate.Code)
            await CheckCode(categoryUpdate.Code);

        mapper.Map(categoryUpdate, category);

        categoryRepository.Update(category);
        await categoryRepository.SaveChangesAsync();

        logger.LogInformation($"Updated category with id '{categoryId}'.");

        return mapper.Map<Category>(category);
    }

    private async Task CheckCode(string code)
    {
        if (await categoryRepository.ExistsAsync(x => x.Code == code && !x.DeletedFlag))
        {
            logger.LogError($"There is already a category with code '{code}' in the datebase.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidCategoryCode);
        }
    }

    private async Task CheckParentExists(Guid parentId)
    {
        var parentExists = await categoryRepository.ExistsAsync(x => x.Id == parentId && !x.DeletedFlag);
        if (!parentExists)
        {
            logger.LogError($"Parent category with id '{parentId}' not found.");
            throw new ServiceException(HttpStatusCode.NotFound, Errors.CategoryNotFound);
        }
    }

    private async Task<CategoryEntity> GetCategoryEntityAsync(Guid categoryId)
    {
        var category = await categoryRepository.SingleOrDefaultAsync(x => x.Id == categoryId && !x.DeletedFlag, c => c.Subcategories);

        if (category == null)
        {
            logger.LogError($"Category with id '{categoryId}' not found.");
            throw new ServiceException(HttpStatusCode.NotFound, Errors.CategoryNotFound);
        }

        return category;
    }

    private void ValidateCategory(CategoryRequest categoryRequest)
    {
        var validationResult = new CategoryRequestValidator().Validate(categoryRequest);

        if (!validationResult.IsValid)
        {
            var errorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
            logger.LogError("Category request failed validation: {@errors}", errorDescription);

            throw new ValidationException(validationResult);
        }
    }
    public async Task<List<CategoriesListResponse>> GetCategoriesList()
    {
        try
        {
            return await categoryRepository.GetCategoriesList();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "something has gone wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, Errors.InternalServerError);
        }
    }

}
