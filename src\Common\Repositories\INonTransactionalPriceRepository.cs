﻿using Common.Entities;
using Common.Models.NonTransactionalPrice;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Common.Repositories;
public interface INonTransactionalPriceRepository : IRepository<Guid, NonTransactionalPriceEntity>
{
    Task SaveNonTransactionalPricesAsync(List<NonTransactionalPriceEntity> NonTransactionalPrices);
    Task<NonTransactionalPriceEntity?> GetByIdAsync(Guid id);
    Task<List<NonTransactionalPriceEntity>> GetNonTransactionalPricesByIdsAsync(List<Guid> ids);
    Task<NonTransactionalPriceEntity?> GetNonTransactionalPriceByIdAsync(Guid id);
    Task<int> DeleteBulkAsync(List<Guid> ids);
    Task<List<NonTransactionalPriceEntity>> GetExistNonTransactionalPrices(Expression<Func<NonTransactionalPriceEntity, bool>> predicate);
    Task<NonTransactionalFeesPriceListResponse> GetNonTransactionalPriceList(NonTransactionalFeesPriceListRequest request);
    Task AddLogsAsync(List<NonTransactionalPriceLogEntity> logs);
    Task UpdateNonTransactionalPricesAsync(List<NonTransactionalPriceEntity> nonTransPrices);
}
