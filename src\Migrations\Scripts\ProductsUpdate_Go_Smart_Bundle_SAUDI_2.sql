﻿USE PRODUCTS;
GO
DECLARE @ProductId UNIQUEIDENTIFIER
DECLARE @GOFamily UNIQUEIDENTIFIER
DECLARE @NewProductPartId UNIQUEIDENTIFIER

SELECT TOP 1 @GOFamily = ID FROM Category where Code = 'GO_FAMILY' and Counterparty='GEIDEA_SAUDI';

EXEC NewProductVersion_v2 'GO_SMART', 7, 'GO_SMART', 8, 1,'GEIDEA_SAUDI'
SELECT TOP 1 @ProductId = ID FROM Products where Code='GO_SMART' and [Version] = 8 and CounterParty='GEIDEA_SAUDI'

SELECT TOP 1 @NewProductPartId = ID FROM Products where Code='PAX_A920' and CounterParty='GEIDEA_SAUDI'
INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProductId, @NewProductPartId)

Delete from ProductParts where ProductId = @ProductId  and PartId IN
    (Select ID FROM Products where Code  ='SMARTPOS_A920' and Counterparty ='GEIDEA_SAUDI')

