﻿USE PRODUCTS
GO

BEGIN TRANSACTION;

DECLAR<PERSON> @CounterpartyHeader NVARCHAR(255);
DECLARE @Currency NVARCHAR(3);
DECLARE @CategoryCode NVARCHAR(255);
DECLARE @PayByLinkBundleProductId UNIQUEIDENTIFIER;
DECLAR<PERSON> @CategoryId UNIQUEIDENTIFIER;

create table #CounterpartyHeaders (HeaderName NVARCHAR(255), Currency NVARCHAR(3), CategoryCode NVARCHAR(255));    
INSERT INTO #CounterpartyHeaders VALUES ('GEIDEA_SAUDI','SAR','GO_FAMILY')
INSERT INTO #CounterpartyHeaders VALUES ('GEIDEA_EGYPT','EGP','GO_FAMILY')
INSERT INTO #CounterpartyHeaders VALUES ('GEIDEA_UAE','AED','PAY_FAMILY')

WHILE (SELECT COUNT(*) FROM #CounterpartyHeaders) > 0
BEGIN

    SELECT TOP 1 @CounterpartyHeader = HeaderName, @Currency = Currency, @CategoryCode = CategoryCode FROM #CounterpartyHeaders

    --insert into PRODUCTS
    INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty)
    VALUES('Live', 'PAY_BY_LINK_BUNDLE', 'BUNDLE', GETUTCDATE(), 'n/a', GETUTCDATE(), 1, 0, @CounterpartyHeader)

    SELECT TOP 1 @PayByLinkBundleProductId = Id
    FROM PRODUCTS
    WHERE Code='PAY_BY_LINK_BUNDLE' and Counterparty=@CounterpartyHeader

    SELECT TOP 1 @CategoryId = Id
    FROM Category
    WHERE Code=@CategoryCode and Counterparty=@CounterpartyHeader and DeletedFlag=0

    --insert into ProductCategories
    INSERT INTO ProductCategories (ProductId, CategoryId) VALUES (@PayByLinkBundleProductId, @CategoryId)

    --insert into Prices
    INSERT INTO [dbo].[Prices]
           ([ChargeFrequency]
           ,[ChargeType]
           ,[ExemptFromVAT]
           ,[ProductId]
           ,[PerItemPrice]
           ,[Priority]
           ,[ValidFrom]
           ,[DeletedFlag]
           ,[CreatedBy]
           ,[CreatedDate]
           ,[UpdatedBy]
           ,[Currency])
     VALUES
           ('ONE_OFF'
           ,'RETAIL_PRICE'
           ,0
           ,@PayByLinkBundleProductId
           ,0
           ,1
           ,GETUTCDATE()
           ,0
           ,'00000000-0000-0000-0000-000000000000'
           ,GETUTCDATE()
           ,'00000000-0000-0000-0000-000000000000'
           ,@Currency)
    
    --insert into ProductParts
    INSERT INTO ProductParts (ProductId, PartId, Quantity)
    SELECT @PayByLinkBundleProductId, PartId, Quantity from ProductParts
    where ProductId=
        (SELECT TOP 1 Id
        FROM PRODUCTS
        WHERE Code='PAYMENT_GATEWAY_BUNDLE' and Counterparty=@CounterpartyHeader and [Availability]='Live')
    

    DELETE FROM #CounterpartyHeaders WHERE HeaderName=@CounterpartyHeader

END

--mark PAY_BY_LINK product as Obsolete
UPDATE Products SET Availability='Obsolete' where Code='PAY_BY_LINK'

COMMIT;
