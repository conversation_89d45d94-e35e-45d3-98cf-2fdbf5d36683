﻿using Common.Models;
using Common.Models.Apex;
using Common.Models.TerminalDataSets;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProductService.Controllers;

[Route("api/v1/[controller]")]
[ApiController]
public class TerminalDataSetController : ControllerBase
{
    private readonly ITerminalDataSetService _terminalDataSetService;
    private readonly ILogger<TerminalDataSetController> _logger;
    private readonly IApexService _apexService;

    public TerminalDataSetController(ITerminalDataSetService terminalDataSetService, ILogger<TerminalDataSetController> logger, IApexService apexService)
    {
        _terminalDataSetService = terminalDataSetService;
        _logger = logger;
        _apexService = apexService;
    }

    [HttpPost("advancedSearch")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AdvancedSearch([FromBody] TerminalDataSetSearchRequest terminalDataSerRequest)
    {
        var result = await _terminalDataSetService.AdvancedSearchAsync(terminalDataSerRequest);
        return Ok(result);
    }

    //create
    /// <summary>
    /// Creates a new terminal data set.
    /// </summary>
    /// <returns>The newly created terminal data set.</returns>
    /// <response code="200">Returns the newly created terminal data set.</response>
    /// <response code="400">Returns the error.</response> 
    [HttpPost("import")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> Create([FromBody] List<TerminalDataSet> terminalDataSets)
    {
        var createdTerminalData = await _terminalDataSetService.CreateAsync(terminalDataSets);
        return Ok(createdTerminalData);
    }

    [HttpPost("updateTerminalDatasetsTIDAndMID")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> GenerateTIDAndMIDAndAddEditTerminalDataSets([FromBody] TerminalDataSetsRequest terminalDataSets)
    {
        var generatedData = await _terminalDataSetService.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets);
        return Ok(generatedData);
    }

    [HttpPost("updateOrdersTerminalDatasetsTIDAndMID")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> GenerateTIDAndMIDAndAddEditOrderTerminalDataSets([FromBody] TerminalDataSetsMidTidRequest terminalDataSetsMidTid)
    {
        var generatedData = await _terminalDataSetService.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(terminalDataSetsMidTid);
        return Ok(generatedData);
    }

    [HttpPost("updateTerminalDatasetTID")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> GenerateTIDAndAddEditTerminalDataSet([FromBody] TerminalDataSet terminalDataSet)
    {
        var generatedData = await _terminalDataSetService.GenerateTIDAndAddEditTerminalDataSet(terminalDataSet);
        return Ok(generatedData);
    }

    [HttpPost("updateTerminalDataSetMcc")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> AddUpdateTerminalDataSetByMcc([FromBody] TerminalDataSetsMidTidRequest terminalDataSetsRequest)
    {
        var terminals = await _terminalDataSetService.AddUpdateTerminalDataSetMcc(terminalDataSetsRequest);
        return Ok(terminals);
    }

    [HttpPost("updateTerminalDatasetMID")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> GenerateMIDAndAddEditTerminalDataSet([FromBody] TerminalDataSetMidRequest terminalDataSetMidRequest)
    {
        var generatedMID = await _terminalDataSetService.GenerateMIDAndAddEditTerminalDataSet(terminalDataSetMidRequest);
        return Ok(generatedMID);
    }

    /// <summary>
    /// Patch terminal data set.
    /// </summary>
    /// <response code="200">Returns updated terminal data set</response>
    /// <response code="404">Terminal data set with provided id not found</response> 
    /// <response code="404">Returns the error</response> 

    [HttpPatch]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Patch([FromBody] List<TerminalDataSetPatchRequest> terminalDataSetPatchRequest)
    {
        var response = await _terminalDataSetService.PatchAsync(terminalDataSetPatchRequest);

        _logger.LogInformation("Successfully patched terminal data sets");

        return Ok(response);
    }

    /// <summary>
    /// Retrieves the count of available TerminalDataSet by acquiring ledger.
    /// </summary>
    /// <param name="acquiringLedger"></param>
    /// <returns></returns>
    [HttpGet("count/{acquiringLedger}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetAvailableTerminalDataSetCount(string acquiringLedger)
    {
        var response = await _terminalDataSetService.GetAvailableTerminalDataSetCountAsync(acquiringLedger);

        return Ok(response);
    }

    /// <summary>
    /// Retrieves a list with fields that are already in the DB
    /// </summary>
    /// <param name="terminalDataSets"></param>
    /// <returns></returns>
    [HttpPost("requiredFields")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetValidationRequiredFields([FromBody] List<TerminalDataSet> terminalDataSets)
    {
        var result = await _terminalDataSetService.GetValidationRequiredFieldsAsync(terminalDataSets);
        return Ok(result);
    }

    /// <summary>
    /// Retrieves a list with fields that are already in the DB
    /// </summary>
    /// <param name="terminalDataSets"></param>
    /// <returns></returns>
    [HttpPost("orderMigration/requiredFields")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetOrderMigrationValidationRequiredFields([FromBody] List<TerminalDataSet> terminalDataSets)
    {
        return Ok(await _terminalDataSetService.GetOrderMigrationValidationRequiredFieldsAsync(terminalDataSets));
    }

    /// <summary>
    /// Retrieves TerminalDataSet by Id.
    /// </summary>
    /// <param name="terminalDataSetId"></param>
    /// <returns></returns>
    [HttpGet("{terminalDataSetId}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetTerminalDataByIdAsync(Guid terminalDataSetId)
    {
        var response = await _terminalDataSetService.GetTerminalDataByIdAsync(terminalDataSetId);
        return Ok(response);
    }

    /// <summary>
    /// Get terminals by the product instances' ids associated to them.
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("getByProductInstances")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetByProductInstances([FromBody] IdsRequest request)
    {
        var result = await _terminalDataSetService.GetTerminalDataByProductInstanceId(request.Ids);
        return Ok(result);
    }

    /// <summary>
    /// Get New TID Sequence number 
    /// </summary>
    /// <returns> Next TID sequence value</returns>
    [HttpGet("GetNewTIDSequence")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetNewTIDSequence()
    {
        var result = await _terminalDataSetService.GetNewTIDSequence();
        return Ok(result);
    }

    /// <summary>
    /// Get TerminalSet from Apex based on mId.
    /// </summary>
    /// <param name="mId"></param>
    /// <returns></returns>
    [HttpPost("getMerchantIdTerminalId/{mId}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetMerchantIdTerminalId(string mId)
    {
        var response = await _apexService.GetMerchantInquiryAsync(new MerchantInquiryRequest { merchantId = mId });
        return Ok(response);
    }

    [HttpPost("GetTidByOrderId")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetTidByOrderId([FromBody] List<string?> orderNumber)
    {
        var response = await _terminalDataSetService.GetTidByOrderId(orderNumber);
        return Ok(response);
    }
}
