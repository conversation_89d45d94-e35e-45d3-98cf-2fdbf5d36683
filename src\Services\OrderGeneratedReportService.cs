﻿using Common.Models.OrderGeneratedReport;
using Common.Repositories;
using Common.Services;
using System.Threading.Tasks;

namespace Services;

public class OrderGeneratedReportService : IOrderGeneratedReportService
{
    private readonly IOrderGeneratedReportRepository orderReportRepository;

    public OrderGeneratedReportService(IOrderGeneratedReportRepository orderReportRepository)
    {
        this.orderReportRepository = orderReportRepository;
    }

    public async Task AddOrdersReport(OrdersReportRequest reportOrdersRequest)
    {
        await orderReportRepository.AddOrdersReport(reportOrdersRequest);
    }
}
