﻿using Common.Entities;
using Common.Models.CommissionFees;
using Common.Models.NonTransactionalFees;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Repositories;
public interface INonTransactionalFeesRepository : IRepository<Guid, NonTransactionalFeesEntity>
{
    Task<NonTransactionalFeesEntity?> GetByIdAsync(Guid id);
    Task<GetNonTransactionalFeesListResponse> GetNonTransactionalFeesList(GetNonTransactionalFeesListRequest request);
    Task<List<BasicNonTransactionalFeeInfo>> GetNonTransactionalFeesNamesAsync();
}
