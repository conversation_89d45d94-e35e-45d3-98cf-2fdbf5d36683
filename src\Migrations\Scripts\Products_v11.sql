﻿EXEC NewProductVersion_v2 'GO_A920', 4, 'GO_A920', 5, 1, 'GEIDEA_SAUDI'
EXEC NewProductVersion_v2 'GO_SMART', 5, 'GO_SMART', 6, 1, 'GEIDEA_SAUDI'
EXEC NewProductVersion_v2 'GO_SMART', 0, 'GO_SMART', 1, 1, 'GEIDEA_EGYPT'
EXEC NewProductVersion_v2 'PRO_SMART', 6, 'PRO_SMART', 7, 1,'GEIDEA_SAUDI'
EXEC NewProductVersion_v2 'GO_AIR', 4, 'GO_AIR', 5, 1,'GEIDEA_SAUDI'
EXEC NewProductVersion_v2 'GO_MPOS', 4, 'GO_MPOS', 5, 1,'GEIDEA_SAUDI'

DELETE ProductPart FROM [PRODUCTS].[dbo].[ProductParts] ProductPart
INNER JOIN [PRODUCTS].[dbo].[Products] Parts on ProductPart.PartId = parts.Id and Parts.Code= 'GEIDEA_GO_APP'
INNER JOIN [PRODUCTS].[dbo].[Products] Products on ProductPart.ProductId = Products.Id and Products.Type = 'BUNDLE' and Products.Availability = 'Live'