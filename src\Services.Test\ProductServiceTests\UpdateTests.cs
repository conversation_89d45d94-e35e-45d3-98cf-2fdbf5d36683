﻿using AutoMapper;
using Common;
using Common.Entities;
using Common.Models;
using Common.Options;
using DataAccess;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NSubstitute;
using ProductService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Xunit;

namespace Services.Test.ProductServiceTests;

public class UpdateTests
{
    private readonly Mock<ILogger<ProductService>> logger = new Mock<ILogger<ProductService>>();
    private readonly Mock<ILogger<ProductInstanceRepository>> repoLogger = new Mock<ILogger<ProductInstanceRepository>>();
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly ProductRepository productRepository;
    private readonly ProductService productService;
    private readonly IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;
    private readonly ICounterpartyProvider counterpartyProvider;

    private readonly ProductEntity productEntity;
    private readonly ProductEntity productEntity2;
    private readonly ProductInstanceEntity productInstance;

    public UpdateTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
           .UseInMemoryDatabase(databaseName: "ProductPatchTests" + Guid.NewGuid().ToString())
           .Options;

        var context = new DataContext(options, new CounterpartyProvider());

        // Initialize productEntity with a ProductCategory
        productEntity = new ProductEntity
        {
            Availability = Constants.Availability.Live,
            SalesChannel = Constants.SalesChannel.All,
            Code = "TEST_VALID",
            Description = "Test description",
            Type = "BUNDLE",
            Version = 0,
            ProductCategories = new List<ProductCategoriesEntity>
            {
                new ProductCategoriesEntity { CategoryId = Guid.NewGuid() }
            }
        };

        productEntity2 = new ProductEntity
        {
            Availability = Constants.Availability.Live,
            SalesChannel = Constants.SalesChannel.All,
            Code = "TEST_VALID",
            Description = "Test description",
            Type = "BUNDLE",
            Version = 0,
            ProductCategories = new List<ProductCategoriesEntity>
            {
                new ProductCategoriesEntity { CategoryId = Guid.NewGuid() }
            },
            Images = new List<ProductImage>
            {
                new ProductImage { ImageId = Guid.NewGuid(), Language = "en", DisplayOrder = 1 },
                new ProductImage { ImageId = Guid.NewGuid(), Language = "ar", DisplayOrder = 2 }
            }
        };

        productInstance = new ProductInstanceEntity
        {
            Product = new ProductEntity
            {
                Code = "TEST_USED"
            }
        };

        context.Products.Add(productEntity);
        context.Products.Add(productEntity2);

        context.ProductInstances.Add(productInstance);
        context.SaveChanges();

        softposEgyptFeatureToggle = Substitute.For<IOptions<SoftposEgyptFeatureToggle>>();
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = true });
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();

        productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);
        var categoryRepository = new CategoryRepository(context, httpContext.Object);
        var productInstanceRepo = new ProductInstanceRepository(context, httpContext.Object, repoLogger.Object, mapper);
        productService = new ProductService(logger.Object, productRepository, mapper, categoryRepository, productInstanceRepo);
    }

    [Fact]
    public async Task UpdateExistingProduct_Success()
    {
        // Arrange
        var existingProductId = productEntity.Id;
        var updateRequest = new ProductRequest
        {
            Version = 0,
            Flow = "Normal",
            Code = "UPDATED_TEST",
            Availability = "Live",
            Description = "Updated description",
            Type = Constants.ProductType.Bundle,
            SalesChannel = Constants.SalesChannel.Shop,
            CategoryId = productEntity.ProductCategories.First().CategoryId // Assuming it has a valid category ID
        };

        // Act
        await productService.UpdateAsync(existingProductId, updateRequest);

        // Assert
        var updatedProduct = await productRepository.GetByIdAsync(existingProductId);
        updatedProduct.Should().NotBeNull();
        updatedProduct.Code.Should().Be(updateRequest.Code);
        updatedProduct.Description.Should().Be(updateRequest.Description);
        updatedProduct.Type.Should().Be(updateRequest.Type);
        updatedProduct.SalesChannel.Should().Be(updateRequest.SalesChannel);
    }


    //[Fact]
    //public async Task UpdateProductImages_Success()
    //{
    //    // Arrange
    //    var existingProductId = productEntity2.Id;
    //    var newImages = new List<ImageData>
    //    {
    //        new ImageData { ImageId = Guid.NewGuid(), Language = "en", DisplayOrder = 1 },
    //        new ImageData { ImageId = Guid.NewGuid(), Language = "ar", DisplayOrder = 2 }
    //    };

    //    var updateRequest = new ProductRequest
    //    {
    //        Version = 0,
    //        Flow = "Normal",
    //        Code = "UPDATED_TEST2",
    //        Availability = "Live",
    //        Description = "Updated description",
    //        Type = Constants.ProductType.Bundle,
    //        SalesChannel = Constants.SalesChannel.Shop,
    //        Images = newImages,
    //        CategoryId = productEntity2.ProductCategories.First().CategoryId // Assuming it has a valid category ID
    //    };

    //    // Act
    //    await productService.UpdateAsync(existingProductId, updateRequest);

    //    // Assert
    //    var updatedProduct = await productRepository.GetByIdAsync(existingProductId);
    //    updatedProduct.Should().NotBeNull();
    //    updatedProduct.Images.Should().BeEquivalentTo(newImages);
    //}

    [Fact]
    public async Task UpdateProduct_PartialSuccess()
    {
        // Arrange
        var existingProductId = productEntity.Id;
        var partialUpdateRequest = new ProductRequest
        {
            Flow = "Normal",
            Code = "UPDATED_PARTIAL",
            Availability = "Live",
            Type = productEntity.Type,
            SalesChannel = productEntity.SalesChannel,
            CategoryId = productEntity.ProductCategories.First().CategoryId // Assuming it has a valid category ID
        };

        // Act
        await productService.UpdateAsync(existingProductId, partialUpdateRequest);

        // Assert
        var updatedProduct = await productRepository.GetByIdAsync(existingProductId);
        updatedProduct.Code.Should().Be(partialUpdateRequest.Code);
        updatedProduct.Description.Should().Be(productEntity.Description); // Ensure unchanged
        updatedProduct.Type.Should().Be(productEntity.Type); // Ensure unchanged
    }

    [Fact]
    public async Task UpdateProduct_InvalidCategoryId_ThrowsException()
    {
        // Arrange
        var existingProductId = productEntity.Id;
        var invalidCategoryId = Guid.NewGuid(); // Generate a new GUID for an invalid category ID
        var updateRequest = new ProductRequest
        {
            Version = 0,
            Flow = "Normal",
            Code = "UPDATED_TEST",
            Availability = "Live",
            Description = "Updated description",
            Type = Constants.ProductType.Bundle,
            SalesChannel = Constants.SalesChannel.Shop,
            CategoryId = invalidCategoryId // Invalid category ID
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ServiceException>(() => productService.UpdateAsync(existingProductId, updateRequest));
        exception.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        // exception.Message.Should().Contain("Invalid category ID: " + invalidCategoryId.ToString().ToUpper());
    }
    [Fact]
    public async Task UpdateProduct_DuplicateCode_ThrowsException()
    {
        // Arrange
        var existingProductId = productEntity.Id;
        var duplicateProduct = new ProductEntity
        {
            Availability = Constants.Availability.Live,
            SalesChannel = Constants.SalesChannel.All,
            Code = "DUPLICATE_CODE",
            Description = "Duplicate description",
            Type = "BUNDLE",
            Version = 0
        };
        productRepository.Add(duplicateProduct);
        await productRepository.SaveChangesAsync();

        var updateRequest = new ProductRequest
        {
            Version = 0,
            Flow = "Normal",
            Code = "DUPLICATE_CODE",
            Availability = "Live",
            Description = "Updated description",
            Type = Constants.ProductType.Bundle,
            SalesChannel = Constants.SalesChannel.Shop,
            CategoryId = productEntity.ProductCategories.First().CategoryId // Assuming it has a valid category ID
        };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ServiceException>(() => productService.UpdateAsync(existingProductId, updateRequest));
        exception.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }
}
