﻿using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Common.Models.ProductInstance;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace ProductService.Controllers;

[Route("api/v1/[controller]")]
[ApiController]
public class CertificateController : ControllerBase
{
    private readonly ICertificateService certificateService;

    public CertificateController(ICertificateService certificateService)
    {
        this.certificateService = certificateService;
    }

    /// <summary>
    /// Generate CSR.
    /// </summary>
    /// <returns>The newly generated CSR.</returns>
    /// <response code="200">Returns the newly generated CSR.</response>
    /// <response code="400">Returns the error.</response>

    [HttpPost("csr")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/x-pem-file")]
    public async Task<IActionResult> GenerateCsr([FromBody] GenerateCsrRequest request)
    {
        request.ValidateAndThrow();

        var generatedCsr = await certificateService.CreateCsrAndPrivateKey(request);

        var csrBytes = Encoding.UTF8.GetBytes(generatedCsr);
        var memoryStream = new MemoryStream(csrBytes);
        return File(memoryStream, "application/pkcs10", "request.csr");
    }

    /// <summary>
    /// Uploads signed certificate.
    /// </summary>
    /// <returns>The updated product instance.</returns>
    /// <response code="200">Returns the updated product instance.</response>
    /// <response code="400">Returns the error.</response>
    /// <response code="404">Returns product instance with provided id not found.</response>
    [HttpPost("upload")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Upload(Guid productInstanceId, IFormFile file)
    {
        await using var cerStream = new MemoryStream();
        await file.OpenReadStream().CopyToAsync(cerStream);
        var signedCertificate = cerStream.ToArray();

        var productInstance = await certificateService.UploadSignedCertificate(productInstanceId, signedCertificate);

        return Ok(productInstance);
    }


    [HttpPost("SamsungCsr")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/x-pem-file")]
    public async Task<IActionResult> GenerateSamsungCsr([FromBody] GenerateCsrRequest request)
    {
        request.ValidateAndThrow();

        var generatedCsr = await certificateService.CreateSamsungCsrAndPrivateKey(request);

        var csrBytes = Encoding.UTF8.GetBytes(generatedCsr);
        var memoryStream = new MemoryStream(csrBytes);
        return File(memoryStream, "application/pkcs10", "request.csr");
    }
}
