﻿using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.Products;
[Migration(2025_03_05_1157)]
public class AddPendingComissionPriceTable : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "AddPendingComissionPriceTable.sql")
            );
    }
}
[Migration(2025_03_17_1149)]
public class AlterPendingComissionPrice : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "AlterPendingComissionPrice.sql")
            );
    }
}
