﻿using System;

namespace Common.Models.TerminalDataSets;

public class TerminalDataSetSearchResponse
{
    public Guid? Id { get; set; }
    public string? AcquiringLedger { get; set; }
    public string? Mid { get; set; }
    public string? Tid { get; set; }
    public string? FullTid { get; set; }
    public string? MCC { get; set; }
    public string? Trsm { get; set; }
    public string? OrderNumber { get; set; }
    public DateTime? ConfigDate { get; set; }
    public string? Availability { get; set; }
    public string? Counterparty { get; set; }
    public string? CreatedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public string? UpdatedBy { get; set; }
    public DateTime? UpdatedDate { get; set; }
    public Guid? StoreId { get; set; }
    public Guid? ProductInstanceId { get; set; }
    public string? VendorName { get; set; }
    public Guid? VendorId { get; set; }
}
