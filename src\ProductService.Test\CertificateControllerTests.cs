using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Common.Models.ProductInstance;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using ProductService.Controllers;
using Xunit;

namespace ProductService.Test;

public class CertificateControllerTests
{
    private readonly Mock<ICertificateService> service;
    private readonly CertificateController controller;
    private static readonly Guid ProductInstanceId = Guid.NewGuid();

    private readonly GenerateCsrRequest csrRequest = new GenerateCsrRequest
    {
        ProductInstanceId = ProductInstanceId,
        CommonName = "Name",
        Country = "US",
        Email = "<EMAIL>",
        Location = "Location",
        Organization = "Org",
        OrganizationalUnit = "OU",
        State = "State"
    };

    private readonly ProductInstanceResponse productInstanceResponse = new ProductInstanceResponse
    {
        ProductInstanceId = Guid.NewGuid(),
        Data = "Sample Data"
    };

    public CertificateControllerTests()
    {
        service = new Mock<ICertificateService>();

        service.Setup(x => x.CreateCsrAndPrivateKey(It.IsAny<GenerateCsrRequest>()))
            .Returns(Task.FromResult("Sample Csr"));
        service.Setup(x => x.CreateSamsungCsrAndPrivateKey(It.IsAny<GenerateCsrRequest>()))
           .Returns(Task.FromResult("Sample Csr"));
        service.Setup(x => x.UploadSignedCertificate(It.IsAny<Guid>(), It.IsAny<byte[]>()))
            .Returns(Task.FromResult(productInstanceResponse));

        controller = new CertificateController(service.Object);
    }

    [Fact]
    public async Task GenerateCsrShouldReturnTheGeneratedCsr()
    {
        var response = await controller.GenerateCsr(csrRequest);

        var fileResult = response as FileResult;
        Assert.NotNull(fileResult);
        Assert.Equal("application/pkcs10", fileResult.ContentType);
        Assert.Equal("request.csr", fileResult.FileDownloadName);

        service.Verify(x => x.CreateCsrAndPrivateKey(csrRequest), Times.Once);
    }

    [Fact]
    public async Task UploadShouldUploadSignedCertificate()
    {
        var signedCertificate = Encoding.UTF8.GetBytes("Example signed certificate");
        await using var memoryStream = new MemoryStream(signedCertificate);
        var formFile = new FormFile(memoryStream, 0, memoryStream.Length, "certificate.cer", "certificate.cer");

        var response = await controller.Upload(ProductInstanceId, formFile);

        var okResult = response as OkObjectResult;
        Assert.NotNull(okResult);

        var actualProductInstanceResponse = okResult.Value as ProductInstanceResponse;
        Assert.NotNull(actualProductInstanceResponse);
        Assert.Equal(productInstanceResponse, actualProductInstanceResponse);

        service.Verify(x => x.UploadSignedCertificate(ProductInstanceId, signedCertificate), Times.Once);
    }

    [Fact]
    public async Task GenerateCsrShouldReturnTheGenerateSamsungCsr()
    {
        var response = await controller.GenerateSamsungCsr(csrRequest);

        var fileResult = response as FileResult;
        Assert.NotNull(fileResult);
        Assert.Equal("application/pkcs10", fileResult.ContentType);
        Assert.Equal("request.csr", fileResult.FileDownloadName);

        service.Verify(x => x.CreateSamsungCsrAndPrivateKey(csrRequest), Times.Once);
    }
}
