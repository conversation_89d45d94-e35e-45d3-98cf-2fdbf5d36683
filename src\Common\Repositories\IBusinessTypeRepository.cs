﻿using Common.Entities;
using Common.Models;
using Common.Models.businessType;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Repositories;
public interface IBusinessTypeRepository : IRepository<Guid, BusinessTypeEntity>
{
    Task<BusinessTypeEntity?> GetByIdAsync(Guid id);
    Task<GetBusinessTypesListResponse> GetBusinessTypesList(GetBusinessTypesListRequest request);
    Task<BusinessTypeDetailsResponse?> GetBusinessTypeDetails(Guid Id);
    Task<List<BasicBusinessTypesInfo>> GetBusinessTypesNamesAsync();
}
