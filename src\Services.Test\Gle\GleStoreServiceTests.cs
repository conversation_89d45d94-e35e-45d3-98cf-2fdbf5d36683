﻿using System;
using System.Threading.Tasks;
using AutoMapper;
using Common;
using Common.Entities.Gle;
using Common.Models.Gle;
using Common.Repositories.Gle;
using Common.Services.Gle;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;
using Services.Gle;
using Services.Test.TestData;

namespace Services.Test.Gle;

public class GleStoreServiceTests
{
    private readonly ILogger<GleStoreService> logger = Substitute.For<ILogger<GleStoreService>>();
    private IMapper mapper;
    private readonly IGleStoreRepository gleStoreRepository = Substitute.For<IGleStoreRepository>();
    private IGleStoreService gleStoreService;

    [SetUp]
    public void SetUp()
    {
        mapper = new Mapper(new MapperConfiguration(x => x.AddMaps(typeof(MappingProfileHelper))));
        gleStoreService = new GleStoreService(logger, mapper, gleStoreRepository);

        gleStoreRepository.GetGleStoreByStoreIdAsync(Arg.Any<Guid>()).Returns(new GleStoreEntity());

        gleStoreRepository.ClearReceivedCalls();
    }

    [Test]
    public async Task AddGleStoreAsync_WhenRequestIsValid_ShouldCallRepository()
    {
        //Arrange
        var createRequest = new GleStoreRequest()
        {
            GleRegistrationStatus = Constants.GleRegistrationStatus.Failed,
            GleRegistrationResponse = "Ledger reference is missing!",
            ReferenceMmsId = "3472001",
            GleMerchantId = Guid.NewGuid(),
            StoreId = Guid.NewGuid()
        };

        //Act and assert
        await gleStoreService.Invoking(x => x.AddGleStoreAsync(createRequest))
            .Should().NotThrowAsync<ValidationException>();

        await gleStoreRepository.Received(1)
            .AddGle(Arg.Is<GleStoreEntity>(x => x.StoreId == createRequest.StoreId));
    }

    [Test]
    [TestCase("Failed", null)]
    [TestCase(null, null)]
    public async Task AddGleStoreAsync_WhenRequestIsInvalid_ShouldThrowValidationException(string status, string response)
    {
        //Arrange
        var createRequest = new GleStoreRequest()
        {
            GleRegistrationStatus = status,
            GleRegistrationResponse = response,
            GleMerchantId = Guid.NewGuid(),
            StoreId = Guid.NewGuid()
        };

        //Act and assert
        await gleStoreService.Invoking(x => x.AddGleStoreAsync(createRequest))
            .Should().ThrowAsync<ValidationException>();

        await gleStoreRepository.DidNotReceive()
            .AddGle(Arg.Is<GleStoreEntity>(x => x.StoreId == createRequest.StoreId));
    }

    [Test]
    [TestCase("GleRegistrationStatus", "Success")]
    [TestCase("GleRegistrationStatus", "Failed")]
    [TestCase("GleUserId", "357629293")]
    [TestCase("GleLoginId", "10000123")]
    [TestCase("GleLoginId2", "100001")]
    [TestCase("ParentGleUserId", "400091")]
    [TestCase("GleRegistrationResponse", "This is a test")]
    [TestCase("ReferenceMmsId", "100983")]
    public async Task UpdateGleStoreAsync_WhenRequestIsValid_ShouldCallRepository(string path, string value)
    {
        //Arrange
        var gleStoreId = Guid.NewGuid();
        var updateDocument = new JsonPatchDocument<UpdateGleStoreRequest>();
        updateDocument.Operations.Add(new Operation<UpdateGleStoreRequest>
        {
            op = "replace",
            path = path,
            value = value
        });

        //Act and assert
        await gleStoreService.Invoking(x => x.UpdateGleStoreAsync(gleStoreId, updateDocument))
            .Should().NotThrowAsync<ValidationException>();

        await gleStoreRepository.Received(1)
            .UpdateGle(gleStoreId, Arg.Any<JsonPatchDocument<GleStoreEntity>>());
    }

    [Test]
    [TestCase("GleRegistrationStatus", "Pending")]
    [TestCase("GleRegistrationStatus", "Fail")]
    public async Task UpdateGleStoreAsync_WhenRequestIsInvalid_ShouldThrowValidationException(string path, string value)
    {
        //Arrange
        var gleStoreId = Guid.NewGuid();
        var updateDocument = new JsonPatchDocument<UpdateGleStoreRequest>();
        updateDocument.Operations.Add(new Operation<UpdateGleStoreRequest>
        {
            op = "replace",
            path = path,
            value = value
        });

        //Act and assert
        await gleStoreService.Invoking(x => x.UpdateGleStoreAsync(gleStoreId, updateDocument))
            .Should().ThrowAsync<ValidationException>();

        await gleStoreRepository.DidNotReceive()
            .UpdateGle(gleStoreId, Arg.Any<JsonPatchDocument<GleStoreEntity>>());
    }

    [Test]
    public async Task GetGleStoreByStoreIdAsync_WhenRequestIsValid_ShouldCallRepository()
    {
        //Arrange
        var storeId = Guid.NewGuid();

        //Act
        var result = await gleStoreService.GetGleStoreByStoreIdAsync(storeId);

        //Assert
        Assert.NotNull(result);
        await gleStoreRepository.Received(1).GetGleStoreByStoreIdAsync(storeId);
    }

    [Test]
    public async Task GetGleStoreByStoreIdAsync_WhenRequestIsInvalid_ShouldThrowServiceException()
    {
        //Act and assert
        await gleStoreService.Invoking(x => x.GetGleStoreByStoreIdAsync(Guid.Empty))
            .Should().ThrowAsync<ServiceException>();

        await gleStoreRepository.DidNotReceive().GetGleStoreByStoreIdAsync(Arg.Any<Guid>());
    }
}