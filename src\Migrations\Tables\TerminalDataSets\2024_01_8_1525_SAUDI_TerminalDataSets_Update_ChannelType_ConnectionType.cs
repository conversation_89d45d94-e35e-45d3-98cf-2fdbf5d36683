﻿using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.TerminalDataSets;

[Migration(2024_01_08_1525)]
public class SAUDI_TerminalDataSets_Update_ChannelType_ConnectionType : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("<PERSON><PERSON><PERSON>", "SAUDI_TerminalDataSets_Update_ChannelType_ConnectionType.sql"));
    }
}
