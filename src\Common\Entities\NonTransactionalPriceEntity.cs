﻿using Common.Enums.ProductCommisssionPrices;
using Common.Enums.UnitPrice;
using Geidea.Utils.DataAccess.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Entities;
public class NonTransactionalPriceEntity : AuditableEntity<Guid>
{
    public Guid ProductId { get; set; }
    public ProductEntity Product { get; set; } = null!;
    public Guid MccId { get; set; }
    public Mcc Mcc { get; set; } = null!;
    public Guid BusinessTypeId { get; set; }
    public BusinessTypeEntity BusinessType { get; set; } = null!;
    public Guid NonTransFeeId { get; set; }
    public NonTransactionalFeesEntity NonTransactionalFees { get; set; } = null!;
    public decimal FeeValue { get; set; }
    public FeeType FeeType { get; set; }
    public BillingType BillingType { get; set; }
    public PriceBillingFrequency BillingFrequency { get; set; }
}
