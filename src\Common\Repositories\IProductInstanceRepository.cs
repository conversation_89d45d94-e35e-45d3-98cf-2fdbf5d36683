﻿using Common.Entities;
using Common.Models;
using Common.Models.ProductInstance;
using Geidea.ProductService.Models;
using Geidea.Utils.DataAccess.Repositories;
using Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Repositories;

public interface IProductInstanceRepository : IRepository<Guid, ProductInstanceEntity>
{
    Task<List<ProductInstanceEntity>> FindAsync(FindProductInstanceRequest findProductInstanceRequest, bool includeDeleted = false, bool track = false);
    Task<List<Guid?>> FindGatewayCompanyIds(FindGatewayCompanyIds request, bool includeDeleted = false, bool track = false);
    Task DeleteAsync(Guid productInstanceId);
    Task<ProductInstanceEntity> GetByIdAsync(Guid productInstanceId, bool track = false);
    Task<ProductInstanceEntity?> GetByIdWithProductAndChildrenAsync(Guid productInstanceId);
    Task<List<ProductInstanceEntity>> FindByIdsAsync(IdsRequest request, bool track = false);
    Task<int> CountAsync(CountProductInstanceRequest request, bool includeDeleted = false);
    Task<List<ProductInstanceEntity>> GetProductInstancesForStore(Guid storeId);
    Task<List<StoreProductInstanceResponse>> GetProductInstancesForMultipleStores(Guid[] storeIds);
    Task<ProductInstanceEntity[]> SearchProductInstanceBaseAsync(SearchProductInstanceBaseRequest request);
    Task<SearchGatewayConfigurationsResponse> SearchGatewayConfigurations(SearchGatewayConfigurationsRequest request);
    Task<bool> MeezaMerchantIdExists(string meezaMerchantId);
    Task<bool> IsSubscribedToPaymentGateway(Guid merchantId);
    Task CleanExpiredMPGSHistory(DateTimeOffset ExpirationDate);
    Task<List<ProductInstanceEntity>> GetProductInstanceByCompanyIdAsync(Guid companyId, bool track = false);
    Task SendCompanyIdsToStoredProcedure(IList<SubMerchantMigrationInfoProductInstance> request);
    Task<List<string>> SelectByQueryString(string queryStatemet);
    Task<bool> UpdateByQueryString(string queryStatemet);
}
