{"ConfigService": {"Url": "http://uae-dev-config.uae-dev.gd-azure-dev.net", "VaultSecretsPath": "/usr/share/secrets/secrets.json", "Keys": ["ConfigService:VaultSecretsPath", "Serilog:MinimumLevel", "Migrations:IsAutoMigrationEnabled", "ServiceExceptions:UseSlim", "ApplePay:CertificatePassword", "Default:RabbitMqConfig:*", "ProductDatabase:ConnectionString", "ProductDatabase:MigrationConnectionString", "Meeza:MerchantPrefix", "Default:HealthChecks:*", "Currency:*", "MpgsConfig:CleanHistoryPeriod", "SoftposEgyptFeatureToggle:EnableSoftposEgypt", "ApexSettings:*", "ExternalApiSettings:*"]}, "Meeza": {"MerchantPrefix": "675"}, "Application": {"Name": "ProductService", "Version": "0.1.0"}, "ProductDatabase": {"ConnectionString": "Server=tcp:***********,1435;Initial Catalog=PRODUCTS;Persist Security Info=False;MultipleActiveResultSets=False; Encrypt=False; TrustServerCertificate=True; Connection Timeout=30;User ID=engineering_team; Password=**********************;", "MigrationConnectionString": "Server=tcp:***********,1435;Initial Catalog=PRODUCTS; Persist Security Info=False; User ID=engineering_team; Password=**********************; MultipleActiveResultSets=False; Encrypt=False; TrustServerCertificate=True; Connection Timeout=30;", "MigrationAssembly": "Migrations"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Error", "System": "Error"}}, "Using": ["Serilog.Sinks.Console"], "Enrich": ["FromLogContext", "WithMachineName", "WithEnvironmentUserName", "WithProcessId"], "Properties": {"ApplicationName": "ProductService", "ProcessName": "ProductService"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"}}]}, "Migrations": {"IsAutoMigrationEnabled": true}, "ServiceExceptions": {"UseSlim": false}, "AllowedHosts": "*", "Default": {"RabbitMqConfig": {"HostName": "*************", "Port": "30567", "VirtualHost": "/", "UserName": "upgw-dev", "Password": "JU#dY6GR+py*8>LA", "SslEnabled": false}}, "Currency": {"SupportedCurrencies": ["SAR", "EGP", "AED", "USD", "EUR", "GBP", "BHD", "KWD", "OMR", "QAR"]}, "MpgsConfig": {"CleanHistoryPeriod": "-30.00:00:00"}, "SoftposEgyptFeatureToggle": {"EnableSoftposEgypt": true}, "ApexSettings": {"clientId": "5RbpkCihqsZLaECt2owurU1fxBo3UUzbcarVEllGhhE", "clientsecret": "SdwipsOm5gerDQ2YXHrbUgMayIjTvfWdQOUtHyZDBIg"}, "ExternalApiSettings": {"BaseUrl": "https://***********:8443/api"}}