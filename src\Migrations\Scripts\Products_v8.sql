﻿EXEC NewProductVersion_v2 'PRO_SMART', 3, 'PRO_SMART', 4, 1

DECLARE @ProSmart UNIQUEIDENTIFIER
SELECT TOP 1 @ProSmart = ID FROM Products where Code='PRO_SMART' and Version = 4

DECLARE @Ids TABLE(ID UNIQUEIDENTIFIER)
DECLARE @GeideaPos UNIQUEIDENTIFIER

INSERT INTO PRODUCTS(Availability, Flow, SalesChannel, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom, Counterparty) OUTPUT inserted.Id INTO @Ids
VALUES('Live', 'Normal', 'All', 'GEIDEA_POS', 'SERVICES', 'n/a', GETUTCDATE(), GETUTCDATE(), 'GEIDEA_SAUDI')

SELECT TOP 1 @GeideaPos = ID FROM @Ids 
DELETE FROM @Ids

INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmart, @GeideaPos)