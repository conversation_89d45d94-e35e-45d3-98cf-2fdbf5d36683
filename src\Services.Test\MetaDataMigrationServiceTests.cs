using Common.Entities;
using Common.Repositories;
using Microsoft.Extensions.Logging;
using Moq;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using Xunit;

namespace Services.Test;

public class MetaDataMigrationServiceTests
{
    private readonly Mock<IMetaDataMigrationFilesRepository> metaDataMigrationFilesRepositoryMock;
    private readonly Mock<IProductInstanceRepository> productInstanceRepositoryMock;
    private readonly MetaDataMigrationService metaDataMigrationService;
    public MetaDataMigrationServiceTests()
    {
        metaDataMigrationFilesRepositoryMock = new Mock<IMetaDataMigrationFilesRepository>();
        productInstanceRepositoryMock = new Mock<IProductInstanceRepository>();

        metaDataMigrationService = new MetaDataMigrationService(
            new Mock<ILogger<MetaDataMigrationService>>().Object,
            metaDataMigrationFilesRepositoryMock.Object,
            productInstanceRepositoryMock.Object);
    }

    [Fact]
    public async Task MigrateMetaDataScripts_ShouldHandleEmptyScriptsList()
    {
        metaDataMigrationFilesRepositoryMock.Setup(repo => repo.GetAllMigrationFiles())
            .ReturnsAsync(new List<MetaDataMigrationFilesEntity>());

        await metaDataMigrationService.MigrateMetaDataScripts();

        metaDataMigrationFilesRepositoryMock.Verify(repo => repo.UpdateMigrationFile(It.IsAny<Guid>(), It.IsAny<MetaDataMigrationFilesEntity>()), Times.AtLeastOnce);

    }

    [Fact]
    public async Task MigrateMetaDataScripts_ShouldHandleExceptionInMigrateMethod()
    {
        metaDataMigrationFilesRepositoryMock.Setup(repo => repo.GetAllMigrationFiles())
            .ReturnsAsync(new List<MetaDataMigrationFilesEntity> { new MetaDataMigrationFilesEntity() });

        productInstanceRepositoryMock.Setup(repo => repo.SelectByQueryString(It.IsAny<string>()))
            .ReturnsAsync(new List<string> { "1", "2" });

        productInstanceRepositoryMock.Setup(repo => repo.UpdateByQueryString(It.IsAny<string>()))
            .ThrowsAsync(new Exception("Simulated exception in UpdateByQueryString"));

        await metaDataMigrationService.MigrateMetaDataScripts();

        metaDataMigrationFilesRepositoryMock.Verify(repo => repo.UpdateMigrationFile(It.IsAny<Guid>(), It.IsAny<MetaDataMigrationFilesEntity>()), Times.Never);
        productInstanceRepositoryMock.Verify(repo => repo.SelectByQueryString(It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task MigrateMetaDataScripts_ShouldCallUpdateScriptInDataBase()
    {
        metaDataMigrationFilesRepositoryMock.Setup(repo => repo.GetAllMigrationFiles())
            .ReturnsAsync(new List<MetaDataMigrationFilesEntity>());

        await metaDataMigrationService.MigrateMetaDataScripts();

        metaDataMigrationFilesRepositoryMock.Verify(repo => repo.UpdateMigrationFile(It.IsAny<Guid>(), It.IsAny<MetaDataMigrationFilesEntity>()), Times.Once);
    }
}
