﻿using System;
using System.Collections.Generic;

namespace Geidea.ProductService.Models;
public class MetaDataMigrationFiles
{
    public Guid? Id { get; set; }
    public string? MigrationFileName { get; set; }
    public string? UpdateQuery { get; set; }
    public string? SelectQuery { get; set; }
    public string Status { get; set; } = string.Empty;
    public List<string>? FailedIds { get; set; }
    public DateTime CreatedDate { get; set; }
}

public static class MetaDataMigrationFilesList
{
    /// <summary>
    /// Samples of script files
    /// new MetaDataMigrationFiles
    ///{
    ///    MigrationFileName = "test1",
    ///    UpdateQuery = "UPDATE pi SET Metadata = JSON_MODIFY(pi.Metadata, '$.TestManualMigration', 'true') FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE p.Type = 'GWAY' AND ISJSON(pi.Metadata) = 1 AND Counterparty = 'GEIDEA_EGYPT' ",
    ///    SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE p.Type = 'GWAY' AND ISJSON(pi.Metadata) = 1 AND Counterparty = 'GEIDEA_EGYPT' ",
    ///},
    ///new MetaDataMigrationFiles
    ///{
    ///    MigrationFileName = "test2",
    ///    UpdateQuery = "UPDATE [PRODUCTS].[dbo].[ProductInstances] SET [Metadata] = [dbo].[TestManualMigrationList](Metadata, pi.Id) FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0 AND Counterparty = 'GEIDEA_EGYPT' ",
    ///    SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0 AND Counterparty = 'GEIDEA_EGYPT' ",
    ///}
    /// </summary>
    /// <returns></returns>
    public static List<MetaDataMigrationFiles> getMetaDataMigrationFilesList()
    {
        string tabbySelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.Id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0";

        return new List<MetaDataMigrationFiles>()
        {
            new MetaDataMigrationFiles
            {
                MigrationFileName = "2024_02_01_1200_ProductInstance_Add_IsSecureHPPVersionEnabled",
                UpdateQuery = "UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsSecureHPPVersionEnabled', CAST(0 AS BIT) END) FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2024_01_31_1200_ProductInstance_Add_IsMerchantReportingAPIEnabled",
                UpdateQuery = "UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsMerchantReportingAPIEnabled', CASE WHEN p.Counterparty = 'GEIDEA_UAE' THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END) FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2024_01_04_1200_ProductInstance_Add_IsVisaInstallmentEnabled",
                UpdateQuery = "UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsVisaInstallmentEnabled', CASE WHEN p.Counterparty = 'GEIDEA_UAE' THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END) FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2024_01_08_1416_ProductInstance_Remove_PayByGeideaCodeConfiguration",
                UpdateQuery = "UPDATE pi SET Metadata = JSON_MODIFY(pi.Metadata,'$.PayByGeideaCode', JSON_QUERY(NULL)) FROM ProductInstances pi JOIN Products p ON pi.ProductId = p.Id WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.Id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2024_01_09_1214_ProductInstance_Add_NewPayByGeideaCodeConfiguration",
                UpdateQuery = "UPDATE pi SET Metadata = JSON_MODIFY(pi.Metadata,'$.PayByGeideaCode', JSON_QUERY('{{\"IsEnabled\": false}}')) FROM ProductInstances pi JOIN Products p ON pi.ProductId = p.Id WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.Id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2024_01_09_1405_ProductInstance_Update_PayByGeideaCodePaymentReferenceExpiration",
                UpdateQuery = "UPDATE pi SET Metadata = JSON_MODIFY(JSON_MODIFY(pi.Metadata,'lax $.PayByGeideaCode.PaymentReferenceExpirationDays', ''), 'strict $.PayByGeideaCode.PaymentReferenceExpirationDays', NULL) FROM ProductInstances pi JOIN Products p ON pi.ProductId = p.Id WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.Id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2024_01_09_1718_ProductInstance_Update_PayByGeideaCodeRefundReferenceExpiration",
                UpdateQuery = "UPDATE pi SET Metadata = JSON_MODIFY(JSON_MODIFY(pi.Metadata,'lax $.PayByGeideaCode.RefundReferenceExpirationDays', ''), 'strict $.PayByGeideaCode.RefundReferenceExpirationDays', NULL) FROM ProductInstances pi JOIN Products p ON pi.ProductId = p.Id WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.Id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2024_01_11_1200_ProductInstance_Add_PartnerReferenceID",
                UpdateQuery = "UPDATE pi SET Metadata = JSON_MODIFY(pi.Metadata, '$.PartnerReferenceID', convert(nvarchar(50), LOWER(CAST('00000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER)))) FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE p.Type = 'GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) = 1",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2024_03_19_0400_ProductInstance_Add_CUP_card_brand",
                UpdateQuery = "UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.CardBrandProviders', JSON_QUERY(REPLACE(JSON_QUERY([Metadata], '$.CardBrandProviders'),']',', {{ \"CardBrand\": \"CUP_GW\", \"AcquiringProvider\": \"MPGS\", \"ThreeDSecureProvider\": \"MPGS\" }} ]'),'$'))  FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE JSON_QUERY([Metadata], '$.CardBrandProviders') NOT LIKE '%CUP%'",
                SelectQuery = "SELECT pi.[Id] FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0 AND p.Counterparty = 'GEIDEA_UAE' AND JSON_QUERY([Metadata], '$.CardBrandProviders') IS NOT NULL AND JSON_QUERY([Metadata], '$.CardBrandProviders[0]') IS NOT NULL",
            },
            new MetaDataMigrationFiles
            {
               MigrationFileName = "2024_05_09_0200_ProductInstance_Add_JCB_card_brand",
               UpdateQuery = "UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.CardBrandProviders', JSON_QUERY(REPLACE(JSON_QUERY([Metadata], '$.CardBrandProviders'),']',', {{ \"CardBrand\": \"JCB_GW\", \"AcquiringProvider\": \"MPGS\", \"ThreeDSecureProvider\": \"MPGS\" }} ]'),'$'))  FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE JSON_QUERY([Metadata], '$.CardBrandProviders') NOT LIKE '%JCB%'",
               SelectQuery = "SELECT pi.[Id] FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0 AND p.Counterparty = 'GEIDEA_UAE' AND JSON_QUERY([Metadata], '$.CardBrandProviders') IS NOT NULL AND JSON_QUERY([Metadata], '$.CardBrandProviders[0]') IS NOT NULL",
            },
             new MetaDataMigrationFiles
            {
                MigrationFileName = "2024_05_12_0956_ProductInstance_Add_NewTabbyConfiguration",
                UpdateQuery = "UPDATE pi SET Metadata = JSON_MODIFY(pi.Metadata,'$.Tabby', JSON_QUERY('{{\"IsTabbyEnabled\": false}}')) FROM ProductInstances pi JOIN Products p ON pi.ProductId = p.Id WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = tabbySelectQuery,
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2024_05_12_0956_ProductInstance_Update_TabbyMerchantCode",
                UpdateQuery = "UPDATE pi SET Metadata = JSON_MODIFY(JSON_MODIFY(pi.Metadata,'lax $.Tabby.MerchantCode', ''), 'strict $.Tabby.MerchantCode', NULL) FROM ProductInstances pi JOIN Products p ON pi.ProductId = p.Id WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = tabbySelectQuery,
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2024_05_12_0956_ProductInstance_Update_TabbySecretKey",
                UpdateQuery = "UPDATE pi SET Metadata = JSON_MODIFY(JSON_MODIFY(pi.Metadata,'lax $.Tabby.SecretKey', ''), 'strict $.Tabby.SecretKey', NULL) FROM ProductInstances pi JOIN Products p ON pi.ProductId = p.Id WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = tabbySelectQuery,
            },
            new MetaDataMigrationFiles
            {
                MigrationFileName = "2024_05_20_0117_ProductInstance_Update_TabbyPublicKey",
                UpdateQuery = "UPDATE pi SET Metadata = JSON_MODIFY(JSON_MODIFY(pi.Metadata,'lax $.Tabby.TabbyPublicKey', ''), 'strict $.Tabby.TabbyPublicKey', NULL) FROM ProductInstances pi JOIN Products p ON pi.ProductId = p.Id WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = tabbySelectQuery,
            },
            new MetaDataMigrationFiles
            {
                MigrationFileName = "2024_09_04_1048_ProductInstance_Add_IsMerchantBrandingForPblQrEnabled",
                UpdateQuery = "UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsMerchantBrandingForPblQrEnabled', CAST(0 AS BIT) END) FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
            },
            new MetaDataMigrationFiles
            {
                MigrationFileName = "2025_02_20_1617_ProductInstance_Add_IsUpdateAuthorizationEnabled",
                UpdateQuery = "UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsUpdateAuthorizationEnabled', CAST(0 AS BIT)) FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2025_02_20_1620_ProductInstance_Add_IsUpdateAuthorizationPercentageEnabled",
                UpdateQuery = "UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsUpdateAuthorizationPercentageEnabled', CAST(0 AS BIT)) FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2025_02_20_1622_ProductInstance_Add_UpdateAuthorizationPercentage",
                UpdateQuery = "UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.UpdateAuthorizationPercentage', 0.00) FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2025_03_02_1608_ProductInstance_Add_IsGenerateAndUseNetworkTokenEnabled",
                UpdateQuery = "UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsGenerateAndUseNetworkTokenEnabled', CAST(0 AS BIT)) FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2025_03_02_1609_ProductInstance_Add_IsUseNetworkTokenEnabled",
                UpdateQuery = "UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsUseNetworkTokenEnabled', CAST(0 AS BIT)) FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2025_03_09_1623_ProductInstance_Add_IsSendNetworkTokenToMerchantEnabled",
                UpdateQuery = "UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsSendNetworkTokenToMerchantEnabled', CAST(0 AS BIT)) FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0 AND p.Counterparty = 'GEIDEA_UAE'",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2025_03_10_1405_ProductInstance_Add_NetworkTokenEncryptionKey",
                UpdateQuery = "UPDATE pi SET Metadata = JSON_MODIFY(JSON_MODIFY(pi.Metadata,'$.NetworkTokenEncryptionKey', ''), 'strict $.NetworkTokenEncryptionKey', NULL) FROM ProductInstances pi JOIN Products p ON pi.ProductId = p.Id WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0 AND p.Counterparty = 'GEIDEA_UAE'",
            },

            new MetaDataMigrationFiles
            {
                MigrationFileName = "2025_03_10_1406_ProductInstance_Add_NetworkTokenCallBackUrl",
                UpdateQuery = "UPDATE pi SET Metadata = JSON_MODIFY(JSON_MODIFY(pi.Metadata,'$.NetworkTokenCallBackUrl', ''), 'strict $.NetworkTokenCallBackUrl', NULL) FROM ProductInstances pi JOIN Products p ON pi.ProductId = p.Id WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0",
                SelectQuery = "SELECT pi.Id FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0 AND p.Counterparty = 'GEIDEA_UAE'",
            },
        };
    }
}
