﻿using Common.Models.Apex;
using Common.Options;
using Common.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services;
public class ApexService : IApexService
{
    private readonly IApexClient apexClient;
    private readonly ILogger<ApexService> logger;
    private readonly ApexSettings apexSettings;
    private readonly ExternalApiSettings externalApiSettings;

    public ApexService(IApexClient apexClient, IOptionsMonitor<ApexSettings> apexSettings, ILogger<ApexService> logger, IOptionsMonitor<ExternalApiSettings> externalSettingMonitor)
    {
        this.apexClient = apexClient;
        this.apexSettings = apexSettings.CurrentValue;
        this.logger = logger;
        this.externalApiSettings = externalSettingMonitor.CurrentValue;

    }

    public async Task<MerchantInquiryResponse> GetMerchantInquiryAsync(MerchantInquiryRequest merchantInquiryRequest)
    {

        var response = await apexClient.GetMerchantInquiry(merchantInquiryRequest);

        return response;
    }
}
