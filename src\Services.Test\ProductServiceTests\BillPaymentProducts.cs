﻿using System;
using System.Threading.Tasks;
using AutoMapper;
using Common.Models;
using Common.Repositories;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;

namespace Services.Test.ProductServiceTests;

public class BillPaymentProducts
{
    private readonly ILogger<ProductService> logger = Substitute.For<ILogger<ProductService>>();
    private readonly IProductRepository productRepo = Substitute.For<IProductRepository>();
    private readonly IMapper mapper = Substitute.For<IMapper>();
    private readonly ICategoryRepository categoryRepo = Substitute.For<ICategoryRepository>();
    private readonly IProductInstanceRepository productInstanceRepo = Substitute.For<IProductInstanceRepository>();
    private ProductService productService;

    [SetUp]
    public void Setup()
    {
        productService = new ProductService(logger, productRepo, mapper, categoryRepo, productInstanceRepo);

        productRepo.ProductsContainBillPaymentTypeAsync(Arg.Any<IdsRequest>()).Returns(new BillPaymentServiceAndBundleFlags());
        productRepo.ProductsContainBillPaymentServiceOrBundleAsync(Arg.Any<IdsRequest>()).Returns(true);
        productRepo.ClearReceivedCalls();
    }

    [Test]
    public async Task ProductsContainBillPaymentServiceOrBundleAsync_ShouldCallRepository()
    {
        var idsRequest = new IdsRequest
        {
            Ids = new[] { Guid.NewGuid(), Guid.NewGuid() }
        };
        var result = await productService.ProductsContainBillPaymentServiceOrBundleAsync(idsRequest);

        await productRepo.Received(1).ProductsContainBillPaymentServiceOrBundleAsync(idsRequest);
        result.Should().BeTrue();
    }

    [Test]
    public async Task ProductsContainBillPaymentTypeAsync_ShouldCallRepository()
    {
        var idsRequest = new IdsRequest
        {
            Ids = new[] { Guid.NewGuid(), Guid.NewGuid(), Guid.NewGuid() }
        };
        var result = await productService.ProductsContainBillPaymentTypeAsync(idsRequest);

        await productRepo.Received(1).ProductsContainBillPaymentTypeAsync(idsRequest);
        result.Should().NotBeNull();
    }
}
