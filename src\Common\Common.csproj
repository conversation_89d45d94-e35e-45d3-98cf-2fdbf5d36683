﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Nullable>enable</Nullable>
		<LangVersion>latest</LangVersion>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<ProjectGuid>{02594494-54bf-495d-8e85-9a3da5ff738d}</ProjectGuid>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Geidea.Messaging" Version="2.2.340" />
		<PackageReference Include="FluentValidation" Version="11.10.0" />
		<PackageReference Include="Geidea.Utils.Counterparty" Version="2.0.232" />
		<PackageReference Include="Geidea.Utils.Common" Version="1.0.44" />
		<PackageReference Include="Geidea.Utils.DataAccess" Version="1.1.218" />
		<PackageReference Include="Geidea.Utils.HealthChecks" Version="1.1.342" />
		<PackageReference Include="Geidea.Utils.Logging" Version="1.1.561" />
		<PackageReference Include="Geidea.Utils.Migrations" Version="1.0.134" />
		<PackageReference Include="Geidea.Utils.Swagger" Version="1.1.256" />
		<PackageReference Include="Geidea.Utils.Validation" Version="2.1.114" />
		<PackageReference Include="GeideaPaymentGateway.Utils.InputValidations" Version="2.2.5" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Models\Models.csproj" />
	</ItemGroup>

</Project>
