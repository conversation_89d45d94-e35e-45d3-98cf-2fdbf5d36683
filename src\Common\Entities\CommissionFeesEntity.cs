﻿using Common.Enums;
using Geidea.Utils.DataAccess.Entities;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Entities;
[ExcludeFromCodeCoverage]
public class CommissionFeesEntity : AuditableEntity<Guid>
{
    public string? Code { get; set; }
    public string? Name { get; set; }
    public string? NameAr { get; set; }
    public Guid ImageId { get; set; }
    public Status Status { get; set; }
    public ICollection<ProductCommissionPriceEntity>? CommissionPrices { get; set; }

}
