﻿using System;
using System.Threading.Tasks;
using Common.Data;
using Common.Entities;
using Common.Models;
using Common.Repositories;
using Common.Services;
using Geidea.Messages.Gsdk;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;
using Geidea.Utils.Counterparty.Providers;

namespace Services.Test;

public class PointOfSaleResponseMessageReceiverTests
{
    private static readonly string SecretKey = Guid.NewGuid().ToString();
    private static readonly Guid ProductInstanceId = Guid.NewGuid();

    private IPointOfSaleResponseMessageClient messageClient;
    private IProductInstanceRepository productInstanceRepository;
    private ICounterpartyProvider counterpartyProvider;

    private ILogger<PointOfSaleResponseMessageReceiver> logger;
    private PointOfSaleResponseMessageReceiver messageReceiver;



    private readonly GsdkPointOfSaleResponse gsdkMerchantPointOfSaleMessage =
        new()
        {
            SecretKey = SecretKey,
            ProductInstanceId = ProductInstanceId
        };

    [SetUp]
    public void Setup()
    {
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();
        productInstanceRepository = Substitute.For<IProductInstanceRepository>();

        var serviceProvider = Substitute.For<IServiceProvider>();
        serviceProvider.GetService<ICounterpartyProvider>().Returns(counterpartyProvider);
        serviceProvider.GetService<IProductInstanceRepository>().Returns(productInstanceRepository);
        var serviceScope = Substitute.For<IServiceScope>();
        serviceScope.ServiceProvider.Returns(serviceProvider);
        var serviceScopeFactory = Substitute.For<IServiceScopeFactory>();
        serviceScopeFactory.CreateScope().Returns(serviceScope);

        messageClient = Substitute.For<IPointOfSaleResponseMessageClient>();
        logger = Substitute.For<ILogger<PointOfSaleResponseMessageReceiver>>();

        messageReceiver = new PointOfSaleResponseMessageReceiver(serviceScopeFactory, messageClient, logger);
    }

    [Test]
    public void StartReceivingShouldSubscribeAndUnsubscribeSuccessfully()
    {
        messageReceiver.StartReceiving();

        messageClient.Received(1).OnPointOfSaleResponseMessageReceived += Arg.Any<EventHandler<PointOfSaleResponseMessageReceivedEventArgs>>();
        messageClient.Received(1).Connect();

        messageReceiver.Dispose();

        messageClient.Received(1).OnPointOfSaleResponseMessageReceived -= Arg.Any<EventHandler<PointOfSaleResponseMessageReceivedEventArgs>>();
    }

    [Test]
    public async Task StartReceivingShouldAddGsdkSecretCodeToProductInstanceWhenGateway()
    {
        var productInstance = new ProductInstanceEntity()
        {
            AgreementId = Guid.NewGuid(),
            Product = new ProductEntity
            {
                Id = Guid.NewGuid(),
                Type = "GWAY"
            },
            Data = new GatewayData()
            {
                GsdkSecretKey = "Secret",
            },
        };

        productInstanceRepository.GetByIdAsync(Arg.Any<Guid>()).Returns(productInstance);

        messageReceiver.StartReceiving();
        messageClient.OnPointOfSaleResponseMessageReceived += Raise.EventWith(new PointOfSaleResponseMessageReceivedEventArgs(gsdkMerchantPointOfSaleMessage, "GEIDEA_SAUDI"));

        productInstanceRepository.Received(1).Update(Arg.Any<ProductInstanceEntity>());
        await productInstanceRepository.Received(1).SaveChangesAsync();
    }

    [Test]
    public async Task StartReceivingShouldAddGsdkSecretCodeToProductInstanceWhenTerminal()
    {
        var productInstance = new ProductInstanceEntity()
        {
            AgreementId = Guid.NewGuid(),
            Product = new ProductEntity
            {
                Id = Guid.NewGuid(),
                Type = "TERMINAL"
            },
            Data = new TerminalData()
            {
                GSDKKey = "Secret",
            },
        };

        productInstanceRepository.GetByIdAsync(Arg.Any<Guid>()).Returns(productInstance);

        messageReceiver.StartReceiving();
        messageClient.OnPointOfSaleResponseMessageReceived += Raise.EventWith(new PointOfSaleResponseMessageReceivedEventArgs(gsdkMerchantPointOfSaleMessage, "GEIDEA_SAUDI"));

        productInstanceRepository.Received(1).Update(Arg.Any<ProductInstanceEntity>());
        await productInstanceRepository.Received(1).SaveChangesAsync();
    }

    [Test]
    public async Task StartReceivingShouldNotAddGsdkSecretCodeWhenProductInstanceDataNotExpected()
    {
        var productInstance = new ProductInstanceEntity()
        {
            AgreementId = Guid.NewGuid(),
            Product = new ProductEntity
            {
                Id = Guid.NewGuid(),
                Type = "ECR"
            },
            Data = new MiniEcrData()
        };

        productInstanceRepository.GetByIdAsync(Arg.Any<Guid>()).Returns(productInstance);

        messageReceiver.StartReceiving();
        messageClient.OnPointOfSaleResponseMessageReceived += Raise.EventWith(new PointOfSaleResponseMessageReceivedEventArgs(gsdkMerchantPointOfSaleMessage, "GEIDEA_SAUDI"));

        productInstanceRepository.DidNotReceive().Update(Arg.Any<ProductInstanceEntity>());
        await productInstanceRepository.DidNotReceive().SaveChangesAsync();
    }
}
