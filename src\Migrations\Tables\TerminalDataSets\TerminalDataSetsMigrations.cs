﻿using Common;
using FluentMigrator;
using System.IO;
using System;

namespace Migrations.Tables.TerminalDataSets;

[Migration(2021_10_13_1030)]
public class TerminalDataSetsMigrations : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("TerminalDataSets")
          .WithColumn("Id").AsGuid().NotNullable().PrimaryKey().WithDefault(SystemMethods.NewGuid)
          .WithColumn("AcquiringLedger").AsString(255).Nullable()
          .WithColumn("MID").AsString(47).Nullable()
          .WithColumn("TID").AsString(23).Nullable()
          .WithColumn("FullTID").AsString(16).Nullable()
          .WithColumn("TRSM").AsString(18).Nullable()
          .WithColumn("OrderNumber").AsString(255).Nullable()
          .WithColumn("ConfigDate").AsDateTime2().Nullable()
          .WithColumn("Availability").AsString(255).NotNullable()
          .WithColumn("Counterparty").AsString().Nullable()
          .WithColumn("CreatedBy").AsString(150).NotNullable()
          .WithColumn("CreatedDate").AsDateTime2().NotNullable()
          .WithColumn("UpdatedBy").AsString(150).Nullable()
          .WithColumn("UpdatedDate").AsDateTime2().Nullable();
    }
}

[Migration(2021_10_15_1400)]
public class StoreAndProductInstanceId : ForwardOnlyMigration
{
    public override void Up()
    {
        Alter.Table("TerminalDataSets").AddColumn("StoreId").AsGuid().Nullable();
        Alter.Table("TerminalDataSets").AddColumn("ProductInstanceId").AsGuid().Nullable();
    }
}

[Migration(2022_02_24_1700)]
public class AddMccColumn : ForwardOnlyMigration
{
    public override void Up()
    {
        Alter.Table("TerminalDataSets").AddColumn("MCC").AsString(4).Nullable();
    }
}

[Migration(2022_04_06_1400)]
public class AddTmsExtractionFlags : ForwardOnlyMigration
{
    public override void Up()
    {
        Alter.Table("TerminalDataSets").AddColumn("ExtractionDate").AsDateTime2().Nullable();
        Alter.Table("TerminalDataSets").AddColumn("Extracted").AsBoolean().NotNullable().WithDefaultValue(false);
    }
}

[Migration(2022_09_14_1000)]
public class SyncOrderMigrationAndTerminalDataSet : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"USE PRODUCTS
        GO
        CREATE OR ALTER PROCEDURE [dbo].[SyncTerminalDataSetWithOrderMigration]
        AS
        DECLARE @applyChanges BIT = 1
        BEGIN 
        TRY
        BEGIN TRAN 

        DECLARE @configCursor CURSOR
        DECLARE @mid nvarchar(255)
        DECLARE @tid nvarchar(255)
        DECLARE @fullTid nvarchar(255)
        DECLARE @trsm nvarchar(255)
        DECLARE @availability nvarchar(255)
        DECLARE @acquiringLedger nvarchar(255)
        DECLARE @orderNumber nvarchar(255)
        DECLARE @createdDate DATETIME2(7)
        DECLARE @configDate DATETIME2(7)
        DECLARE @productInstanceId nvarchar(255)
        DECLARE @storeId nvarchar(255)
        DECLARE @createdBy nvarchar(255)

        DECLARE @DataSetFinal table (MID NVARCHAR(255), TID NVARCHAR(255), FullTID NVARCHAR(255), TRSM NVARCHAR(255),
        AcquiringLedger NVARCHAR(255), ProductInstanceId NVARCHAR(255), ConfigDate DATETIME2(7), OrderNumber NVARCHAR(255), OrderStatus NVARCHAR(255),
        OrderId NVARCHAR(255), StoreId NVARCHAR(255), CompanyId NVARCHAR(255), Counterparty NVARCHAR(255), Availability NVARCHAR(255), Extracted bit,
        CreatedDate DATETIME2(7), CreatedBy NVARCHAR(255))

        INSERT INTO @DataSetFinal(MID, TID, FullTID, TRSM, AcquiringLedger, ProductInstanceId, ConfigDate, OrderNumber, OrderStatus, OrderId, StoreId,
        CompanyId, Counterparty, Extracted, CreatedDate, CreatedBy)
        SELECT JSON_VALUE(LOWER(Metadata), '$.midmerchantreference') AS MID,
        JSON_VALUE(LOWER(Metadata), '$.tid') AS TID,
        JSON_VALUE(LOWER(Metadata), '$.fulltid') AS FullTId,
        JSON_VALUE(LOWER(Metadata), '$.posdatacode') AS TRSM,
        JSON_VALUE(LOWER(Metadata), '$.providerbank') AS AcquiringLedger,
        pi.Id AS ProductInstanceId, o.CheckoutDate AS ConfigDate, o.OrderNumber, o.OrderStatus, o.Id, pi.StoreId, pi.CompanyId, 'GEIDEA_SAUDI',
        0 AS Extracted, o.CreatedDate, o.CreatedBy
        FROM [PRODUCTS].[dbo].[ProductInstances] AS pi
        JOIN [PRODUCTS].[dbo].[Products] AS p ON p.Id = pi.ProductId
        JOIN [ORDERS].[dbo].[OrderItemProductInstances] AS oipi ON oipi.productInstanceId = pi.Id
        JOIN [ORDERS].[dbo].[OrderItem] AS oi ON oi.Id = oipi.OrderItemId
        JOIN [ORDERS].[dbo].[Order] AS o ON oi.OrderId = o.Id
        WHERE p.Type='TERMINAL' AND LOWER(pi.Metadata) LIKE '%midmerchantreference%' AND LOWER(pi.Metadata) LIKE '%fulltid%' AND LOWER(pi.Metadata) LIKE '%tid%' AND LOWER(pi.Metadata) LIKE '%posdatacode%'
        AND LOWER(pi.Metadata) LIKE '%providerbank%' AND p.Code IN ('VX_675','Spectra_SP_530')
        AND o.Counterparty='GEIDEA_SAUDI' AND o.ProjectName='MERCHANT_MIGRATION'

        DELETE T1 FROM @DataSetFinal as T1
        INNER JOIN [PRODUCTS].[dbo].[TerminalDataSets] AS T2 ON 
        T1.MID=T2.MID AND T1.TID = T2.TID AND T1.FullTID = T2.FullTID AND T1.TRSM = T2.TRSM AND T1.Counterparty = T2.Counterparty AND T2.AcquiringLedger = 'DEFAULT_BANK'

        SET @configCursor = CURSOR FOR SELECT MID, TID, FullTID, TRSM, OrderNumber, ConfigDate, ProductInstanceId, StoreId, CreatedBy, CreatedDate FROM @DataSetFinal
        OPEN @configCursor FETCH NEXT FROM @configCursor INTO @mid, @tid, @fullTid, @trsm, @orderNumber, @configDate, @productInstanceId, @storeId, @createdBy, @createdDate WHILE @@FETCH_STATUS = 0
        BEGIN
        IF EXISTS (SELECT * FROM [PRODUCTS].[dbo].[TerminalDataSets] where Counterparty = 'GEIDEA_SAUDI' AND AcquiringLedger = 'DEFAULT_BANK' AND Availability = 'AVAILABLE'
        AND MID=@mid AND TID = @tid AND FullTID = @fullTid AND TRSM = @trsm)
        BEGIN
            UPDATE [PRODUCTS].[dbo].[TerminalDataSets] SET Availability = 'USED', OrderNumber = @orderNumber, ConfigDate = @configDate WHERE Counterparty = 'GEIDEA_SAUDI'
            AND AcquiringLedger = 'DEFAULT_BANK' AND Availability = 'AVAILABLE' AND MID=@mid AND TID = @tid AND FullTID = @fullTid AND TRSM = @trsm
        END
        ELSE IF EXISTS (SELECT * FROM [PRODUCTS].[dbo].[TerminalDataSets] where Counterparty = 'GEIDEA_SAUDI' and AcquiringLedger = 'DEFAULT_BANK' and Availability = 'INVALID'
        AND MID=@mid AND TID = @tid AND FullTID = @fullTid AND TRSM = @trsm)
        BEGIN
            INSERT INTO [PRODUCTS].[dbo].[TerminalDataSets](MID, TID, FullTID, TRSM, AcquiringLedger, OrderNumber, ConfigDate, Counterparty, CreatedBy, CreatedDate, Availability,
            ProductInstanceId, StoreId)
            VALUES (@mid,@tid, @fullTid, @trsm, 'DEFAULT_BANK', @orderNumber, @configDate, 'GEIDEA_SAUDI', @createdBy, @createdDate, 'USED', @productInstanceId, @storeId)
        END
        ELSE IF NOT EXISTS (SELECT * FROM [PRODUCTS].[dbo].[TerminalDataSets] where Counterparty = 'GEIDEA_SAUDI' AND AcquiringLedger = 'DEFAULT_BANK'
        AND MID=@mid AND TID = @tid AND FullTID = @fullTid AND TRSM = @trsm)
        BEGIN
            INSERT INTO [PRODUCTS].[dbo].[TerminalDataSets](MID, TID, FullTID, TRSM, AcquiringLedger, OrderNumber, ConfigDate, Counterparty, CreatedBy, CreatedDate, Availability,
            ProductInstanceId, StoreId)
            VALUES (@mid,@tid, @fullTid, @trsm, 'DEFAULT_BANK', @orderNumber, @configDate, 'GEIDEA_SAUDI', @createdBy, @createdDate, 'USED', @productInstanceId, @storeId)
        END

        FETCH NEXT FROM @configCursor INTO @mid, @tid, @fullTid, @trsm, @orderNumber, @configDate, @productInstanceId, @storeId, @createdBy, @createdDate
        END
        CLOSE @configCursor
        DEALLOCATE @configCursor

        IF @applyChanges = 1
	        BEGIN
		        PRINT 'COMMIT TRANSACTION ...'
		        COMMIT;
	        END
        ELSE
	        BEGIN
		        PRINT 'ROLLBACK TRANSACTION ...'
		        ROLLBACK;
	        END
        END try
        BEGIN catch
	        PRINT 'ROLLBACK TRANSACTION ...'
	        ROLLBACK;
	        throw;
        END CATCH
        GO");
    }
}


[Migration(2022_09_15_1800)]
public class SyncOrderMigrationAndTerminalDataSet_V2 : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(AppDomain.CurrentDomain.BaseDirectory +
           Path.Combine("Scripts", "TerminalDataSetSyncWithOrderMigration_V2.sql"));
    }
}

[Migration(2022_10_06_1200)]
public class SyncOrderMigrationAndTerminalDataSet_V3 : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(AppDomain.CurrentDomain.BaseDirectory +
           Path.Combine("Scripts", "TerminalDataSetSyncWithOrderMigration_V2.sql"));
    }
}


[Migration(2023_06_20_0130)]
public class TerminalDataSetIndices : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Index("IX_TerminalDataSet_MID").OnTable("TerminalDataSets")
            .OnColumn("StoreId").Ascending()
            .OnColumn("Counterparty").Ascending()
            .OnColumn("MID").Ascending();

        Create.Index("IX_TerminalDataSet_TID").OnTable("TerminalDataSets")
            .OnColumn("Counterparty").Ascending()
            .OnColumn("StoreId").Ascending()
            .OnColumn("OrderNumber").Ascending()
            .OnColumn("TID").Ascending();
    }
}