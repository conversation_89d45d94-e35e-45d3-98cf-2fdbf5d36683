﻿using Common.Enums;
using Common.Enums.ProductCommisssionPrices;
using Common.Enums.UnitPrice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Views;
public class NonTransactionalFeesPriceListView
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public string ProductName { get; set; } = null!;
    public string ProductCode { get; set; } = null!;
    public Guid MccId { get; set; }
    public string MccName { get; set; } = null!;
    public string MccCode { get; set; } = null!;
    public string MccCategory { get; set; } = null!;
    public Guid BusinessTypeId { get; set; }
    public string BusinessType { get; set; } = null!;
    public Guid NonTransFeeId { get; set; }
    public string NonTransFeeName { get; set; } = null!;
    public string NonTransFeeNameAr { get; set; } = null!;
    public string NonTransFeeCode { get; set; } = null!;
    public Status FeeStatus { get; set; }
    public decimal FeeValue { get; set; }
    public BillingType BillingType { get; set; }
    public PriceBillingFrequency BillingFrequency { get; set; }
    public FeeType FeeType { get; set; }
    public DateTime CreatedDate { get; set; }
}
