﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Models;
using Common.Models.ProductInstance;
using Microsoft.AspNetCore.JsonPatch;

namespace Common.Services;

public interface IProductConfiguratorService
{
    Task<ProductInstanceResponse> CreateAsync(CreateProductInstanceRequest productInstanceRequest, bool isFirstInstanceCreation = false);
    Task<ProductInstanceResponse[]> BulkCreateAsync(IList<CreateProductInstanceRequest> productInstancesRequest);
    Task<Guid[]> DeleteAsync(DeleteProductInstanceRequest deleteProductInstanceRequest);
    Task<ProductInstanceWithParentResponse[]> FindAsync(FindProductInstanceRequest findProductInstanceRequest, bool track = false);
    Task<List<Guid?>> FindGatewayProductCompanyIds(bool track = false);
    Task<ProductInstanceResponse> PatchAsync(Guid id, JsonPatchDocument<UpdateProductInstanceRequest> patchDocument);
    Task<List<Guid>> UpdateTerminalProductInstancesMeta(List<UpdateProductInstanceMetaRequest> updateProductInstances);
    Task<ProductInstanceResponse[]> FindByIdsAsync(IdsRequest request);
    Task<ProductInstanceResponse> FindByIdAsync(Guid id);
    Task<Guid[]> CreateDefaultProductInstancesAsync(DefaultProductInstanceRequest request);
    Task<List<BasicInstanceInfoWithChildren>> CreateDefaultProductInstancesChildrenAsync(DefaultProductInstanceRequest request);
    Task<List<StoreProductInstanceResponse>> GetProductInstancesForStore(Guid storeId);
    Task<List<StoreProductInstanceResponse>> GetProductInstancesForMultipleStores(Guid[] storeIds);
    Task<ProductInstanceResponse[]> SearchProductInstanceBaseAsync(SearchProductInstanceBaseRequest request);
    Task<SearchGatewayConfigurationsResponse> SearchGatewayConfigurations(SearchGatewayConfigurationsRequest request);
    Task UpdateProductInstancesStoreNameForMerchant(MerchantStoreNameProductInstanceRequest request);
    Task<Guid> CreateMerchantMigrationDefaultProductInstancesAsync(MerchantMigrationProductInstanceRequest request);
    Task CleanExpiredMPGSHistory();
    DateTimeOffset GetMpgsHistoryExpirationDate(DateTimeOffset expiryDate, TimeSpan period);
    Task<bool> IsSubscribedToPaymentGateway(Guid merchantId);
    Task UpdateProductInstancesMsoSubMerchantInfo(IList<Geidea.ProductService.Models.SubMerchantMigrationInfoProductInstance> request);
}
