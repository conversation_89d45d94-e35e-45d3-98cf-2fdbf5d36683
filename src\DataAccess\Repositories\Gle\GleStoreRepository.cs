﻿using System;
using System.Threading.Tasks;
using Common.Entities.Gle;
using Common.Repositories.Gle;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DataAccess.Repositories.Gle;

public class GleStoreRepository : GleBaseRepository<GleStoreEntity>, IGleStoreRepository
{
    private readonly ILogger<GleStoreRepository> logger;

    public GleStoreRepository(DbContext context,
        IHttpContextAccessor contextAccessor,
        ILogger<GleBaseRepository<GleStoreEntity>> baseLogger,
        ILogger<GleStoreRepository> logger)
        : base(context, contextAccessor, baseLogger)
    {
        this.logger = logger;
    }

    public async Task<GleStoreEntity?> GetGleStoreByStoreIdAsync(Guid storeId)
    {
        var result = await context.Set<GleStoreEntity>()
            .AsQueryable()
            .AsNoTracking()
            .SingleOrDefaultAsync(x => x.StoreId == storeId);

        logger.LogInformation("Returning GLE store for store ID: {id}", storeId);
        return result;
    }

    public async Task<GleStoreEntity?> GetGleHierarchyByStoreIdAsync(Guid storeId)
    {
        var result = await context.Set<GleStoreEntity>()
            .AsQueryable()
            .AsNoTracking()
            .Include(x => x.GleMerchant)
            .Include(x => x.GleTerminalEntities)
            .SingleOrDefaultAsync(x => x.StoreId == storeId);

        logger.LogInformation("Returning the entire GLE hierarchy based on the store ID: {id}", storeId);
        return result;
    }
}