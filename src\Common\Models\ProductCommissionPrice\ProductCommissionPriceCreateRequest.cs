﻿using System;
using System.Collections.Generic;
using Common.Enums.ProductCommisssionPrices;

namespace Common.Models.ProductCommissionPrice;
public class ProductCommissionPriceCreateRequest
{
    public List<Guid> ProductIds { get; set; } = new List<Guid>();
    public List<Guid> MccIds { get; set; } = new List<Guid>();
    public List<Guid> BusinessTypeIds { get; set; } = new List<Guid>();
    public List<Guid> CommissionFeeIds { get; set; } = new List<Guid>();
    public FeeType FeeType { get; set; }
    public decimal FeeValue { get; set; }
    public CommisssionPricesBillingType BillingType { get; set; }
    public PriceBillingFrequency BillingFrequency { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}
