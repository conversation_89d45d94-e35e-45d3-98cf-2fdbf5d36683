﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.Products;
[Migration(2024_10_29_0427)]
public class AlterOnboardingUnitPriceList : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "OnboardingUnitPriceList.sql"));
    }
}
