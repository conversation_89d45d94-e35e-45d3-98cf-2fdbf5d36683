﻿using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Common;
using Common.Models;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;

namespace ProductService.Controllers;

[Route("api/v1/[controller]")]
[ApiController]
public class PriceController : ControllerBase
{
    private readonly IPriceService priceService;

    public PriceController(IPriceService priceService)
    {
        this.priceService = priceService;
    }

    /// <summary>
    /// Returns a price entry by its unique identifier.
    /// </summary>
    /// <response code="200">Price details</response>
    /// <response code="404">No valid price entry with the specified id was found</response> 
    [HttpGet]
    [Route("{id}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Price), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetById(Guid id)
    {
        var result = await priceService.FindAsync(new FindPriceRequest { Id = id }, false);

        if (result.Length == 0)
        {
            throw new ServiceException(HttpStatusCode.NotFound, Errors.PriceNotFound);
        }

        return Ok(result.Single());
    }

    /// <summary>
    /// Returns all prices matching the search criteria.
    /// </summary>
    /// <response code="200">Search was successfull</response>
    [HttpGet]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Price[]), StatusCodes.Status200OK)]
    public async Task<IActionResult> Find([FromQuery] FindPriceRequest request)
    {
        var result = await priceService.FindAsync(request, false);
        return Ok(result);
    }

    /// <summary>
    /// Creates a new price entry and attaches it to an existing product.
    /// </summary>
    /// <response code="201">The newly created price</response>
    /// <response code="400">A business error has occurred</response> 
    /// <response code="404">The specified product was not found</response> 
    [ApiExplorerSettings(IgnoreApi = true)]
    [HttpPost]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Product), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Create([FromBody] PriceRequest request)
    {
        var result = await priceService.CreateAsync(request);

        return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
    }

    /// <summary>
    /// Patch price.
    /// </summary>
    /// <response code="200">Returns updated price</response>
    /// <response code="404">Price with provided id not found</response> 
    /// <response code="404">Returns the error</response> 

    [ApiExplorerSettings(IgnoreApi = true)]
    [HttpPatch("{priceId}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> PatchPrice(Guid priceId, [FromBody] JsonPatchDocument<PriceRequest> patchPrice)
    {
        var updatedPrice = await priceService.PatchAsync(priceId, patchPrice);
        return Ok(updatedPrice);
    }

    /// <summary>
    /// Delete price.
    /// </summary>
    /// <response code="204">The price has been deleted</response>
    /// <response code="404">Price with provided id not found</response> 
    /// <response code="404">Returns the error</response> 

    [ApiExplorerSettings(IgnoreApi = true)]
    [HttpDelete("{priceId}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeletePrice(Guid priceId)
    {
        await priceService.DeleteAsync(priceId);
        return NoContent();
    }
}
