CREATE TABLE ValueAddedServicePricing (

    Id UNIQUEIDENTIFIER PRIMARY KEY,
    ProductID UNIQUEIDENTIFIER NOT NULL,
    MCCID UNIQUEIDENTIFIER NOT NULL,
    BusinessTypeID UNIQUEIDENTIFIER NOT NULL,
    VASID UNIQUEIDENTIFIER NOT NULL,

    SubscriptionFee DECIMAL(9, 2) NOT NULL,  	  
    FeeType INT NOT NULL,  
    BillingType INT NOT NULL, 
    BillingFrequency INT NOT NULL,  

    CreatedBy VARCHAR(255) NOT NULL,
    CreatedDate DATETIME NOT NULL,
    UpdatedBy VARCHAR(255),
    UpdatedDate DATETIME,

    -- Adding Unique Constraint
    CONSTRAINT UQ_Product_MCC_BusinessType_VASID UNIQUE (ProductID, MCCID,BusinessTypeID,VASID),

    FOREIG<PERSON> KEY (ProductID) REFERENCES Products(Id),
    FOREIG<PERSON> KEY (VASID) REFERENCES ValueAddedServices(Id),
    FOR<PERSON><PERSON><PERSON> (MCCID) REFERENCES MCC(Id),
    <PERSON><PERSON><PERSON><PERSON><PERSON> (BusinessTypeID) REFERENCES BusinessTypes(Id)
);
