﻿using Common.Models;
using System;
using System.Collections;
using System.Collections.Generic;

namespace Services.Test.TestData;

public class InvalidProductCreateData : IEnumerable<object[]>
{
    public IEnumerator<object[]> GetEnumerator()
    {
        yield return new object[]
        {
                new ProductRequest
                {
                    Availability="AVAILABILITY",
                    Code="Test",
                    Description="Test description",
                    Type="BUNDLE",

                    Version=0
                }
        };
        yield return new object[]
        {
                new ProductRequest
                {
                    Availability="Bundle",
                    Description="Test description",
                    Type="BUNDLE",
                    Version=0
                }
        };
        yield return new object[]
        {
                new ProductRequest
                {
                    Availability="Bundle",
                    Code="Test",
                    Description="Test description",
                    Type="BUNDLE",

                    Version=-1
                }
        };
        yield return new object[]
        {
                new ProductRequest
                {
                    Availability="Bundle",
                    Code="Test",
                    Description="Test description",

                    Version=0
                }
        };
        yield return new object[]
        {
                new ProductRequest
                {
                    Availability="Bundle",
                    Code="Test",
                    Description="Test description",
                    Type="test",

                    Version=0
                }
        };
        yield return new object[]
        {
                new ProductRequest
                {
                    Availability="Bundle",
                    Code="Test",
                    Description="Test description",
                    Type="BUNDLE",

                    Version=0
                }
        };
        yield return new object[]
        {
                new ProductRequest
                {
                    Availability="Bundle",
                    Code="Test",
                    Description="Test description",
                    Type="BUNDLE",

                    Version=0
                }
        };
        yield return new object[]
{
    new ProductRequest
    {
        Availability = null,  // Test the handling of null values
        Code = "Test",
        Type = "BUNDLE",
        Description = "Description with null availability",
        Version = 0
    }
};

        yield return new object[]
        {
    new ProductRequest
    {
        Availability = "INVALID_AVAILABILITY",  // Test invalid availability input
        Code = "Test",
        Type = "BUNDLE",
        Description = "Invalid availability",
        Version = 0
    }
        };
    }

    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }
}
