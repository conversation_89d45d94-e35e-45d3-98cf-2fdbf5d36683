﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_10_24_2100)]
public class ProductInstance_Set_IsSubscriptionAutoDebitEnabled_To_False : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
            UPDATE pi
            SET pi.Metadata = JSON_MODIFY(pi.Metadata, '$.IsSubscriptionAutoDebitEnabled', CAST(0 AS BIT))
            FROM [dbo].[ProductInstances] pi
            INNER JOIN [dbo].[Products] p ON p.Id = pi.ProductId
            WHERE pi.Metadata IS NOT NULL AND p.[Type] = 'GWAY' AND ISJSON(pi.Metadata) = 1"
        );
    }
}