﻿using Common.Entities;
using Common.Models;
using Common.Models.Search;
using Common.Models.TerminalDataSets;
using Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services;

public interface ITerminalDataSetService
{
    Task<SearchResponse<TerminalDataSetSearchResponse>> AdvancedSearchAsync(TerminalDataSetSearchRequest terminalDataSetRequest);
    Task<List<TerminalDataSet>> CreateAsync(List<TerminalDataSet> terminalDataSets);
    Task<List<TerminalDataSet>> PatchAsync(List<TerminalDataSetPatchRequest> terminalDataSetPatchRequest);
    Task<TerminalsCountResponse> GetAvailableTerminalDataSetCountAsync(string acquiringLedger);
    Task<List<TerminalDataSetValidationRequiredFields>> GetValidationRequiredFieldsAsync(List<TerminalDataSet> terminalDataSet);
    Task<TerminalDataSetSearchResponse> GetTerminalDataByIdAsync(Guid terminalDataId);
    Task<List<TerminalDataSet>> GetOrderMigrationValidationRequiredFieldsAsync(List<TerminalDataSet> terminalDataSet);
    Task<List<TerminalDataSet>> GetTerminalDataByProductInstanceId(Guid[] productInstanceIds);
    Task<List<TerminalDataSetsResponse>> GenerateTIDAndMIDAndAddEditTerminalDataSets(TerminalDataSetsRequest terminalDataSets);
    Task<List<TerminalDataSetsResponse>> GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(TerminalDataSetsMidTidRequest terminalDataSetsMidTid);
    Task<TerminalDataSetsResponse> GenerateTIDAndAddEditTerminalDataSet(TerminalDataSet terminalDataSet);
    Task<string> GenerateMIDAndAddEditTerminalDataSet(TerminalDataSetMidRequest terminalDataSetMidRequest);
    Task<List<TerminalDataSetsResponse>> AddUpdateTerminalDataSetMcc(TerminalDataSetsMidTidRequest terminalDataSetsRequest);
    Task<string> GetNewTIDSequence();
    Task<List<OrderTid>> GetTidByOrderId(List<string?> orderNumber);

}