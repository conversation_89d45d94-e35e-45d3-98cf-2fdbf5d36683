﻿using Common.Enums.ProductCommisssionPrices;
using Common.Enums.UnitPrice;
using System;
using System.Collections.Generic;

namespace Common.Models.NonTransactionalPrice;
public class NonTransactionalFeePriceCreateRequest
{
    public List<Guid> ProductIds { get; set; } = new List<Guid>();
    public List<Guid> MccIds { get; set; } = new List<Guid>();
    public List<Guid> BusinessTypeIds { get; set; } = new List<Guid>();
    public List<Guid> NonTransFeeId { get; set; } = new List<Guid>();
    public decimal FeeValue { get; set; }
    public FeeType FeeType { get; set; }
    public BillingType BillingType { get; set; }
    public PriceBillingFrequency BillingFrequency { get; set; }
}

