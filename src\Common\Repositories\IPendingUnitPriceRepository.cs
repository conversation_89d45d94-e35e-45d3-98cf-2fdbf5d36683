﻿using Common.Entities;
using Common.Models.PendingUnitPrice;
using Common.Models.UnitPrice;
using Common.Views;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Common.Repositories;
public interface IPendingUnitPriceRepository : IRepository<Guid, PendingUnitPriceEntity>
{
    Task<List<PendingUnitPriceEntity>> GetExistPendingUnitPrices(Expression<Func<PendingUnitPriceEntity, bool>> predicate);
    Task<PendingUnitPricesListResponse> GetPendingUnitPricesList(PendingUnitPricesListRequest request);
    Task AddRange(IEnumerable<PendingUnitPriceEntity> entities);
    IExecutionStrategy CreateExecutionStrategy();
}
