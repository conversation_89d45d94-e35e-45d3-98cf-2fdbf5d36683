﻿using Common.Enums;
using Common.Enums.UnitPrice;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace Common.Models.UnitPrice;
public class UnitPricesListRequest
{
    [DefaultValue(Constants.UnitPriceSearchDictionary)]
    public Dictionary<UnitPriceSearchKey, string> SearchTerms { get; set; } = new Dictionary<UnitPriceSearchKey, string>();
    public List<Guid>? FilterByProducts { get; set; }
    public List<Guid>? FilterByMccType { get; set; }
    public List<Guid>? FilterByBusinessType { get; set; }
    public List<VatType>? FilterByVatType { get; set; }
    [DefaultValue("desc")]
    public string OrderType { get; set; } = SortType.desc.ToString();
    [DefaultValue("productDisplayOrder")]
    public string OrderFieldName { get; set; } = "productDisplayOrder";
    [DefaultValue(1)]
    public int Page { get; set; } = 1;
    [DefaultValue(10)]
    public int Size { get; set; } = 10;
}
public enum UnitPriceSearchKey
{
    All,
    ProductName,
    ProductCode,
    MccName,
    MccCode,
    BusinessType
}