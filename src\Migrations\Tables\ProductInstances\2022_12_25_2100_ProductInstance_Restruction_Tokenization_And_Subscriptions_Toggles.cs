﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_12_25_2100)]
public class ProductInstance_Restruction_Tokenization_And_Subscriptions_Toggles : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
	        UPDATE pi
            SET pi.Metadata =  JSON_MODIFY(pi.Metadata,'$.IsMerchantInitiatedTransactionsEnabled',
            JSON_VALUE(pi.Metadata,'$.IsSubscriptionAutoDebitEnabled'))
            FROM [dbo].[ProductInstances] pi
            INNER JOIN [dbo].[Products] p ON p.Id = pi.ProductId
            WHERE pi.Metadata IS NOT NULL AND p.[Type] = 'GWAY' AND ISJSON(pi.Metadata) = 1 "
        );

        Execute.Sql(@"
	          UPDATE pi
            SET pi.Metadata =  JSON_MODIFY(pi.Metadata,'$.IsUnscheduledPaymentsEnable',CAST(0 as bit))
            FROM [dbo].[ProductInstances] pi
            INNER JOIN [dbo].[Products] p ON p.Id = pi.ProductId
            WHERE pi.Metadata IS NOT NULL AND p.[Type] = 'GWAY' AND ISJSON(pi.Metadata) = 1"
       );

        Execute.Sql(@"
	          UPDATE pi
            SET pi.Metadata =  JSON_MODIFY(pi.Metadata,'$.IsCustomerInitiatedTransactionsEnabled',
            JSON_VALUE(pi.Metadata,'$.IsTokenizationEnabled'))
            FROM [dbo].[ProductInstances] pi
            INNER JOIN [dbo].[Products] p ON p.Id = pi.ProductId
            WHERE pi.Metadata IS NOT NULL AND p.[Type] = 'GWAY' AND ISJSON(pi.Metadata) = 1"
        );

        Execute.Sql(@"
	        UPDATE pi
            SET pi.Metadata =  JSON_MODIFY(JSON_MODIFY(pi.Metadata,'$.IsRecurringPaylinksEnabled',
            JSON_VALUE(pi.Metadata,'$.IsSubscriptionPayLinkEnabled')),'$.IsSubscriptionPayLinkEnabled', NULL)
            FROM [dbo].[ProductInstances] pi
            INNER JOIN [dbo].[Products] p ON p.Id = pi.ProductId
             WHERE pi.Metadata IS NOT NULL AND p.[Type] = 'GWAY' AND ISJSON(pi.Metadata) = 1"
        );

        Execute.Sql(@"
	        UPDATE pi
            SET pi.Metadata =  JSON_MODIFY(JSON_MODIFY(pi.Metadata,'$.IsRecurringPaymentsEnabled',
            JSON_VALUE(pi.Metadata,'$.IsSubscriptionAutoDebitEnabled')),'$.IsSubscriptionAutoDebitEnabled', NULL)
            FROM [dbo].[ProductInstances] pi
            INNER JOIN [dbo].[Products] p ON p.Id = pi.ProductId
            WHERE pi.Metadata IS NOT NULL AND p.[Type] = 'GWAY' AND ISJSON(pi.Metadata) = 1"
       );
    }
}