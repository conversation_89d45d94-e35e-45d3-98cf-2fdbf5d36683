﻿using System;
using System.Diagnostics.CodeAnalysis;
using System.Text;
using System.Text.Json;
using Common.Models;
using Common.Services;
using Geidea.Messages.Base;
using Geidea.Utils.Json;
using GeideaPaymentGateway.Utils.RabbitMQ;
using Microsoft.Extensions.Logging;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using IConnectionFactory = GeideaPaymentGateway.Utils.RabbitMQ.IConnectionFactory;

namespace Messaging;
public class OrderMigrationSyncScriptClient : MessageClient, IOrderMigrationSyncScriptClient
{
    public const string ExchangeName = "MMS.OrderMigrationSync.Exchange";
    public const string QueueName = "MMS.OrderMigrationSync.Request";
    public const string RoutingKey = "";

    private readonly ILogger<OrderMigrationSyncScriptClient> logger;

    public event EventHandler<CreateOrderMigrationSyncScriptMessageReceivedRequestEventArgs>? OnOrderMigrationTriggerSyncScript;

    public OrderMigrationSyncScriptClient(
        IConnectionFactory connectionFactory,
        ILogger<OrderMigrationSyncScriptClient> logger) : base(connectionFactory)
    {
        this.logger = logger;
    }

    public void Connect()
    {
        SetupConnection();
        SetupChannel();

        EventingBasicConsumer? consumer = new EventingBasicConsumer(Channel);
        consumer.Received += OnOrderMigrationAndTerminalDataSetSync;

        Channel.BasicConsume(QueueName, true, consumer);
    }

    [ExcludeFromCodeCoverage]
    private void SetupChannel()
    {
        Channel.ExchangeDeclare(ExchangeName, ExchangeType.Direct, true);
        Channel.QueueDeclare(QueueName, true, false, false);
        Channel.QueueBind(QueueName, ExchangeName, RoutingKey);
    }

    [ExcludeFromCodeCoverage]
    private void OnOrderMigrationAndTerminalDataSetSync(object? sender, BasicDeliverEventArgs args)
    {
        try
        {
            var json = Encoding.UTF8.GetString(args.Body.ToArray());
            var orderMigrationSyncTriggerMessageReceived = Json.Deserialize<GenericMessage<CreateOrderMigrationSyncScriptRequest>>(json);

            logger.LogInformation("OrderMigrationSync received message client");

            if (orderMigrationSyncTriggerMessageReceived == null)
                throw new JsonException("Invalid JSON encountered, resulting is deserialization result of null");

            OnOrderMigrationTriggerSyncScript?.Invoke(this, new CreateOrderMigrationSyncScriptMessageReceivedRequestEventArgs(orderMigrationSyncTriggerMessageReceived.Data));
        }
        catch (Exception ex)
        {
            logger.LogError("OrderMigrationSync client threw an error while trying to process the message! {@ex}", ex);
        }
    }
}
