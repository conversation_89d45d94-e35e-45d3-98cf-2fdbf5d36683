﻿using Common;
using Common.Entities;
using Common.Services.Acquirers;
using Geidea.Utils.Exceptions;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using static Common.Constants;
using GeideaPaymentUtils = GeideaPaymentGateway.Utils.RandomSequenceGenerator;

namespace Services.Acquirers;
public class AlxAcquirer : Acquirer, IAcquirer
{
    public string GenerateUniqueMID(string? maxMID)
    {
        if (string.IsNullOrEmpty(maxMID))
        {
            return RandomSequenceGenerator.ALXMIDInitialValue;
        }

        if (maxMID.Length != RandomSequenceGenerator.ALXMIDInitialValue.Length ||
           !maxMID.StartsWith(RandomSequenceGenerator.ALXMIDStaticPart) ||
           !int.TryParse(maxMID.AsSpan(3), out int dynamicPart))
        {
            throw new ServiceException(Errors.ALXMIDFormat);
        }

        IsReachedToMax(maxMID, RandomSequenceGenerator.ALXMIDLastValue);

        dynamicPart += RandomSequenceGenerator.ALXMIDIncrementalValue;

        return $"{RandomSequenceGenerator.ALXMIDStaticPart}{string.Concat(Enumerable.Repeat('0', 5 - dynamicPart.ToString().Length))}{dynamicPart}";
    }

    public string GenerateUniqueTID(IQueryable<TerminalDataSetEntity> terminalDataSetEntities, List<string> generatedTidsInMemory, string counterparty, string? merchantTag)
    {
        var generator = new GeideaPaymentUtils.RandomSequenceGenerator(RandomSequenceGenerator.ALXTIDAllowedChars.ToCharArray());

        while (true)
        {
            var tid = generator.GenerateWithFormat(GetAlXTIDFormatBasedOnMerchantTag(merchantTag)).ToUpper();

            if (!IsTIDExist(terminalDataSetEntities, tid, counterparty) && !generatedTidsInMemory.Contains(tid))
            {
                return tid;
            }
        }
    }
    public string GenerateUAEUniqueMID(string? maxMID, string? BusinessId, string? CardChannelType)
    {
        if (string.IsNullOrEmpty(maxMID))
        {
            return ModelMIdSequenceGenerator.GeIdeaMIDInitialValue;
        }

        if (!int.TryParse(maxMID.AsSpan(6), out int dynamicPart))
        {
            throw new ServiceException(Errors.MIDMustBeNumbers);
        }

        if (maxMID.AsSpan(6, 3).ToString() == "999") // take the third 3 numbers 001 770 999 001 which is 999
        {
            IsReachedToMax(maxMID, RandomSequenceGenerator.NBEMIDLastValue);

            string newSet = $"000{int.Parse(maxMID.AsSpan(9)) + 1}"; // take the last 3 numbers 001770 999 001 which is 001

            return $"{RandomSequenceGenerator.NBEMIDStaticPart}{string.Concat(Enumerable.Repeat('0', 6 - newSet.ToString().Length))}{newSet}";
        }

        dynamicPart += RandomSequenceGenerator.NBEMIDIncrementalValue;
        return $"{RandomSequenceGenerator.NBEMIDStaticPart}{string.Concat(Enumerable.Repeat('0', 6 - dynamicPart.ToString().Length))}{dynamicPart}";
    }
    public string GenerateUniqueUAETID(IQueryable<TerminalDataSetEntity> terminalDataSetEntities, List<string> generatedTidsInMemory, string counterparty, string? merchantTag, string? Model, string? acquirerBank)
    {
        var generator = new GeideaPaymentUtils.RandomSequenceGenerator(RandomSequenceGenerator.NBETIDAllowedChars.ToCharArray());
        string? strmaxTID = string.Empty;
        if (!string.IsNullOrEmpty(Model))
        {
            var maxTID = terminalDataSetEntities.AsNoTracking()
             .Where(c => c.Counterparty == counterparty && /*c.AcquiringLedger == acquirerBank &&*/ c.ConfigDate != null && c.TID != null)
             .Select(c => new { c.TRSM, c.ConfigDate })
             .GroupBy(c => c.TRSM)
             .Select(c => new { TID = c.Max(m => m.TRSM), ConfigDate = c.Min(m => m.ConfigDate) })
             .OrderByDescending(c => c.TID)
             .Select(c => c.TID)
             .FirstOrDefaultAsync();

            if (maxTID.Result != null)
                strmaxTID = "00" + maxTID.Result.ToString();


        }

        while (true)
        {
            string TIDFormat = GetTIDFormatBasedOnModel(Model);
            var tid = TIDFormat + GenerateUniqueUAETID(strmaxTID);

            if (!IsTIDExist(terminalDataSetEntities, tid, counterparty) && !generatedTidsInMemory.Contains(tid))
            {
                return tid;
            }
            else
            {
                throw new ServiceException(Errors.ErrorInTerminalId);
            }
        }
    }
    public string GenerateUniqueUAETID(string? maxTID)
    {
        if (string.IsNullOrEmpty(maxTID))
        {
            return ModelTIdSequenceGenerator.NBETIDInitialValue;
        }

        if (maxTID.Substring(2, 6).ToString() == "999999")
        {
            IsUAEReachedToMax(maxTID, ModelTIdSequenceGenerator.NBETIDLastValue);
        }
        int maxumumTID = Convert.ToInt32(maxTID) + 1;
        string UpdateTID = string.Empty;
        if (maxumumTID.ToString().Length == 1)
            UpdateTID = "0000000" + maxumumTID.ToString();
        else if (maxumumTID.ToString().Length == 2)
            UpdateTID = "000000" + maxumumTID.ToString();
        else if (maxumumTID.ToString().Length == 3)
            UpdateTID = "00000" + maxumumTID.ToString();
        if (maxumumTID.ToString().Length == 4)
            UpdateTID = "0000" + maxumumTID.ToString();
        if (maxumumTID.ToString().Length == 5)
            UpdateTID = "000" + maxumumTID.ToString();
        if (maxumumTID.ToString().Length == 6)
            UpdateTID = "00" + maxumumTID.ToString();
        if (maxumumTID.ToString().Length == 7)
            UpdateTID = "" + maxumumTID.ToString();

        return UpdateTID.ToString().Substring(2, 6).ToString();

    }

    private static string GetTIDFormatBasedOnModel(string? model)
    {
        return model switch
        {
            Model.Sunmi => ModelSequenceGenerator.SunmiSequenceGenerator,
            Model.Ingenico => ModelSequenceGenerator.IngenicoSequenceGenerator,
            Model.PGW => ModelSequenceGenerator.PGWSequenceGenerator,
            _ => ModelSequenceGenerator.DefaultSequenceGenerator
        };
    }

    private static string GetAlXTIDFormatBasedOnMerchantTag(string? merchantTag)
    {
        return merchantTag switch
        {
            MerchantTag.Chain => RandomSequenceGenerator.ALXTIDChainFormat,
            MerchantTag.Retial => RandomSequenceGenerator.ALXTIDRetailOrMultiStore,
            MerchantTag.Wholesaler => RandomSequenceGenerator.ALXTIDWholesalerOrSubWholesaler,
            MerchantTag.SubWholesaler => RandomSequenceGenerator.ALXTIDWholesalerOrSubWholesaler,
            MerchantTag.MasterBusiness => RandomSequenceGenerator.ALXTIDMasterBusinessOrSubBusiness,
            MerchantTag.SubBusiness => RandomSequenceGenerator.ALXTIDMasterBusinessOrSubBusiness,
            MerchantTag.MultiStore => RandomSequenceGenerator.ALXTIDRetailOrMultiStore,
            _ => RandomSequenceGenerator.ALXTIDRetailOrMultiStore
        };
    }
}
