﻿using Common.Enums;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Common.Data.Extensions;
[ExcludeFromCodeCoverage]

public static class QueryableExtensions
{
    public static IQueryable<T> OrderBy<T>(this IQueryable<T> source, string orderBy, SortType orderDirection = SortType.asc)
    {
        if (source == null)
            throw new ArgumentNullException(nameof(source), "Source is null.");

        if (string.IsNullOrEmpty(orderBy))
            throw new ArgumentException("OrderBy parameter is null or empty.", nameof(orderBy));

        bool isDescending = orderDirection == SortType.desc;

        // Create the lambda expression dynamically
        var parameter = Expression.Parameter(typeof(T), "x");
        var property = typeof(T).GetProperty(orderBy, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance); if (property == null)
            throw new ArgumentException($"No property '{orderBy}' found on type '{typeof(T).Name}'.");

        var propertyAccess = Expression.MakeMemberAccess(parameter, property);
        var orderByExpression = Expression.Lambda(propertyAccess, parameter);

        // Create the expression tree for the OrderBy method call
        var orderByMethod = isDescending ? "OrderByDescending" : "OrderBy";
        var orderByCallExpression = Expression.Call(
            typeof(Queryable),
            orderByMethod,
            new Type[] { typeof(T), property.PropertyType },
            source.Expression,
            Expression.Quote(orderByExpression));

        // Create and return the IQueryable instance with the ordering applied
        return source.Provider.CreateQuery<T>(orderByCallExpression);
    }

}
