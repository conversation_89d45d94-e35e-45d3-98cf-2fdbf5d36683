﻿using Common.Entities;
using Common.Models;
using Common.Repositories;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ProductService.Controllers;

[Route("api/v1/[controller]")]
[ApiController]
public class ProductController : ControllerBase
{
    private readonly IProductService productService;
    private readonly IProductRepository productRepository;

    public ProductController(
        IProductService productService,
        IProductRepository productRepository
        )
    {
        this.productService = productService;
        this.productRepository = productRepository;
    }

    /// <summary>
    /// Returns a product by its unique identifier.
    /// </summary>
    /// <response code="200">Product details</response>
    /// <response code="404">No valid product entry with the specified id was found</response> 
    [HttpGet]
    [Route("{id}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Product), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetById(Guid id)
    {
        var result = await productService.FindAsync(new FindProductRequest { Id = id }, false);

        if (result.Length == 0)
        {
            return NotFound();
        }

        return Ok(result.Single());
    }

    /// <summary>
    /// Returns all products matching the search criteria.
    /// </summary>
    /// <response code="200">Search was successfull</response>
    [HttpGet]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Product[]), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Find([FromQuery] FindProductRequest request)
    {
        var result = await productService.FindAsync(request, false);
        return Ok(result);
    }

    /// <summary>
    /// Returns all products with info matching the search criteria.
    /// </summary>
    /// <response code="200">Search was successfull</response>
    [HttpPost]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Product[]), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> FindWithInfo([FromBody] FindProductRequest request)
    {
        var result = await productService.FindWithInfoAsync(request, false);
        return Ok(result);
    }

    /// <summary>
    /// Create new product to be added in AdminPanel
    /// </summary>
    /// <returns>Returns the created product</returns>
    [HttpPost("CreateProduct")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Product), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateAsync([FromBody] ProductRequest request)
    {
        var createdProduct = await productService.CreateAsync(request);
        return Ok(createdProduct);
    }
    /// <summary>
    /// Update product corresponding to the requested ids.
    ///</summary>
    /// <response code="200">updates was successfull</response>
    [HttpPut("UpdateProduct/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Product), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateAsync(Guid id, [FromBody] ProductRequest productRequest)
    {
        var updatedProduct = await productService.UpdateAsync(id, productRequest);

        return Ok(updatedProduct);
    }

    /// <summary>
    /// Returns the products corresponding to the requested ids.
    /// </summary>
    /// <response code="200">Search was successfull</response>
    [HttpPost("ids")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Product[]), StatusCodes.Status200OK)]
    public async Task<IActionResult> FindByIds([FromBody] IdsRequest request)
    {
        var result = await productService.FindByIdsAsync(request);
        return Ok(result);
    }

    /// <summary>
    /// Get products that have specified product code or contain such a product (this include obsolete products).
    /// </summary>
    /// <response code="200">Returns array with ids for all corresponding products.</response>

    [HttpPost("related")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public IActionResult GetRelatedProducts([FromBody] ProductCodesRequest productCodesRequest)
    {
        return Ok(productService.GetRelatedProductsIds(productCodesRequest));
    }

    [HttpPost("productIds")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetProductIdsFromCodes([FromBody] ProductCodesRequest productCodesRequest)
    {
        return Ok(await productRepository.GetProductIdsFromCodes(productCodesRequest));
    }

    /// <summary>
    /// Returns if a list of product IDs contain BP bundle or service
    /// </summary>
    /// <response code="200">Flags for BP bundle and service</response>
    [HttpPost("hasBillPayment/ids")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(BillPaymentServiceAndBundleFlags), StatusCodes.Status200OK)]
    public async Task<IActionResult> ProductsContainBillPaymentType([FromBody] IdsRequest request)
    {
        return Ok(await productService.ProductsContainBillPaymentTypeAsync(request));
    }

    [HttpGet("GetProductsDetails/{TID}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetProductDetails(string TID)
    {
        var result = await productService.GetProductsDetails(TID);
        return Ok(result);
    }

    [HttpGet("GetProductOnBusiness/{MID}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Product), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetProductOnBusiness(string MID)
    {
        var result = await productService.GetProductsOnBusinessByMID(MID);
        return Ok(result);
    }
    /// <summary>
    /// Get a list of products with only needed data to be displayed in AdminPanel
    /// </summary>
    /// <param name="request">AdminPanelProductRequest, Which contains the needed params for searching,filtering and pagination processes</param>
    /// <returns>Returns a list of products with only needed data to be displayed in AdminPanel</returns>
    [HttpPost("GetProductsList", Name = "GetProductsList")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(ProductListResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetProductsList([FromBody] GetProductsListRequest request)
    {
        var productListResponse = await productService.GetProductsList(request);
        return Ok(productListResponse);
    }

    [HttpGet("GetProductOnAccount/{accountID}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Product), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetProductOnAccount(string accountID)
    {
        var result = await productService.GetProductsOnAccountByaccountID(accountID);
        return Ok(result);
    }

    [HttpGet("GetTerminalDataSet/{MID}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Product), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetTerminalDataSet(string MID)
    {
        var result = await productService.GetTerminalDataSetByMID(MID);
        return Ok(result);
    }

    [HttpGet("GetMultipleProductOnAccount/{accountID}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Product), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetMultipleProductOnAccount(string accountID)
    {
        var result = await productService.GetMultipleProductsOnAccountByaccountID(accountID);
        return Ok(result);
    }

    [HttpGet("GetProductsNames")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(List<BasicProductsInfo>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetProductsNames()
    {
        var productsNames = await productService.GetProductsNames();
        return Ok(productsNames);
    }

    [HttpGet("GetProductDetails/{ProductId}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(ProductEntity), statusCode: StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetProductDetails(Guid ProductId)
    {
        var Product = await productService.GetProductDetails(ProductId);

        return Ok(Product);
    }

    [HttpGet("GetProductsListByChannel")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(ProductEntity), statusCode: StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetProductsListByChannel(bool isCNP)
    {
        var Product = await productService.GetProductsListByChannel(isCNP);

        return Ok(Product);
    }
}
