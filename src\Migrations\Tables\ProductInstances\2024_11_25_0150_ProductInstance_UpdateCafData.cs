﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2024_11_25_0150)]
public class ProductInstance_UpdateCafData : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
            Update PRI 
            set PRI.Metadata = JSON_MODIFY(PRI.Metadata,'$.ExtraChargesPbl', JSON_QUERY('{""IsEnabled"": false, ""Charges"": {}}'))
            from [dbo].[ProductInstances] PRI
            join [dbo].Products P
            on PRI.ProductId = P.Id
            WHERE PRI.[Metadata] IS NOT NULL
            AND p.Type = 'GWAY'
            AND ISJSON([Metadata]) > 0;
        ");

        Execute.Sql(@"
            Update PRI 
            set PRI.Metadata = JSON_MODIFY(PRI.Metadata,'$.ExtraChargesPbl.IsEnabled', CAST(JSON_VALUE(PRI.[Metadata], '$.ExtraCharges.IsExtraChargesEnabled') as BIT))
            from [dbo].[ProductInstances] PRI
            join [dbo].Products P
            on PRI.ProductId = P.Id
            WHERE PRI.[Metadata] IS NOT NULL
            AND p.Type = 'GWAY'
            AND ISJSON([Metadata]) > 0;
        ");

        Execute.Sql(@"
            Update PRI 
            set PRI.Metadata = JSON_MODIFY(PRI.Metadata,'$.ExtraChargesPbl.Charges', JSON_QUERY(PRI.[Metadata], '$.ExtraCharges.PBLExtraCharges'))
            from [dbo].[ProductInstances] PRI
            join [dbo].Products P
            on PRI.ProductId = P.Id
            WHERE PRI.[Metadata] IS NOT NULL
            AND p.Type = 'GWAY'
            AND ISJSON([Metadata]) > 0;
        ");

        Execute.Sql(@"
            Update PRI 
            set PRI.Metadata = JSON_MODIFY(PRI.Metadata,'$.AddOnFeesPbl', JSON_QUERY('{""IsEnabled"": false, ""Fees"": {}}'))
            from [dbo].[ProductInstances] PRI
            join [dbo].Products P
            on PRI.ProductId = P.Id
            WHERE PRI.[Metadata] IS NOT NULL
            AND p.Type = 'GWAY'
            AND ISJSON([Metadata]) > 0;
        ");

        Execute.Sql(@"
            Update PRI 
            set PRI.Metadata = JSON_MODIFY(PRI.Metadata,'$.AddOnFeesPbl.IsEnabled', CAST(JSON_VALUE(PRI.[Metadata], '$.AddOnFees.IsAddOnFeesEnabled') as BIT))
            from [dbo].[ProductInstances] PRI
            join [dbo].Products P
            on PRI.ProductId = P.Id
            WHERE PRI.[Metadata] IS NOT NULL
            AND p.Type = 'GWAY'
            AND ISJSON([Metadata]) > 0;
        ");

        Execute.Sql(@"
            Update PRI 
            set PRI.Metadata = JSON_MODIFY(PRI.Metadata,'$.AddOnFeesPbl.Fees', JSON_QUERY(PRI.[Metadata], '$.AddOnFees.PBLAddOnFees'))
            from [dbo].[ProductInstances] PRI
            join [dbo].Products P
            on PRI.ProductId = P.Id
            WHERE PRI.[Metadata] IS NOT NULL
            AND p.Type = 'GWAY'
            AND ISJSON([Metadata]) > 0;
        ");

        Execute.Sql(@"
            Update PRI 
            set PRI.Metadata = JSON_MODIFY(PRI.Metadata,'$.ExtraChargesHpp', JSON_QUERY('{""IsEnabled"": false, ""Charges"": {""Label"": """",""Value"": 0,""ValueType"": ""Amount""}}'))
            from [dbo].[ProductInstances] PRI
            join [dbo].Products P
            on PRI.ProductId = P.Id
            WHERE PRI.[Metadata] IS NOT NULL
            AND p.Type = 'GWAY'
            AND ISJSON([Metadata]) > 0;
        ");

        Execute.Sql(@"
            Update PRI 
            set PRI.Metadata = JSON_MODIFY(PRI.Metadata,'$.AddOnFeesHpp', JSON_QUERY('{""IsEnabled"": false, ""Fees"": {""Label"": """",""Value"": 0,""ValueType"": ""Amount""}}'))
            from [dbo].[ProductInstances] PRI
            join [dbo].Products P
            on PRI.ProductId = P.Id
            WHERE PRI.[Metadata] IS NOT NULL
            AND p.Type = 'GWAY'
            AND ISJSON([Metadata]) > 0;
        ");
    }
}
