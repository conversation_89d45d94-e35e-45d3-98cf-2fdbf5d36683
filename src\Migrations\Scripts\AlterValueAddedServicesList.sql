CREATE OR ALTER VIEW [dbo].[ValueAddedServicePricingList]
AS
    SELECT 
        vasp.Id,
        p.Id AS ProductId,
        p.Name AS ProductName,
        m.Id AS MccId,
        m.Name AS MccName,
        b.Id AS BusinessTypeId,
        b.Name AS BusinessType,
        vas.Id AS ValueAddedServiceId,
        vas.Name AS ValueAddedServiceName,
        vas.NameAr AS ValueAddedServiceNameAr,
        vas.Code AS VASCode,
        vas.Description AS ValueAddedServiceDescription,
        vas.DescriptionAr AS ValueAddedServiceDescriptionAr,
        vas.ImageId AS ValueAddedServiceImageId,        
        vas.Status AS ValueAddedServiceStatus,           
        vasp.SubscriptionFee,
        vasp.FeeType,
        vasp.BillingType,
        vasp.BillingFrequency,
        p.Code AS ProductCode,
        m.Code AS MccCode,
        mc.Name AS MccCategory,
        vasp.CreatedDate
    FROM 
        [dbo].[ValueAddedServicePricing] vasp
        JOIN [dbo].[Products] p ON vasp.ProductID = p.Id
        JOIN [dbo].[BusinessTypes] b ON vasp.BusinessTypeID = b.Id
        JOIN [dbo].[Mcc] m ON vasp.MCCID = m.Id
        JOIN [dbo].[MccCategory] mc ON m.MccCategoryId = mc.Id
        JOIN [dbo].[ValueAddedServices] vas ON vasp.VASID = vas.Id;
GO