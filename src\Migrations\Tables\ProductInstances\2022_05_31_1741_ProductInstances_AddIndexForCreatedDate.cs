﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_05_31_1741)]
public class ProductInstances_AddIndexForCreatedDate : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
IF NOT EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'IDX_PRODUCTINSTANCES_CREATEDDATE' AND object_id = OBJECT_ID('[dbo].[ProductInstances]'))
BEGIN
	CREATE NONCLUSTERED INDEX [IDX_PRODUCTINSTANCES_CREATEDDATE] ON [dbo].[ProductInstances] ([CreatedDate])
END
");
    }
}
