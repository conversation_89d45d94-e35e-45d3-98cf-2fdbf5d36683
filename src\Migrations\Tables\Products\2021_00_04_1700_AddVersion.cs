﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_00_04_1700)]
public class AddVersion : ForwardOnlyMigration
{
    public override void Up()
    {
        Alter.Table("Products").AddColumn("Version").AsInt32().NotNullable().WithDefaultValue(0);

        if (Schema.Table("Products").Constraint("UQ_Code").Exists())
        {
            Delete.UniqueConstraint("UQ_Code").FromTable("Products");
        }

        Create.UniqueConstraint("UQ_Code_Version")
              .OnTable("Products")
              .Columns("Code", "Version");

        Update.Table("Products").Set(new { Code = "AMEX_ONLINE", Version = "1" }).Where(new { Code = "AMEX_ONLINE_V1" });
        Update.Table("Products").Set(new { Code = "BUSINESS_RESTAURANT", Version = "2" }).Where(new { Code = "BUSINESS_RESTAURANT_V2" });
        Update.Table("Products").Set(new { Code = "BUSINESS_RESTAURANT", Version = "3" }).Where(new { Code = "BUSINESS_RESTAURANT_V3" });
        Update.Table("Products").Set(new { Code = "BUSINESS_RETAIL", Version = "2" }).Where(new { Code = "BUSINESS_RETAIL_V2" });
        Update.Table("Products").Set(new { Code = "BUSINESS_RETAIL", Version = "3" }).Where(new { Code = "BUSINESS_RETAIL_V3" });
        Update.Table("Products").Set(new { Code = "ENTERPRISE_RESTAURANT", Version = "2" }).Where(new { Code = "ENTERPRISE_RESTAURANT_V2" });
        Update.Table("Products").Set(new { Code = "ENTERPRISE_RETAIL", Version = "2" }).Where(new { Code = "ENTERPRISE_RETAIL_V2" });
        Update.Table("Products").Set(new { Code = "ENTERPRISE_RETAIL", Version = "3" }).Where(new { Code = "ENTERPRISE_RETAIL_V3" });
        Update.Table("Products").Set(new { Code = "GO_A920", Version = "2" }).Where(new { Code = "GO_A920_V2" });
        Update.Table("Products").Set(new { Code = "GO_A920", Version = "3" }).Where(new { Code = "GO_A920_V3" });
        Update.Table("Products").Set(new { Code = "GO_A920", Version = "4" }).Where(new { Code = "GO_A920_V4" });
        Update.Table("Products").Set(new { Code = "GO_AIR", Version = "2" }).Where(new { Code = "GO_AIR_V2" });
        Update.Table("Products").Set(new { Code = "GO_AIR", Version = "3" }).Where(new { Code = "GO_AIR_V3" });
        Update.Table("Products").Set(new { Code = "GO_LITE", Version = "2" }).Where(new { Code = "GO_LITE_V2" });
        Update.Table("Products").Set(new { Code = "GO_LITE", Version = "3" }).Where(new { Code = "GO_LITE_V3" });
        Update.Table("Products").Set(new { Code = "GO_MPOS", Version = "2" }).Where(new { Code = "GO_MPOS_V2" });
        Update.Table("Products").Set(new { Code = "GO_MPOS", Version = "3" }).Where(new { Code = "GO_MPOS_V3" });
        Update.Table("Products").Set(new { Code = "GO_MPOS", Version = "4" }).Where(new { Code = "GO_MPOS_V4" });
        Update.Table("Products").Set(new { Code = "GO_SMART", Version = "2" }).Where(new { Code = "GO_SMART_V2" });
        Update.Table("Products").Set(new { Code = "GO_SMART", Version = "3" }).Where(new { Code = "GO_SMART_V3" });
        Update.Table("Products").Set(new { Code = "MADA_POS", Version = "2" }).Where(new { Code = "MADA_POS_V2" });
        Update.Table("Products").Set(new { Code = "MADA", Version = "2" }).Where(new { Code = "MADA_V2" });
        Update.Table("Products").Set(new { Code = "MC_ONLINE", Version = "1" }).Where(new { Code = "MC_ONLINE" });
        Update.Table("Products").Set(new { Code = "PAYMENT_GATEWAY_BUNDLE", Version = "2" }).Where(new { Code = "PAYMENT_GATEWAY_BUNDLE_V2" });
        Update.Table("Products").Set(new { Code = "PAYMENT_GATEWAY_BUNDLE", Version = "3" }).Where(new { Code = "PAYMENT_GATEWAY_BUNDLE_V3" });
        Update.Table("Products").Set(new { Code = "PRO_SMART", Version = "2" }).Where(new { Code = "PRO_SMART_V2" });
        Update.Table("Products").Set(new { Code = "PRO_SMART", Version = "3" }).Where(new { Code = "PRO_SMART_V3" });
        Update.Table("Products").Set(new { Code = "WEBSITE_BUILDER_BUNDLE", Version = "2" }).Where(new { Code = "WEBSITE_BUILDER_BUNDLE_V2" });
        Update.Table("Products").Set(new { Code = "WEBSITE_BUILDER_BUNDLE", Version = "3" }).Where(new { Code = "WEBSITE_BUILDER_BUNDLE_V3" });
    }
}
