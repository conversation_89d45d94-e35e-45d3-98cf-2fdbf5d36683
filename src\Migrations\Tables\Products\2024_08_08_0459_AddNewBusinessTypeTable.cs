﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.Products;
[Migration(2024_08_11_0453)]
public class AddNewBusinessTypeTable : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
        AppDomain.CurrentDomain.BaseDirectory +
        Path.Combine("Scripts", "AddNewBusinessTypeTable.sql"));
    }
}