﻿using Common.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.ProductCommissionPrice;
public class ProductCommissionPriceResponse
{
    public List<ProductCommissionPriceEntity> NewCommissionPriceList { get; set; } = new List<ProductCommissionPriceEntity>();
    public List<ProductCommissionPriceEntity> NewExistedCommissionPriceList { get; set; } = new List<ProductCommissionPriceEntity>();
    public List<ProductCommissionPriceEntity>? OldExistedCommissionPriceList { get; set; } = new List<ProductCommissionPriceEntity>();
}
