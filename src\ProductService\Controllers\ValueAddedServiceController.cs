﻿using Common.Models;
using Common.Models.ValueAddedServices;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Services;
using System.Net;
using System;
using System.Threading.Tasks;
using Elastic.Apm.Api;
using Common.Models.businessType;
using System.Collections.Generic;

namespace ProductService.Controllers;
[Route("api/v1/[controller]")]
[ApiController]
public class ValueAddedServiceController : ControllerBase
{
    private readonly IValueAddedServiceService valueAddedServiceService;
    public ValueAddedServiceController(IValueAddedServiceService valueAddedServiceService)
    {
        this.valueAddedServiceService = valueAddedServiceService;
    }
    /// <summary>
    /// Retrieves a list of value-added services with optional filtering, sorting, and pagination.
    /// </summary>
    [HttpPost("GetAllValueAddedServices")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetValueAddedServiceListAsync(GetValueAddedServicesListRequest requestParams)
    {
        var valueAddedServiceResponses = await valueAddedServiceService.GetValueAddedServicesListAsync(requestParams);
        return Ok(valueAddedServiceResponses);
    }
    /// <summary>
    /// Create new value added service to be added in AdminPanel
    /// </summary>
    /// <returns>Returns the created value added service</returns>
    [HttpPost("CreateValueAddedService")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateAsync([FromBody] ValueAddedServiceRequest request)
    {

        var createdValueAddedService = await valueAddedServiceService.CreateAsync(request);

        return Ok(createdValueAddedService);

    }
    /// <summary>
    /// Update an existing value added service in AdminPanel
    /// </summary>
    /// <param name="id">The ID of the value added service to update</param>
    /// <param name="request">The updated value added service details</param>
    /// <returns>Returns the updated value added service</returns>
    [HttpPut("UpdateValueAddedService/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateAsync(Guid id, [FromBody] ValueAddedServiceRequest request)
    {

        var updatedValueAddedService = await valueAddedServiceService.UpdateAsync(id, request);
        return Ok(updatedValueAddedService);
    }

    /// <summary>
    /// Get details of a value added service by ID.
    /// </summary>
    /// <param name="id">The ID of the value added service to retrieve</param>
    /// <returns>Returns the value added service details</returns>
    [HttpGet("GetValueAddedServicesDetailsAsync/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetValueAddedServicesDetailsAsync(Guid id)
    {
        var valueAddedServiceDetails = await valueAddedServiceService.GetValueAddedServicesDetailsAsync(id);
        return Ok(valueAddedServiceDetails);

    }
    /// <summary>
    /// Set the status of a value added service by ID.
    /// </summary>
    /// <param name="id">The ID of the value added service to update</param>
    /// <param name="statusRequest">The status request containing the new status</param>
    /// <returns>Returns no content if successful, or not found if the service does not exist</returns>
    [HttpPost("status/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> SetStatus(Guid id, [FromBody] StatusRequest statusRequest)
    {
        var result = await valueAddedServiceService.SetStatusAsync(id, statusRequest.IsActive);
        if (!result)
        {
            return NotFound();
        }

        return NoContent();
    }

    /// <summary>
    /// get Vas  Ids , Names ;
    /// </summary>
    [HttpGet("GetValueAddedServiceNames")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(List<BasicValueAddedServiceInfo>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetValueAddedServiceNamesAsync()
    {
        var valueAddedServiceServiceNames = await valueAddedServiceService.GetValueAddedServiceNamesAsync();
        return Ok(valueAddedServiceServiceNames);
    }
}
