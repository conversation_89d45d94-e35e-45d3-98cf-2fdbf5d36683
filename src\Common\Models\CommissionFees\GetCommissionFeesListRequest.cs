﻿using Common.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.CommissionFees;
[ExcludeFromCodeCoverage]

public class GetCommissionFeesListRequest
{
    [DefaultValue(Constants.CommissionFeesDictionary)]
    public Dictionary<CommissionFeesSearchKey, string> SearchTerms { get; set; } = new Dictionary<CommissionFeesSearchKey, string>();
    public string OrderType { get; set; } = SortType.desc.ToString();
    public string OrderFieldName { get; set; } = "CreatedDate";
    public List<Status>? FilterByStatus { get; set; }
    public int Page { get; set; } = 1;
    public int Size { get; set; } = 10;
}
public enum CommissionFeesSearchKey
{
    Id,
    Name,
    All
}
