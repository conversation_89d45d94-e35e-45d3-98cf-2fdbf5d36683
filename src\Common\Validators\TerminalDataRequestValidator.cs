﻿using Common.Attributes;
using Common.Entities;
using Common.Models.TerminalDataSets;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Common.Models.Validators;

public class TerminalDataRequestValidator : AbstractValidator<TerminalDataSetSearchRequest>
{
    public TerminalDataRequestValidator()
    {
        RuleFor(td => td.Sort).Must(x => string.Compare(x, "asc", StringComparison.OrdinalIgnoreCase) == 0 || string.Compare(x, "desc", StringComparison.OrdinalIgnoreCase) == 0)
            .WithMessage("Only 'asc' or 'desc' can be used.");
        RuleFor(td => td.OrderBy).SetValidator(new OrderByValidator());
        RuleFor(td => td.SearchIn).SetValidator(new SearchInValidator());
        RuleFor(td => td.Take).GreaterThan(0).WithMessage("Take should be greater than 0.");
        RuleFor(td => td.Skip).GreaterThanOrEqualTo(0).WithMessage("Skip should be greater than 0.");
    }

    private class OrderByValidator : AbstractValidator<string>
    {
        public OrderByValidator()
        {
            var nonQueryableAllowedProperties = new List<string> { "CreatedDate" };
            var allowedPropertiesNames = typeof(TerminalDataSetEntity).GetProperties()
                .Where(p => (p.GetCustomAttributes(typeof(QueryableAttribute), false).FirstOrDefault() as QueryableAttribute)?.CanOrder ?? false)
                .Select(p => p.Name).ToList();
            RuleFor(x => x).Must(x => nonQueryableAllowedProperties.Contains(x, StringComparer.OrdinalIgnoreCase) || allowedPropertiesNames.Contains(x, StringComparer.OrdinalIgnoreCase))
                .WithMessage($"Only {string.Join(",", allowedPropertiesNames)} can be used for ordering");
        }
    }

    private class SearchInValidator : AbstractValidator<string[]?>
    {
        public SearchInValidator()
        {
            var allowedPropertiesNames = typeof(TerminalDataSetEntity).GetProperties()
                .Where(p => (p.GetCustomAttributes(typeof(QueryableAttribute), false).FirstOrDefault() as QueryableAttribute)?.CanSearch ?? false)
                .Select(p => p.Name).ToList();
            RuleFor(values => values).Must(values => values!.All(value => allowedPropertiesNames.Contains(value, StringComparer.OrdinalIgnoreCase))).When(values => values is not null)
                .WithMessage($"Only {string.Join(",", allowedPropertiesNames)} can be used for searching");
        }
    }
}
