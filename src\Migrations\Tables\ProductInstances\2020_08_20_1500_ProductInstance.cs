﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2020_08_20_1500)]
public class ProductInstance : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("ProductInstances")
          .WithColumn("Id").AsGuid().PrimaryKey().WithDefaultValue(SystemMethods.NewSequentialId)
          .WithColumn("ProductId").AsGuid().NotNullable().ForeignKey("Products", "Id")
          .WithColumn("AgreementId").AsInt32().NotNullable()
          .WithColumn("StoreId").AsInt32().NotNullable()
          .WithColumn("ParentId").AsGuid().Nullable().ForeignKey("ProductInstances", "Id")
          .WithColumn("Metadata").AsString(int.MaxValue).Nullable()
          .WithColumn("DeletedFlag").AsBoolean().WithDefaultValue(false)
          .WithColumn("ValidFrom").AsDateTime2().Nullable()
          .WithColumn("ValidTo").AsDateTime2().Nullable()
          .WithColumn("CreatedBy").AsString(250).NotNullable()
          .WithColumn("UpdatedBy").AsString(250).Nullable()
          .WithColumn("CreatedDateUtc").AsDateTime2().NotNullable().WithDefaultValue(SystemMethods.CurrentUTCDateTime)
          .WithColumn("UpdatedDateUtc").AsDateTime2().Nullable();
    }
}
