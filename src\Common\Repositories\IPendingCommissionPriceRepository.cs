﻿using Common.Entities;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Common.Repositories;
public interface IPendingCommissionPriceRepository : IRepository<Guid, PendingComissionPriceEntity>
{
    Task<List<PendingComissionPriceEntity>> GetExistPendingCommissionPrices(Expression<Func<PendingComissionPriceEntity, bool>> predicate);
    Task AddRange(IEnumerable<PendingComissionPriceEntity> entities);
    IExecutionStrategy CreateExecutionStrategy();
}
