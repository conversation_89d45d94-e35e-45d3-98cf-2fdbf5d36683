﻿using Common.Enums;
using Common.Enums.ProductCommisssionPrices;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Views;
public class ProductCommissionPriceListView
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public string? ProductName { get; set; }
    public Guid MccId { get; set; }
    public string MccName { get; set; } = null!;
    public Guid BusinessTypeId { get; set; }
    public string BusinessType { get; set; } = null!;
    public string BusinessTypeCode { get; set; } = null!;
    public Guid FeeId { get; set; }
    public string FeeName { get; set; } = null!;
    public decimal FeeValue { get; set; }
    public string? FeeNameAr { get; set; }
    public Status FeeStatus { get; set; }
    public Guid? FeeImageId { get; set; }
    public string ProductCode { get; set; } = null!;
    public string MccCode { get; set; } = null!;
    public string MccCategory { get; set; } = null!;
    public CommisssionPricesBillingType BillingType { get; set; }
    public PriceBillingFrequency BillingFrequency { get; set; }
    public DateTime CreatedDate { get; set; }
    public string CommissionFeeCode { get; set; } = null!;
    public FeeType FeeType { get; set; }
}
