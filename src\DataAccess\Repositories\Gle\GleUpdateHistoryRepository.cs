﻿using Common;
using Common.Entities.Gle;
using Common.Models.Gle;
using Common.Repositories.Gle;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DataAccess.Repositories.Gle;
public class GleUpdateHistoryRepository : AuditableRepository<Guid, GleUpdateHistoryEntity>, IGleUpdateHistoryRepository
{
    #region Constructor
    public GleUpdateHistoryRepository(DbContext context, IHttpContextAccessor contextAccessor) : base(context, contextAccessor)
    {
    }
    #endregion

    #region Public Methods
    public async Task<List<GleUpdateHistoryEntity>?> FindGleUpdateHistoriesAsync(List<Guid> ids)
    {
        var result = await context.Set<GleUpdateHistoryEntity>()
                            .AsQueryable()
                            .Where(x => ids.Contains(x.Id)).ToListAsync();

        return result;
    }

    public async Task<GleUpdateHistoryEntity?> FindGleUpdateHistoryAsync(Guid merchantId, string requestType)
    {
        var result = await context.Set<GleUpdateHistoryEntity>()
                            .AsQueryable()
                            .OrderByDescending(x => x.CreatedDate)
                            .FirstOrDefaultAsync(x => x.MerchantId == merchantId && x.RequestType == requestType);

        return result;
    }

    public async Task AddGleUpdateHistoryAsync(List<GleUpdateHistoryEntity> entities)
    {
        context.Set<GleUpdateHistoryEntity>().AddRange(entities);
        await context.SaveChangesAsync();
    }

    public async Task UpdateGleUpdateHistoryAsync(List<GleUpdateHistoryEntity> entities)
    {
        context.Set<GleUpdateHistoryEntity>().UpdateRange(entities);
        await context.SaveChangesAsync();
    }
    #endregion
}
