﻿using System.Collections.Generic;
using static Geidea.Utils.Common.Constants;

namespace Common.Data.Helpers;

public static class CounterpartyHelper
{
    public static string? GetGatewayMpgsMsoProvider(string counterparty)
    {
        return counterparty switch
        {
            CounterpartySaudi => MpgsMsoProviders.Geidea,
            CounterpartyEgypt => MpgsMsoProviders.Bm,
            CounterpartyUae => MpgsMsoProviders.Magnati,
            _ => null
        };
    }

    public static string GetGatewayMerchantCountry(string counterparty)
    {
        return counterparty switch
        {
            CounterpartySaudi => Constants.MerchantCountries.Sau,
            CounterpartyEgypt => Constants.MerchantCountries.Egy,
            CounterpartyUae => Constants.MerchantCountries.Are,
            _ => string.Empty
        };
    }

    public static List<string> GetGatewayDefaultCurrency(string counterparty)
    {
        return counterparty switch
        {
            CounterpartySaudi => new() { Constants.AlphaCodes.Sar },
            CounterpartyEgypt => new() { Constants.AlphaCodes.Egp },
            CounterpartyUae => new() { Constants.AlphaCodes.Aed },
            _ => new()
        };
    }
}
