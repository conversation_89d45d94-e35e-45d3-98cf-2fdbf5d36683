﻿using Common.Entities;
using Common.Models;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Common.Repositories;

public interface IProductRepository : IRepository<Guid, ProductEntity>
{
    bool ExistsAsync(params Guid[] ids);
    IQueryable<ProductEntity> Query();
    Task<bool> IsProductUsedAsync(Guid productId);
    Task<ProductEntity?> GetByIdAsync(Guid productId);
    Task<ProductEntity[]> FindAsync(FindProductRequest request, bool attach);
    Task<ProductEntity[]> FindWithInfoAsync(FindProductRequest request, bool attach);
    Task<ProductEntity[]> FindByIdsAsync(IdsRequest request);
    Task HardDeleteProductAsync(ProductEntity product);
    Task BindPartAsync(Guid partId, Guid productId, int quantity);
    Task<bool> IsPartOfAsync(Guid partId, Guid productId);
    Task UnbindPartAsync(Guid partId, Guid productId);
    Task<ProductEntity[]> GetParentProducts(Guid partId);
    Task AddCategoryAsync(Guid productId, Guid categoryId);
    Task<bool> HasCategoryAsync(Guid productId, Guid categoryId);
    Task DeleteCategoryAsync(Guid productId, Guid categoryId);
    Guid[] GetRelatedProductsIds(ProductCodesRequest productCodesRequest);
    Task<List<ProductShortResponse>> GetProductIdsFromCodes(ProductCodesRequest productCodesRequest);
    Task<bool> ProductsContainBillPaymentServiceOrBundleAsync(IdsRequest productIds);
    Task<BillPaymentServiceAndBundleFlags> ProductsContainBillPaymentTypeAsync(IdsRequest productIds);
    Task<ProductListResponse> GetProductsList(GetProductsListRequest request);

    Task<TerminalDetails> GetProductsDetails(string TID);
    Task<List<ProductOnBusiness>> GetProductsOnBusinessByMID(string MID);
    Task<ProductsOnAccount> GetProductsOnAccountAsync(string accountId);

    Task<List<ProductsOnAccount>> GetMultipleProductsOnAccountAsync(string accountId);

    Task<TerminalDataSetEntity> GetTerminalDataSet(string MID);
    Task<List<BasicProductsInfo>> GetProductsNamesAsync();
    Task<ProductDetailsResponse?> GetProductDetails(Guid Id);
    Task<List<ProductDetailsResponse>> GetProductsListByChannel(bool isCNP);
    Task DeleteProductImages(Guid productId);
}
