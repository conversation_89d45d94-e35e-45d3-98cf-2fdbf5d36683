FROM mcr.microsoft.com/dotnet/aspnet:6.0-alpine
RUN apk add --no-cache --upgrade apk-tools bash icu-libs curl krb5 tzdata libc6-compat openssl
RUN ln -sf /lib64/ld-linux-x86-64.so.2 /lib/ld-linux-x86-64.so.2
RUN ln -s /usr/share/zoneinfo/Asia/Kuwait /etc/localtime
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
WORKDIR /app
EXPOSE 80
COPY . /app
RUN chown -R 999:999 /app
ENTRYPOINT ["./ProductService"]