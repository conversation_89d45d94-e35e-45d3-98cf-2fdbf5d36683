trigger: none

pool:
  name: 'GD-Azure'

resources:
  pipelines: 
    - pipeline: AutoUpdateNugetPackages
      source: 'AutoUpdateNugetPackages'
      trigger:
        branches:
          - dev

steps:
- checkout: self
  clean: true
  persistCredentials: false

- task: UseDotNet@2
  displayName: 'Use .Net Core sdk 5.x'
  inputs:
    packageType: 'sdk'
    version: 5.x
    installationPath: $(Agent.TempDirectory)/dotnet

- task: UseDotNet@2
  displayName: 'Use .Net Core sdk 6.x'
  inputs:
    packageType: 'sdk'
    version: 6.x
    installationPath: $(Agent.TempDirectory)/dotnet

- task: NuGetAuthenticate@0
  inputs:
    nuGetServiceConnections: 'Nuget New service connection'

- task: Bash@3
  displayName: 'Create PR for nugets updates'
  inputs:
    targetType: 'inline'
    script: |

      Secret_B64=$(echo -n "u:${SECRET}" | base64)
      branch_to_delete="refs/heads/canary/update-nuget-packages"

      pullrequestid=$(curl -s -f -H "Authorization: Basic ${Secret_B64}" -H "Content-Type: application/json" -X GET "$(System.TeamFoundationCollectionUri)/$(System.TeamProject)/_apis/git/repositories/$(Build.Repository.Name)/pullrequests?searchCriteria.sourceRefName=${branch_to_delete}&\$top=1&api-version=6.0" | jq -r '.value[].pullRequestId')
      pullrequesturl=$(curl -s -f -H "Authorization: Basic ${Secret_B64}" -H "Content-Type: application/json" -X GET "$(System.TeamFoundationCollectionUri)/$(System.TeamProject)/_apis/git/repositories/$(Build.Repository.Name)/pullrequests?searchCriteria.sourceRefName=${branch_to_delete}&\$top=1&api-version=6.0" | jq -r '.value[].url')

      if [ ! -z "$pullrequestid" ]
      then
            echo "Exiting PullRequest is $pullrequestid here - $pullrequesturl. Deleting it..."

            curl -s -f -H "Authorization: Basic ${Secret_B64}" -H "Content-Type: application/json" -X PATCH "$(System.TeamFoundationCollectionUri)/$(System.TeamProject)/_apis/git/repositories/$(Build.Repository.Name)/pullrequests/${pullrequestid}?api-version=6.0" -d '{"status": "abandoned"}'
            echo
            echo
            echo
      fi

      cd $(System.DefaultWorkingDirectory)
      git -c http.extraHeader="Authorization: Basic ${Secret_B64}" push origin --delete canary/update-nuget-packages  2>&1 | grep -v error || true
      echo
      echo
      cd -

      pwd
      echo $(System.DefaultWorkingDirectory)
      nukeeper --version
      nukeeper repo --specver "AspNetCore:6.x;Microsoft.AspNetCore:6.x;Microsoft.EntityFrameworkCore:6.x;Microsoft.Extensions:6.x;Microsoft.VisualStudio:6.x;System.Text:6.x;Serilog.AspNetCore:6.x;Serilog.Settings:6.x;Serilog.Extensions:6.x" --consolidate --deletebranchaftermerge true --age 0 --maxpackageupdates 1000 --branchnametemplate "canary/update-nuget-packages" --useprerelease Never -v d -git /usr/bin/git https://<EMAIL>/GeideaPaymentGateway/GeideaPaymentGateway/_git/$(Build.Repository.Name) ${SECRET}

      createdpullrequestid=$(curl -s -f -H "Authorization: Basic ${Secret_B64}" -H "Content-Type: application/json" -X GET "$(System.TeamFoundationCollectionUri)/$(System.TeamProject)/_apis/git/repositories/$(Build.Repository.Name)/pullrequests?searchCriteria.sourceRefName=${branch_to_delete}&\$top=1&api-version=6.0" | jq -r '.value[].pullRequestId')
      createdpullrequestauthor=$(curl -s -f -H "Authorization: Basic ${Secret_B64}" -H "Content-Type: application/json" -X GET "$(System.TeamFoundationCollectionUri)/$(System.TeamProject)/_apis/git/repositories/$(Build.Repository.Name)/pullrequests?searchCriteria.sourceRefName=${branch_to_delete}&\$top=1&api-version=6.0" | jq -r '.value[].createdBy.id')
      createdpullrequesttitle=$(curl -s -f -H "Authorization: Basic ${Secret_B64}" -H "Content-Type: application/json" -X GET "$(System.TeamFoundationCollectionUri)/$(System.TeamProject)/_apis/git/repositories/$(Build.Repository.Name)/pullrequests?searchCriteria.sourceRefName=${branch_to_delete}&\$top=1&api-version=6.0" | jq -r '.value[].title')
      if [ ! -z "$createdpullrequestid" ]
      then
            echo
            echo "The new PullRequest is $createdpullrequestid. Adding reviewers."
            curl -s -f -H "Authorization: Basic ${Secret_B64}" -H "Content-Type: application/json" -X PUT "$(System.TeamFoundationCollectionUri)/$(System.TeamProject)/_apis/git/repositories/$(Build.Repository.Name)/pullrequests/${createdpullrequestid}/reviewers/${reviewerId}?api-version=6.0" -d '{"vote": 0}'
            curl -s -f -H "Authorization: Basic ${Secret_B64}" -H "Content-Type: application/json" -X PATCH "$(System.TeamFoundationCollectionUri)/$(System.TeamProject)/_apis/git/repositories/$(Build.Repository.Name)/pullrequests/${createdpullrequestid}?api-version=6.0" -d '{"autoCompleteSetBy": {"id": "'"${createdpullrequestauthor}"'"}, "completionOptions": {"mergeCommitMessage": "Merged PR '"${createdpullrequestid}"': '"${createdpullrequesttitle}"'", "deleteSourceBranch": true, "mergeStrategy": "squash"}}'
            echo
      fi

    workingDirectory: '$(Agent.TempDirectory)'
    failOnStderr: true
  env:
    SECRET: $(PAT)
    EMAIL: $(GIT_USER_EMAIL)
    GIT_AUTHOR_EMAIL: $(GIT_USER_EMAIL)
    GIT_COMMITTER_EMAIL: $(GIT_USER_EMAIL)
    GIT_AUTHOR_NAME: $(GIT_USER_NAME)
    GIT_COMMITTER_NAME: $(GIT_USER_NAME)
    reviewerId: $(REVIEWERID)

- task: PostBuildCleanup@3
  condition: always()
