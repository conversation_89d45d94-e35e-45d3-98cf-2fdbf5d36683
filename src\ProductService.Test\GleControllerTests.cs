﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Models;
using Common.Models.Gle;
using Common.Services.Gle;
using Geidea.Utils.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using ProductService.Controllers;
using Xunit;

namespace ProductService.Test;

public class GleControllerTests
{
    private readonly IGleMerchantService gleMerchantService = Substitute.For<IGleMerchantService>();
    private readonly IGleStoreService gleStoreService = Substitute.For<IGleStoreService>();
    private readonly IGleTerminalService gleTerminalService = Substitute.For<IGleTerminalService>();
    private readonly IGleUpdateHistoryService gleUpdateHistoryService = Substitute.For<IGleUpdateHistoryService>();
    private readonly GleController gleController;

    public GleControllerTests()
    {
        gleMerchantService.AddGleMerchantAsync(Arg.Any<GleMerchantRequest>()).Returns(Task.CompletedTask);
        gleMerchantService.UpdateGleMerchantAsync(Arg.Any<Guid>(), Arg.Any<JsonPatchDocument<UpdateGleMerchantRequest>>()).Returns(Task.CompletedTask);
        gleMerchantService.GetGleMerchantByMerchantIdAsync(Arg.Any<Guid>()).Returns(new GleMerchant());
        gleMerchantService.GetGleHierarchyByMerchantIdAsync(Arg.Any<Guid>()).Returns(new GleMerchant());
        gleMerchantService.IsMerchantRegisteredInGleAsync(Arg.Any<Guid>()).Returns(true);

        gleStoreService.AddGleStoreAsync(Arg.Any<GleStoreRequest>()).Returns(Task.CompletedTask);
        gleStoreService.UpdateGleStoreAsync(Arg.Any<Guid>(), Arg.Any<JsonPatchDocument<UpdateGleStoreRequest>>()).Returns(Task.CompletedTask);
        gleStoreService.GetGleStoreByStoreIdAsync(Arg.Any<Guid>()).Returns(new GleStore());
        gleStoreService.GetGleHierarchyByStoreIdAsync(Arg.Any<Guid>()).Returns(new GleHierarchyByStore());

        gleTerminalService.AddGleTerminalAsync(Arg.Any<GleTerminalRequest>()).Returns(Task.CompletedTask);
        gleTerminalService.UpdateGleTerminalAsync(Arg.Any<Guid>(), Arg.Any<JsonPatchDocument<UpdateGleTerminalRequest>>()).Returns(Task.CompletedTask);
        gleTerminalService.GetGleTerminalByProductInstanceIdAsync(Arg.Any<Guid>()).Returns(new GleTerminal());
        gleTerminalService.GetGleTerminalListByOrderIdAsync(Arg.Any<Guid>()).Returns(new List<GleTerminalRequest> { new() });

        gleUpdateHistoryService.GetGleUpdateHistoryByMerchantIdAsync(Arg.Any<Guid>()).Returns(new GleUpdateHistory());
        gleUpdateHistoryService.AddGleUpdateHistoryAsync(Arg.Any<List<GleUpdateHistoryRequest>>()).Returns(Task.CompletedTask);

        var httpContext = new DefaultHttpContext();
        httpContext.Request.Headers[Constants.UserIdHeaderName] = Guid.NewGuid().ToString();
        gleController = new GleController(gleMerchantService, gleStoreService, gleTerminalService, gleUpdateHistoryService)
        {
            ControllerContext = new ControllerContext() { HttpContext = httpContext }
        };

        gleMerchantService.ClearReceivedCalls();
        gleStoreService.ClearReceivedCalls();
        gleTerminalService.ClearReceivedCalls();
    }

    [Fact]
    public async Task AddGleMerchant_ShouldCallService()
    {
        var result = await gleController.AddGleMerchant(new GleMerchantRequest());

        var okResult = result as OkResult;
        Assert.NotNull(okResult);

        await gleMerchantService.Received(1).AddGleMerchantAsync(Arg.Any<GleMerchantRequest>());
    }

    [Fact]
    public async Task UpdateGleMerchant_ShouldCallService()
    {
        var result = await gleController.UpdateGleMerchant(Guid.NewGuid(), new JsonPatchDocument<UpdateGleMerchantRequest>());

        var okResult = result as NoContentResult;
        Assert.NotNull(okResult);

        await gleMerchantService.Received(1)
            .UpdateGleMerchantAsync(Arg.Any<Guid>(), Arg.Any<JsonPatchDocument<UpdateGleMerchantRequest>>());
    }

    [Fact]
    public async Task GetGleHierarchyByMerchantId_ShouldCallService()
    {
        var result = await gleController.GetGleHierarchyByMerchantId(Guid.NewGuid());

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as GleMerchant;
        Assert.NotNull(resultInstance);

        await gleMerchantService.Received(1).GetGleHierarchyByMerchantIdAsync(Arg.Any<Guid>());
    }

    [Fact]
    public async Task IsMerchantRegisteredInGle_ShouldCallService()
    {
        var result = await gleController.IsMerchantRegisteredInGle(Guid.NewGuid());

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as bool?;
        Assert.NotNull(resultInstance);

        await gleMerchantService.Received(1).IsMerchantRegisteredInGleAsync(Arg.Any<Guid>());
    }

    [Fact]
    public async Task GetGleHierarchyByStoreId_ShouldCallService()
    {
        var result = await gleController.GetGleHierarchyByStoreId(Guid.NewGuid());

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as GleHierarchyByStore;
        Assert.NotNull(resultInstance);

        await gleStoreService.Received(1).GetGleHierarchyByStoreIdAsync(Arg.Any<Guid>());
    }

    [Fact]
    public async Task AddGleStore_ShouldCallService()
    {
        var result = await gleController.AddGleStore(new GleStoreRequest());

        var okResult = result as OkResult;
        Assert.NotNull(okResult);

        await gleStoreService.Received(1).AddGleStoreAsync(Arg.Any<GleStoreRequest>());
    }

    [Fact]
    public async Task UpdateGleStore_ShouldCallService()
    {
        var result = await gleController.UpdateGleStore(Guid.NewGuid(), new JsonPatchDocument<UpdateGleStoreRequest>());

        var okResult = result as NoContentResult;
        Assert.NotNull(okResult);

        await gleStoreService.Received(1)
            .UpdateGleStoreAsync(Arg.Any<Guid>(), Arg.Any<JsonPatchDocument<UpdateGleStoreRequest>>());
    }

    [Fact]
    public async Task AddGleTerminal_ShouldCallService()
    {
        var result = await gleController.AddGleTerminal(new GleTerminalRequest());

        var okResult = result as OkResult;
        Assert.NotNull(okResult);

        await gleTerminalService.Received(1).AddGleTerminalAsync(Arg.Any<GleTerminalRequest>());
    }

    [Fact]
    public async Task UpdateGleTerminal_ShouldCallService()
    {
        var result = await gleController.UpdateGleTerminal(Guid.NewGuid(), new JsonPatchDocument<UpdateGleTerminalRequest>());

        var okResult = result as NoContentResult;
        Assert.NotNull(okResult);

        await gleTerminalService.Received(1)
            .UpdateGleTerminalAsync(Arg.Any<Guid>(), Arg.Any<JsonPatchDocument<UpdateGleTerminalRequest>>());
    }

    [Fact]
    public async Task CalculateBillPaymentsStatus_ShouldReturnStatus()
    {
        var result = await gleController.CalculateBillPaymentsStatus(Guid.NewGuid(), new IdsRequest());

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as string;
        Assert.NotNull(resultInstance);

        await gleTerminalService.Received(1).CalculateBillPaymentsStatusAsync(Arg.Any<Guid>(), Arg.Any<IdsRequest>());
    }

    [Fact]
    public async Task CalculateGleRegistrationStatusForOrderWithBp_ShouldReturnStatus()
    {
        var result = await gleController.CalculateGleRegistrationStatusForOrderWithBp(Guid.NewGuid());

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as string;
        Assert.NotNull(resultInstance);

        await gleTerminalService.Received(1).CalculateGleRegistrationStatusForOrderWithBpAsync(Arg.Any<Guid>());
    }

    [Fact]
    public async Task GetGleTerminalListByOrderId_ShouldCallService()
    {
        var result = await gleController.GetGleTerminalListByOrderId(Guid.NewGuid());

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as IReadOnlyCollection<GleTerminalRequest>;
        Assert.NotNull(resultInstance);

        await gleTerminalService.Received(1).GetGleTerminalListByOrderIdAsync(Arg.Any<Guid>());
    }

    [Fact]
    public async Task GetGleRegistration_ShouldCallMerchantService()
    {
        var result = await gleController.GetGleRegistration(Common.Constants.GleRegistrationType.Merchant, Guid.NewGuid());

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as GleMerchant;
        Assert.NotNull(resultInstance);

        await gleMerchantService.Received(1).GetGleMerchantByMerchantIdAsync(Arg.Any<Guid>());
        await gleStoreService.DidNotReceive().GetGleStoreByStoreIdAsync(Arg.Any<Guid>());
        await gleTerminalService.DidNotReceive().GetGleTerminalByProductInstanceIdAsync(Arg.Any<Guid>());
    }

    [Fact]
    public async Task GetGleRegistration_ShouldCallStoreService()
    {
        var result = await gleController.GetGleRegistration(Common.Constants.GleRegistrationType.Store, Guid.NewGuid());

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as GleStore;
        Assert.NotNull(resultInstance);

        await gleMerchantService.DidNotReceive().GetGleMerchantByMerchantIdAsync(Arg.Any<Guid>());
        await gleStoreService.Received(1).GetGleStoreByStoreIdAsync(Arg.Any<Guid>());
        await gleTerminalService.DidNotReceive().GetGleTerminalByProductInstanceIdAsync(Arg.Any<Guid>());
    }

    [Fact]
    public async Task GetGleRegistration_ShouldCallTerminalService()
    {
        var result = await gleController.GetGleRegistration(Common.Constants.GleRegistrationType.Terminal, Guid.NewGuid());

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as GleTerminal;
        Assert.NotNull(resultInstance);

        await gleMerchantService.DidNotReceive().GetGleMerchantByMerchantIdAsync(Arg.Any<Guid>());
        await gleStoreService.DidNotReceive().GetGleStoreByStoreIdAsync(Arg.Any<Guid>());
        await gleTerminalService.Received(1).GetGleTerminalByProductInstanceIdAsync(Arg.Any<Guid>());
    }

    [Fact]
    public async Task GetGleRegistrationStatusHistory_ShouldCallService()
    {
        var result = await gleController.GetGleRegistrationStatusHistory(new GleHistoryLogRequest());

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as IReadOnlyCollection<GleRegistrationStatusInfo>;
        Assert.NotNull(resultInstance);

        await gleTerminalService.Received(1).GetGleRegistrationStatusHistoryAsync(Arg.Any<GleHistoryLogRequest>());
    }

    [Fact]
    public async Task GetGleUpdateHistoryByMerchantId_ShouldCallService()
    {
        var merchantId = Guid.NewGuid();
        var result = await gleController.GetGleUpdateHistoryByMerchantId(merchantId);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as GleUpdateHistory;
        Assert.NotNull(resultInstance);

        await gleUpdateHistoryService.Received(1).GetGleUpdateHistoryByMerchantIdAsync(merchantId);
    }

    [Fact]
    public async Task AddGleUpdateHistory_ShouldCallService()
    {
        var result = await gleController.AddGleUpdateHistory(new List<GleUpdateHistoryRequest>());

        var okResult = result as OkResult;
        Assert.NotNull(okResult);

        await gleUpdateHistoryService.Received(1).AddGleUpdateHistoryAsync(Arg.Any<List<GleUpdateHistoryRequest>>());
    }

    [Fact]
    public async Task UpdateGleUpdateHistory_ShouldCallService()
    {
        var request = new List<GleUpdateHistoryEditRequest>()
        {
            new GleUpdateHistoryEditRequest
            {
                Id = Guid.NewGuid(),
                GleResponse = "NA",
                GleStatus = "Status"
            }
        };

        var result = await gleController.UpdateGleUpdateHistory(request);

        var okResult = result as OkResult;
        Assert.NotNull(okResult);

        await gleUpdateHistoryService.Received(1).UpdateGleUpdateHistoryAsync(request);
    }
}