﻿using Common.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models;
public class GetBusinessTypesListRequest
{
    [DefaultValue(Constants.BusinessTypeSearchDictionary)]
    public Dictionary<BusinessTypesSearchKey, string> SearchTerms { get; set; } = new Dictionary<BusinessTypesSearchKey, string>();
    public List<Status>? FilterByStatus { get; set; }
    public string OrderType { get; set; } = SortType.desc.ToString();
    [DefaultValue("CreatedDate")]
    public string OrderFieldName { get; set; } = "CreatedDate";
    [DefaultValue(1)]
    public int Page { get; set; } = 1;
    [DefaultValue(10)]
    public int Size { get; set; } = 10;
}
public enum BusinessTypesSearchKey
{
    All,
    Id,
    Type,
}