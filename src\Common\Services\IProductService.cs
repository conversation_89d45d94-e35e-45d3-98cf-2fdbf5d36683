﻿using Common.Entities;
using Common.Models;
using Microsoft.AspNetCore.JsonPatch;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services;

public interface IProductService
{
    Task<ProductResponse> CreateAsync(ProductRequest request);
    Task<Product[]> FindAsync(FindProductRequest request, bool attach);
    Task<Product[]> FindWithInfoAsync(FindProductRequest request, bool attach);
    Task BindPartAsync(Guid partId, Guid productId, int quantity);
    Task UnbindPartAsync(Guid partId, Guid productId);
    Task<ProductResponse> UpdateAsync(Guid id, ProductRequest productRequest);
    Task<bool> IsProductUsedAsync(Guid id);
    Task DeleteProductAsync(Guid id);
    Task AddCategoryAsync(Guid productId, Guid categoryId);
    Task DeleteCategoryAsync(Guid productId, Guid categoryId);
    Task<Product[]> FindByIdsAsync(IdsRequest request);
    Guid[] GetRelatedProductsIds(ProductCodesRequest productCodesRequest);
    Task<bool> ProductsContainBillPaymentServiceOrBundleAsync(IdsRequest productIds);
    Task<BillPaymentServiceAndBundleFlags> ProductsContainBillPaymentTypeAsync(IdsRequest productIds);
    Task<ProductListResponse> GetProductsList(GetProductsListRequest request);
    Task<TerminalDetails> GetProductsDetails(string TID);
    Task<List<ProductOnBusiness>> GetProductsOnBusinessByMID(string MID);
    Task<ProductsOnAccount> GetProductsOnAccountByaccountID(string accountID);
    Task<TerminalDataSetEntity> GetTerminalDataSetByMID(string MID);
    Task<List<ProductsOnAccount>> GetMultipleProductsOnAccountByaccountID(string accountID);
    Task<List<BasicProductsInfo>> GetProductsNames();
    Task<ProductDetailsResponse> GetProductDetails(Guid ProductId);
    Task<List<ProductDetailsResponse>> GetProductsListByChannel(bool isCNP);
}
