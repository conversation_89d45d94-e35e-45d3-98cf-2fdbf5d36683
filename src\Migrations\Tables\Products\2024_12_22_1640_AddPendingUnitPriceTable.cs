﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.Products;
[Migration(2024_12_22_1640)]
public class AddPendingUnitPriceTable : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
        AppDomain.CurrentDomain.BaseDirectory +
        Path.Combine("Scripts", "AddPendingUnitPriceTable.sql"));
    }
}

[Migration(2025_02_04_1550)]
public class AlterPendingUnitPriceTable_AddReviewColumns : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
        AppDomain.CurrentDomain.BaseDirectory +
        Path.Combine("Scripts", "AlterPendingUnitPriceTableAddReviewColumns.sql"));
    }
}
