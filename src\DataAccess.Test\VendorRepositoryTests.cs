﻿using Common.Entities;
using Common.Models.Vendor;
using DataAccess.Repositories;
using DataAccess.Test.TestData;
using Geidea.Utils.Counterparty.Providers;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DataAccess.Test;
public class VendorRepositoryTests
{
    private IHttpContextAccessor _contextAccessor;
    private DataContext context;
    ILogger<VendorRepository> vendorLogger;
    private ICounterpartyProvider counterpartyProvider = Substitute.For<ICounterpartyProvider>();

    public VendorRepositoryTests()
    {

        vendorLogger = Substitute.For<ILogger<VendorRepository>>();
    }
    [SetUp]
    public void Setup()
    {
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();
        counterpartyProvider.GetCode().Returns("GEIDEA_SAUDI");

        context = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider);

        _contextAccessor = Mock.Of<IHttpContextAccessor>();
    }

    [Test]
    public async Task IsVendorExistWithSameNameAndType_ExistingVendor_ReturnsTrue()
    {
        // Arrange
        var vendorRequest = new VendorRequest { Name = "Test Vendor", TerminalType = "Type" };

        context.Set<VendorEntity>().Add(new VendorEntity { Name = "Test Vendor", TerminalType = "Type" });
        await context.SaveChangesAsync();

        // Act
        var repository = new VendorRepository(context, _contextAccessor, vendorLogger);
        var result = await repository.IsVendorExistWithSameNameAndType(vendorRequest);

        // Assert
        Assert.IsTrue(result);
    }

    [Test]
    public async Task AddAsync_ValidRequest_AddsVendor()
    {
        // Arrange
        var vendorRequest = new VendorRequest { Name = "Test Vendor", TerminalType = "Type", Prefix = new List<string> { "12", "42" } };

        // Act
        var repository = new VendorRepository(context, _contextAccessor, vendorLogger);
        await repository.CreateAsync(vendorRequest);

        // Assert
        Assert.AreEqual(1, await context.Set<VendorEntity>().CountAsync());
        var addedVendor = await context.Set<VendorEntity>().FirstAsync();
        Assert.AreEqual(vendorRequest.Name, addedVendor.Name);
        Assert.AreEqual(vendorRequest.TerminalType, addedVendor.TerminalType);
    }

    [Test]
    public async Task GetByIdAsync_ExistingId_ReturnsVendor()
    {
        // Arrange
        var existingVendor = new VendorEntity { Id = Guid.NewGuid(), Name = "Test Vendor", TerminalType = "Type" };

        context.Set<VendorEntity>().Add(existingVendor);
        await context.SaveChangesAsync();

        // Act
        var repository = new VendorRepository(context, _contextAccessor, vendorLogger);
        var result = await repository.GetByIdAsync(existingVendor.Id);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(existingVendor.Id, result!.Id);
        Assert.AreEqual(existingVendor.Name, result.Name);
        Assert.AreEqual(existingVendor.TerminalType, result.TerminalType);
    }

    [Test]
    public async Task GetAsync_WithTerminalType_ReturnsFilteredVendors()
    {
        // Arrange
        var terminalType = "Type2";
        var vendors = new List<VendorEntity>
        {
            new VendorEntity { Name = "Vendor 1", TerminalType = terminalType },
            new VendorEntity { Name = "Vendor 2", TerminalType = "OtherType" },
            new VendorEntity { Name = "Vendor 3", TerminalType = terminalType }
        };

        await context.AddRangeAsync(vendors);
        await context.SaveChangesAsync();

        // Act
        var repository = new VendorRepository(context, _contextAccessor, vendorLogger);
        var result = await repository.GetAsync(terminalType);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(2, result!.Count);
        Assert.IsTrue(result.All(v => v.TerminalType == terminalType));
    }

    [Test]
    public async Task AdvancedSearch_ReturnsFilteredVendors()
    {
        var searchRequest = new VendorSearchRequest
        {
            Name = "Vendor",
            TerminalType = new List<string> { "Type5" },
            Skip = 0,
            Take = 10,
            OrderBy = "CreatedDate"
        };

        var vendors = new List<VendorEntity>
        {
            new VendorEntity { Name = "Vendor 1", TerminalType = "Type5" },
            new VendorEntity { Name = "Vendor 2", TerminalType = "OtherType" },
            new VendorEntity { Name = "Vendor 3", TerminalType = "Type5" }
        };

        await context.AddRangeAsync(vendors);
        await context.SaveChangesAsync();

        var repository = new VendorRepository(context, _contextAccessor, vendorLogger);
        var result = await repository.AdvancedSearch(searchRequest);

        Assert.IsNotNull(result);
        Assert.AreEqual(2, result!.Records.Count);
        Assert.IsTrue(result.Records.All(v => v.TerminalType == "Type5"));
        Assert.IsTrue(result.Records.All(v => v.Name.Contains("Vendor")));
    }

    [Test]
    public async Task AdvancedSearch_EmptyRequest_ReturnsAllVendors()
    {
        var searchRequest = new VendorSearchRequest
        {
            Skip = 0,
            Take = 10,
            OrderBy = "CreatedDate"
        };

        var vendors = new List<VendorEntity>
        {
            new VendorEntity { Id = Guid.NewGuid(), Name = "Vendor 6", TerminalType = "Type6" },
            new VendorEntity { Id = Guid.NewGuid(), Name = "Vendor 7", TerminalType = "Type7" },
            new VendorEntity { Id = Guid.NewGuid(), Name = "Vendor 8", TerminalType = "Type8" }
        };

        context = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider);
        await context.AddRangeAsync(vendors);
        await context.SaveChangesAsync();

        var repository = new VendorRepository(context, _contextAccessor, vendorLogger);
        var result = await repository.AdvancedSearch(searchRequest);

        Assert.IsNotNull(result);
        Assert.AreNotEqual(0, result.Records!.Count);
    }

    [Test]
    public async Task GetDefaultVendorAsync_ExistingDefaultVendor_ReturnsDefaultVendor()
    {
        // Arrange
        var defaultVendor = new VendorEntity { Id = Guid.NewGuid(), IsDefault = true, Name = "Test default Vendor", TerminalType = "Type" };
        var normalVendor = new VendorEntity { Id = Guid.NewGuid(), IsDefault = false, Name = "Test normal Vendor", TerminalType = "Type" };
        context.Set<VendorEntity>().Add(defaultVendor);
        context.Set<VendorEntity>().Add(normalVendor);
        await context.SaveChangesAsync();

        // Act
        var repository = new VendorRepository(context, _contextAccessor, vendorLogger);
        var result = await repository.GetDefaultVendorAsync();

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(defaultVendor.Id, result.Id);
        Assert.AreEqual(defaultVendor.IsDefault, result.IsDefault);
    }

    [Test]
    public async Task SetDefaultVendorAsync_ShouldBeSuccessfuly()
    {
        // Arrange
        var defaultVendor = new VendorEntity { Id = Guid.NewGuid(), IsDefault = true, Name = "Test default Vendor", TerminalType = "Type" };
        var normalVendor = new VendorEntity { Id = Guid.NewGuid(), IsDefault = false, Name = "Test normal Vendor", TerminalType = "Type" };
        context.Set<VendorEntity>().Add(defaultVendor);
        context.Set<VendorEntity>().Add(normalVendor);
        await context.SaveChangesAsync();

        // Act
        var repository = new VendorRepository(context, _contextAccessor, vendorLogger);
        var newDefaultVendor = await repository.SetDefaultVendorAsync(normalVendor.Id);

        // Assert
        Assert.AreEqual(newDefaultVendor.IsDefault, normalVendor.IsDefault);
        Assert.AreEqual(false, defaultVendor.IsDefault);
    }
}
