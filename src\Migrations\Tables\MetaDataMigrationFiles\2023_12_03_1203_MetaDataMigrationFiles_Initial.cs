﻿using FluentMigrator;

namespace Migrations.Tables.MetaDataMigrationFiles;

[Migration(2023_12_03_1203)]
public class MetaDataMigrationFiles : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("MetaDataMigrationFiles")
          .WithColumn("Id").AsGuid().NotNullable().PrimaryKey().WithDefault(SystemMethods.NewGuid)
          .WithColumn("MigrationFileName").AsString(int.MaxValue)
          .WithColumn("SelectQuery").AsString(int.MaxValue)
          .WithColumn("UpdateQuery").AsString(int.MaxValue)
          .WithColumn("IsFinished").AsBoolean();
    }
}

