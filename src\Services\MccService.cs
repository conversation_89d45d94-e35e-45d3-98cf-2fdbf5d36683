﻿using AutoMapper;
using ClosedXML.Excel;
using Common;
using Common.Entities;
using Common.Models;
using Common.Models.MccManagement;
using Common.Models.NonTransactionalFees;
using Common.Repositories;
using Common.Services;
using Common.Validators.MccManagement;
using DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Services;
public class MccService : IMccService
{
    #region Fields
    private readonly ILogger<MccService> logger;
    private readonly IMccRepository mccRepository;
    private readonly IMccCategoryRepository mccCategoryRepository;

    private readonly IMapper mapper;
    #endregion

    #region Constructor
    public MccService(ILogger<MccService> logger, IMccRepository mccRepository, IMapper mapper, IMccCategoryRepository mccCategoryRepository)
    {
        this.logger = logger;
        this.mccRepository = mccRepository;
        this.mapper = mapper;
        this.mccCategoryRepository = mccCategoryRepository;
        this.mccCategoryRepository = mccCategoryRepository;
    }
    #endregion

    #region Create New MCC
    public async Task<Mcc> CreateAsync(MccRequest request)
    {
        await ValidateMccRequest(request);
        var categoryExists = await mccCategoryRepository.ExistsAsync(request.MccCategoryId);
        if (!categoryExists)
        {
            logger.LogError("CreateAsync: Invalid category ID '{CategoryId}' does not exist for mcc with Code '{Code}'.", request.MccCategoryId.ToString().ToUpper(), request.Code);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.CategoryNotFound);
        }
        var ExistedCode = await mccRepository.ExistsAsync(n => n.Code == request.Code);
        if (ExistedCode)
        {
            logger.LogError("CreateAsync: Invalid mcc, Code '{code}' already exists", request.Code);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.CodeAlreadyExist);
        }
        try
        {

            var mccRes = mapper.Map<Mcc>(request);
            mccRepository.Add(mccRes);
            await mccRepository.SaveChangesAsync();

            logger.LogInformation("A mcc  with Id '{Id}' and Name '{Name}'", mccRes.Id, mccRes.Name);
            return mccRes;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Something went wrong");
            throw new ServiceException(HttpStatusCode.InternalServerError, $"Error while adding a new mcc: {ex.Message}");
        }
    }
    #endregion
    #region Update Existing MCC
    public async Task<Mcc> UpdateAsync(Guid id, MccRequest request)
    {
        await ValidateMccRequest(request);

        var existingMcc = await GetExistingMccAsync(id);

        await ValidateRequestCodeAsync(existingMcc, request);

        var categoryExists = await mccCategoryRepository.ExistsAsync(request.MccCategoryId);
        if (!categoryExists)
        {
            logger.LogError("UpdateAsync: Invalid category ID '{CategoryId}' does not exist for MCC with Code '{Code}'.", request.MccCategoryId.ToString().ToUpper(), request.Code);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.CategoryNotFound);
        }

        try
        {
            // Update the existing MCC entity with new values
            mapper.Map(request, existingMcc);

            mccRepository.Update(existingMcc);
            await mccRepository.SaveChangesAsync();

            logger.LogInformation("MCC with Id '{Id}' and Name '{Name}' has been updated.", existingMcc.Id, existingMcc.Name);
            return existingMcc;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Something went wrong");
            throw new ServiceException(HttpStatusCode.InternalServerError, $"Error while updating MCC: {ex.Message}");
        }
    }
    #endregion

    #region Listing
    public async Task<GetMccListResponse> GetMCCList(GetMccListRequest request)
    {
        if (request == null)
        {
            logger.LogError("Invalid Get MCC List request.");
            throw new ServiceException(HttpStatusCode.BadRequest, "Invalid Get MCC List request.");
        }
        try
        {
            return await mccRepository.GetMCCList(request);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "something has went wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, "something has went wrong!");
        }
    }
    #endregion

    #region MccDetails
    public async Task<MccDetailsResponse?> GetMccDetails(Guid Id)
    {
        if (Id == Guid.Empty)
        {
            logger.LogError("Invalid MCC Id.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidIdentifier);
        }
        var MccDetailsObj = await mccRepository.GetMccDetails(Id);
        if (MccDetailsObj == null)
        {
            logger.LogError("MCC with Id {Id} not found", Id);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductNotFound);
        }
        return MccDetailsObj;
    }
    #endregion

    #region Helpers
    public async Task ValidateMccRequest(MccRequest request)
    {
        var validationResult = await new AddMccRequestValidator().ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            var ErrorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
            logger.LogError("The request to create a new mcc failed validation {@errors}", ErrorDescription);
            throw new ValidationException(validationResult);
        }
    }

    public async Task<Mcc> GetExistingMccAsync(Guid id)
    {
        var ExistedMcc = await mccRepository.GetByIdAsync(id);
        if (ExistedMcc == null)
        {
            logger.LogWarning("Mcc with id '{id}' not found.", id);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductNotFound);
        }
        return ExistedMcc;
    }
    public async Task ValidateRequestCodeAsync(Mcc ExistedMcc, MccRequest request)
    {
        bool ExistedCode = await mccRepository.AnyAsync(s => s.Code == request.Code);
        if (ExistedMcc.Code != request.Code && ExistedCode)
        {
            logger.LogError("A Mcc with code '{Code}' already exists.", request.Code);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.CodeAlreadyExist);
        }
    }


    #endregion

    #region ToggleStatus
    public async Task<bool> ToggleStatus(Guid Id, bool Status)
    {
        var result = false;
        if (Id == Guid.Empty)
        {
            logger.LogError("Invalid MCC Id .");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidIdentifier);
        }

        var MccObj = await mccRepository.GetByIdAsync(Id);
        if (MccObj == null)
            return result;

        MccObj.Status = Status ? Common.Enums.Status.Active : Common.Enums.Status.Inactive;
        mccRepository.Update(MccObj);
        await mccRepository.SaveChangesAsync();
        return true;
    }
    #endregion

    #region ExportToExcel
    public async Task<MemoryStream> ExportMccToExcel(GetMccListRequest request)
    {
        request.Size = 0; //Set to 0, to disable pagination

        var MccListResponse = await GetMCCList(request);
        List<ExportMccListToExcelResponse> exportData = new List<ExportMccListToExcelResponse>();
        if (MccListResponse != null && MccListResponse.MCCList != null)
        {
            exportData = MccListResponse.MCCList.Select(mcc => new ExportMccListToExcelResponse
            {
                MccCode = mcc.Code,
                MccName = mcc.Name,
                MccNameAr = mcc.NameAr,
                MccCategory = mcc.Category,
                Status = mcc.Status,
            }).ToList();
        }

        var stream = new MemoryStream();
        using (var workbook = new XLWorkbook())
        {
            var worksheet = workbook.Worksheets.Add("sheet1");
            worksheet.Cell(1, 1).InsertTable(exportData);
            workbook.SaveAs(stream);
        }
        stream.Position = 0;
        return stream;
    }
    #endregion


    #region ImportFromExcel
    public async Task<(int SuccessCount, int FailureCount, byte[] InvalidRecordsExcel)> ImportMccAsync(Guid categoryId, Stream fileStream, CancellationToken cancellationToken)
    {
        if (categoryId == Guid.Empty)
        {
            logger.LogError("Invalid category ID for MCC import.");
            throw new ServiceException(HttpStatusCode.BadRequest, "Invalid category ID for MCC import.");
        }

        if (fileStream == null || fileStream.Length == 0)
        {
            logger.LogError("Empty file stream for MCC import.");
            throw new ServiceException(HttpStatusCode.BadRequest, "Empty file stream for MCC import.");
        }

        try
        {
            return await mccRepository.ImportExcelToDatabase(categoryId, fileStream, cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred during MCC import.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An error occurred during MCC import.");
        }
    }
    #endregion

    #region Get MCCs By IDs
    public async Task<List<Mcc>> GetMCCsByCodesAsync(List<string?> codes)
    {
        if (codes == null || !codes.Any())
        {
            logger.LogError("GetMCCsByCodesAsync: The provided list of codes is null or empty.");
            throw new ServiceException(HttpStatusCode.BadRequest, "The provided list of IDs is null or empty.");
        }

        try
        {
            return await mccRepository.GetMCCsByCodesAsync(codes);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while retrieving MCCs by Codess.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An error occurred while retrieving MCCs by Codes.");
        }
    }
    #endregion
    public async Task<List<MccListCategoryResponse>> GetAllMccsWithCategoryAsync()
    {
        try
        {
            // Call the repository method to get the list of MCCs with categories
            return await mccRepository.GetAllMccsWithCategoryAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while retrieving MCCs with their categories.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An error occurred while retrieving MCCs with their categories.");
        }
    }
}
