﻿using FluentMigrator;

namespace Migrations.Tables.Products;

[Migration(2023_08_03_1035)]
public class AddAmexProductToProductTable : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
DECLARE @AmexProductId_EGYPT AS uniqueidentifier=NEWID();
 
--add new product with type AMEX_GW to EGYPT

INSERT INTO [dbo].[Products]
([Id]
,[Availability]
,[Code]
,[Type]
,[ValidFrom]
,[CreatedBy]
,[CreatedDate]
,[UpdatedBy]
,[Version]
,[Counterparty],
[DisplayOrder],
[SalesChannel])
VALUES
(@AmexProductId_EGYPT
,'Live'
,'AMEX_GW'
,'SCHEME'
,GETUTCDATE()
,'00000000-0000-0000-0000-000000000000'
,GETUTCDATE()
,'00000000-0000-0000-0000-000000000000'
,0
,'GEIDEA_EGYPT',NULL,NULL)

--add new Amex_EGYPT product as part of BUNDLE

INSERT INTO ProductParts (ProductId, PartId, Quantity) VALUES
((SELECT TOP 1 Id FROM Products WHERE type='BUNDLE' AND Counterparty='GEIDEA_EGYPT' AND Availability='Live' AND Code='PAYMENT_GATEWAY_BUNDLE'), @AmexProductId_EGYPT, 1)

");
    }
}
