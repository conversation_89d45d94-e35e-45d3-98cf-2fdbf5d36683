﻿using System;
using System.Collections.Generic;

namespace Common.Models.ProductInstance;

public class ProductInstanceResponse
{
    public Guid ProductInstanceId { get; set; }
    public Guid? AgreementId { get; set; }
    public Guid? CompanyId { get; set; }
    public Guid? StoreId { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public Guid? ParentId { get; set; }
    public Guid? ParentConfigurationId { get; set; }
    public object? Data { get; set; }
    public List<ProductInstanceResponse> Children { get; set; } = new List<ProductInstanceResponse>();
    public ProductShortResponse Product { get; set; } = null!;
    public string? EPosTicketId { get; set; }
    public bool EPosTicketCompleted { get; set; }
    public bool EPosBillPayments { get; set; }
    public DateTime? EPosLastUpdated { get; set; }
    public bool DeletedFlag { get; set; }
    public string? Mid { get; set; }
}
