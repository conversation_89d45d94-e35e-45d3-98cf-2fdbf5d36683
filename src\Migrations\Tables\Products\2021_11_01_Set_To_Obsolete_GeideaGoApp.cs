﻿using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.Products;

[Migration(2021_11_08_1600)]
public class Set_To_Obsolete_GeideaGoApp : ForwardOnlyMigration
{
    public override void Up()
    {
        Update.Table("Products").Set(new { Availability = "Obsolete" }).Where(new { Code = "GEIDEA_GO_APP" });
        Execute.Script(AppDomain.CurrentDomain.BaseDirectory + Path.Combine("Scripts", "Products_v11.sql"));
    }
}
