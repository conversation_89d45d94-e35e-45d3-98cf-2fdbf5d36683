﻿using AutoMapper;
using Common.Models.Onboarding;
using Common.Repositories;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using System.Net;
using System.Threading.Tasks;

namespace Services;
public class OnboardingUnitPriceService : IOnboardingUnitPriceService
{
    #region Fields
    private readonly ILogger<OnboardingUnitPriceService> logger;
    private readonly IOnboardingUnitPriceRepository onboardingUnitPriceRepository;

    private readonly IMapper mapper;
    #endregion

    #region Constructor
    public OnboardingUnitPriceService(ILogger<OnboardingUnitPriceService> logger, IOnboardingUnitPriceRepository onboardingUnitPriceRepository, IMapper mapper)
    {
        this.logger = logger;
        this.onboardingUnitPriceRepository = onboardingUnitPriceRepository;
        this.mapper = mapper;
    }
    #endregion

    #region Listing
    public async Task<OnboardingUnitPricesListResponse> GetOnboardingUnitPricesList(OnboardingUnitPricesListRequest request)
    {
        if (request == null)
        {
            logger.LogError("Invalid Get Unit Price List request.");
            throw new ServiceException(HttpStatusCode.BadRequest, "Invalid Get Unit Price List request.");
        }
        try
        {
            return await onboardingUnitPriceRepository.GetOnboardingUnitPricesList(request);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "something has went wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, "something has went wrong!");
        }
    }
    #endregion
}
