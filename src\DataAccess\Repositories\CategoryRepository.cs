﻿using Common.Entities;
using Common.Repositories;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using Common.Models.CategoryRequests;
using System.Collections.Generic;
using Common.Models;

namespace DataAccess.Repositories;

public class CategoryRepository : AuditableRepository<Guid, CategoryEntity>, ICategoryRepository
{
    public CategoryRepository(DbContext context, IHttpContextAccessor contextAccessor) : base(context, contextAccessor)
    {
    }

    public async Task HardDeleteCategory(CategoryEntity category)
    {
        context.Set<CategoryEntity>().Remove(category);
        await context.SaveChangesAsync();
    }

    public async Task<CategoryEntity[]> FindCategories(FindCategoryRequest request)
    {
        var query = context.Set<CategoryEntity>().AsQueryable();

        query = query
            .Include(x => x.Subcategories)
            .Where(x => !x.DeletedFlag)
            .AsNoTracking();

        if (request.CategoryId != null && request.CategoryId != Guid.Empty)
            query = query.Where(x => x.Id == request.CategoryId);

        if (request.Code != null)
            query = query.Where(x => x.Code == request.Code);

        if (request.Type > 0)
            query = query.Where(x => x.Type == request.Type);

        if (request.SalesChannel != null)
            query = query.Where(x => x.SalesChannel == request.SalesChannel);

        return await query.ToArrayAsync();
    }

    public async Task<bool> ExistsAsync(Guid categoryId)
    {
        return await context.Set<CategoryEntity>().AsNoTracking().AnyAsync(x => x.Id == categoryId && !x.DeletedFlag);
    }
    public async Task<List<CategoriesListResponse>> GetCategoriesList()
    {
        var query = await context.Set<CategoryEntity>()
                           .Where(x => !x.DeletedFlag)
                           .Select(x => new CategoriesListResponse()
                           {
                               Id = x.Id,
                               Name = x.Name,
                           }).ToListAsync();

        return query;
    }
}
