﻿using FluentMigrator;

namespace Migrations.Tables.Products;

[Migration(2021_06_03_1500)]
public class Product_ShopUpdates : ForwardOnlyMigration
{
    public override void Up()
    {
        Update.Table("Products").Set(new { SalesChannel = "Shop" }).Where(new { Code = "GO_MPOS", Version = 4, Counterparty = "GEIDEA_SAUDI" });
        Update.Table("Products").Set(new { SalesChannel = "Shop" }).Where(new { Code = "GO_A920", Version = 4, Counterparty = "GEIDEA_SAUDI" });
    }
}
