﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_06_18_1100)]
public class ProductInstance_AddIndexes : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
IF EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'ParentId' AND object_id = OBJECT_ID('[dbo].[ProductInstances]'))
BEGIN
	EXEC sp_rename N'[dbo].[ProductInstances].ParentId', N'IDX_PRODUCTINSTANCES_PARENTID', 'INDEX'
END

IF NOT EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'IDX_PRODUCTINSTANCES_PARENTID' AND object_id = OBJECT_ID('[dbo].[ProductInstances]'))
BEGIN
	CREATE NONCLUSTERED INDEX [IDX_PRODUCTINSTANCES_PARENTID] ON [dbo].[ProductInstances] ([ParentId])
END

IF EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'ParentConfigurationId' AND object_id = OBJECT_ID('[dbo].[ProductInstances]'))
BEGIN
	EXEC sp_rename N'[dbo].[ProductInstances].ParentConfigurationId', N'IDX_PRODUCTINSTANCES_PARENTCONFIGURATIONID', 'INDEX'
END

IF NOT EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'IDX_PRODUCTINSTANCES_PARENTCONFIGURATIONID' AND object_id = OBJECT_ID('[dbo].[ProductInstances]'))
BEGIN
	CREATE NONCLUSTERED INDEX [IDX_PRODUCTINSTANCES_PARENTCONFIGURATIONID] ON [dbo].[ProductInstances] ([ParentConfigurationId])
END
");
    }
}
