﻿using Common;
using Common.Entities;
using Geidea.Utils.Exceptions;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace Services.Acquirers;
public abstract class Acquirer
{
    protected static bool IsTIDExist(IQueryable<TerminalDataSetEntity> terminalDataSetEntities, string tid, string counterparty)
    {
        return terminalDataSetEntities.AsNoTracking()
            .Any(e => e.TID == tid && e.Counterparty == counterparty);
    }

    protected static void IsReachedToMax(string mId, string acquirerMIDMaxValue)
    {
        if (mId == acquirerMIDMaxValue)
        {
            throw new ServiceException(Errors.ReachedMaxMID);
        }
    }
    protected static void IsUAEReachedToMax(string mId, string acquirerMIDMaxValue)
    {
        if (mId == acquirerMIDMaxValue)
        {
            throw new ServiceException(Errors.ReachedMaxMID);
        }
    }
}
