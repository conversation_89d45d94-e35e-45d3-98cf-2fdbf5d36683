
BEGIN TRANSACTION;
-- Adding new fields to Products if they do not exist
    ALTER TABLE Products ADD NameAr NVARCHAR(255) NULL;

    ALTER TABLE Products ADD Name NVARCHAR(255) NULL;

    ALTER TABLE Products ADD Subname NVARCHAR(255) NULL;

    ALTER TABLE Products ADD SubnameAr NVARCHAR(255) NULL;

    ALTER TABLE Products ADD DescriptionAr NVARCHAR(255) NULL;

    ALTER TABLE Products ADD ProductLink NVARCHAR(255) NULL;

COMMIT;