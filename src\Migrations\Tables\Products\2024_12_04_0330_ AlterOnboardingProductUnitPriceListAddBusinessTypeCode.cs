﻿using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.Products;
[Migration(2024_12_04_0302)]
public class AlterOnboardingProductUnitPriceListAddBusinessTypeCode : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "OnboardingUnitPriceList.sql"));
    }
}
