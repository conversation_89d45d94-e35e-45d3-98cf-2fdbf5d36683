﻿CREATE OR ALTER PROCEDURE [dbo].[GleHistoryLog] @MerchantId UNIQUEIDENTIFIER
	,@OrderId UNIQUEIDENTIFIER
	,@ParentMerchantId UNIQUEIDENTIFIER
AS
BEGIN
	DECLARE @StoreId AS UNIQUEIDENTIFIER;

	SELECT TOP 1 @StoreId = s.[StoreId]
	FROM [GleTerminal] t
	INNER JOIN [GleStore] s ON s.[Id] = t.[GleStoreId]
	WHERE t.[OrderId] = @OrderId;

	DECLARE @HistoryOfGle TABLE (
		[ID] UNIQUEIDENTIFIER
		,[RegistrationType] VARCHAR(50)
		,[RequestDate] DATETIME
		,[SubmittedBy] VARCHAR(100)
		,[RegistrationStatus] VARCHAR(50)
		,[RegistrationResponse] NVARCHAR(MAX)
		,[ReferenceMMSId] VARCHAR(250)
		);

	INSERT @HistoryOfGle
	SELECT GleMerchantHistory.[Id]
		,CASE 
			WHEN GleMerchantHistory.UserCategoryCode = 'CHNMBS'
				THEN 'Master Business'
			WHEN GleMerchantHistory.UserCategoryCode = 'WSMBS'
				THEN 'Wholesaler'
			ELSE 'Merchant'
			END AS GleRegistrationType
		,GleMerchantHistory.[StartDate]
		,GleMerchantHistory.[UpdatedBy]
		,GleMerchantHistory.[GleRegistrationStatus]
		,GleMerchantHistory.[GleRegistrationResponse]
		,GleMerchantHistory.[ReferenceMMSId]
	FROM [dbo].[GleMerchant] FOR SYSTEM_TIME ALL AS GleMerchantHistory
	WHERE GleMerchantHistory.MerchantId IN (@ParentMerchantId ,@MerchantId)

	INSERT @HistoryOfGle
	SELECT GleStoreHistory.[Id]
		,'Store' AS GleRegistrationType
		,GleStoreHistory.[StartDate]
		,GleStoreHistory.[UpdatedBy]
		,GleStoreHistory.[GleRegistrationStatus]
		,GleStoreHistory.[GleRegistrationResponse]
		,GleStoreHistory.[ReferenceMMSId]
	FROM [dbo].[GleStore] FOR SYSTEM_TIME ALL AS GleStoreHistory
	WHERE GleStoreHistory.StoreId = @StoreId

	INSERT @HistoryOfGle
	SELECT GleTerminalHistory.[Id]
		,CASE 
		    WHEN GleTerminalHistory.[IsTerminalUser] = 0
				THEN 'Terminal'
			ELSE 'Terminal-User'
			END AS GleRegistrationType
		,GleTerminalHistory.[StartDate]
		,GleTerminalHistory.[UpdatedBy]
		,GleTerminalHistory.[GleRegistrationStatus]
		,GleTerminalHistory.[GleRegistrationResponse]
		,GleTerminalHistory.[ReferenceMMSId]
	FROM [dbo].[GleTerminal] FOR SYSTEM_TIME ALL AS GleTerminalHistory
	WHERE GleTerminalHistory.OrderId = @OrderId

	SELECT *
	FROM @HistoryOfGle gleHistory
	ORDER BY gleHistory.RequestDate DESC
END
GO