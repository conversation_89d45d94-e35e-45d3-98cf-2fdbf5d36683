﻿using AutoMapper;
using Common.Entities;
using Common.Enums.UnitPrice;
using Common.Models.ProductCommissionPrice;
using Common.Models.UnitPrice;
using Common.Repositories;
using Common.Services;
using DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace Services.Test.ProductCommissionPriceServiceTests;
public class CreateTests
{
    private readonly Mock<ILogger<ProductCommissionPriceService>> logger;
    private readonly Mock<IProductCommissionPriceRepository> CommissionPriceRepositoryMock;
    private readonly Mock<IMapper> mapperMock;
    private readonly ProductCommissionPriceService productCommissionPriceService;

    public CreateTests()
    {
        logger = new Mock<ILogger<ProductCommissionPriceService>>();
        CommissionPriceRepositoryMock = new Mock<IProductCommissionPriceRepository>();
        mapperMock = new Mock<IMapper>();
        productCommissionPriceService = new ProductCommissionPriceService(logger.Object, CommissionPriceRepositoryMock.Object, mapperMock.Object);
    }
    [Fact]
    public async Task CreateAsync_ShouldReturn_UnitPriceResponse_With_Created_And_Existing_Prices()
    {
        // Arrange
        var request = new ProductCommissionPriceCreateRequest
        {
            ProductIds = new List<Guid> { Guid.NewGuid() },
            MccIds = new List<Guid> { Guid.NewGuid() },
            BusinessTypeIds = new List<Guid> { Guid.NewGuid() },
            CommissionFeeIds = new List<Guid> { Guid.NewGuid() },
            FeeType = Common.Enums.ProductCommisssionPrices.FeeType.Flat,
            FeeValue = 10,
            BillingType = Common.Enums.ProductCommisssionPrices.CommisssionPricesBillingType.PostPaid,
            BillingFrequency = Common.Enums.ProductCommisssionPrices.PriceBillingFrequency.Monthly
        };
        var existingEntity = new ProductCommissionPriceEntity { };
        var newEntity = new ProductCommissionPriceEntity { };
        // Mock the repository to return an existing entity
        CommissionPriceRepositoryMock.Setup(r => r.GetExistCommissionPrices(It.IsAny<Expression<Func<ProductCommissionPriceEntity, bool>>>()))
                            .ReturnsAsync(new List<ProductCommissionPriceEntity> { existingEntity });
        // Mock the mapper to return a new entity for the new unit prices
        mapperMock.Setup(m => m.Map<ProductCommissionPriceEntity>(request))
                  .Returns(newEntity);
        // Act
        var result = await productCommissionPriceService.CreateAsync(request);
        // Assert
        Assert.NotNull(result);
    }
}
