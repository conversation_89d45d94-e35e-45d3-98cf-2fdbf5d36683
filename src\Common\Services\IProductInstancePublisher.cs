﻿using Common.Data.ProductType;
using Common.Entities;
using System;
using System.Collections.Generic;

namespace Common.Services;

public interface IProductInstancePublisher
{
    List<ProductTypes> ProductTypes { get; }

    void PublishCreatedEvent(ProductInstanceEntity productInstance);

    void PublishUpdatedEvent(ProductInstanceEntity productInstance);

    void PublishDeletedEvent(Guid productInstanceId);
}
