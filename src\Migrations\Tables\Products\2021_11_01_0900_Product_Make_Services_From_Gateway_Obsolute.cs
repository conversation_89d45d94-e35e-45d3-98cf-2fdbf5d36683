﻿using System;
using System.Collections.Generic;
using System.Text;
using FluentMigrator;

namespace Migrations.Tables.Products;

[Migration(2021_11_01_0900)]
public class Product_Make_Services_From_Gateway_Obsolute : ForwardOnlyMigration
{
    public override void Up()
    {
        Update.Table("Products").Set(new { Availability = "Obsolete" })
            .Where(new { Code = "GENERATE_REFERENCE", Version = 0, Counterparty = "GEIDEA_UAE" });

        Update.Table("Products").Set(new { Availability = "Obsolete" })
            .Where(new { Code = "QR_PAYMENT", Version = 0, Counterparty = "GEIDEA_UAE" });

        Update.Table("Products").Set(new { Availability = "Obsolete" })
            .Where(new { Code = "PAY_BY_LINK", Version = 0, Counterparty = "GEIDEA_UAE" });

        Update.Table("Products").Set(new { Availability = "Obsolete" })
            .Where(new { Code = "CASHOUT_VOUCHER_CREATION", Version = 0, Counterparty = "GEIDEA_UAE" });
    }
}
