﻿using Common;
using Common.Data;
using Common.Data.ProductType;
using Common.Entities;
using Common.Services;
using Geidea.Messages.GatewayConfiguration;
using Geidea.Messages.GatewayConfiguration.Messages;
using Geidea.Messages.GatewayConfiguration.Rules;
using Geidea.Messages.Gsdk;
using Geidea.Utils.Exceptions;
using GeideaPaymentGateway.Utils.RabbitMQ;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text.Json;
using static Common.Data.Helpers.DataDefaultValuesHelper;
using Constants = Common.Constants;
using SubMerchantInformation = Geidea.Messages.GatewayConfiguration.SubMerchantInformation;

namespace Messaging;

public class GatewayConfigurationPublisher : ConfigurationPublisherPublisher, IGatewayProductInstancePublisher
{
    private const string GatewayConfigurationCreated = "gateway.configuration.created.event";
    private const string GatewayConfigurationUpdated = "gateway.configuration.updated.event";
    private const string GatewayConfigurationDeleted = "gateway.configuration.deleted.event";

    public List<ProductTypes> ProductTypes => new() { Common.Data.ProductType.ProductTypes.GWAY };

    public GatewayConfigurationPublisher(
        IOptionsMonitor<RabbitMqConfig> options,
        ILogger<GatewayConfigurationPublisher> logger,
        IHttpContextAccessor contextAccessor,
        IConnectionFactory factory)
        : base(options, logger, contextAccessor, factory, "GatewayConfiguration.Fanout")
    {
    }

    public void PublishCreatedEvent(ProductInstanceEntity productInstance)
    {
        PublishCreatedEvent(productInstance, null);
    }

    public void PublishCreatedEvent(ProductInstanceEntity productInstance, ProductInstanceEntity? meezaProductInstance)
    {
        if (!IsConfigValid)
            return;

        var message = new GatewayConfigurationCreatedEvent
        {
            Header = BuildHeader(GatewayConfigurationCreated),
            GatewayConfiguration = BuildGatewayConfiguration(productInstance, meezaProductInstance)
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(GatewayConfigurationCreated, messageAsByteArray);
    }

    public void PublishUpdatedEvent(ProductInstanceEntity productInstance)
    {
        if (!IsConfigValid)
            return;

        var message = new GatewayConfigurationUpdatedEvent
        {
            Header = BuildHeader(GatewayConfigurationUpdated),
            GatewayConfiguration = BuildGatewayConfiguration(productInstance)
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(GatewayConfigurationUpdated, messageAsByteArray);
    }

    public void PublishDeletedEvent(Guid productInstanceId)
    {
        if (!IsConfigValid)
            return;

        var message = new GatewayConfigurationDeletedEvent
        {
            Header = BuildHeader(GatewayConfigurationDeleted),
            GatewayConfigurationId = productInstanceId
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(GatewayConfigurationDeleted, messageAsByteArray);
    }

    private GatewayConfiguration BuildGatewayConfiguration(ProductInstanceEntity gatewayProductInstance, ProductInstanceEntity? meezaProductInstance = null)
    {
        if (gatewayProductInstance.Data is not GatewayData gatewayData)
        {
            logger.LogError("Expected gateway product instance.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.ExpectedGateway);
        }

        var result = new GatewayConfiguration
        {
            GatewayConfigurationId = gatewayProductInstance.Id,
            CompanyId = gatewayProductInstance.CompanyId ?? Guid.Empty,
            MerchantId = gatewayProductInstance.StoreId ?? Guid.Empty,
            PaymentMethods = gatewayProductInstance.Children
        .Where(c => c.Product.Type == Common.Data.ProductType.ProductTypes.SCHEME.ToString())
        .Where(c => !c.DeletedFlag)
        .Select(c => c.Product.Code)
        .ToList(),
            MpgsMsoProvider = gatewayData.MpgsMsoProvider,
            MpgsApiKey = gatewayData.MpgsAccounts.FirstOrDefault()?.MpgsApiKey,
            MpgsMerchantId = gatewayData.MpgsAccounts.FirstOrDefault()?.MpgsMerchantId,
            Mid = gatewayProductInstance.Mid,
            MpgsAccounts = gatewayData.MpgsAccounts.Select(mpgsAccount => new MpgsAccount
            {
                MpgsMerchantId = mpgsAccount.MpgsMerchantId,
                MpgsApiKey = mpgsAccount.MpgsApiKey,
                CardBrands = mpgsAccount.CardBrands,
                CreatedDate = mpgsAccount.CreatedDate,
                Currencies = mpgsAccount.Currencies
            }).ToList(),
            MpgsHistory = gatewayData.MpgsHistory.Select(mpgsHistory => new MpgsAccountHistory
            {
                MpgsMerchantId = mpgsHistory.MpgsMerchantId,
                MpgsApiKey = mpgsHistory.MpgsApiKey,
                CardBrands = mpgsHistory.CardBrands,
                Currencies = mpgsHistory.Currencies,
                CreatedDate = mpgsHistory.CreatedDate,
                BankProvider = mpgsHistory.BankProvider,
                DeactivatedDate = mpgsHistory.DeactivatedDate,
                Operation = mpgsHistory.Operation
            }).ToList(),
            MsoSubMerchantInfo = gatewayData.MsoSubMerchantInfo.Select(v => new Geidea.Messages.GatewayConfiguration.MsoSubMerchantInfo()
            {


                Mso = v.Mso,

                Mcc = v.Mcc,

                SubMerchantInfo = new()
                {
                    Id = v.SubMerchantInfo?.Id,
                    RegisteredName = v.SubMerchantInfo?.RegisteredName,
                    TradingName = v.SubMerchantInfo?.TradingName,
                    Address = new Address
                    {
                        City = v.SubMerchantInfo?.City,
                        Country = v.SubMerchantInfo?.Country,
                        Governorate = v.SubMerchantInfo?.Governorate,
                        ZipCode = v.SubMerchantInfo?.ZipCode,
                        StreetAndNumber = v.SubMerchantInfo?.StreetAndNumber
                    },
                    Contact = new Contact
                    {
                        Email = v.SubMerchantInfo?.Email,
                        PhoneNumber = v.SubMerchantInfo?.PhoneNumber,
                        CountryPrefix = v.SubMerchantInfo?.CountryPrefix
                    }
                }
            }).ToList() ?? new(),
            GsdkMid = gatewayData.GsdkMid,
            GsdkTid = gatewayData.GsdkTid,
            GsdkSecretKey = gatewayData.GsdkSecretKey,
            CyberSourceMerchantId = gatewayData.CyberSourceMerchantId,
            CyberSourceMerchantKeyId = gatewayData.CyberSourceMerchantKeyId,
            CyberSourceSharedSecretKey = gatewayData.CyberSourceSharedSecretKey,
            DefaultPaymentOperation = gatewayData.DefaultPaymentOperation,
            ApplePartnerInternalMerchantIdentifier = gatewayData.ApplePartnerInternalMerchantIdentifier,
            AppleCertificatePrivateKey = gatewayData.AppleCertificatePrivateKey,
            ApplePaymentProcessingCertificate = gatewayData.ApplePaymentProcessingCertificate,
            ApplePaymentProcessingCertificateExpiryDate = gatewayData.ApplePaymentProcessingCertificateExpiryDate,
            AppleCertificatePrivateKeyNew = gatewayData.AppleCertificatePrivateKeyNew,
            ApplePaymentProcessingCertificateNew = gatewayData.ApplePaymentProcessingCertificateNew,
            ApplePaymentProcessingCertificateExpiryDateNew = gatewayData.ApplePaymentProcessingCertificateExpiryDateNew,
            IsApplePayWebEnabled = gatewayData.IsApplePayWebEnabled,
            IsApplePayWebRecurringEnabled = gatewayData.IsApplePayWebRecurringEnabled,
            IsApplePayMobileEnabled = gatewayData.IsApplePayMobileEnabled,
            IsApplePayMobileRecurringEnabled = gatewayData.IsApplePayMobileRecurringEnabled,
            IsApplePayMobileCertificateAvailable = gatewayData.IsApplePayMobileCertificateAvailable,
            AppleDeveloperId = gatewayData.AppleDeveloperId,
            IsMeezaDigitalEnabled = gatewayData.IsMeezaDigitalEnabled,
            MerchantNameEn = gatewayData.MerchantName,
            MerchantNameAr = gatewayData.MerchantNameAr,
            MerchantWebsite = gatewayData.MerchantWebsite,
            MerchantDomain = gatewayData.MerchantDomain,
            MerchantLogoUrl = gatewayData.MerchantLogoUrl,
            MerchantCountry = gatewayData.MerchantCountry,
            Mcc = gatewayData.Mcc,
            CallbackUrl = gatewayData.CallbackUrl,
            MaxRetryCount = gatewayData.MaxRetryCount,
            NumberOfQuickRetries = gatewayData.NumberOfQuickRetries,
            PeriodBetweenQuickRetries = gatewayData.PeriodBetweenQuickRetries,
            PeriodBetweenSlowRetries = gatewayData.PeriodBetweenSlowRetries,
            ApiPassword = gatewayData.ApiPassword ?? string.Empty,
            IsTest = gatewayData.IsTest,
            IsLuhnCheckActive = gatewayData.IsLuhnCheckActive,
            IsActive = gatewayData.IsActive,
            AllowedInitiatedByValues = gatewayData.AllowedInitiatedByValues != null && gatewayData.AllowedInitiatedByValues.Any()
                ? gatewayData.AllowedInitiatedByValues
                : new List<string> { Constants.InitiatedByInternet },
            IsMulticurrencyEnabled = gatewayData.IsMulticurrencyEnabled,
            Currencies = gatewayData.Currencies,
            IsMulticurrencySettlementEnabled = gatewayData.IsMulticurrencySettlementEnabled,
            SettlementCurrency = gatewayData.SettlementCurrency,
            IsMarkUpEnabled = gatewayData.IsMarkUpEnabled,
            MarkUpForMCC = gatewayData.MarkUpForMCC,
            CardBrandProviders = gatewayData.CardBrandProviders.Select(cbp => new CardBrandProvider()
            {
                CardBrand = cbp.CardBrand,
                AcquiringProvider = cbp.AcquiringProvider,
                ThreeDSecureProvider = cbp.ThreeDSecureProvider,
            }).ToList(),
            IsCallbackEnabled = gatewayData.IsCallbackEnabled,
            IsPaymentMethodSelectionEnabled = gatewayData.IsPaymentMethodSelectionEnabled,
            IsTransactionReceiptEnabled = gatewayData.IsTransactionReceiptEnabled,
            IsFederationToGsdkEnabled = gatewayData.IsFederationToGsdkEnabled,
            IsRefundEnabled = gatewayData.IsRefundEnabled,
            UseMpgsApiV60 = gatewayData.UseMpgsApiV60,
            MerchantPaymentNotification = gatewayData.MerchantPaymentNotification,
            CustomerPaymentNotification = gatewayData.CustomerPaymentNotification,
            IsCallbackEmailNotificationEnabled = gatewayData.IsCallbackEmailNotificationEnabled,
            MerchantNotificationEmails = gatewayData.MerchantNotificationEmail?.Split(';').ToList() ?? new List<string>(),
            CustomerNotificationFromEmail = gatewayData.CustomerNotificationFromEmail,
            CallbackNotificationEmail = gatewayData.CallbackNotificationEmail,
            MerchantEmail = gatewayData.MerchantEmail,
            MinAmount = gatewayData.MinAmount,
            MaxAmount = gatewayData.MaxAmount,
            IsValuBnplEnabled = gatewayData.IsValuBnplEnabled,
            ValuProductId = gatewayData.ValuProductId,
            ValuStoreId = gatewayData.ValuStoreId,
            ValuVendorId = gatewayData.ValuVendorId,
            ValuCapLimit = gatewayData.ValuCapLimit,
            IsShahryCnpBnplEnabled = gatewayData.IsShahryCnpBnplEnabled,
            ShahryCnpBnplBranchCode = gatewayData.ShahryCnpBnplBranchCode,
            ShahryCnpBnplMerchantCode = gatewayData.ShahryCnpBnplMerchantCode,
            IsShahryCpBnplEnabled = gatewayData.IsShahryCpBnplEnabled,
            ShahryCpBnplBranchCode = gatewayData.ShahryCpBnplBranchCode,
            ShahryCpBnplMerchantCode = gatewayData.ShahryCpBnplMerchantCode,
            IsSouhoolaCnpBnplEnabled = gatewayData.IsSouhoolaCnpBnplEnabled,
            SouhoolaMerchantNationalId = gatewayData.SouhoolaMerchantNationalId,
            SouhoolaMerchantPhoneNumber = gatewayData.SouhoolaMerchantPhoneNumber,
            SouhoolaAccessKey = gatewayData.SouhoolaAccessKey,
            IsSouhoolaCpBnplEnabled = gatewayData.IsSouhoolaCpBnplEnabled,
            IsSouhoolaCpMerchantRegistered = gatewayData.IsSouhoolaCpMerchantRegistered,
            SouhoolaCpBnplNationalId = gatewayData.SouhoolaCpBnplNationalId,
            SouhoolaCpBnplGlobalId = gatewayData.SouhoolaCpBnplGlobalId,
            SouhoolaCpBnplUsername = gatewayData.SouhoolaCpBnplUserName,
            SouhoolaCpBnplPassword = gatewayData.SouhoolaCpBnplPassword,
            SouhoolaCnpUserName = gatewayData.SouhoolaCnpUserName,
            SouhoolaCnpPassword = gatewayData.SouhoolaCnpPassword,
            IsCustomerBillingAddressMandatory = gatewayData.IsCustomerBillingAddressMandatory,
            IsCustomerEmailMandatory = gatewayData.IsCustomerEmailMandatory,
            IsCustomerShippingAddressMandatory = gatewayData.IsCustomerShippingAddressMandatory,
            AllowCashOnDeliveryShahry = gatewayData.AllowCashOnDeliveryShahry,
            AllowCashOnDeliverySouhoola = gatewayData.AllowCashOnDeliverySouhoola,
            AllowCashOnDeliveryValu = gatewayData.AllowCashOnDeliveryValu,
            AllowDownPaymentValu = gatewayData.AllowDownPaymentValu,
            IsBankInstallmentsCnpEnabled = gatewayData.IsBankInstallmentsCnpEnabled,
            AddOnFeesPbl = GetAddOnFeesPbl(gatewayData),
            ExtraChargesPbl = GetExtraChargesPbl(gatewayData),
            IsAlwaysShowBNPLReceipt = gatewayData.IsAlwaysShowBNPLReceipt,
            SubMerchantInformation = new SubMerchantInformation
            {
                Id = gatewayData.SubMerchantInformation.Id,
                RegisteredName = gatewayData.SubMerchantInformation.RegisteredName,
                TradingName = gatewayData.SubMerchantInformation.TradingName,
                Address = new Address
                {
                    City = gatewayData.SubMerchantInformation.City,
                    Country = gatewayData.SubMerchantInformation.Country,
                    Governorate = gatewayData.SubMerchantInformation.Governorate,
                    ZipCode = gatewayData.SubMerchantInformation.ZipCode,
                    StreetAndNumber = gatewayData.SubMerchantInformation.StreetAndNumber
                },
                Contact = new Contact
                {
                    Email = gatewayData.SubMerchantInformation.Email,
                    PhoneNumber = gatewayData.SubMerchantInformation.PhoneNumber,
                    CountryPrefix = gatewayData.SubMerchantInformation.CountryPrefix
                }
            },
            EnableSubMerchantInformation = gatewayData.EnableSubMerchantInformation,
            Rules = GetRules(gatewayData),
            IsRecurringPaylinksEnabled = gatewayData.IsRecurringPaylinksEnabled,
            IsTokenizationEnabled = gatewayData.IsTokenizationEnabled,
            IsCustomerInitiatedTransactionsEnabled = gatewayData.IsCustomerInitiatedTransactionsEnabled,
            IsCvvRequiredForTokenPayments = gatewayData.IsCvvRequiredForTokenPayments ?? IsMerchantCountryEgypt(gatewayData.MerchantCountry),
            Is3dsRequiredForTokenPayments = gatewayData.Is3dsRequiredForTokenPayments ?? IsMerchantCountryEgypt(gatewayData.MerchantCountry),
            IsMerchantInitiatedTransactionsEnabled = gatewayData.IsMerchantInitiatedTransactionsEnabled,
            IsRecurringPaymentsEnabled = gatewayData.IsRecurringPaymentsEnabled,
            IsUnscheduledPaymentsEnable = gatewayData.IsUnscheduledPaymentsEnable,
            IsSmartRoutingEnabled = gatewayData.IsSmartRoutingEnabled,
            MadaAccounts = gatewayData.MadaAccounts.Select(madaAccount => new MadaAccount
            {
                MadaApiKey = madaAccount.MadaApiKey,
                MadaMerchantId = madaAccount.MadaMerchantId,
                AcquirerMid = madaAccount.AcquirerMid,
                CardInfo = madaAccount.CardInfo.Select(cardInfo => new CardInfo
                {
                    CardBrand = cardInfo.CardBrand,
                    PrimaryGatewayId = cardInfo.PrimaryGatewayId
                }).ToList(),
            }).ToList(),
            IsReferenceIDUnique = gatewayData.IsReferenceIDUnique,
            IsUseRegisteredPhoneEnabled = gatewayData.IsUseRegisteredPhoneEnabled,
            ExcessiveCapturePercentage = gatewayData.ExcessiveCapturePercentage,
            IsExcessiveCaptureEnabled = gatewayData.IsExcessiveCaptureEnabled,
            IsGooglePayEnabled = gatewayData.IsGooglePayEnabled,
            GooglePayMerchantId = gatewayData.GooglePayMerchantId,
            IsTamaraEnabled = gatewayData.IsTamaraEnabled,
            TamaraApiToken = gatewayData.TamaraApiToken,
            TamaraPublicKey = gatewayData.TamaraPublicKey,
            IsSaveCardEnabled = gatewayData.IsSaveCardEnabled,
            IsRefundPeriodEnabled = gatewayData.IsRefundPeriodEnabled,
            RefundPeriodDays = gatewayData.RefundPeriodDays,
            IsStcPayEnabled = gatewayData.IsStcPayEnabled,
            StcPayMerchantId = gatewayData.StcPayMerchantId,
            PayByGeideaCode = new()
            {
                IsEnabled = gatewayData.PayByGeideaCode.IsEnabled,
                PaymentReferenceExpirationDays = gatewayData.PayByGeideaCode.PaymentReferenceExpirationDays,
                RefundReferenceExpirationDays = gatewayData.PayByGeideaCode.RefundReferenceExpirationDays
            },
            IsVisaInstallmentEnabled = gatewayData.IsVisaInstallmentEnabled,
            PartnerReferenceID = gatewayData.PartnerReferenceID,
            IsSamsungPayWebEnabled = gatewayData.IsSamsungPayWebEnabled,
            IsSamsungPayAppEnabled = gatewayData.IsSamsungPayAppEnabled,
            SamsungPayCsr = gatewayData.SamsungPayCsr,
            SamsungPayCertificatePrivateKey = gatewayData.SamsungPayCertificatePrivateKey,
            IsMerchantReportingAPIEnabled = gatewayData.IsMerchantReportingAPIEnabled,
            IsSecureHPPVersionEnabled = gatewayData.IsSecureHPPVersionEnabled,
            Tabby = new()
            {
                IsTabbyEnabled = gatewayData.Tabby.IsTabbyEnabled,
                MerchantCode = gatewayData.Tabby.MerchantCode,
                SecretKey = gatewayData.Tabby.SecretKey,
                TabbyPublicKey = gatewayData.Tabby.TabbyPublicKey,
            },
            IsPartialRefundEnabled = gatewayData.IsPartialRefundEnabled,
            IsCaptureEnabled = gatewayData.IsCaptureEnabled,
            IsPartialCaptureEnabled = gatewayData.IsPartialCaptureEnabled,
            IsMerchantBrandingForPblQrEnabled = gatewayData.IsMerchantBrandingForPblQrEnabled,
            CyberSourceMsoProvider = gatewayData.CyberSourceMsoProvider,
            CyberSourceAccounts = gatewayData.CyberSourceAccounts.Select(cybersourceAccount => new CybersourceAccount
            {
                CyberSourceMerchantId = cybersourceAccount.CyberSourceMerchantId,
                CyberSourceMerchantKeyId = cybersourceAccount.CyberSourceMerchantKeyId,
                CyberSourceSharedSecretKey = cybersourceAccount.CyberSourceSharedSecretKey,
                CardBrands = cybersourceAccount.CardBrands,
                CreatedDate = cybersourceAccount.CreatedDate,
                Currencies = cybersourceAccount.Currencies
            }).ToList(),
            InstaPay = new()
            {
                IsInstaPayEnabled = gatewayData.InstaPay.IsInstaPayEnabled,
                IpnBank = gatewayData.InstaPay.IpnBank,
                IpnMerchantId = gatewayData.InstaPay.IpnMerchantId,
                IpnSharedSecret = gatewayData.InstaPay.IpnSharedSecret,
                IsIpnQRCodeEnabled = gatewayData.InstaPay.IsIpnQRCodeEnabled,
                IsIpnLinkEnabled = gatewayData.InstaPay.IsIpnLinkEnabled,
                IsIpnCollectRequestEnabled = gatewayData.InstaPay.IsIpnCollectRequestEnabled
            },
            IsUpdateAuthorizationEnabled = gatewayData.IsUpdateAuthorizationEnabled,
            IsUpdateAuthorizationPercentageEnabled = gatewayData.IsUpdateAuthorizationPercentageEnabled,
            UpdateAuthorizationPercentage = gatewayData.UpdateAuthorizationPercentage,
            IsGenerateAndUseNetworkTokenEnabled = gatewayData.IsGenerateAndUseNetworkTokenEnabled,
            IsUseNetworkTokenEnabled = gatewayData.IsUseNetworkTokenEnabled,
            IsSendNetworkTokenToMerchantEnabled = gatewayData.IsSendNetworkTokenToMerchantEnabled,
            NetworkTokenEncryptionKey = gatewayData.NetworkTokenEncryptionKey,
            NetworkTokenCallBackUrl = gatewayData.NetworkTokenCallBackUrl,
            IsAuthorizationRefundEnable = gatewayData.IsAuthorizationRefundEnable,
            AddOnFeesHpp = GetAddOnFeesHpp(gatewayData),
            ExtraChargesHpp = GetExtraChargesHpp(gatewayData),
        };

        if (meezaProductInstance != null && meezaProductInstance.Data is MeezaData meezaData)
        {
            result.MeezaConfigurationId = meezaProductInstance.Id;
            result.MeezaMerchantId = meezaData.MeezaMerchantId;
            result.MeezaRegistrationStatus = meezaData.RegistrationStatus;
        }

        Guid.TryParse(gatewayData.MerchantGatewayKey, out var guid);
        result.GeideaGatewayKey = guid;

        return result;
    }

    private static Rules GetRules(GatewayData gatewayData) => new()
    {
        RuleAttemptedAuth = gatewayData.Rules.RuleAttemptedAuth != null
            ? new()
            {
                CardTypes = gatewayData.Rules.RuleAttemptedAuth.CardTypes,
                Threshold = gatewayData.Rules.RuleAttemptedAuth.Threshold
            }
            : null,
        RuleNotEnrolled = gatewayData.Rules.RuleNotEnrolled != null
            ? new()
            {
                CardTypes = gatewayData.Rules.RuleNotEnrolled.CardTypes,
                Threshold = gatewayData.Rules.RuleNotEnrolled.Threshold
            }
            : null
    };

    private static AddOnFees GetAddOnFeesPbl(GatewayData gatewayData) => new()
    {
        IsAddOnFeesEnabled = gatewayData.AddOnFeesPbl.IsEnabled,
        PBLAddOnFees = gatewayData.AddOnFeesPbl.Fees is not null ?
        new()
        {
            Label = gatewayData.AddOnFeesPbl.Fees.Label,
            Value = gatewayData.AddOnFeesPbl.Fees.Value,
            ValueType = (AmountType?)gatewayData.AddOnFeesPbl.Fees.ValueType
        }
        :
        null
    };

    private static ExtraCharges GetExtraChargesPbl(GatewayData gatewayData) => new()
    {
        IsExtraChargesEnabled = gatewayData.ExtraChargesPbl.IsEnabled,
        PBLExtraCharges = gatewayData.ExtraChargesPbl.Charges is not null ?
        new()
        {
            Label = gatewayData.ExtraChargesPbl.Charges.Label,
            Value = gatewayData.ExtraChargesPbl.Charges.Value,
            ValueType = (AmountType?)gatewayData.ExtraChargesPbl.Charges.ValueType
        }
        :
        null
    };

    private static AddOnFees GetAddOnFeesHpp(GatewayData gatewayData) => new()
    {
        IsAddOnFeesEnabled = gatewayData.AddOnFeesHpp.IsEnabled,
        PBLAddOnFees = gatewayData.AddOnFeesHpp.Fees is not null ?
        new()
        {
            Label = gatewayData.AddOnFeesHpp.Fees.Label,
            Value = gatewayData.AddOnFeesHpp.Fees.Value,
            ValueType = (AmountType?)gatewayData.AddOnFeesHpp.Fees.ValueType
        }
        :
        null
    };

    private static ExtraCharges GetExtraChargesHpp(GatewayData gatewayData) => new()
    {
        IsExtraChargesEnabled = gatewayData.ExtraChargesHpp.IsEnabled,
        PBLExtraCharges = gatewayData.ExtraChargesHpp.Charges is not null ?
        new()
        {
            Label = gatewayData.ExtraChargesHpp.Charges.Label,
            Value = gatewayData.ExtraChargesHpp.Charges.Value,
            ValueType = (AmountType?)gatewayData.ExtraChargesHpp.Charges.ValueType
        }
        :
        null
    };
}