﻿using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.Products;

[Migration(2021_09_27_1300)]
public class Prices_UpdatePaymentGatewayBundlePrice : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scrip<PERSON>", "PriceUpdates_Egypt_PaymentGatewayBundle.sql"));
    }
}
