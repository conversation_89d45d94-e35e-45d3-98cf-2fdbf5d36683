﻿using System;
using System.Threading.Tasks;
using Common.Models.Gle;
using Microsoft.AspNetCore.JsonPatch;

namespace Common.Services.Gle;

public interface IGleStoreService
{
    public Task AddGleStoreAsync(GleStoreRequest createGleStoreRequest);
    public Task UpdateGleStoreAsync(Guid gleStoreId, JsonPatchDocument<UpdateGleStoreRequest> updateDocument);
    public Task<GleStore?> GetGleStoreByStoreIdAsync(Guid storeId);
    public Task<GleHierarchyByStore> GetGleHierarchyByStoreIdAsync(Guid storeId);
}