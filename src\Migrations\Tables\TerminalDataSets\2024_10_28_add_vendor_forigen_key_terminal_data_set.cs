﻿using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.TerminalDataSets;

[Migration(2024_10_28_1252)]
public class AddVendorForigenKeyTerminalDataSet : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
                    AppDomain.CurrentDomain.BaseDirectory +
                    Path.Combine("Scripts", "AddVendorForigenKeyInTerminalDataSet.sql"));
    }
}
