﻿using System;

namespace Common.Models;

public class PriceRequest
{
    public string? ChargeFrequency { get; set; } = null!;
    public string ChargeType { get; set; } = null!;
    public bool ExemptFromVAT { get; set; }
    public Guid ProductId { get; set; }

    public int? PerItemPrice { get; set; }
    public int? PercentagePrice { get; set; }
    public int? Threshold { get; set; }
    public string? Group { get; set; }

    /// <summary>
    /// Could be LT,LTE,GT,GTE
    /// </summary>
    public string? ThresholdType { get; set; }

    /// <summary>
    /// Priority of price rules when Threshold is not null. 
    /// Evaluation goes from high to low and last rule that evaluates to TRUE wins.
    /// </summary>
    public int Priority { get; set; } = 1;
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public string? Currency { get; set; } = null!;
    public int? RentalPeriod { get; set; }
    public int? MaxPrice { get; set; }
}
