﻿DECLARE @ProductId UNIQUEIDENTIFIER

EXEC NewProductVersion_v2 'GO_AIR', 5, 'GO_AIR', 6, 1,'GEIDEA_SAUDI'
SELECT TOP 1 @ProductId = ID FROM Products WHERE Code='GO_AIR' AND [Version] = 6 AND CounterParty='GEIDEA_SAUDI'
UPDATE [PRODUCTS].[dbo].[Prices] SET PerItemPrice = 0 WHERE ChargeType = 'SARIE_CHARGE' and ProductId = @ProductId

EXEC NewProductVersion_v2 'GO_SMART', 8, 'GO_SMART', 9, 1,'GEIDEA_SAUDI'
SELECT TOP 1 @ProductId = ID FROM Products WHERE Code='GO_SMART' AND [Version] = 9 AND CounterParty='GEIDEA_SAUDI'
UPDATE [PRODUCTS].[dbo].[Prices] SET PerItemPrice = 0 WHERE ChargeType = 'SARIE_CHARGE' and ProductId = @ProductId

EXEC NewProductVersion_v2 'PRO_SMART', 7, 'PRO_SMART', 8, 1,'GEIDEA_SAUDI'
SELECT TOP 1 @ProductId = ID FROM Products WHERE Code='PRO_SMART' AND [Version] = 8 AND CounterParty='GEIDEA_SAUDI'
UPDATE [PRODUCTS].[dbo].[Prices] SET PerItemPrice = 0 WHERE ChargeType = 'SARIE_CHARGE' and ProductId = @ProductId

EXEC NewProductVersion_v2 'PAYMENT_GATEWAY_BUNDLE', 3, 'PAYMENT_GATEWAY_BUNDLE', 4, 1,'GEIDEA_SAUDI'
SELECT TOP 1 @ProductId = ID FROM Products WHERE Code='PAYMENT_GATEWAY_BUNDLE' AND [Version] = 4 AND CounterParty='GEIDEA_SAUDI'
UPDATE [PRODUCTS].[dbo].[Prices] SET PerItemPrice = 0 WHERE ChargeType = 'SARIE_CHARGE' and ProductId = @ProductId
