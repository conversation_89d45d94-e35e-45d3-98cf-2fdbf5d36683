﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Geidea.Utils.DataAccess.Entities;
using Common.Enums.UnitPrice;

namespace Common.Entities;
public class UnitPriceEntity : AuditableEntity<Guid>
{
    public Guid ProductID { get; set; }
    public ProductEntity Product { get; set; } = null!;

    public Guid MCCID { get; set; }
    public Mcc Mcc { get; set; } = null!;

    public Guid BusinessTypeID { get; set; }
    public BusinessTypeEntity BusinessType { get; set; } = null!;

    public decimal UnitPrice { get; set; }
    public decimal VATRate { get; set; }

    public VatType VATType { get; set; }
    public BillingType BillingType { get; set; }
    public BillingFrequency BillingFrequency { get; set; }

}
