﻿BEGIN TRANSACTION;

DECLARE @CategoryId UNIQUEIDENTIFIER;
DECLAR<PERSON> @GoSmartBpBundleId UNIQUEIDENTIFIER;
DECLARE @BillPaymentServiceId UNIQUEIDENTIFIER;
DECLARE @GoSmartBundleId UNIQUEIDENTIFIER;
DECLARE @CounterpartyHeader NVARCHAR(255) = 'GEIDEA_EGYPT';
DECLARE @Currency NVARCHAR(3) = 'EGP';
DECLARE @CategoryCode NVARCHAR(255) = 'GO_FAMILY';
DECLARE @ServiceName AS NVARCHAR(100) = 'BILL_PAYMENT';
DECLARE @GoSmartBillPaymentCode as NVARCHAR(30) = 'GO_SMART_BP';
DECLARE @BillPaymentsServiceVersion as INT = 1;
DECLARE @BillPaymentsPreviousServiceVersion as INT = 0;

PRINT('Script start.');

-- Mark previous BILL_PAYMENT service as Obsolete
UPDATE [PRODUCTS].[dbo].[Products] set Availability = 'Obsolete' where Code = 'BILL_PAYMENT' and Counterparty = @CounterPartyHeader and Version = @BillPaymentsPreviousServiceVersion

PRINT('Checking for existence of BILL_PAYMENT service.');
-- Check if BILL_Payment with this version exists
IF(@serviceName IN (SELECT pr.Code FROM [PRODUCTS].[dbo].[Products] AS pr where pr.Type = 'SERVICES' and pr.Counterparty = @CounterPartyHeader and pr.Version = @BillPaymentsServiceVersion))
-- Check if BILL_Payment with this version and is live exists
	IF(@serviceName IN (SELECT pr.Code FROM [PRODUCTS].[dbo].[Products] AS pr where pr.Type = 'SERVICES' and pr.Counterparty = @CounterPartyHeader and pr.Version = @BillPaymentsServiceVersion and pr.Availability = 'Live'))
		PRINT('Found BILL_PAYMENT service that is of specified version and is Live')
	ELSE PRINT('Found not available BILL_PAYMENT service of specified version manual intervention required.')
ELSE INSERT INTO [PRODUCTS] ([Availability],[Code],[Type],[ValidFrom],[CreatedBy],[CreatedDate],[DisplayOrder],[Version],[Counterparty],[SalesChannel]) VALUES ('Live',@serviceName,'SERVICES',GETUTCDATE(),'n/a',GETUTCDATE(),2,@BillPaymentsServiceVersion,@CounterpartyHeader,'Shop');

PRINT('Checking GO_SMART_BP bundle.')
IF (@goSmartBillPaymentCode NOT IN (SELECT pr.Code FROM [PRODUCTS].[dbo].[Products] AS pr where pr.Type = 'BUNDLE' and pr.Counterparty = @CounterPartyHeader))
	INSERT INTO [PRODUCTS] ([Availability],[Code],[Type],[ValidFrom],[CreatedBy],[CreatedDate],[DisplayOrder],[Version],[Counterparty],[SalesChannel]) VALUES ('Live',@goSmartBillPaymentCode,'BUNDLE',GETUTCDATE(),'n/a',GETUTCDATE(),2,0,@CounterpartyHeader,'Shop')
ELSE PRINT('GO_SMART_BP bundle for set counterparty was found no need to insert.')

PRINT('Retrieving BILL_PAYMENT service Id')
SELECT TOP 1 @billPaymentServiceId = [Id] FROM [PRODUCTS].[dbo].[Products] AS pr where pr.Type = 'SERVICES' AND pr.CounterParty = @CounterpartyHeader AND pr.Version = @BillPaymentsServiceVersion
PRINT('ID is :')PRINT(@billPaymentServiceId)

PRINT('Retrieving existing uniqueidentifiers from db')
--get newly created bundle
SELECT TOP 1 @GoSmartBpBundleId = [Id] FROM [PRODUCTS] WHERE [Code] = @goSmartBillPaymentCode AND [Counterparty] = @CounterpartyHeader

PRINT('GO_SMART_BP bundle Id is : ')PRINT(@GoSmartBpBundleId)

--get id of the category
SELECT TOP 1 @CategoryId = [Id] FROM [Category] WHERE [Code] = @CategoryCode AND [Counterparty] = @CounterpartyHeader AND [DeletedFlag] = 0

PRINT('Checking Product categories')
IF(NOT EXISTS (SELECT * FROM [ProductCategories] where ProductId =  @GoSmartBpBundleId AND [CategoryId] = @CategoryId)) 
	INSERT INTO [ProductCategories] ([ProductId],[CategoryId]) VALUES (@GoSmartBpBundleId,@CategoryId)
ELSE PRINT('PRODUCT Category Exists no need to insert.')

PRINT('Checking Prices')
IF(NOT EXISTS (SELECT * FROM [Prices] WHERE [ProductId] = @GoSmartBpBundleId)) 
	INSERT INTO [Prices] ([ChargeFrequency],[ChargeType],[ExemptFromVAT],[ProductId],[PerItemPrice],[Priority],[ValidFrom],[DeletedFlag],[CreatedBy],[CreatedDate],[UpdatedBy],[Currency]) VALUES ('MONTH','RECCURRING_CHARGE',0,@GoSmartBpBundleId,0,1,GETUTCDATE(),0,'00000000-0000-0000-0000-000000000000',GETUTCDATE(),'00000000-0000-0000-0000-000000000000',@Currency)
ELSE PRINT('Price for GO_SMART_BP was found no need to insert.')

IF(NOT EXISTS (SELECT * FROM [Prices] WHERE [ProductId] = @BillPaymentServiceId)) 
	INSERT INTO [Prices] ([ChargeFrequency],[ChargeType],[ExemptFromVAT],[ProductId],[PerItemPrice],[Priority],[ValidFrom],[DeletedFlag],[CreatedBy],[CreatedDate],[UpdatedBy],[Currency]) VALUES ('MONTH','RECCURRING_CHARGE',0,@BillPaymentServiceId,0,1,GETUTCDATE(),0,'00000000-0000-0000-0000-000000000000',GETUTCDATE(),'00000000-0000-0000-0000-000000000000',@Currency)
ELSE PRINT('Price for BILL_PAYMENT service was found no need to insert.')

-- Copy GO_SMART configuration to GO_SMART_BP
PRINT('Transfering existsing GO_SMART configuration to GO_SMART_BP')
SELECT TOP 1 @GoSmartBundleId = (SELECT TOP 1 Id FROM PRODUCTS WHERE [Code] = 'GO_SMART'AND [Counterparty] = @CounterpartyHeader AND [Availability] = 'Live')


PRINT('GO Smart Bundle Id is : ')PRINT(@GoSmartBundleId)
IF(NOT EXISTS (SELECT * FROM ProductParts pp where pp.ProductId = @GoSmartBpBundleId)) INSERT INTO [ProductParts] (ProductId,PartId,Quantity) SELECT @GoSmartBpBundleId,[PartId],[Quantity] FROM ProductParts WHERE [ProductId] = @GoSmartBundleId 

PRINT('Adding BILL_PAYMENT service to GO_SMART_BP bundle')
IF(NOT EXISTS (SELECT * FROM ProductParts pp where pp.ProductId = @GoSmartBpBundleId and PartId = @billPaymentServiceId)) INSERT INTO [ProductParts] (ProductId,PartId,Quantity) VALUES (@GoSmartBpBundleId,@billPaymentServiceId,1)

COMMIT TRANSACTION