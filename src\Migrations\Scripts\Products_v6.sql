﻿EXEC NewProductVersion_v2 'GO_AIR', 3, 'GO_AIR', 4, 1
EXEC NewProductVersion_v2 'GO_LITE', 3, 'GO_LITE', 4, 1
EXEC NewProductVersion_v2 'GO_SMART', 3, 'GO_SMART', 4, 1

DECLARE @GoAirPriceId UNIQUEIDENTIFIER
SELECT TOP 1 @GoAirPriceId = ID FROM Prices WHERE ProductId = (SELECT TOP 1 ID FROM Products where Code='GO_AIR' and Version = 4)

DECLARE @GoLitePriceId UNIQUEIDENTIFIER
SELECT TOP 1 @GoLitePriceId = ID FROM Prices WHERE ProductId = (SELECT TOP 1 ID FROM Products where Code='GO_LITE' and Version = 4)

DECLARE @GoSmartPriceId UNIQUEIDENTIFIER
SELECT TOP 1 @GoSmartPriceId = ID FROM Prices WHERE ProductId = (SELECT TOP 1 ID FROM Products where Code='GO_SMART' and Version = 4)

UPDATE Prices SET PerItemPrice = 0 WHERE Id = @GoAirPriceId;
UPDATE Prices SET PerItemPrice = 0 WHERE Id = @GoLitePriceId;
UPDATE Prices SET PerItemPrice = 0 WHERE Id = @GoSmartPriceId;