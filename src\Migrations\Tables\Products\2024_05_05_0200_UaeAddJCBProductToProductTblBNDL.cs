﻿using FluentMigrator;

namespace Migrations.Tables.Products;
[Migration(2024_05_05_0200)]
public class UaeAddJCBProductToProductTblBNDL : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"

        --add new JCB_UAE product as part of BUNDLE

        INSERT INTO ProductParts (ProductId, PartId, Quantity) VALUES
        ((SELECT TOP 1 Id FROM Products WHERE type='BUNDLE' AND Counterparty='GEIDEA_UAE' AND Availability='Live' AND Code='PAY_BY_LINK_BUNDLE'), (SELECT Id
          FROM [PRODUCTS].[dbo].[Products]
          where code = 'JCB_GW' and Counterparty = 'GEIDEA_UAE'), 1)

        ");
    }
}