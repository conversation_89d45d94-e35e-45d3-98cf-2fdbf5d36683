﻿using System;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Common;
using Common.Entities;
using Common.Models.ProductInstance;
using Common.Repositories;
using Common.Services;
using DataAccess;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using ProductService;
using Services.Settings;
using Xunit;

namespace Services.Test.ProductConfiguratorTests;

public class DeleteTests
{
    private readonly Mock<ILogger<ProductConfiguratorService>> logger = new Mock<ILogger<ProductConfiguratorService>>();
    private readonly Mock<ILogger<ProductInstanceRepository>> repoLogger = new Mock<ILogger<ProductInstanceRepository>>();
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly Mock<IProductService> productService = new Mock<IProductService>();
    private readonly Mock<IProductRepository> productRepository = new Mock<IProductRepository>();
    private readonly Mock<ICounterpartyProvider> counterpartyProvider = new Mock<ICounterpartyProvider>();
    private readonly Mock<IProductChangeSenderService> productSender = new Mock<IProductChangeSenderService>();
    private readonly Mock<IOptionsMonitor<MeezaSettings>> meezaSettings = new Mock<IOptionsMonitor<MeezaSettings>>();
    private readonly Mock<IOptionsMonitor<CurrencySettings>> currencySettings = new Mock<IOptionsMonitor<CurrencySettings>>();
    private readonly Mock<IOptionsMonitor<MpgsAccountsSettings>> mpgsAccountsSettings = new Mock<IOptionsMonitor<MpgsAccountsSettings>>();
    private DataContext context;
    private ProductInstanceRepository productInstanceRepo;
    private ProductConfiguratorService productConfiguratorService;

    internal void Setup()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
           .UseInMemoryDatabase(databaseName: "ProductInstanceDeleteTests" + Guid.NewGuid().ToString())
           .Options;

        context = new DataContext(options, new CounterpartyProvider());
        productInstanceRepo = new ProductInstanceRepository(context, httpContext.Object, repoLogger.Object, mapper);
        productConfiguratorService = new ProductConfiguratorService(
            meezaSettings.Object,
            logger.Object,
            mapper,
            productService.Object,
            productInstanceRepo,
            productRepository.Object,
            counterpartyProvider.Object,
            productSender.Object,
            currencySettings.Object,
            mpgsAccountsSettings.Object);
    }

    [Fact]
    public async Task InvalidDeleteRequest()
    {
        Setup();

        await productConfiguratorService
            .Invoking(x => x.DeleteAsync(new DeleteProductInstanceRequest()))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.InvalidDeleteParameters.Code);
    }

    [Fact]
    public async Task DeleteById()
    {
        Setup();
        var productInstance = new ProductInstanceEntity
        {
            AgreementId = Guid.NewGuid(),
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            Product = new ProductEntity
            {
                Code = "TEST",
                Type = "SCHEME"
            }
        };
        context.ProductInstances.Add(productInstance);

        var childInstance = new ProductInstanceEntity
        {
            AgreementId = Guid.NewGuid(),
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            Product = new ProductEntity
            {
                Code = "TEST",
                Type = "SCHEME"
            },
            ParentConfigurationId = productInstance.Id
        };
        context.ProductInstances.Add(childInstance);
        context.SaveChanges();

        var deletedIds = await productConfiguratorService.DeleteAsync(new DeleteProductInstanceRequest { ProductInstanceId = productInstance.Id });
        deletedIds.Should().NotBeNullOrEmpty();
        deletedIds.Should().HaveCount(1);

        childInstance.ParentConfigurationId.Should().BeNull();
    }

    [Fact]
    public async Task DeleteById_AndChildren()
    {
        Setup();
        var productInstance = new ProductInstanceEntity
        {
            AgreementId = Guid.NewGuid(),
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            Product = new ProductEntity
            {
                Code = "TEST",
                Type = "SCHEME"
            }
        };
        context.ProductInstances.Add(productInstance);

        var childInstance = new ProductInstanceEntity
        {
            AgreementId = Guid.NewGuid(),
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            Product = new ProductEntity
            {
                Code = "TEST",
                Type = "SCHEME"
            },
            ParentId = productInstance.Id
        };
        context.ProductInstances.Add(childInstance);
        context.SaveChanges();

        var deletedIds = await productConfiguratorService.DeleteAsync(new DeleteProductInstanceRequest { ProductInstanceId = productInstance.Id });
        deletedIds.Should().NotBeNullOrEmpty();
        deletedIds.Should().HaveCount(2);

        var retrievedChild = await productInstanceRepo.FirstOrDefaultAsync(p => p.Id == childInstance.Id);
        retrievedChild.DeletedFlag.Should().BeTrue();
    }


    [Fact]
    public async Task DeleteByProductId()
    {
        Setup();
        var productInstance = new ProductInstanceEntity
        {
            AgreementId = Guid.NewGuid(),
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            Product = new ProductEntity
            {
                Code = "TEST",
                Type = "SCHEME"
            }
        };
        context.ProductInstances.Add(productInstance);
        context.SaveChanges();

        var deletedIds = await productConfiguratorService.DeleteAsync(new DeleteProductInstanceRequest { ProductId = productInstance.ProductId });
        deletedIds.Should().NotBeNullOrEmpty();
        deletedIds.Should().HaveCount(1);
    }

    [Fact]
    public async Task DeleteByAgreementId()
    {
        Setup();
        var productInstance = new ProductInstanceEntity
        {
            AgreementId = Guid.NewGuid(),
            CompanyId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            Product = new ProductEntity
            {
                Code = "TEST",
                Type = "SCHEME"
            }
        };
        context.ProductInstances.Add(productInstance);
        context.SaveChanges();

        var deletedIds = await productConfiguratorService.DeleteAsync(new DeleteProductInstanceRequest { AgreementId = productInstance.AgreementId });
        deletedIds.Should().NotBeNullOrEmpty();
        deletedIds.Should().HaveCount(1);
    }

    [Fact]
    public async Task DeleteByStoreId()
    {
        Setup();
        var storeId = Guid.NewGuid();
        var companyId = Guid.NewGuid();
        var productInstance = new ProductInstanceEntity
        {
            AgreementId = Guid.NewGuid(),
            CompanyId = companyId,
            StoreId = storeId,
            Product = new ProductEntity
            {
                Code = "TEST",
                Type = "SCHEME"
            }
        };
        var productInstance1 = new ProductInstanceEntity
        {
            AgreementId = Guid.NewGuid(),
            CompanyId = companyId,
            StoreId = storeId,
            Product = new ProductEntity
            {
                Code = "TEST1",
                Type = "SCHEME"
            }
        };
        context.ProductInstances.Add(productInstance);
        context.ProductInstances.Add(productInstance1);
        context.SaveChanges();

        var deletedIds = await productConfiguratorService.DeleteAsync(new DeleteProductInstanceRequest { StoreId = productInstance.StoreId });
        deletedIds.Should().NotBeNullOrEmpty();
        deletedIds.Should().HaveCount(2);
    }

    [Fact]
    public async Task DeleteManyInstances()
    {
        Setup();
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var agreementId = Guid.NewGuid();
        for (var i = 0; i < 5; i++)
            context.ProductInstances.Add(new ProductInstanceEntity
            {
                AgreementId = agreementId,
                CompanyId = Guid.NewGuid(),
                StoreId = Guid.NewGuid(),
                Product = new ProductEntity
                {
                    Code = "TEST",
                    Type = "SCHEME"
                }
            });
        context.SaveChanges();
        var productInstanceRep = new ProductInstanceRepository(context, httpContext.Object, repoLogger.Object, mapper);
        productConfiguratorService = new ProductConfiguratorService(
            meezaSettings.Object,
            logger.Object,
            mapper,
            productService.Object,
            productInstanceRep,
            productRepository.Object,
            counterpartyProvider.Object,
            productSender.Object,
            currencySettings.Object, mpgsAccountsSettings.Object);

        var deletedIds = await productConfiguratorService.DeleteAsync(new DeleteProductInstanceRequest { AgreementId = agreementId });
        deletedIds.Should().NotBeNullOrEmpty();
        deletedIds.Should().HaveCount(5);
    }
}
