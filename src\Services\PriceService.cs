﻿using AutoMapper;
using Common;
using Common.Entities;
using Common.Models;
using Common.Repositories;
using Common.Services;
using Common.Validators;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Services;

public class PriceService : IPriceService
{
    private readonly ILogger<PriceService> logger;
    private readonly IProductRepository productRepo;
    private readonly IPriceRepository priceRepo;
    private readonly IMapper mapper;
    private readonly IProductService productService;

    public PriceService(
        ILogger<PriceService> logger,
        IProductRepository productRepo,
        IPriceRepository priceRepo,
        IMapper mapper,
        IProductService productService)
    {
        this.logger = logger;
        this.productRepo = productRepo;
        this.priceRepo = priceRepo;
        this.mapper = mapper;
        this.productService = productService;
    }

    public async Task<Price[]> FindAsync(FindPriceRequest request, bool attach)
    {
        var result = await priceRepo.FindAsync(request, attach);
        return mapper.Map<PriceEntity[], Price[]>(result);
    }

    public async Task<Price> CreateAsync(PriceRequest request)
    {
        ValidatePriceAsync(request);
        CheckProductExists(request.ProductId);
        await CheckProductIsNotUsed(request.ProductId);

        var price = mapper.Map<PriceEntity>(request);

        priceRepo.Save(price);
        await priceRepo.SaveChangesAsync();

        logger.LogInformation($"Added price with id '{price.Id}'.");

        return mapper.Map<Price>(price);
    }

    public async Task<Price> PatchAsync(Guid priceId, JsonPatchDocument<PriceRequest> patchPrice)
    {
        var price = await GetByIdAsync(priceId);

        await CheckProductIsNotUsed(price.ProductId);

        var priceUpdate = mapper.Map<PriceRequest>(price);

        try
        {
            patchPrice.ApplyTo(priceUpdate);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Invalid product patch request.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidPatchRequest);
        }

        ValidatePriceAsync(priceUpdate);
        if (priceUpdate.ProductId != price.ProductId)
        {
            CheckProductExists(priceUpdate.ProductId);
            await CheckProductIsNotUsed(priceUpdate.ProductId);
        }

        mapper.Map(priceUpdate, price);

        priceRepo.Update(price);
        await priceRepo.SaveChangesAsync();

        logger.LogInformation($"Updated price with id '{priceId}'.");

        return mapper.Map<Price>(price);
    }

    public async Task DeleteAsync(Guid priceId)
    {
        var price = await GetByIdAsync(priceId);

        await CheckProductIsNotUsed(price.ProductId);

        await priceRepo.HardDeleteAsync(price);

        logger.LogInformation($"Deleted price with id '{priceId}'.");
    }

    private async Task<PriceEntity> GetByIdAsync(Guid priceId)
    {
        var price = (await priceRepo.FindAsync(new FindPriceRequest() { Id = priceId }, true)).SingleOrDefault();
        if (price == null)
        {
            logger.LogError($"Price with id {priceId} not found.");
            throw new ServiceException(HttpStatusCode.NotFound, Errors.PriceNotFound);
        }

        return price;
    }

    private void ValidatePriceAsync(PriceRequest priceRequest)
    {
        var validationResult = new PriceRequestValidator().Validate(priceRequest);
        if (!validationResult.IsValid)
        {
            var errorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
            logger.LogError("Price request failed validation: {@errors}", errorDescription);

            throw new ValidationException(validationResult);
        }
    }

    private void CheckProductExists(Guid productId)
    {
        if (!productRepo.ExistsAsync(productId))
        {
            logger.LogError($"Product with id '{productId}' not found.");
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductNotFound);
        }

    }

    private async Task CheckProductIsNotUsed(Guid productId)
    {
        if (await productService.IsProductUsedAsync(productId))
        {
            logger.LogError($"Cannot update or create a price for product with id {productId} because this product it is used by a product instance.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidProductPriceRequest);
        }
    }
}
