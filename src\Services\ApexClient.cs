﻿using Common.Models.Apex;
using Common.Options;
using Common.Services;
using Geidea.Utils.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Mime;
using System.Text;
using System.Text.Json.Serialization;
using System.Text.Json;
using System.Threading.Tasks;
using Geidea.Utils.Exceptions;
using System.Net;
using Common;

namespace Services;
public class ApexClient : IApexClient
{
    private const string ApexMerchantInquiryEndpoint = "merchant-inquiry/getMerchantIdTerminalId";

    private readonly HttpClient httpClient;
    private ApexSettings apexSettings;
    private readonly ILogger<ApexClient> logger;
    private readonly IApexToken token;
    private ExternalApiSettings externalApiSettings;

    public ApexClient(
       HttpClient httpClient,
       IOptionsMonitor<ApexSettings> apexOptions,
       ILogger<ApexClient> logger, IApexToken token, IOptionsMonitor<ExternalApiSettings> externalApiOtions)
    {
        HttpClientHandler clientHandler = new HttpClientHandler();
        clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
        httpClient = new HttpClient(clientHandler);
        this.httpClient = httpClient;
        apexSettings = apexOptions.CurrentValue;
        this.logger = logger;
        this.token = token;
        externalApiSettings = externalApiOtions.CurrentValue;
    }

    public async Task<MerchantInquiryResponse> GetMerchantInquiry(MerchantInquiryRequest request)
    {

        var apexEnvironmentSetting = apexSettings;

        var baseUrl = externalApiSettings.BaseUrl;

        var url = $"{baseUrl}/{ApexMerchantInquiryEndpoint}";

        var data = GetData(request);

        var tokenResponse = await token.GetTokenAsync();

        if (tokenResponse != null && !string.IsNullOrEmpty(tokenResponse.access_token))
        {
            var response = await SendData(data, url, tokenResponse.access_token);

            var responseData = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogInformation("Apex merchant inquiry response status code is not successful, response {@response}, responseContent {@responseContent}", response, responseData);

                return new MerchantInquiryResponse();
            }

            logger.LogInformation("Response from Apex merchant inquiry: {@responseContent}", responseData);

            var result = Json.Deserialize<MerchantInquiryResponse>(responseData, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return result;
        }
        else
        {
            return new MerchantInquiryResponse();
        }


    }

    private static string GetData(MerchantInquiryRequest request)
    {
        return JsonSerializer.Serialize(request, options: new JsonSerializerOptions
        {
            DictionaryKeyPolicy = JsonNamingPolicy.CamelCase,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Converters = { new JsonStringEnumConverter() }
        });
    }

    private async Task<HttpResponseMessage> SendData(string data, string url, string accessToken)
    {
        try
        {
            SetAuthorizationHeader(accessToken);

            var requestData = new StringContent(data, Encoding.UTF8, MediaTypeNames.Application.Json);

            logger.LogInformation("Sending Get Cumulative Transaction request data to APEX. Sending request to url {@url} with {@data}", url, data);

            return await httpClient.PostAsync(url, requestData);
        }
        catch (HttpRequestException exception)
        {
            logger.LogCritical("CRITICAL: Sending post request to apex to {@requestUri} resulted in Exception {@exception}", url, exception);
            throw new ServiceException(HttpStatusCode.InternalServerError, Errors.InternalServerError);

        }
    }

    private void SetAuthorizationHeader(string accessToken)
    {
        httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
    }

}
