﻿USE PRODUCTS
GO

DECLARE @Ids TABLE(ID UNIQUEIDENTIFIER)
DECLARE @InsertedId UNIQUEIDENTIFIER

INSERT INTO PRODUCTS([Availability], Flow, SalesChannel, Code, [Type], CreatedBy, CreatedDateUtc, ValidFrom, Counterparty) OUTPUT inserted.Id INTO @Ids
VALUES('Live', 'Normal', null, 'SUNMI_P2', 'TERMINAL', '00000000-0000-0000-0000-000000000000', GETUTCDATE(), GETUTCDATE(), 'GEIDEA_SAUDI')

SELECT TOP 1 @InsertedId = ID FROM @Ids 
DELETE FROM @Ids

EXEC NewProductVersion_v2 'GO_SMART', 4, 'GO_SMART', 5, 1, 'GEIDEA_SAUDI'

EXEC ChangePartFromProduct 'GO_SMART', 5, 'SMARTPOS_A920', 0, 'SUNMI_P2', 0,'GEIDEA_SAUDI'
