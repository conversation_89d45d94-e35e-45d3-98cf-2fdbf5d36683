﻿using Common.Models;
using Common.Models.Search;
using Common.Models.TerminalDataSets;
using Common.Services;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using NSubstitute;
using ProductService.Controllers;
using Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace ProductService.Test;

public class TerminalDataSetControllerTests
{
    private readonly Mock<ITerminalDataSetService> terminalDataMock;
    private readonly Mock<ILogger<TerminalDataSetController>> loggerMock;
    private readonly IApexService apexService;

    private static readonly Guid TerminalDataSetId = Guid.Parse("C90C2468-F86F-4CAE-3175-08D9A35E3117");
    private readonly SearchResponse<TerminalDataSetSearchResponse> searchResultMock = new SearchResponse<TerminalDataSetSearchResponse>()
    {
        Records = new List<TerminalDataSetSearchResponse>()
            {
                new TerminalDataSetSearchResponse()
                {
                    AcquiringLedger = "test",
                    Availability = "test",
                    ConfigDate = DateTime.UtcNow,
                    Counterparty = "SA",
                    FullTid = "76789",
                    Mid = "test",
                    OrderNumber = "test",
                    Tid = "098789",
                    Trsm = "test",
                    Id = TerminalDataSetId
                }
            }
    };

    private readonly List<TerminalDataSet> instance = new List<TerminalDataSet>()
    {
        new TerminalDataSet()
        {
            AcquiringLedger="test",
            Availability="test",
            FullTid = "11111111111",
            Tid = "111111",
            Mid = "test",
            Trsm="test",
        }
    };

    private readonly List<TerminalDataSetValidationRequiredFields> terminalDataSetsRequiredFields = new List<TerminalDataSetValidationRequiredFields>()
    {
        new TerminalDataSetValidationRequiredFields()
        {
            Data = Guid.NewGuid().ToString(),
            Label = "TID"
        }
    };

    private readonly TerminalDataSetController controller;

    public TerminalDataSetControllerTests()
    {
        var terminalDataSetRes = new TerminalDataSetsResponse
        {
            TId = "tid",
            FullTId = "FullTid",
            Trsm = "Trsm"
        };

        loggerMock = new Mock<ILogger<TerminalDataSetController>>();
        terminalDataMock = new Mock<ITerminalDataSetService>();
        terminalDataMock.Setup(x => x.AdvancedSearchAsync(It.IsAny<TerminalDataSetSearchRequest>()))
                            .Returns(Task.FromResult(searchResultMock));
        terminalDataMock.Setup(x => x.CreateAsync(It.IsAny<List<TerminalDataSet>>()))
                            .Returns(Task.FromResult(instance));

        terminalDataMock.Setup(x => x.PatchAsync(It.IsAny<List<TerminalDataSetPatchRequest>>()))
                            .Returns(Task.FromResult(instance));

        terminalDataMock.Setup(x => x.GetAvailableTerminalDataSetCountAsync(It.IsAny<string>())).Returns(Task.FromResult(new Common.Models.TerminalsCountResponse { ValidTerminalDataSetCount = 1 }));

        terminalDataMock.Setup(x => x.GetValidationRequiredFieldsAsync(It.IsAny<List<TerminalDataSet>>()))
                            .Returns(Task.FromResult(terminalDataSetsRequiredFields));

        terminalDataMock.Setup(x => x.GetOrderMigrationValidationRequiredFieldsAsync(It.IsAny<List<TerminalDataSet>>()))
                            .Returns(Task.FromResult(instance));

        terminalDataMock.Setup(x => x.GetTerminalDataByIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(searchResultMock.Records[0]));

        terminalDataMock.Setup(x => x.GenerateTIDAndAddEditTerminalDataSet(It.IsAny<TerminalDataSet>()))
                            .Returns(Task.FromResult(terminalDataSetRes));

        terminalDataMock.Setup(x => x.GenerateMIDAndAddEditTerminalDataSet(It.IsAny<TerminalDataSetMidRequest>()))
                            .Returns(Task.FromResult("mid"));
        apexService = Substitute.For<IApexService>();
        controller = new TerminalDataSetController(terminalDataMock.Object, loggerMock.Object, apexService);
    }

    [Fact]
    public async Task SearchTerminalData_ShouldReturnValues()
    {
        var terminalDataSearchRequest = new TerminalDataSetSearchRequest();
        var result = await controller.AdvancedSearch(terminalDataSearchRequest);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultData = okResult.Value as SearchResponse<TerminalDataSetSearchResponse>;

        Assert.Equal(resultData.Records, searchResultMock.Records);

        terminalDataMock.Verify(x => x.AdvancedSearchAsync(terminalDataSearchRequest), Times.Once);
    }

    [Fact]
    public async Task CreateTerminalDataSet_ShouldBeSuccessful()
    {
        List<TerminalDataSet> terminalDataSets = new List<TerminalDataSet>();
        var result = await controller.Create(terminalDataSets);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultData = okResult.Value as List<TerminalDataSet>;
        Assert.Equal(instance, resultData);

        terminalDataMock.Verify(x => x.CreateAsync(terminalDataSets), Times.Once);
    }

    [Fact]
    public async Task PatchTerminalDataSet_ShouldBeSuccessful()
    {
        var instanceId = Guid.NewGuid();
        var terminalDataSetPatchRequest = new List<TerminalDataSetPatchRequest>()
            {
                new TerminalDataSetPatchRequest()
                {
                    TerminalDataSetId = instanceId,
                    TerminalDataSetPatchDocument = new JsonPatchDocument<TerminalDataSet>()
                }
            };

        var result = await controller.Patch(terminalDataSetPatchRequest);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as List<TerminalDataSet>;
        Assert.NotNull(resultInstance);
        Assert.Equal(instance, resultInstance);

        terminalDataMock.Verify(x => x.PatchAsync(terminalDataSetPatchRequest), Times.Once);
    }

    [Fact]
    public async Task GetAvailableTerminalDataSetCount_ShouldBeSuccessful()
    {
        var acquiringLedger = "test";
        int actualCount = 1;

        var result = await controller.GetAvailableTerminalDataSetCount(acquiringLedger);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var expectedCount = (TerminalsCountResponse)okResult.Value;

        Assert.Equal(expectedCount.ValidTerminalDataSetCount, actualCount);
    }

    [Fact]
    public async Task GetValidationRequiredFields_ShouldReturnValues()
    {
        var terminalDataSearchRequest = new List<TerminalDataSet>();
        var result = await controller.GetValidationRequiredFields(terminalDataSearchRequest);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultData = okResult.Value as List<TerminalDataSetValidationRequiredFields>;

        Assert.Equal(resultData, terminalDataSetsRequiredFields);

        terminalDataMock.Verify(x => x.GetValidationRequiredFieldsAsync(terminalDataSearchRequest), Times.Once);
    }

    [Fact]
    public async Task GetOrderMigrationValidationRequiredFields_ShouldReturnValues()
    {
        var terminalDataSearchRequest = new List<TerminalDataSet>();
        var result = await controller.GetOrderMigrationValidationRequiredFields(terminalDataSearchRequest);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultData = okResult.Value as List<TerminalDataSet>;

        Assert.Equal(resultData, instance);

        terminalDataMock.Verify(x => x.GetOrderMigrationValidationRequiredFieldsAsync(terminalDataSearchRequest), Times.Once);
    }

    [Fact]
    public async Task GetTerminalDataByIdAsync_ShouldReturnData()
    {
        var terminalDataSetIdRequest = TerminalDataSetId;
        var result = await controller.GetTerminalDataByIdAsync(terminalDataSetIdRequest);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultData = okResult.Value as TerminalDataSetSearchResponse;

        Assert.Equal(terminalDataSetIdRequest, resultData.Id);

        terminalDataMock.Verify(x => x.GetTerminalDataByIdAsync(terminalDataSetIdRequest), Times.Once);
    }

    [Fact]
    public async Task AddOrUpdateTerminalDataSet_ShouldReturnData()
    {
        var terminalDataSetList = new TerminalDataSetsRequest
        {
            OrderNumber = "fake",
            ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.NewGuid(), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
        };
        var result = await controller.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSetList);
        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);
    }

    [Fact]
    public async Task AddOrUpdateNBEOrderTerminalDataSet_ShouldReturnData()
    {
        var terminalDataSetList = new TerminalDataSetsMidTidRequest
        {
            StoresIds = new List<Guid>(),
            TerminalDataSets = new List<TerminalDataSet>()
        };

        var result = await controller.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(terminalDataSetList);
        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);
    }

    [Fact]
    public async Task GenerateTIDAndAddEditTerminalDataSet_ShouldReturnData()
    {
        var terminalDataSetRes = new TerminalDataSetsResponse
        {
            TId = "tid",
            FullTId = "FullTid",
            Trsm = "Trsm",
        };

        var terminalDataSet = new TerminalDataSet
        {
            OrderNumber = "fake",
            ProductInstanceId = Guid.NewGuid()
        };
        var result = await controller.GenerateTIDAndAddEditTerminalDataSet(terminalDataSet);

        OkObjectResult okResult = result as OkObjectResult;

        TerminalDataSetsResponse actualResult = okResult.Value as TerminalDataSetsResponse;

        Assert.NotNull(result);
        Assert.Equal(terminalDataSetRes.TId, actualResult.TId);
        Assert.Equal(terminalDataSetRes.FullTId, actualResult.FullTId);
        Assert.Equal(terminalDataSetRes.Trsm, actualResult.Trsm);
        Assert.Equal(200, ((ObjectResult)result).StatusCode);

        terminalDataMock.Verify(x => x.GenerateTIDAndAddEditTerminalDataSet(terminalDataSet), Times.Once);
    }

    [Fact]
    public async Task GenerateMIDAndAddEditTerminalDataSet_ShouldReturnData()
    {
        var terminalDataSet = new TerminalDataSet
        {
            OrderNumber = "fake",
            ProductInstanceId = Guid.NewGuid()
        };
        var terminalDataSetMid = new TerminalDataSetMidRequest
        {
            TerminalDataSet = terminalDataSet,
            StoresIds = new List<Guid> { Guid.NewGuid() }
        };

        var result = await controller.GenerateMIDAndAddEditTerminalDataSet(terminalDataSetMid);

        Assert.NotNull(result);
        Assert.Equal("mid", ((ObjectResult)result).Value);
        Assert.Equal(200, ((ObjectResult)result).StatusCode);

        terminalDataMock.Verify(x => x.GenerateMIDAndAddEditTerminalDataSet(terminalDataSetMid), Times.Once);
    }

    [Fact]
    public async Task AddUpdateTerminalDataSetByMcc_ShouldReturnData()
    {
        var terminalDataSetList = new TerminalDataSetsMidTidRequest
        {
            StoresIds = new List<Guid>(),
            TerminalDataSets = new List<TerminalDataSet>()
        };

        var result = await controller.AddUpdateTerminalDataSetByMcc(terminalDataSetList);
        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);
    }
}
