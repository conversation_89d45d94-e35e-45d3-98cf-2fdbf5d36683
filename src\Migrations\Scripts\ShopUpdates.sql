﻿update Products set SalesChannel = 'All' where SalesChannel = 'Onboarding' and Counterparty = 'GEIDEA_SAUDI'

update Products set SalesChannel = null where Type not in ('BUNDLE', 'ACCESSORIES', 'SERVICES') and Counterparty = 'GEIDEA_SAUDI' 

DECLARE @GeideaGoCategory UNIQUEIDENTIFIER
DECLARE @PayByLink UNIQUEIDENTIFIER

SELECT TOP 1 @GeideaGoCategory = ID FROM Category where  Code = 'GO_FAMILY' and Counterparty = 'GEIDEA_SAUDI'
SELECT TOP 1 @PayByLink = ID FROM Products where  Code = 'PAY_BY_LINK' and Counterparty = 'GEIDEA_SAUDI'

insert into ProductCategories(ProductId, CategoryId) values(@PayByLink, @GeideaGoCategory)