﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

public class ProductInstance_AddDiscovercardBrand : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"DECLARE @uaeIds table(
            id  uniqueidentifier
            )
insert into @uaeIds
SELECT pi.[Id]
FROM ProductInstances pi
INNER JOIN Products p ON p.id = pi.ProductId
WHERE [Metadata] IS NOT NULL
AND p.Type = 'GWAY'
AND ISJSON([Metadata]) > 0
AND (p.Counterparty = 'GEIDEA_UAE')
AND JSON_QUERY([Metadata], '$.CardBrandProviders') IS NOT NULL
AND JSON_QUERY([Metadata], '$.CardBrandProviders[0]') IS NOT NULL
UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.CardBrandProviders', JSON_QUERY(REPLACE(JSON_QUERY([Metadata], '$.CardBrandProviders'),']',', { ""CardBrand"": ""DISCOVER_GW"", ""AcquiringProvider"": ""MPGS"", ""ThreeDSecureProvider"": ""MPGS"" } ]'),'$')) 
FROM ProductInstances pi
INNER JOIN @uaeIds e ON e.id = pi.Id
WHERE JSON_QUERY([Metadata], '$.CardBrandProviders') NOT LIKE '%DISCOVER%'");
    }
}
