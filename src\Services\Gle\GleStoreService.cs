﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Common;
using Common.Entities.Gle;
using Common.Models.Gle;
using Common.Repositories.Gle;
using Common.Services.Gle;
using Common.Validators.Gle;
using FluentValidation.Results;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.Extensions.Logging;

namespace Services.Gle;

public class GleStoreService : IGleStoreService
{
    private readonly ILogger<GleStoreService> logger;
    private readonly IMapper mapper;
    private readonly IGleStoreRepository gleStoreRepository;

    public GleStoreService(ILogger<GleStoreService> logger,
        IMapper mapper,
        IGleStoreRepository gleStoreRepository)
    {
        this.logger = logger;
        this.mapper = mapper;
        this.gleStoreRepository = gleStoreRepository;
    }

    public async Task AddGleStoreAsync(GleStoreRequest createGleStoreRequest)
    {
        ValidateRequest(await new GleStoreRequestValidator().ValidateAsync(createGleStoreRequest));

        var gleStoreEntity = mapper.Map<GleStoreEntity>(createGleStoreRequest);
        await gleStoreRepository.AddGle(gleStoreEntity);

        logger.LogInformation("Added GLE store entity for storeId: {id}", createGleStoreRequest.StoreId);
    }

    public async Task UpdateGleStoreAsync(Guid gleStoreId, JsonPatchDocument<UpdateGleStoreRequest> updateDocument)
    {
        if (gleStoreId == Guid.Empty)
        {
            logger.LogError("Invalid GLE store ID {id}", gleStoreId);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidGleMerchantId.Message);
        }
        ValidateRequest(await new GleBasePatchRequestValidator<UpdateGleStoreRequest>().ValidateAsync(updateDocument));

        var mappedDocument = mapper.Map<JsonPatchDocument<GleStoreEntity>>(updateDocument);
        await gleStoreRepository.UpdateGle(gleStoreId, mappedDocument);

        logger.LogInformation("Updated GLE store with id:{id}", gleStoreId);
    }

    public async Task<GleStore?> GetGleStoreByStoreIdAsync(Guid storeId)
    {
        if (storeId == Guid.Empty)
        {
            logger.LogError("Invalid store ID {id}", storeId);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidStoreId.Message);
        }
        var gleStoreEntity = await gleStoreRepository.GetGleStoreByStoreIdAsync(storeId);

        return mapper.Map<GleStore?>(gleStoreEntity);
    }

    public async Task<GleHierarchyByStore> GetGleHierarchyByStoreIdAsync(Guid storeId)
    {
        if (storeId == Guid.Empty)
        {
            logger.LogError("Invalid store ID {id}", storeId);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidStoreId.Message);
        }

        var gleHierarchy = await gleStoreRepository.GetGleHierarchyByStoreIdAsync(storeId);
        if (gleHierarchy == null)
        {
            return new GleHierarchyByStore();
        }

        return new GleHierarchyByStore
        {
            Merchant = mapper.Map<GleMerchantDto?>(gleHierarchy!.GleMerchant),
            Store = mapper.Map<GleStoreDto?>(gleHierarchy),
            TerminalList = mapper.Map<IReadOnlyCollection<GleTerminal>>(gleHierarchy!.GleTerminalEntities)
        };
    }

    private void ValidateRequest(ValidationResult validationResult)
    {
        if (!validationResult.IsValid)
        {
            var errorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
            logger.LogError("GLE Store Registration request validation failed: {@errors}", errorDescription);
            throw new ValidationException(validationResult);
        }
    }
}