﻿using Common.Entities;
using Common.Models.Vendor;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Repositories;
public interface IVendorRepository : IRepository<Guid, VendorEntity>
{
    Task<bool> IsVendorExistWithSameNameAndType(VendorRequest request);
    Task<VendorResponse> CreateAsync(VendorRequest vendor);
    Task<VendorResponse?> GetByIdAsync(Guid vendorId);
    Task<List<VendorResponse>?> GetAsync(string? terminalType = null);
    Task<VendorSearchResponse> AdvancedSearch(VendorSearchRequest searchRequest);
    Task<VendorResponse?> SetDefaultVendorAsync(Guid vendorId);
    Task<VendorResponse?> GetDefaultVendorAsync();
}