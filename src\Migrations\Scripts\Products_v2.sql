﻿ UPDATE Products SET Availability = 'Obsolete' WHERE Code IN ('GO_LITE','GO_SMART','GO_AIR','GO_MPOS','GO_A920','PRO_SMART', 
 'BUSINESS_RESTAURANT','BUSINESS_RETAIL', 'ENTERPRISE_RETAIL')

 DECLARE @Ids TABLE(ID UNIQUEIDENTIFIER)
 DECLARE @Id UNIQUEIDENTIFIER
 DECLARE @GoFamilyCategoryId UNIQUEIDENTIFIER
 DECLARE @RestaurantCategoryId UNIQUEIDENTIFIER
 DECLARE @RetailCategoryId UNIQUEIDENTIFIER
 DECLARE @GoLiteId UNIQUEIDENTIFIER
 DECLARE @GeideaGoAppId UNIQUEIDENTIFIER
 DECLARE @D135Reader UNIQUEIDENTIFIER
 DECLARE @GoSmartId UNIQUEIDENTIFIER
 DECLARE @SmartPosId UNIQUEIDENTIFIER
 DECLARE @GoAirId UNIQUEIDENTIFIER
 DECLARE @SoftPosId UNIQUEIDENTIFIER
 DECLARE @MPOS_SP530Id UNIQUEIDENTIFIER
 DECLARE @Go_A920 UNIQUEIDENTIFIER
 DECLARE @Go_MPOS UNIQUEIDENTIFIER
 DECLARE @ProSmartId UNIQUEIDENTIFIER
 DECLARE @BusinessRestaurantId UNIQUEIDENTIFIER
 DECLARE @PosRocketId UNIQUEIDENTIFIER
 DECLARE @BusinessRetailId UNIQUEIDENTIFIER
 DECLARE @EnterpriseRetailId UNIQUEIDENTIFIER
 DECLARE @TillPointId UNIQUEIDENTIFIER

 SELECT TOP 1 @GoFamilyCategoryId = ID FROM Category where  Code = 'GO_FAMILY'
 SELECT TOP 1 @RestaurantCategoryId = ID FROM Category where  Code = 'RESTAURANT'
 SELECT TOP 1 @RetailCategoryId = ID FROM Category where  Code = 'RETAIL'
 SELECT TOP 1 @GeideaGoAppId = ID FROM Products where  Code = 'GEIDEA_GO_APP'
 SELECT TOP 1 @SmartPosId = ID FROM Products where  Code = 'SMARTPOS_A920'
 SELECT TOP 1 @MPOS_SP530Id = ID FROM Products where  Code = 'MOBILE_POS_SP530'
 SELECT TOP 1 @Go_A920 = ID FROM Products where  Code = 'GO_A920'
 SELECT TOP 1 @Go_MPOS = ID FROM Products where  Code = 'GO_MPOS'
 SELECT TOP 1 @PosRocketId = ID FROM Products where  Code = 'POS_ROCKET'
 SELECT TOP 1 @TillPointId = ID FROM Products where  Code = 'TILL_POINT'

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('PartOf', 'D135_READER', 'M_POS', 'n/a', GETUTCDATE(), GETUTCDATE())
 SELECT TOP 1 @D135Reader = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('PartOf', 'SOFT_POS', 'TERMINAL', 'n/a', GETUTCDATE(), GETUTCDATE())

 SELECT TOP 1 @SoftPosId = ID FROM @Ids 
 DELETE FROM @Ids

 --Go Lite
 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Bundle_Preorder', 'GO_LITE_V2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @GoLiteId = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@GoLiteId, @GoFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @GoLiteId, 3900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @D135Reader)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @GeideaGoAppId)

 --Go Smart
 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Bundle', 'GO_SMART_V2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @GoSmartId = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@GoSmartId, @GoFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @GoSmartId, 13900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @SmartPosId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @GeideaGoAppId)

 --Go Air
 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Bundle_Preorder', 'GO_AIR_V2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @GoAirId = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@GoAirId, @GoFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @GoAirId, 2900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @SoftPosId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @GeideaGoAppId)

 --Geidea A920
 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Bundle', 'GO_A920_V2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @Go_A920 = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@Go_A920, @GoFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('ONE_OFF','SETUP_CHARGE', 0, @Go_A920, 240000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Go_A920, @SmartPosId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Go_A920, @GeideaGoAppId)

 --Geidea MPOS
 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Bundle', 'GO_MPOS_V2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @Go_MPOS = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@Go_MPOS, @GoFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('ONE_OFF','SETUP_CHARGE',0, @Go_MPOS, 75000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Go_MPOS, @MPOS_SP530Id)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Go_MPOS, @GeideaGoAppId)

 --Pro Smart
 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Bundle_HelpRequired', 'PRO_SMART_V2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @ProSmartId = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@ProSmartId, @RestaurantCategoryId)
 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@ProSmartId, @RetailCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @ProSmartId, 22900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @SmartPosId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @GeideaGoAppId)

 --Business Restaurant
 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Bundle_HelpRequired', 'BUSINESS_RESTAURANT_V2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @BusinessRestaurantId = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@BusinessRestaurantId, @RestaurantCategoryId)
 
 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @BusinessRestaurantId, 32900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @SmartPosId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @PosRocketId)

 --Business Retail
 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Bundle_HelpRequired', 'BUSINESS_RETAIL_V2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @BusinessRetailId = ID FROM @Ids 
 DELETE FROM @Ids 

 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@BusinessRetailId, @RetailCategoryId)
 
 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @BusinessRetailId, 32900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @SmartPosId)  
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @PosRocketId)

 --Enterprise Retail
 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Bundle_HelpRequired', 'ENTERPRISE_RETAIL_V2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @EnterpriseRetailId = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@EnterpriseRetailId, @RetailCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @EnterpriseRetailId, 49900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @SmartPosId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @TillPointId)

 --pos card schemes
	--mada
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
	 VALUES('Normal', 'MADA_POS', 'SCHEME', 'n/a', GETUTCDATE(), GETUTCDATE())

	 SELECT TOP 1 @Id = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, Threshold)
	 VALUES('PURCHASE_CP', 1, @Id, 70, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 10000)

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, Threshold)
	 VALUES('PURCHASE_CP', 1, @Id, 80, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 10000)

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('REFUND_CP', 1, @Id, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Go_A920, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Go_MPOS, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @Id)

	 --visa
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
	 VALUES('Normal', 'VISA_POS', 'SCHEME', 'n/a', GETUTCDATE(), GETUTCDATE())

	 SELECT TOP 1 @Id = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('PURCHASE_CP', 1, @Id, 275, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('REFUND_CP', 1, @Id, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Go_A920, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Go_MPOS, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @Id)

	 --mastercard
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
	 VALUES('Normal', 'MC_POS', 'SCHEME', 'n/a', GETUTCDATE(), GETUTCDATE())

	 SELECT TOP 1 @Id = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('PURCHASE_CP', 1, @Id, 275, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('REFUND_CP', 1, @Id, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Go_A920, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Go_MPOS, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @Id)

	 --amex
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
	 VALUES('Normal', 'AMEX_POS', 'SCHEME', 'n/a', GETUTCDATE(), GETUTCDATE())

	 SELECT TOP 1 @Id = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('PURCHASE_CP', 1, @Id, 275, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('REFUND_CP', 1, @Id, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @Id)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @Id)

UPDATE Products SET DisplayOrder = 1 WHERE Code = 'GO_AIR_V2'
UPDATE Products SET DisplayOrder = 2 WHERE Code = 'GO_LITE_V2'
UPDATE Products SET DisplayOrder = 3 WHERE Code = 'GO_SMART_V2'
UPDATE Products SET DisplayOrder = 4 WHERE Code = 'GO_A920_V2'
UPDATE Products SET DisplayOrder = 5 WHERE Code = 'GO_MPOS_V2'