﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models;
public class ProductResponse
{
    public Guid Id { get; set; }
    public string? Code { get; set; }
    public string? Name { get; set; }
    public string? NameAr { get; set; }
    public string? Subname { get; set; }
    public string? SubnameAr { get; set; }
    public string? Description { get; set; }
    public string? DescriptionAr { get; set; }
    public string? SalesChannel { get; set; }
    public string? ProductLink { get; set; }
    public string? CategoryName { get; set; }
    public int? ProductDisplayOrder { get; set; }
    public Guid CategoryId { get; set; }
    public List<ProductImagesResponse>? ProductImages { get; set; }
}
