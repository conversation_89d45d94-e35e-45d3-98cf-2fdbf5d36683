﻿using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Xunit;

namespace Services.Test;

public class CounterpartyProviderTests
{
    private ICounterpartyProvider provider;

    public CounterpartyProviderTests()
    {
        provider = new CounterpartyProvider();
    }

    [Fact]
    public void SetCodeShouldChangeCounterparty()
    {
        provider.SetCode("OriginalValue");
        var firstResponse = provider.GetCode();
        firstResponse.Should().BeEquivalentTo("OriginalValue");

        provider.SetCode("UpdatedValue");
        var secondResponse = provider.GetCode();
        secondResponse.Should().BeEquivalentTo("UpdatedValue");
    }
}
