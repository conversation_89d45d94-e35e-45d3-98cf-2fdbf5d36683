﻿using System;
using System.Linq;
using System.Security.Cryptography;
using Common.Entities;
using Geidea.Utils.Exceptions;
using static Common.Constants;

namespace Services.Test.Helpers;

public static class TestsHelper
{
    public static bool HasErrorCode(ValidationException ex, string code)
    {
        return (ex.ProblemDetails as ExtendedValidationProblemDetails)
               !.Errors.SelectMany(group => group.Value).Any(error => error.Type == code);
    }

    public static readonly TerminalDataSetEntity repositoryResponseEntity = new TerminalDataSetEntity()
    {
        AcquiringLedger = "test",
        Availability = "test",
        ConfigDate = DateTime.UtcNow,
        Counterparty = "SA",
        CreatedBy = Guid.NewGuid().ToString(),
        FullTID = "11111111111",
        TID = "111111",
        MID = "123456789012345",
        OrderNumber = "test",
        TRSM = "test",
        UpdatedBy = Guid.NewGuid().ToString(),
        Id = Guid.NewGuid(),
        CountryPrefix = "+20",
        PhoneNumber = "1175491578",
        ChannelType = "test",
        ConnectionType = "ConnectionTest",
        MPGSMID = "MPGSMID",
        MPGSKEY = "MPGSKEY"
    };
}
