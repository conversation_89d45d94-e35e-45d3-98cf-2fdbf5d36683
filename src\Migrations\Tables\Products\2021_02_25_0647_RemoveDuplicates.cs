﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_02_25_0647)]
public class RemoveDuplicates : ForwardOnlyMigration
{
    public override void Up()
    {
        Update.Table("Products").Set(new { Availability = "Obsolete" }).Where(new { Code = "AMEX_ONLINE", Version = "0" });
        Update.Table("Products").Set(new { Availability = "Obsolete" }).Where(new { Code = "MADA", Version = "0" });
        Update.Table("Products").Set(new { Availability = "Obsolete" }).Where(new { Code = "MADA_POS", Version = "0" });
        Update.Table("Products").Set(new { Availability = "Obsolete" }).Where(new { Code = "MC_ONLINE", Version = "0" });
    }
}
