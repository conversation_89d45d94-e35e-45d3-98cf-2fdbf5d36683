﻿using System;
using Geidea.Utils.DataAccess.Entities;
using Common.Enums.UnitPrice;
using System.ComponentModel.DataAnnotations.Schema;

namespace Common.Entities;
public class PendingUnitPriceEntity : AuditableEntity<Guid>
{
    public Guid? UnitPriceId { get; set; }
    [ForeignKey(nameof(UnitPriceId))]
    public UnitPriceEntity UnitPriceEntity { get; set; } = null!;
    public Guid ProductID { get; set; }
    public ProductEntity Product { get; set; } = null!;
    public Guid MCCID { get; set; }
    public Mcc Mcc { get; set; } = null!;
    public Guid BusinessTypeID { get; set; }
    public BusinessTypeEntity BusinessType { get; set; } = null!;
    public decimal? UnitPrice { get; set; }
    public decimal VATRate { get; set; }
    public VatType VATType { get; set; }
    public BillingType BillingType { get; set; }
    public BillingFrequency BillingFrequency { get; set; }
    public decimal? NewPrice { get; set; }
    public ActionTypesEnum ActionType { get; set; }
    public StatusEnum Status { get; set; }
    public Guid? ReviewedBy { get; set; }
    public DateTime? ReviewedDate { get; set; }
}
