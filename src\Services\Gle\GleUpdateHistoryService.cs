﻿using AutoMapper;
using Common;
using Common.Entities.Gle;
using Common.Models.Gle;
using Common.Repositories.Gle;
using Common.Services.Gle;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System.Net;
using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;

namespace Services.Gle;
public class GleUpdateHistoryService : IGleUpdateHistoryService
{
    #region Private Properties
    private readonly ILogger<GleUpdateHistoryService> logger;
    private readonly IMapper mapper;
    private readonly IGleUpdateHistoryRepository gleUpdateHistoryRepository;
    #endregion

    #region Constructor
    public GleUpdateHistoryService(ILogger<GleUpdateHistoryService> logger,
        IMapper mapper,
        IGleUpdateHistoryRepository gleUpdateHistoryRepository)
    {
        this.logger = logger;
        this.mapper = mapper;
        this.gleUpdateHistoryRepository = gleUpdateHistoryRepository;
    }
    #endregion

    #region Public Methods
    public async Task AddGleUpdateHistoryAsync(List<GleUpdateHistoryRequest> requests)
    {
        logger.LogInformation("Add new GleUpdateHistory record for merchant {merchantId}", requests.First().MerchantId);
        var entities = mapper.Map<List<GleUpdateHistoryEntity>>(requests);

        var userId = requests.Select(x => x.UserId.GetValueOrDefault()).FirstOrDefault().ToString();
        foreach (var entity in entities)
        {
            entity.CreatedBy = string.IsNullOrEmpty(userId) ? string.Empty : userId;
            entity.CreatedDate = DateTime.UtcNow;
        }
        await gleUpdateHistoryRepository.AddGleUpdateHistoryAsync(entities);
    }

    public async Task UpdateGleUpdateHistoryAsync(List<GleUpdateHistoryEditRequest> gleUpdateHistoryEditRequest)
    {
        var ids = gleUpdateHistoryEditRequest.Select(x => x.Id).ToList();
        var gleUpdateHistoryEntities = await gleUpdateHistoryRepository.FindGleUpdateHistoriesAsync(ids);
        if (gleUpdateHistoryEntities != null)
        {
            gleUpdateHistoryEntities.ForEach(entity =>
            {
                var request = gleUpdateHistoryEditRequest.Find(x => x.Id == entity.Id);
                if (request != null)
                {
                    entity.GleStatus = request.GleStatus;
                    entity.GleResponse = request.GleResponse;
                    entity.UpdatedBy = request.UserId.ToString();
                    entity.UpdatedDate = DateTime.UtcNow;
                }
            });

            await gleUpdateHistoryRepository.UpdateGleUpdateHistoryAsync(gleUpdateHistoryEntities);
        }
    }

    public async Task<GleUpdateHistory?> GetGleUpdateHistoryByMerchantIdAsync(Guid merchantId)
    {
        if (merchantId == Guid.Empty)
        {
            logger.LogError("Invalid merchant ID {id}", merchantId);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidMerchantId.Message);
        }

        var gleUpdateHistoryEntity = await gleUpdateHistoryRepository.FindGleUpdateHistoryAsync(merchantId, Constants.GleUpdateRequestType.UpdateMerchantTag);

        var gleUpdateHistory = mapper.Map<GleUpdateHistory?>(gleUpdateHistoryEntity);
        if (gleUpdateHistory != null && gleUpdateHistory.GleStatus == Constants.GleRegistrationStatus.Success)
        {
            gleUpdateHistory.GleResponse = "NA";
        }

        return gleUpdateHistory;
    }
    #endregion

}
