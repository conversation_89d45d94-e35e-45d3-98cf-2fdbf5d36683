﻿update Products set Code = 'BUSINESS_RETAIL' where Code = 'BUSINESS2'
update Products set Code = 'ENTERPRISE_RETAIL' where Code = 'ENTERPRISE2'
update Products set Code = 'BUSINESS_RESTAURANT' where Code = 'BUSINESS1'
update Products set Code = 'ENTERPRISE_RESTAURANT' where Code = 'ENTERPRISE1'

update Products set Code = 'PAYMENT_GATEWAY_BUNDLE' where Code = 'PAYMENT_GATEWAY1'
update Products set Code = 'WEBSITE_BUILDER_BUNDLE' where Code = 'WEBSITE_BUILDER2'

update Products set Code = 'PAYMENT_GATEWAY' where Code = 'PAYMENT_GATEWAY2'
update Products set Code = 'WEBSITE_BUILDER' where Code = 'WEBSITE_BUILDER1'


DECLARE @ProductIds TABLE(ID UNIQUEIDENTIFIER)
DECLARE @EInvoicingGwayId UNIQUEIDENTIFIER
DECLARE @EInvoicingId UNIQUEIDENTIFIER
DECLARE @WebsiteBuilderBundleId UNIQUEIDENTIFIER

INSERT @ProductIds SELECT Id FROM Products WHERE Code='E_INVOICING'
SELECT TOP 1 @EInvoicingId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT @ProductIds SELECT Id FROM Products WHERE Code='WEBSITE_BUILDER_BUNDLE'
SELECT TOP 1 @WebsiteBuilderBundleId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'E_INVOICING_GW', 'GWAY_ADDON', 'n/a', GETUTCDATE(), GETUTCDATE())

SELECT TOP 1 @EInvoicingGwayId = ID FROM @ProductIds 
DELETE FROM @ProductIds

DELETE FROM ProductParts WHERE ProductId = @WebsiteBuilderBundleId and PartId = @EInvoicingId

INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @EInvoicingGwayId)