﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32112.339
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ProductService", "ProductService\ProductService.csproj", "{D3EC21A0-312D-40C8-B408-15EE556BBF1A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Common", "Common\Common.csproj", "{02594494-54BF-495D-8E85-9A3DA5FF738D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataAccess", "DataAccess\DataAccess.csproj", "{F45B5285-2F28-4D09-A56A-8A5DF68A1AD8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Migrations", "Migrations\Migrations.csproj", "{CFDC0F29-6065-4529-BB8D-CC0845C01CE2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Services", "Services\Services.csproj", "{CA9BE52A-44D7-4723-837F-DE030E174CB3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Messaging", "Messaging\Messaging.csproj", "{A8F025F9-2D72-41F9-BD30-39349F53ED92}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Services.Test", "Services.Test\Services.Test.csproj", "{CD1B5538-3152-43E0-9B5B-024BDF30FC7F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ProductService.Test", "ProductService.Test\ProductService.Test.csproj", "{A894B464-F308-45E2-83D9-5880BA31F147}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataAccess.Test", "DataAccess.Test\DataAccess.Test.csproj", "{586C41B2-B567-40AE-AC97-1B0E4DBC53CA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Models", "Models\Models.csproj", "{5D0EDA4A-D567-43B7-8227-64E945AD0128}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Common.Tests", "Common.Tests\Common.Tests.csproj", "{3CEACBBA-5376-446B-BDC7-DAF074CEC8EE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{5EB99C7E-D58E-4643-8CC3-BC1B1C8B7FCF}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		..\.gitignore = ..\.gitignore
		..\AutoUpdateNugetPackages.yml = ..\AutoUpdateNugetPackages.yml
		..\azure-pipelines.yml = ..\azure-pipelines.yml
		Dockerfile = Dockerfile
		nuget.config = nuget.config
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SamsungPayTokenEncryptionTool", "SamsungPayTokenEncryptionTool\SamsungPayTokenEncryptionTool.csproj", "{1418C3CB-4054-49CA-A585-FAD202EC10E5}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D3EC21A0-312D-40C8-B408-15EE556BBF1A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D3EC21A0-312D-40C8-B408-15EE556BBF1A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D3EC21A0-312D-40C8-B408-15EE556BBF1A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D3EC21A0-312D-40C8-B408-15EE556BBF1A}.Release|Any CPU.Build.0 = Release|Any CPU
		{02594494-54BF-495D-8E85-9A3DA5FF738D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{02594494-54BF-495D-8E85-9A3DA5FF738D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{02594494-54BF-495D-8E85-9A3DA5FF738D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{02594494-54BF-495D-8E85-9A3DA5FF738D}.Release|Any CPU.Build.0 = Release|Any CPU
		{F45B5285-2F28-4D09-A56A-8A5DF68A1AD8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F45B5285-2F28-4D09-A56A-8A5DF68A1AD8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F45B5285-2F28-4D09-A56A-8A5DF68A1AD8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F45B5285-2F28-4D09-A56A-8A5DF68A1AD8}.Release|Any CPU.Build.0 = Release|Any CPU
		{CFDC0F29-6065-4529-BB8D-CC0845C01CE2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CFDC0F29-6065-4529-BB8D-CC0845C01CE2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CFDC0F29-6065-4529-BB8D-CC0845C01CE2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CFDC0F29-6065-4529-BB8D-CC0845C01CE2}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA9BE52A-44D7-4723-837F-DE030E174CB3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA9BE52A-44D7-4723-837F-DE030E174CB3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA9BE52A-44D7-4723-837F-DE030E174CB3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA9BE52A-44D7-4723-837F-DE030E174CB3}.Release|Any CPU.Build.0 = Release|Any CPU
		{A8F025F9-2D72-41F9-BD30-39349F53ED92}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A8F025F9-2D72-41F9-BD30-39349F53ED92}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A8F025F9-2D72-41F9-BD30-39349F53ED92}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A8F025F9-2D72-41F9-BD30-39349F53ED92}.Release|Any CPU.Build.0 = Release|Any CPU
		{CD1B5538-3152-43E0-9B5B-024BDF30FC7F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CD1B5538-3152-43E0-9B5B-024BDF30FC7F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CD1B5538-3152-43E0-9B5B-024BDF30FC7F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CD1B5538-3152-43E0-9B5B-024BDF30FC7F}.Release|Any CPU.Build.0 = Release|Any CPU
		{A894B464-F308-45E2-83D9-5880BA31F147}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A894B464-F308-45E2-83D9-5880BA31F147}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A894B464-F308-45E2-83D9-5880BA31F147}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A894B464-F308-45E2-83D9-5880BA31F147}.Release|Any CPU.Build.0 = Release|Any CPU
		{586C41B2-B567-40AE-AC97-1B0E4DBC53CA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{586C41B2-B567-40AE-AC97-1B0E4DBC53CA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{586C41B2-B567-40AE-AC97-1B0E4DBC53CA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{586C41B2-B567-40AE-AC97-1B0E4DBC53CA}.Release|Any CPU.Build.0 = Release|Any CPU
		{5D0EDA4A-D567-43B7-8227-64E945AD0128}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5D0EDA4A-D567-43B7-8227-64E945AD0128}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5D0EDA4A-D567-43B7-8227-64E945AD0128}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5D0EDA4A-D567-43B7-8227-64E945AD0128}.Release|Any CPU.Build.0 = Release|Any CPU
		{3CEACBBA-5376-446B-BDC7-DAF074CEC8EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3CEACBBA-5376-446B-BDC7-DAF074CEC8EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3CEACBBA-5376-446B-BDC7-DAF074CEC8EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3CEACBBA-5376-446B-BDC7-DAF074CEC8EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{1418C3CB-4054-49CA-A585-FAD202EC10E5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1418C3CB-4054-49CA-A585-FAD202EC10E5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1418C3CB-4054-49CA-A585-FAD202EC10E5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1418C3CB-4054-49CA-A585-FAD202EC10E5}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D9797C51-A036-4684-BB3E-BDFF5B031F3F}
	EndGlobalSection
EndGlobal
