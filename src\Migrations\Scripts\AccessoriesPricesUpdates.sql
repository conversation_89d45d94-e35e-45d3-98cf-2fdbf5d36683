﻿--I<PERSON> stand
EXEC NewProductVersion_v2 'IPAD_STAND', 0, 'IPAD_STAND', 1, 1

DECLARE @IpadStand UNIQUEIDENTIFIER
SELECT TOP 1 @IpadStand = ID FROM Products where Code='IPAD_STAND' and Version = 1

UPDATE Prices SET PerItemPrice = 50000 where ProductId = @IpadStand

--Pro Cash Drawer
EXEC NewProductVersion_v2 'PRO_CASH_DRAWER', 0, 'PRO_CASH_DRAWER', 1, 1

DECLARE @ProCashDrawer UNIQUEIDENTIFIER
SELECT TOP 1 @ProCashDrawer = ID FROM Products where Code='PRO_CASH_DRAWER' and Version = 1

UPDATE Prices SET PerItemPrice = 149900 where ProductId = @ProCashDrawer

--Pro Scanner
EXEC NewProductVersion_v2 'PRO_SCANNER', 0, 'PRO_SCANNER', 1, 1

DECLARE @ProScanner UNIQUEIDENTIFIER
SELECT TOP 1 @ProScanner = ID FROM Products where Code='PRO_SCANNER' and Version = 1

UPDATE Prices SET PerItemPrice = 149900 where ProductId = @ProScanner

--Pro Printer
EXEC NewProductVersion_v2 'PRO_PRINTER', 0, 'PRO_PRINTER', 1, 1

DECLARE @ProPrinter UNIQUEIDENTIFIER
SELECT TOP 1 @ProPrinter = ID FROM Products where Code='PRO_PRINTER' and Version = 1

UPDATE Prices SET PerItemPrice = 179900 where ProductId = @ProPrinter

--Pro Screen Lite
EXEC NewProductVersion_v2 'PRO_SCREEN_LITE', 0, 'PRO_SCREEN_LITE', 1, 1

DECLARE @ProScreenLite UNIQUEIDENTIFIER
SELECT TOP 1 @ProScreenLite = ID FROM Products where Code='PRO_SCREEN_LITE' and Version = 1

UPDATE Prices SET PerItemPrice = 290000 where ProductId = @ProScreenLite

--Pro Screen
EXEC NewProductVersion_v2 'PRO_SCREEN', 0, 'PRO_SCREEN', 1, 1

DECLARE @ProScreen UNIQUEIDENTIFIER
SELECT TOP 1 @ProScreen = ID FROM Products where Code='PRO_SCREEN' and Version = 1

UPDATE Prices SET PerItemPrice = 400000 where ProductId = @ProScreen