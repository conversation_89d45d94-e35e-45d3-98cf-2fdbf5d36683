﻿using Common.Data.ProductType;
using Common.Entities;
using Common.Models.ProductInstance;
using Common.Repositories;
using Common.Services;
using Geidea.Utils.Counterparty.Providers;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace Services.Test;

public class ProductChangeSenderServiceTests
{
    private readonly Mock<IProductInstancePublisherSelector> publisherSelector;
    private readonly Mock<IProductInstanceRepository> productInstanceRepository;
    private readonly Mock<ICounterpartyProvider> counterpartyProvider;
    private readonly Mock<IGatewayProductInstancePublisher> gatewayInstancePublisher;

    private static readonly Guid ProductInstanceCompanyId = Guid.NewGuid();

    private static readonly Guid ProductInstanceId = Guid.NewGuid();

    private readonly ProductChangeSenderService productSenderService;

    private static readonly ProductEntity ProductEntity = new()
    {
        Type = ProductTypes.GWAY.ToString()
    };

    private static readonly ProductEntity EgyptGatewayEntity = new()
    {
        Type = ProductTypes.MEEZA.ToString(),
    };

    private readonly ProductInstanceEntity productInstance = new()
    {
        Product = ProductEntity,
        Metadata = "{\"MerchantGatewayKey\":\"MerchantGatewayKey\"}",
        CompanyId = ProductInstanceCompanyId
    };

    private readonly ProductInstanceEntity egyptGatewayInstance = new()
    {
        Product = EgyptGatewayEntity,
        Metadata = "{\"RegistrationStatus\":\"Registered\",\"MeezaMerchantId\":\"*********\"}",
        CompanyId = ProductInstanceCompanyId
    };

    public ProductChangeSenderServiceTests()
    {
        publisherSelector = new Mock<IProductInstancePublisherSelector>();
        productInstanceRepository = new Mock<IProductInstanceRepository>();
        counterpartyProvider = new Mock<ICounterpartyProvider>();
        gatewayInstancePublisher = new Mock<IGatewayProductInstancePublisher>();

        productInstanceRepository
            .Setup(x => x.FindAsync(It.IsAny<FindProductInstanceRequest>(), false, It.IsAny<bool>()))
            .Returns(Task.FromResult(new List<ProductInstanceEntity> { egyptGatewayInstance, new ProductInstanceEntity() }));

        productSenderService = new ProductChangeSenderService(publisherSelector.Object,
            productInstanceRepository.Object,
            counterpartyProvider.Object);

        publisherSelector.Setup(x => x
                .GetProductInstancePublisher(ProductTypes.GWAY))
            .Returns(gatewayInstancePublisher.Object);
    }

    [Fact]
    public async Task PublishCreatedInstanceFromEgyptToRabbitMq()
    {
        counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);

        await productSenderService.SendCreatedEvent(productInstance);

        gatewayInstancePublisher.Verify(x => x.PublishCreatedEvent(productInstance, egyptGatewayInstance), Times.Once);
    }

    [Fact]
    public async Task PublishCreatedInstanceToRabbitMq()
    {
        counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

        await productSenderService.SendCreatedEvent(productInstance);

        gatewayInstancePublisher.Verify(x => x.PublishCreatedEvent(productInstance), Times.Once);
    }

    [Fact]
    public void PublishUpdatedInstanceToRabbitMq()
    {
        counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

        productSenderService.SendUpdatedEvent(productInstance);

        gatewayInstancePublisher.Verify(x => x.PublishUpdatedEvent(productInstance), Times.Once);
    }

    [Fact]
    public void PublishDeletedInstanceToRabbitMq()
    {
        counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

        productSenderService.SendDeletedEvent(ProductInstanceId, ProductTypes.GWAY.ToString());

        gatewayInstancePublisher.Verify(x => x.PublishDeletedEvent(ProductInstanceId), Times.Once);
    }
}
