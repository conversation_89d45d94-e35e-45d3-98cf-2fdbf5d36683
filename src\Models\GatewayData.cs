﻿using Models;
using Models.Rules;
using System;
using System.Collections.Generic;
using static Geidea.ProductService.Models.Helpers;

namespace Geidea.ProductService.Models;

public class GatewayData
{
    public GatewayData()
    {
        MpgsHistory = new List<MpgsAccountHistory>();
    }
    public string? MerchantGatewayKey { get; set; }
    public string? MpgsMsoProvider { get; set; }
    public IReadOnlyCollection<MpgsAccount> MpgsAccounts { get; set; } = new List<MpgsAccount>();
    public ICollection<MpgsAccountHistory> MpgsHistory { get; set; }
    public string? CyberSourceMsoProvider { get; set; }
    public IReadOnlyCollection<CyberSourceAccount> CyberSourceAccounts { get; set; } = new List<CyberSourceAccount>();

    public string? GsdkTid { get; set; }
    public string? GsdkMid { get; set; }
    public string? GsdkSecretKey { get; set; }
    public string? CyberSourceMerchantId { get; set; }
    public string? CyberSourceMerchantKeyId { get; set; }
    public string? CyberSourceSharedSecretKey { get; set; }
    public string DefaultPaymentOperation { get; set; } = "Pay";
    public string? ApiPassword { get; set; }
    public string? ApplePartnerInternalMerchantIdentifier { get; set; }
    public string? AppleCsr { get; set; }
    public string? AppleCertificatePrivateKey { get; set; }
    public string? ApplePaymentProcessingCertificate { get; set; }
    public DateTime? ApplePaymentProcessingCertificateExpiryDate { get; set; }
    public string? AppleCertificatePrivateKeyNew { get; set; }
    public string? ApplePaymentProcessingCertificateNew { get; set; }
    public DateTime? ApplePaymentProcessingCertificateExpiryDateNew { get; set; }
    public string? AppleDeveloperId { get; set; }
    public bool IsMeezaDigitalEnabled { get; set; }
    public bool IsApplePayWebEnabled { get; set; }
    public bool IsApplePayWebRecurringEnabled { get; set; }
    public bool IsApplePayMobileEnabled { get; set; }
    public bool IsApplePayMobileRecurringEnabled { get; set; }
    public bool IsApplePayMobileCertificateAvailable { get; set; }
    public string MerchantName { get; set; } = string.Empty;
    public string? MerchantNameAr { get; set; }
    public string? MerchantWebsite { get; set; }
    public string? MerchantDomain => ExtractDomainNameFromURL(MerchantWebsite);
    public string? MerchantLogoUrl { get; set; }
    public string? MerchantCountry { get; set; }
    public string? Mcc { get; set; }
    public string? CallbackUrl { get; set; }
    public bool IsTest { get; set; } = true;

    public bool IsLuhnCheckActive
    {
        get { return !IsTest || _IsLuhnCheckActive; }
        set { _IsLuhnCheckActive = value; }
    }

    private bool _IsLuhnCheckActive = true;
    public bool IsCallbackEnabled { get; set; } = true;
    public bool IsPaymentMethodSelectionEnabled { get; set; }
    public bool IsTransactionReceiptEnabled { get; set; }
    public bool IsFederationToGsdkEnabled { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsRefundEnabled { get; set; } = true;
    public bool UseMpgsApiV60 { get; set; }
    public bool MerchantPaymentNotification { get; set; }
    public bool CustomerPaymentNotification { get; set; }
    public bool IsCallbackEmailNotificationEnabled { get; set; }

    private string? _merchantNotificationEmail;
    public string? MerchantNotificationEmail
    {
        get => _merchantNotificationEmail;
        set => _merchantNotificationEmail = Helpers.FormatMerchantNotificationEmail(value);
    }
    public string? CustomerNotificationFromEmail { get; set; }
    public string? CallbackNotificationEmail { get; set; }
    public string? MaxRetryCount { get; set; }
    public string? NumberOfQuickRetries { get; set; }
    public string? PeriodBetweenSlowRetries { get; set; }
    public string? PeriodBetweenQuickRetries { get; set; }
    public string? MerchantEmail { get; set; }
    public IReadOnlyCollection<string> AllowedInitiatedByValues { get; set; } = new List<string>();
    public bool IsMulticurrencyEnabled { get; set; }
    public IReadOnlyCollection<string> Currencies { get; set; } = new List<string>();
    public bool IsMulticurrencySettlementEnabled { get; set; }
    public string? SettlementCurrency { get; set; }
    public bool IsMarkUpEnabled { get; set; }
    public string? MarkUpForMCC { get; set; }
    public IReadOnlyCollection<CardBrandProvider> CardBrandProviders { get; set; } = new List<CardBrandProvider>();
    public decimal? MinAmount { get; set; }
    public decimal? MaxAmount { get; set; }
    public bool IsValuBnplEnabled { get; set; } = false;
    public string? ValuVendorId { get; set; }
    public string? ValuStoreId { get; set; }
    public string? ValuProductId { get; set; }
    public decimal? ValuCapLimit { get; set; }
    public bool IsShahryCpBnplEnabled { get; set; } = false;
    public string? ShahryCpBnplMerchantCode { get; set; }
    public string? ShahryCpBnplBranchCode { get; set; }
    public bool IsShahryCnpBnplEnabled { get; set; } = false;
    public string? ShahryCnpBnplMerchantCode { get; set; }
    public string? ShahryCnpBnplBranchCode { get; set; }
    public bool IsSouhoolaCnpBnplEnabled { get; set; } = false;
    public string? SouhoolaMerchantNationalId { get; set; }
    public string? SouhoolaMerchantPhoneNumber { get; set; }
    public string? SouhoolaAccessKey { get; set; }
    public bool IsSouhoolaCpBnplEnabled { get; set; } = false;
    public bool IsSouhoolaCpMerchantRegistered { get; set; } = false;
    public string? SouhoolaCpBnplNationalId { get; set; }
    public string? SouhoolaCpBnplGlobalId { get; set; }
    public string? SouhoolaCpBnplUserName { get; set; }
    public string? SouhoolaCpBnplPassword { get; set; }
    public string? SouhoolaCnpUserName { get; set; }
    public string? SouhoolaCnpPassword { get; set; }
    public bool IsCustomerEmailMandatory { get; set; }
    public bool IsCustomerShippingAddressMandatory { get; set; }
    public bool IsCustomerBillingAddressMandatory { get; set; }
    public bool AllowCashOnDeliveryValu { get; set; }
    public bool AllowDownPaymentValu { get; set; }
    public bool AllowCashOnDeliveryShahry { get; set; }
    public bool AllowCashOnDeliverySouhoola { get; set; }
    public AddOnFees AddOnFeesPbl { get; set; } = new AddOnFees();
    public ExtraCharges ExtraChargesPbl { get; set; } = new ExtraCharges();
    public SubMerchantInformation SubMerchantInformation { get; set; } = new SubMerchantInformation();
    public List<MsoSubMerchantInfo> MsoSubMerchantInfo { get; set; } = new List<MsoSubMerchantInfo>();
    public bool EnableSubMerchantInformation { get; set; }
    public bool IsBankInstallmentsCnpEnabled { get; set; }
    public Rules Rules { get; set; } = new Rules();

    public bool IsRecurringPaylinksEnabled { get; set; }
    public bool IsTokenizationEnabled { get; set; } = false;
    public bool IsCustomerInitiatedTransactionsEnabled { get; set; }
    public bool? Is3dsRequiredForTokenPayments { get; set; }
    public bool? IsCvvRequiredForTokenPayments { get; set; }
    public bool IsMerchantInitiatedTransactionsEnabled { get; set; }
    public bool IsRecurringPaymentsEnabled { get; set; }
    public bool IsUnscheduledPaymentsEnable { get; set; }
    public string? TenantCode { get; set; }

    public IReadOnlyCollection<MadaAccount> MadaAccounts { get; set; } = new List<MadaAccount>();
    public IReadOnlyCollection<SouhoolaCpCredentialsLog> SouhoolaCpCredentialsLogs { get; set; } = new List<SouhoolaCpCredentialsLog>();
    public bool IsSmartRoutingEnabled { get; set; }
    public bool IsReferenceIDUnique { get; set; }
    public bool IsUseRegisteredPhoneEnabled { get; set; }

    public bool IsExcessiveCaptureEnabled { get; set; }
    public decimal? ExcessiveCapturePercentage { get; set; }
    public string? GooglePayMerchantId { get; set; }
    public bool IsGooglePayEnabled { get; set; }
    public bool IsTamaraEnabled { get; set; }
    public string? TamaraApiToken { get; set; }
    public string? TamaraPublicKey { get; set; }
    public string? TamaraMerchantId { get; set; }
    public string? TamaraAdditionalInfo { get; set; }
    public bool IsSaveCardEnabled { get; set; }
    public bool IsRefundPeriodEnabled { get; set; }
    public int? RefundPeriodDays { get; set; }
    public PayByGeideaCode PayByGeideaCode { get; set; } = new();
    public bool IsVisaInstallmentEnabled { get; set; }
    public string? PartnerReferenceID { get; set; }
    public bool IsSamsungPayWebEnabled { get; set; }

    public bool IsSamsungPayAppEnabled { get; set; }
    public string? SamsungPayCsr { get; set; }
    public string? SamsungPayCertificatePrivateKey { get; set; }
    public bool IsMerchantReportingAPIEnabled { get; set; }
    public bool IsSecureHPPVersionEnabled { get; set; }
    public string? StcPayMerchantId { get; set; }
    public bool IsStcPayEnabled { get; set; }
    public Tabby Tabby { get; set; } = new();

    public bool IsPartialRefundEnabled { get; set; }

    public bool IsCaptureEnabled { get; set; }

    public bool IsPartialCaptureEnabled { get; set; }
    public bool IsMerchantBrandingForPblQrEnabled { get; set; }
    public bool IsSplitPayoutEnabled { get; set; }
    public string? FederationType { get; set; }
    public InstaPay InstaPay { get; set; } = new();
    public bool IsOldTokenizationEnabled { get; set; }
    public AddOnFees AddOnFeesHpp { get; set; } = new AddOnFees();
    public ExtraCharges ExtraChargesHpp { get; set; } = new ExtraCharges();
    public bool IsAlwaysShowBNPLReceipt { get; set; } = true;
    public bool IsUpdateAuthorizationEnabled { get; set; }
    public bool IsUpdateAuthorizationPercentageEnabled { get; set; }
    public decimal? UpdateAuthorizationPercentage { get; set; }
    public bool IsGenerateAndUseNetworkTokenEnabled { get; set; }
    public bool IsUseNetworkTokenEnabled { get; set; }
    public bool IsSendNetworkTokenToMerchantEnabled { get; set; }
    public string? NetworkTokenEncryptionKey { get; set; }
    public string? NetworkTokenCallBackUrl { get; set; }
    public bool IsAuthorizationRefundEnable { get; set; }
}
