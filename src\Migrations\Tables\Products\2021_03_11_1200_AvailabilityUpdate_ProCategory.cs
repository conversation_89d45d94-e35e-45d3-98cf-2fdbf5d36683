﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_03_11_1200)]
public class AvailabilityUpdate_ProCategory : ForwardOnlyMigration
{
    public override void Up()
    {
        Update.Table("Products").Set(new { SalesChannel = "Onboarding" }).Where(new { Code = "BUSINESS_RESTAURANT", Counterparty = "GEIDEA_SAUDI" });
        Update.Table("Products").Set(new { SalesChannel = "Onboarding" }).Where(new { Code = "ENTERPRISE_RESTAURANT", Counterparty = "GEIDEA_SAUDI" });
    }
}
