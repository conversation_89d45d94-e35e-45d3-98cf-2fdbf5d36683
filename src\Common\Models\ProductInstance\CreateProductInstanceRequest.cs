﻿using System;
using System.Collections.Generic;

namespace Common.Models.ProductInstance;

public class CreateProductInstanceRequest
{
    public Guid? AgreementId { get; set; }
    public Guid ProductId { get; set; }
    public Guid? CompanyId { get; set; }
    public Guid? StoreId { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public Guid? ParentId { get; set; }
    public Guid? ParentConfigurationId { get; set; }
    public List<CreateProductInstanceRequest> Children { get; set; } = new List<CreateProductInstanceRequest>();
    public object? Data { get; set; }
    public string? Mid { get; set; }
}
