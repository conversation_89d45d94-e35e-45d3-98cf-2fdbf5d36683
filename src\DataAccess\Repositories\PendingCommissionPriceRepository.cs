﻿using Common.Entities;
using Common.Repositories;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace DataAccess.Repositories;
public class PendingCommissionPriceRepository : AuditableRepository<Guid, PendingComissionPriceEntity>, IPendingCommissionPriceRepository
{
    public PendingCommissionPriceRepository(DataContext context, IHttpContextAccessor contextAccessor) : base(context, contextAccessor)
    {
    }
    public async Task AddRange(IEnumerable<PendingComissionPriceEntity> entities)
    {
        context.Set<PendingComissionPriceEntity>().AddRange(entities);
        await context.SaveChangesAsync();
    }

    public IExecutionStrategy CreateExecutionStrategy()
    {
        return context.Database.CreateExecutionStrategy();
    }
    public async Task<List<PendingComissionPriceEntity>> GetExistPendingCommissionPrices(Expression<Func<PendingComissionPriceEntity, bool>> predicate)
    {
        return await context.Set<PendingComissionPriceEntity>().Where(predicate).ToListAsync();
    }
}
