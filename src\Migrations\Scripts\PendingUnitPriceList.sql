﻿CREATE OR ALTER VIEW PendingUnitPriceList
As
	SELECT 
        pu.Id,
		pu.UnitPriceId,
		p.Id AS ProductId,
        p.[Name] AS ProductName,
		m.Id AS MccId,
        m.[Name] AS MccName,
		b.Id As BusinessTypeId,
        b.[Name] AS BusinessType,
        pu.UnitPrice,
        pu.VATRate,
        p.Code AS ProductCode,
        m.Code AS MccCode,
        mc.Name AS MccCategory,
        pu.VATType,
        pu.BillingType,
        pu.BillingFrequency,
        pu.CreatedDate,
		pu.NewPrice,
		pu.ActionType,
		pu.[Status],
		u.Id as CreatedByUserId,
		CASE 
            WHEN ISNULL(u.FirstName, '') = '' AND ISNULL(u.LastName, '') = '' THEN ISNULL(u.Email, '') 
            ELSE ISNULL(u.FirstName, '') + ' ' + ISNULL(u.LastName, '') 
        END AS CreatedByUserName,
		u.Counterparty as UserCounterparty

    FROM
		[dbo].[PendingUnitPrice] pu
        JOIN [dbo].[Products]  p ON pu.ProductID = p.Id
        JOIN [dbo].[BusinessTypes] b ON pu.BusinessTypeID = b.Id
        JOIN [dbo].[Mcc] m ON pu.MCCID = m.Id
        JOIN [dbo].[MccCategory] mc ON m.MccCategoryId = mc.Id
		join [USERS].[dbo].[User] u on pu.CreatedBy = u.Id