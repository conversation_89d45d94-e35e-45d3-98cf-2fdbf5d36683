﻿using AutoMapper;
using Common.Data.Extensions;
using Common.Entities;
using Common.Enums;
using Common.Models;
using Common.Models.UnitPrice;
using Common.Repositories;
using Common.Views;
using DocumentFormat.OpenXml.InkML;
using EFCore.BulkExtensions;
using Geidea.Utils.DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Repositories;
public class UnitPriceRepository : AuditableRepository<Guid, UnitPriceEntity>, IUnitPriceRepository
{
    private IQueryable<UnitPriceListView> UnitPriceListQuery;
    private IQueryable<ProductCategoryView> ProductCategoryViewQuery;

    public UnitPriceRepository(DataContext context, IHttpContextAccessor contextAccessor, IMapper mapper) : base(context, contextAccessor)
    {
        ProductCategoryViewQuery = context.ProductCategoryView.AsQueryable();
        UnitPriceListQuery = context.UnitPriceList.AsQueryable();
    }
    public async Task<List<ProductCategoryView>> ProductCategoryList(Guid MccId, Guid BusinessTypeId)
    {
        var productCategories = await ProductCategoryViewQuery
          .Where(pc => pc.MccId == MccId && pc.BusinessTypeId == BusinessTypeId)
          .ToListAsync();

        return productCategories;
    }
    public void AddRange(IEnumerable<UnitPriceEntity> entities)
    {
        context.Set<UnitPriceEntity>().AddRange(entities);
    }
    public async Task<List<UnitPriceEntity>> GetExistUnitPrices(Expression<Func<UnitPriceEntity, bool>> predicate)
    {
        return await context.Set<UnitPriceEntity>().Where(predicate).ToListAsync();
    }
    public async Task SaveUnitPricesAsync(List<UnitPriceEntity> unitPrices)
    {
        if (unitPrices.Any())
        {
            AddRange(unitPrices);
            try
            {
                await context.SaveChangesAsync();
            }
            catch (DbUpdateException ex) when (ex.InnerException is SqlException sqlEx && sqlEx.Number == 2627)
            {
                throw new InvalidOperationException("A unique constraint violation occurred when attempting to create unit prices.", ex);
            }
        }
    }
    public async Task<UnitPricesListResponse> GetUnitPricesList(UnitPricesListRequest request)
    {
        UnitPriceListQuery = UnitPriceListQuery.AsNoTracking();

        UnitPriceListQuery = SearchUnitPrices(request, UnitPriceListQuery);
        UnitPriceListQuery = FilterUnitPrices(request, UnitPriceListQuery);

        UnitPriceListQuery = SortUnitPrices(request, UnitPriceListQuery);

        int totalCount = await UnitPriceListQuery.CountAsync();

        var unitPriceList = request.Size > 0
            ? await UnitPriceListQuery.Page(request.Page, request.Size).ToArrayAsync()
            : await UnitPriceListQuery.ToArrayAsync();

        return new UnitPricesListResponse
        {
            TotalCount = totalCount,
            TotalPages = request.Size > 0 ? (int)Math.Ceiling((double)totalCount / request.Size) : 1,  // Calculate total pages only if paginated
            UnitPriceList = unitPriceList
        };
    }

    public static IQueryable<UnitPriceListView> SearchUnitPrices(UnitPricesListRequest request, IQueryable<UnitPriceListView> unitPriceList)
    {
        var SearchTerm = request.SearchTerms.FirstOrDefault(s => !string.IsNullOrEmpty(s.Value));
        if (!string.IsNullOrEmpty(SearchTerm.Value))
        {
            switch (SearchTerm.Key)
            {
                case UnitPriceSearchKey.All:
                    unitPriceList = unitPriceList.Where(s => s.ProductName != null && s.ProductName.Contains(SearchTerm.Value) ||
                                                             s.ProductCode.Contains(SearchTerm.Value) ||
                                                             s.MccName.Contains(SearchTerm.Value) ||
                                                             s.MccCode.Contains(SearchTerm.Value) ||
                                                             s.MccCode.Contains(SearchTerm.Value) ||
                                                             s.BusinessType.Contains(SearchTerm.Value));
                    break;
                case UnitPriceSearchKey.ProductName:
                    unitPriceList = unitPriceList.Where(s => s.ProductName != null && s.ProductName.Contains(SearchTerm.Value));
                    break;
                case UnitPriceSearchKey.ProductCode:
                    unitPriceList = unitPriceList.Where(s => s.ProductCode.Contains(SearchTerm.Value));
                    break;
                case UnitPriceSearchKey.MccName:
                    unitPriceList = unitPriceList.Where(s => s.MccName.Contains(SearchTerm.Value));
                    break;
                case UnitPriceSearchKey.MccCode:
                    unitPriceList = unitPriceList.Where(s => s.MccCode.Contains(SearchTerm.Value));
                    break;
                case UnitPriceSearchKey.BusinessType:
                    unitPriceList = unitPriceList.Where(s => s.BusinessType.Contains(SearchTerm.Value));
                    break;
                default:
                    break;
            }
        }
        return unitPriceList;
    }
    public static IQueryable<UnitPriceListView> FilterUnitPrices(UnitPricesListRequest request, IQueryable<UnitPriceListView> unitPriceList)
    {
        if (request.FilterByProducts != null && request.FilterByProducts.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.FilterByProducts.Contains(s.ProductId));
        }

        if (request.FilterByBusinessType != null && request.FilterByBusinessType.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.FilterByBusinessType.Contains(s.BusinessTypeId));
        }
        if (request.FilterByMccType != null && request.FilterByMccType.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.FilterByMccType.Contains(s.MccId));
        }

        if (request.FilterByVatType != null && request.FilterByVatType.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.FilterByVatType.Contains(s.VATType));
        }
        return unitPriceList;
    }
    public static IQueryable<UnitPriceListView> SortUnitPrices(UnitPricesListRequest request, IQueryable<UnitPriceListView> unitPriceList)
    {
        SortType orderType;

        if (request.OrderType.ToLower() == SortType.asc.ToString())
            orderType = SortType.asc;
        else if (request.OrderType.ToLower() == SortType.desc.ToString())
            orderType = SortType.desc;
        else
            throw new ArgumentException("Invalid value for OrderType. Allowed values are 'asc' and 'desc'.", nameof(request));

        if (!string.IsNullOrEmpty(request.OrderFieldName))
        {
            // Primary sorting based on OrderFieldName
            unitPriceList = unitPriceList.OrderBy(request.OrderFieldName, orderType);

            // Add secondary sorting for CreatedDate when the primary sort field is not ProductDisplayOrder
            if (!request.OrderFieldName.Equals(nameof(UnitPriceListView.ProductDisplayOrder), StringComparison.OrdinalIgnoreCase))
            {
                unitPriceList = ((IOrderedQueryable<UnitPriceListView>)unitPriceList).ThenBy(u => u.CreatedDate);
            }
        }
        else
        {
            // Default sorting by CreatedDate if no OrderFieldName is provided
            unitPriceList = unitPriceList.OrderBy(u => u.CreatedDate);
        }

        return unitPriceList;

    }
    public async Task<UnitPriceEntity?> GetByIdAsync(Guid id)
    {
        return await context.Set<UnitPriceEntity>().FindAsync(id);
    }
    public async Task<int> DeleteBulkAsync(List<Guid> ids)
    {
        var rowsAffected = await context.Set<UnitPriceEntity>()
            .Where(up => ids.Contains(up.Id))
            .BatchDeleteAsync();

        return rowsAffected;
    }
    public async Task<List<UnitPriceEntity>> GetUnitPricesByIdsAsync(List<Guid> ids)
    {
        return await context.Set<UnitPriceEntity>()
            .Where(up => ids.Contains(up.Id))
            .ToListAsync();
    }
    public async Task AddLogsAsync(List<UnitPriceLogsEntity> logs)
    {
        await context.Set<UnitPriceLogsEntity>().AddRangeAsync(logs);
        await context.SaveChangesAsync();
    }
    public async Task<UnitPriceEntity?> GetUnitPriceByIdAsync(Guid id)
    {
        return await context.Set<UnitPriceEntity>()
            .FirstOrDefaultAsync(up => up.Id == id);
    }
    public async Task UpdateUnitPricesAsync(List<UnitPriceEntity> unitPriceEntities)
    {
        context.Set<UnitPriceEntity>().UpdateRange(unitPriceEntities);
        await context.SaveChangesAsync();
    }
}
