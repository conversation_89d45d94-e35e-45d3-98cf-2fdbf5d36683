﻿using Common.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Repositories;
public interface IMetaDataMigrationFilesRepository
{
    Task<List<MetaDataMigrationFilesEntity>> GetAllMigrationFiles();
    Task InsertAsync(MetaDataMigrationFilesEntity migrationFile);
    Task UpdateMigrationFile(Guid metaDataMigrationId, MetaDataMigrationFilesEntity newMigrationFile);
    Task<List<MetaDataMigrationFilesEntity>> GetFailedAndPartiallyFailedMigrationFiles();
}