﻿using Common.Entities;
using Common.Enums;
using Common.Models;
using Common.Models.ValueAddedServices;
using Common.Repositories;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Common.Data.Extensions;
using AutoMapper;
using Common.Models.businessType;

namespace DataAccess.Repositories;
public class ValueAddedServiceRepository : AuditableRepository<Guid, ValueAddedService>, IValueAddedServiceRepository
{
    private readonly IMapper _mapper;
    public ValueAddedServiceRepository(DbContext context, IHttpContextAccessor contextAccessor, IMapper mapper) : base(context, contextAccessor)
    {
        _mapper = mapper;
    }
    public async Task<ValueAddedService?> GetByIdAsync(Guid id)
    {
        return await context.Set<ValueAddedService>().FindAsync(id);
    }

    public async Task<ValueAddedServicesListResponse> GetValueAddedServicesList(GetValueAddedServicesListRequest request)
    {
        var services = GetAllValueAddedServices();
        services = SearchValueAddedServices(request, services);
        services = FilterValueAddedServices(request, services);
        SortValueAddedServices(request, ref services);

        int totalCount = await services.CountAsync();
        int totalPages = (int)Math.Ceiling((double)totalCount / request.Size);
        services = services.Skip((request.Page - 1) * request.Size)
                           .Take(request.Size);

        var servicesArray = await services.ToArrayAsync();
        return new ValueAddedServicesListResponse
        {
            valueAddedServices = servicesArray,
            TotalCount = totalCount,
            TotalPages = totalPages
        };
    }
    public IQueryable<GetValueAddedServicesListResponse> GetAllValueAddedServices()
    {
        var services = context.Set<ValueAddedService>().AsNoTracking();
        return _mapper.ProjectTo<GetValueAddedServicesListResponse>(services);
    }
    public static IQueryable<GetValueAddedServicesListResponse> SearchValueAddedServices(GetValueAddedServicesListRequest request, IQueryable<GetValueAddedServicesListResponse> services)
    {
        var searchTerms = request.SearchTerms.Where(kv => !string.IsNullOrEmpty(kv.Value)).ToList();

        if (searchTerms.Count == 0)
            return services;

        var query = services;

        foreach (var term in searchTerms)
        {
            string searchTerm = term.Value;
            ProductSearchKey searchKey = term.Key;

            switch (searchKey)
            {
                case ProductSearchKey.SearchByAll:
                    query = query.Where(s =>
                        (s.Name != null && s.Name.Contains(searchTerm)) ||
                        (s.NameAr != null && s.NameAr.Contains(searchTerm)) ||
                        (s.Code != null && s.Code.Contains(searchTerm)));
                    break;

                case ProductSearchKey.Name:
                    query = query.Where(s => s.Name != null && s.Name.Contains(searchTerm));
                    break;

                case ProductSearchKey.Subname:
                    query = query.Where(s => s.NameAr != null && s.NameAr.Contains(searchTerm));
                    break;

                case ProductSearchKey.Code:
                    query = query.Where(s => s.Code != null && s.Code.Contains(searchTerm));
                    break;
            }
        }

        return query;
    }
    public static IQueryable<GetValueAddedServicesListResponse> FilterValueAddedServices(GetValueAddedServicesListRequest request, IQueryable<GetValueAddedServicesListResponse> services)
    {
        if (request.Status != null && request.Status.Any())
        {
            services = services.Where(s => request.Status.Contains(s.Status));
        }

        return services;
    }
    public static IQueryable<GetValueAddedServicesListResponse> SortValueAddedServices(GetValueAddedServicesListRequest request, ref IQueryable<GetValueAddedServicesListResponse> services)
    {
        SortType orderType;

        if (request.OrderType.ToLower() == SortType.asc.ToString())
            orderType = SortType.asc;
        else if (request.OrderType.ToLower() == SortType.desc.ToString())
            orderType = SortType.desc;
        else
            throw new ArgumentException("Invalid value for OrderType. Allowed values are 'asc' and 'desc'.", nameof(request));

        if (!string.IsNullOrEmpty(request.OrderFieldName))
            services = services.OrderBy(request.OrderFieldName, orderType);

        return services;
    }
    public async Task<List<BasicValueAddedServiceInfo>> GetValueAddedServiceNamesAsync()
    {
        return await context.Set<ValueAddedService>()
        .AsNoTracking()
        .Select(p => new BasicValueAddedServiceInfo
        {
            Id = p.Id,
            Name = p.Name,
            NameAR = p.NameAr,
            Code = p.Code
        })
        .ToListAsync();
    }
}
