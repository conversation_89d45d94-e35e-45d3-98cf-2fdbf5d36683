﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Common.Entities.Gle;

public class GleTerminalEntity : GleBaseEntity
{
    public Guid GleStoreId { get; set; }
    [ForeignKey("GleStoreId")]
    public GleStoreEntity GleStore { get; set; } = null!;

    public Guid ProductInstanceId { get; set; }
    [ForeignKey("ProductInstanceId")]
    public ProductInstanceEntity ProductInstance { get; set; } = null!;

    public Guid OrderId { get; set; }
    public string? Tid { get; set; }
    public bool IsTerminalUser { get; set; }
}