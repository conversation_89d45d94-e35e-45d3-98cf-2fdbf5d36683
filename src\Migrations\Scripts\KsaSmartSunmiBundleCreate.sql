﻿BEGIN TRANSACTION;

DECLARE @ProductIds TABLE(ID UNIQUEIDENTIFIER)

--Categories
DECLARE @GoFamilyCategoryId UNIQUEIDENTIFIER

SELECT TOP 1 @GoFamilyCategoryId = ID FROM [PRODUCTS].[dbo].[Category] WHERE Counterparty = 'GEIDEA_SAUDI' AND Code = 'GO_FAMILY'

-- Bundles
DECLARE @GoSmartSunmiBundleId UNIQUEIDENTIFIER

INSERT INTO [PRODUCTS].[dbo].[Products](Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel, QuickOnboarding , ReferralChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'GO_SMART_SUNMI', 'BUNDLE', GETUTCDATE(), '00000000-0000-0000-0000-000000000000', GETUTCDATE(), 4, 0, 'GEIDEA_SAUDI', 'Normal', 'All', 0, 'Unassigned')

SELECT TOP 1 @GoSmartSunmiBundleId = ID FROM @ProductIds 
DELETE FROM @ProductIds

-- Products

DECLARE @SunmiId UNIQUEIDENTIFIER
SELECT TOP 1 @SunmiId = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_SAUDI' AND Code = 'SUNMI_P2' And Type = 'TERMINAL'

-- Card schemes

DECLARE @MadaScheme UNIQUEIDENTIFIER
SELECT TOP 1 @MadaScheme = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_SAUDI' AND Code = 'MADA_POS' And Type = 'SCHEME'

DECLARE @VisaScheme UNIQUEIDENTIFIER
SELECT TOP 1 @VisaScheme = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_SAUDI' AND Code = 'VISA_POS' And Type = 'SCHEME'

DECLARE @MasterCardScheme UNIQUEIDENTIFIER
SELECT TOP 1 @MasterCardScheme = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_SAUDI' AND Code = 'MC_POS' And Type = 'SCHEME'

-- Associations

INSERT INTO [PRODUCTS].[dbo].[Prices](ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
VALUES('MONTH', 'RECCURRING_CHARGE', 0, @GoSmartSunmiBundleId, 0, NULL, NULL, 1, GETUTCDATE(), 0, '00000000-0000-0000-0000-000000000000', GETUTCDATE(), 'SAR', 24, NULL)

INSERT INTO [PRODUCTS].[dbo].[ProductCategories](ProductId, CategoryId) VALUES(@GoSmartSunmiBundleId, @GoFamilyCategoryId)


INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@GoSmartSunmiBundleId, @SunmiId)
INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@GoSmartSunmiBundleId, @MadaScheme)
INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@GoSmartSunmiBundleId, @VisaScheme)
INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@GoSmartSunmiBundleId, @MasterCardScheme)

COMMIT;