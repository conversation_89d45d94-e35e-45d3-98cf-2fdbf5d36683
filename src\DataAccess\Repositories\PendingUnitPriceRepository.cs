﻿using Common.Data.Extensions;
using Common.Entities;
using Common.Enums;
using Common.Models;
using Common.Models.PendingUnitPrice;
using Common.Repositories;
using Common.Views;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Common.Enums.UnitPrice;
using Common;
using Microsoft.EntityFrameworkCore.Storage;

namespace DataAccess.Repositories;
public class PendingUnitPriceRepository : AuditableRepository<Guid, PendingUnitPriceEntity>, IPendingUnitPriceRepository
{
    private IQueryable<PendingUnitPriceListView> PendingUnitPriceListQuery;

    public PendingUnitPriceRepository(DataContext context, IHttpContextAccessor contextAccessor) : base(context, contextAccessor)
    {
        PendingUnitPriceListQuery = context.PendingUnitPriceList.AsQueryable();
    }
    public async Task AddRange(IEnumerable<PendingUnitPriceEntity> entities)
    {
        context.Set<PendingUnitPriceEntity>().AddRange(entities);
        await context.SaveChangesAsync();
    }
    public async Task<List<PendingUnitPriceEntity>> GetExistPendingUnitPrices(Expression<Func<PendingUnitPriceEntity, bool>> predicate)
    {
        return await context.Set<PendingUnitPriceEntity>().Where(predicate).ToListAsync();
    }
    public async Task<PendingUnitPricesListResponse> GetPendingUnitPricesList(PendingUnitPricesListRequest request)
    {
        PendingUnitPriceListQuery = PendingUnitPriceListQuery
            .Where(x => x.Status == StatusEnum.Pending && (x.UserCounterparty == null || x.UserCounterparty == Constants.CounterParty.Saudi))
            .AsNoTracking();

        PendingUnitPriceListQuery = SearchPendingUnitPrices(request, PendingUnitPriceListQuery);
        PendingUnitPriceListQuery = FilterPendingUnitPrices(request, PendingUnitPriceListQuery);
        PendingUnitPriceListQuery = SortPendingUnitPrices(request, PendingUnitPriceListQuery);

        int totalCount = await PendingUnitPriceListQuery.CountAsync();

        var PendingUnitPriceList = request.Size > 0
            ? await PendingUnitPriceListQuery.Page(request.Page, request.Size).ToArrayAsync()
            : await PendingUnitPriceListQuery.ToArrayAsync();

        return new PendingUnitPricesListResponse
        {
            TotalCount = totalCount,
            TotalPages = request.Size > 0 ? (int)Math.Ceiling((double)totalCount / request.Size) : 1,  // Calculate total pages only if paginated
            PendingUnitPriceList = PendingUnitPriceList
        };
    }

    private static IQueryable<PendingUnitPriceListView> SearchPendingUnitPrices(PendingUnitPricesListRequest request, IQueryable<PendingUnitPriceListView> unitPriceList)
    {
        var SearchTerm = request.SearchTerms.FirstOrDefault(s => !string.IsNullOrEmpty(s.Value));
        if (!string.IsNullOrEmpty(SearchTerm.Value))
        {
            switch (SearchTerm.Key)
            {
                case PendingUnitPriceSearchKey.All:
                    unitPriceList = unitPriceList.Where(s => s.ProductName != null && s.ProductName.Contains(SearchTerm.Value) ||
                                                             s.ProductCode.Contains(SearchTerm.Value) ||
                                                             s.MccName.Contains(SearchTerm.Value) ||
                                                             s.MccCode.Contains(SearchTerm.Value) ||
                                                             s.MccCode.Contains(SearchTerm.Value) ||
                                                             s.BusinessType.Contains(SearchTerm.Value) ||
                                                             s.CreatedByUserName.Contains(SearchTerm.Value));
                    break;
                case PendingUnitPriceSearchKey.ProductName:
                    unitPriceList = unitPriceList.Where(s => s.ProductName != null && s.ProductName.Contains(SearchTerm.Value));
                    break;
                case PendingUnitPriceSearchKey.ProductCode:
                    unitPriceList = unitPriceList.Where(s => s.ProductCode.Contains(SearchTerm.Value));
                    break;
                case PendingUnitPriceSearchKey.MccName:
                    unitPriceList = unitPriceList.Where(s => s.MccName.Contains(SearchTerm.Value));
                    break;
                case PendingUnitPriceSearchKey.MccCode:
                    unitPriceList = unitPriceList.Where(s => s.MccCode.Contains(SearchTerm.Value));
                    break;
                case PendingUnitPriceSearchKey.BusinessType:
                    unitPriceList = unitPriceList.Where(s => s.BusinessType.Contains(SearchTerm.Value));
                    break;
                case PendingUnitPriceSearchKey.CreatedBy:
                    unitPriceList = unitPriceList.Where(s => s.CreatedByUserName.Contains(SearchTerm.Value));
                    break;
                default:
                    break;
            }
        }
        return unitPriceList;
    }
    private static IQueryable<PendingUnitPriceListView> FilterPendingUnitPrices(PendingUnitPricesListRequest request, IQueryable<PendingUnitPriceListView> unitPriceList)
    {
        if (request.FilterByProducts != null && request.FilterByProducts.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.FilterByProducts.Contains(s.ProductId));
        }
        if (request.FilterByBusinessType != null && request.FilterByBusinessType.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.FilterByBusinessType.Contains(s.BusinessTypeId));
        }
        if (request.FilterByMccType != null && request.FilterByMccType.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.FilterByMccType.Contains(s.MccId));
        }
        if (request.FilterByVatType != null && request.FilterByVatType.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.FilterByVatType.Contains(s.VATType));
        }
        if (request.CreatedByFilter != null && request.CreatedByFilter.Any())
        {
            unitPriceList = unitPriceList.Where(s => request.CreatedByFilter.Contains(s.CreatedByUserId));
        }

        return unitPriceList;
    }
    private static IQueryable<PendingUnitPriceListView> SortPendingUnitPrices(PendingUnitPricesListRequest request, IQueryable<PendingUnitPriceListView> unitPriceList)
    {
        if (Enum.TryParse(request.OrderType, true, out SortType orderType) && !string.IsNullOrEmpty(request.OrderFieldName))
            unitPriceList = unitPriceList.OrderBy(request.OrderFieldName, orderType);

        return unitPriceList;
    }
    public IExecutionStrategy CreateExecutionStrategy()
    {
        return context.Database.CreateExecutionStrategy();
    }
}
