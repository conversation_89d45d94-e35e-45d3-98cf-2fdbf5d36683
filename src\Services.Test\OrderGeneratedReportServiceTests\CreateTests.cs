﻿using Common.Models.OrderGeneratedReport;
using Common.Repositories;
using NSubstitute;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace Services.Test.OrderGeneratedReportServiceTests;
public class CreateTests
{
    private readonly IOrderGeneratedReportRepository orderReportRepository;
    private readonly OrderGeneratedReportService orderReportService;

    public CreateTests()
    {
        orderReportRepository = Substitute.For<IOrderGeneratedReportRepository>();
        orderReportService = new OrderGeneratedReportService(orderReportRepository);
    }

    [Fact]
    public async Task CreateOrderGeneratedReport()
    {
        var request = new OrdersReportRequest
        {
            AcquiringBank = "NBE_BANK",
            OrderNumbers = new List<string> { "EX_595974" },
            ReportName = "Legal File"
        };

        await orderReportService.AddOrdersReport(request);

        await orderReportRepository.Received(1).AddOrdersReport(request);
    }
}
