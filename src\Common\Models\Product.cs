﻿using System;
using System.Collections.Generic;

namespace Common.Models;

public class Product
{
    public string Name { get; set; } = null!;
    public string NameAr { get; set; } = null!;
    public string CategoryName { get; set; } = null!;
    public bool DeletedFlag { get; set; }
    public string Availability { get; set; } = Constants.Availability.Live;
    public string Flow { get; set; } = Constants.Flow.Normal;
    public string? SalesChannel { get; set; }
    public string Code { get; set; } = null!;
    public int Version { get; set; } = 0;
    public string Type { get; set; } = null!;
    public string? Description { get; set; }
    public string? DescriptionAr { get; set; }
    public int? DisplayOrder { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public List<Price> Prices { get; set; } = new List<Price>();
    public List<ProductPart> Parts { get; set; } = new List<ProductPart>();
    public List<ProductCategories> ProductCategories { get; set; } = new List<ProductCategories>();
    public string? Counterparty { get; set; }
    public string? CRMProductId { get; set; }
    public bool QuickOnboarding { get; set; } = false;
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedDateUtc { get; set; }
    public string? UpdatedBy { get; set; }
    public DateTime? UpdatedDateUtc { get; set; }
    public Guid Id { get; set; }
    public string ReferralChannel { get; set; } = null!;
    public List<CommisionFee>? CommisionFees { get; set; }
    public string? KnowMoreURL { get; set; }
    public decimal? DeliveryFees { get; set; }
    public decimal? SetupFees { get; set; }

}
