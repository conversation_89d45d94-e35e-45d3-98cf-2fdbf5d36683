﻿using System;

namespace Common.Models;

public class PriceShortResponse
{
    public Guid PriceId { get; set; }
    public string? ChargeFrequency { get; set; } = null!;
    public string ChargeType { get; set; } = null!;
    public bool ExemptFromVAT { get; set; }
    public int? PerItemPrice { get; set; }
    public int? PercentagePrice { get; set; }
    public int? Threshold { get; set; }
    public string? Group { get; set; }
    public string? ThresholdType { get; set; }
    public int Priority { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public bool DeletedFlag { get; set; }
    public string? Currency { get; set; } = null!;
    public int? RentalPeriod { get; set; }
    public int? MaxPrice { get; set; }
}
