﻿using AutoMapper;
using Common.Entities;
using Common.Models.ProductCommissionPrice;
using Common.Models.UnitPrice;
using Common.Models.ValueAddedSerivcePricing;
using Common.Repositories;
using Common.Services;
using DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Services;
public class ValueAddedServicePricingService : IValueAddedServicePricingService
{
    #region Fields
    private readonly ILogger<ValueAddedServicePricingService> logger;
    private readonly IValueAddedServicePricingRepository valueAddedServicePricingRepository;

    private readonly IMapper mapper;
    #endregion

    #region Constructor
    public ValueAddedServicePricingService(ILogger<ValueAddedServicePricingService> logger, IValueAddedServicePricingRepository valueAddedServicePricingRepository, IMapper mapper)
    {
        this.logger = logger;
        this.valueAddedServicePricingRepository = valueAddedServicePricingRepository;
        this.mapper = mapper;
    }
    #endregion

    #region Create
    public async Task<ValueAddedServicePriceResponse> CreateAsync(ValueAddedServicesPricingCreateRequest request)
    {
        try
        {

            var (vasPrices, existingVasPrices) = await ProcessVasPricesAsync(request);

            await valueAddedServicePricingRepository.SaveVasPricesAsync(vasPrices);
            return new ValueAddedServicePriceResponse
            {
                CreatedVASPrices = mapper.Map<List<ValueAddedServicesPricingDetails>>(vasPrices),
                NewExistingVASPrices = mapper.Map<List<ValueAddedServicesPricingDetails>>(existingVasPrices)
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "CreateAddonsPricesAsync: An error occurred while Creating addons prices.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An error occurred while create addons prices.");
        }

    }

    private async Task<(List<ValueAddedServicePricingEntity> vasPrices, List<ValueAddedServicePricingEntity> existingVasPrices)> ProcessVasPricesAsync(ValueAddedServicesPricingCreateRequest request)
    {
        var newVasPricesList = new List<ValueAddedServicePricingEntity>();
        var existedVasPricesList = new List<ValueAddedServicePricingEntity>();

        var allExistedUnitPricesList = await valueAddedServicePricingRepository.GetExistVasPrices(up =>
                                         request.ProductIDs.Contains(up.ProductID) &&
                                         request.MCCIDs.Contains(up.MCCID) &&
                                         request.VASIDs.Contains(up.VASID) &&
                                         request.BusinessTypeIDs.Contains(up.BusinessTypeID));

        ProcessVasPriceLists(request, allExistedUnitPricesList, newVasPricesList, existedVasPricesList);

        return (newVasPricesList, existedVasPricesList);
    }

    private void ProcessVasPriceLists(
     ValueAddedServicesPricingCreateRequest request,
     List<ValueAddedServicePricingEntity> allExistedUnitPricesList,
     List<ValueAddedServicePricingEntity> newVasPricesList,
     List<ValueAddedServicePricingEntity> existedVasPricesList)
    {
        var combinations = from productId in request.ProductIDs
                           from mccId in request.MCCIDs
                           from businessTypeId in request.BusinessTypeIDs
                           from vasId in request.VASIDs
                           select new { productId, mccId, businessTypeId, vasId };

        foreach (var combination in combinations)
        {
            var existingVasPrice = allExistedUnitPricesList.FirstOrDefault(up =>
                up.ProductID == combination.productId &&
                up.MCCID == combination.mccId &&
                up.BusinessTypeID == combination.businessTypeId &&
                up.VASID == combination.vasId);

            if (existingVasPrice == null)
            {
                var vasPrice = CreateNewVasPrice(request, combination.productId, combination.mccId, combination.businessTypeId, combination.vasId);
                newVasPricesList.Add(vasPrice);
            }
            else
            {
                existedVasPricesList.Add(existingVasPrice);
            }
        }
    }

    private ValueAddedServicePricingEntity CreateNewVasPrice(ValueAddedServicesPricingCreateRequest request, Guid productId, Guid mccId, Guid businessTypeId, Guid vasId)
    {
        var vasPrice = mapper.Map<ValueAddedServicePricingEntity>(request);
        vasPrice.ProductID = productId;
        vasPrice.MCCID = mccId;
        vasPrice.BusinessTypeID = businessTypeId;
        vasPrice.VASID = vasId;
        return vasPrice;
    }

    #endregion


    #region Update
    public async Task<ValueAddedServicesPricingDetails> UpdateAsync(Guid id, ValueAddedServicePricingUpdateRequest updateRequest)
    {
        // Fetch the existing unit price entity
        try
        {
            var existingVasPrice = await valueAddedServicePricingRepository.GetByIdAsync(id);

            if (existingVasPrice == null)
            {
                logger.LogWarning("No Addons price found with Id {Id}.", id);
                throw new ServiceException(HttpStatusCode.NotFound, "Addons price not found.");
            }

            // Update only the allowed properties
            existingVasPrice.SubscriptionFee = updateRequest.SubscriptionFee;
            existingVasPrice.FeeType = updateRequest.FeeType;
            existingVasPrice.BillingType = updateRequest.BillingType;
            existingVasPrice.BillingFrequency = updateRequest.BillingFrequency;

            valueAddedServicePricingRepository.Update(existingVasPrice);
            await valueAddedServicePricingRepository.SaveChangesAsync();

            // Return the updated unit price details
            return mapper.Map<ValueAddedServicesPricingDetails>(existingVasPrice);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "UpdateAddonsPricesAsync: An error occurred while updating addons prices.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An error occurred while updating addons prices.");
        }
    }
    #endregion


    #region Delete AddonsPrices
    public async Task DeleteAddonsPricesAsync(List<Guid> ids, string deletedBy)
    {
        if (!ids.Any())
        {
            logger.LogWarning("DeleteAddonsPricesAsync: No IDs were provided for deletion.");
            throw new ServiceException(HttpStatusCode.BadRequest, "No IDs provided for deletion.");
        }

        var addonsPricesToDelete = await valueAddedServicePricingRepository.GetAddonsPricesByIdsAsync(ids);

        if (!addonsPricesToDelete.Any())
        {
            logger.LogWarning("DeleteAddonsPricesAsync: No matching records found for the provided IDs.");
            throw new ServiceException(HttpStatusCode.NotFound, "No matching records found for deletion.");
        }

        try
        {
            var logs = addonsPricesToDelete.Select(up =>
            {
                var log = mapper.Map<ValueAddedServicePricingLogEntity>(up);
                log.DeletedBy = deletedBy;
                log.DeletedDate = DateTime.UtcNow;
                return log;
            }).ToList();

            int affectedRows = await valueAddedServicePricingRepository.DeleteBulkAsync(ids);
            await valueAddedServicePricingRepository.AddLogsAsync(logs);

            logger.LogInformation($"DeleteAddonsPricesAsync: Successfully deleted {affectedRows} addons prices and logged the actions.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "DeleteAddonsPricesAsync: An error occurred while deleting addons prices.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An error occurred while deleting addons prices.");
        }
    }
    #endregion
    #region Listing
    public async Task<ValueAddedServicePriceListResponse> GetValueAddedServicesPricesList(ValueAddedServicePriceListRequest request)
    {
        if (request == null)
        {
            logger.LogError("Invalid Get VAS Price List request.");
            throw new ServiceException(HttpStatusCode.BadRequest, "Invalid Get VAS Price List request.");
        }
        try
        {
            return await valueAddedServicePricingRepository.GetValueAddedServicePricesList(request);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "something has went wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, "something has went wrong!");
        }
    }
    #endregion

    #region Get By Id
    public async Task<ValueAddedServicesPricingDetails> GetAddOnsPriceByIdAsync(Guid id)
    {
        try
        {
            var addonsPriceEntity = await valueAddedServicePricingRepository.GetAddOnsPriceByIdAsync(id);

            if (addonsPriceEntity == null)
            {
                logger.LogWarning("No addons price found with Id {Id}.", id);
                throw new ServiceException(HttpStatusCode.NotFound, "addons price not found.");
            }

            var addonsPriceDetails = mapper.Map<ValueAddedServicesPricingDetails>(addonsPriceEntity);
            return addonsPriceDetails;
        }

        catch (Exception ex)
        {
            logger.LogError(ex, "something has went wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, "something has went wrong!");
        }
    }
    #endregion
    #region Create or Update 
    public async Task<ValueAddedServicePriceResponse> CreateOrUpdateAsync(ValueAddedServicesPricingCreateRequest request)
    {
        var (newVasPrices, oldExistUnitPricesToUpdate, existingVasPricesToUpdate) = await ProcessVasPricesForCreateOrUpdateAsync(request);

        // Save the new and updated VAS prices
        if (newVasPrices.Any())
        {
            await valueAddedServicePricingRepository.SaveVasPricesAsync(newVasPrices);
        }

        if (existingVasPricesToUpdate.Any())
        {
            await valueAddedServicePricingRepository.UpdateVasPricesAsync(existingVasPricesToUpdate);
        }

        return new ValueAddedServicePriceResponse
        {
            CreatedVASPrices = mapper.Map<List<ValueAddedServicesPricingDetails>>(newVasPrices),
            NewExistingVASPrices = mapper.Map<List<ValueAddedServicesPricingDetails>>(existingVasPricesToUpdate),
            OldExistingVASPrices = mapper.Map<List<ValueAddedServicesPricingDetails>>(oldExistUnitPricesToUpdate)
        };
    }

    private async Task<(List<ValueAddedServicePricingEntity> newVasPrices,
                        List<ValueAddedServicePricingEntity> oldExistUnitPricesToUpdate,
                        List<ValueAddedServicePricingEntity> newExistingVasPricesToUpdate)>
        ProcessVasPricesForCreateOrUpdateAsync(ValueAddedServicesPricingCreateRequest request)
    {
        var newVasPricesList = new List<ValueAddedServicePricingEntity>();
        var existingVasPricesToUpdateList = new List<ValueAddedServicePricingEntity>();
        var oldValueList = new List<ValueAddedServicePricingEntity>();

        var allExistedUnitPricesList = await valueAddedServicePricingRepository.GetExistVasPrices(up =>
            request.ProductIDs.Contains(up.ProductID) &&
            request.MCCIDs.Contains(up.MCCID) &&
            request.VASIDs.Contains(up.VASID) &&
            request.BusinessTypeIDs.Contains(up.BusinessTypeID));

        ProcessVasPriceListsForCreateOrUpdate(request, allExistedUnitPricesList, newVasPricesList, existingVasPricesToUpdateList, oldValueList);

        return (newVasPricesList, oldValueList, existingVasPricesToUpdateList);
    }

    private void ProcessVasPriceListsForCreateOrUpdate(
        ValueAddedServicesPricingCreateRequest request,
        List<ValueAddedServicePricingEntity> allExistedUnitPricesList,
        List<ValueAddedServicePricingEntity> newVasPricesList,
        List<ValueAddedServicePricingEntity> existingVasPricesToUpdateList,
        List<ValueAddedServicePricingEntity> oldValueList)
    {
        var combinations = from productId in request.ProductIDs
                           from mccId in request.MCCIDs
                           from businessTypeId in request.BusinessTypeIDs
                           from vasId in request.VASIDs
                           select new { productId, mccId, businessTypeId, vasId };

        foreach (var combination in combinations)
        {
            var existingVasPrice = allExistedUnitPricesList.FirstOrDefault(up =>
                up.ProductID == combination.productId &&
                up.MCCID == combination.mccId &&
                up.BusinessTypeID == combination.businessTypeId &&
                up.VASID == combination.vasId);

            if (existingVasPrice == null)
            {
                var vasPrice = CreateNewVasPrice(request, combination.productId, combination.mccId, combination.businessTypeId, combination.vasId);
                newVasPricesList.Add(vasPrice);
            }
            else
            {
                var oldExistingPrice = JsonConvert.DeserializeObject<ValueAddedServicePricingEntity>(
                    JsonConvert.SerializeObject(existingVasPrice));
                if (oldExistingPrice != null)
                    oldValueList.Add(oldExistingPrice);

                UpdateExistingVasPrice(existingVasPrice, request);
                existingVasPricesToUpdateList.Add(existingVasPrice);
            }
        }
    }

    private void UpdateExistingVasPrice(ValueAddedServicePricingEntity existingVasPrice, ValueAddedServicesPricingCreateRequest request)
    {
        existingVasPrice.SubscriptionFee = request.SubscriptionFee;
        existingVasPrice.FeeType = request.FeeType;
        existingVasPrice.BillingType = request.BillingType;
        existingVasPrice.BillingFrequency = request.BillingFrequency;
    }
    #endregion
    public async Task<List<ValueAddedServicesPricingDetails>> GetAddonsPricesByIdsAsync(List<Guid> ids)
    {
        var valueAddedServicePricingEntities = await valueAddedServicePricingRepository.GetAddonsPricesByIdsAsync(ids);
        return mapper.Map<List<ValueAddedServicesPricingDetails>>(valueAddedServicePricingEntities);
    }
}
