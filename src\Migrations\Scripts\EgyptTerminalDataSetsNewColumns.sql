BEGIN TRANSACTION;

-- Add New Columns to TerminalDataSets Table
ALTER TABLE [PRODUCTS].[dbo].[TerminalDataSets]
ADD 
[CountryPrefix] NVARCHAR(10) null,
[PhoneNumber] NVARCHAR(100) null,
[ChannelType] NVARCHAR(255) null,
[ConnectionType] NVARCHAR(10) null,
[MPGSMID] NVARCHAR(500) null,
[MPGSKEY] NVARCHAR(500) null;
GO

--Add Values to New Columns (ChannelType, ConnectionType)
UPDATE tds
SET
    tds.[ChannelType] = 'Softpos',
	tds.[ConnectionType] = 'MPGS'
FROM 
	[PRODUCTS].[dbo].[TerminalDataSets] tds
	JOIN [PRODUCTS].[dbo].[ProductInstances] AS Pis ON Pis.Id = tds.ProductInstanceId
	JOIN [PRODUCTS].[dbo].[Products] AS P ON P.Id = Pis.ProductId
	WHERE P.Counterparty = tds.Counterparty AND P.Counterparty = 'GEIDEA_EGYPT' 
	AND P.[Type] = 'Terminal' AND P.Code = 'SOFT_POS';
GO

UPDATE tds
SET
    tds.[ChannelType] = 'Smartpos',
	tds.[ConnectionType] = 'H2H'
FROM 
	[PRODUCTS].[dbo].[TerminalDataSets] tds
	JOIN [PRODUCTS].[dbo].[ProductInstances] AS Pis ON Pis.Id = tds.ProductInstanceId
	JOIN [PRODUCTS].[dbo].[Products] AS P ON P.Id = Pis.ProductId
	WHERE P.Counterparty = tds.Counterparty AND P.Counterparty = 'GEIDEA_EGYPT' 
	AND ((P.[Type] = 'Terminal' AND P.Code <> 'SOFT_POS') OR (P.[Type] = 'M_POS'));
GO

COMMIT;