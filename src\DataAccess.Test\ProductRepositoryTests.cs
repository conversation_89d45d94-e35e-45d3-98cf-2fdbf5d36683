﻿using Common.Entities;
using Common.Models;
using DataAccess.Repositories;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using Geidea.Utils.Common;
using Geidea.Utils.Counterparty.Providers;
using ProductService;
using Xunit;
using Constants = Common.Constants;
using Microsoft.Extensions.Options;
using Common.Options;
using Microsoft.CodeAnalysis.Options;
using NSubstitute;

namespace DataAccess.Test;

public class ProductRepositoryTests
{
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly DataContext context;
    private readonly ProductRepository productRepository;

    private Guid productGoSmartId = Guid.Parse("00000000-0000-0000-0000-000000000001");
    private Guid productWebsiteBuilderId = Guid.Parse("00000000-0000-0000-0000-000000000002");
    private Guid productSoftPosId = Guid.Parse("00000000-0000-0000-0000-000000000003");
    private Guid productTillPointId = Guid.Parse("00000000-0000-0000-0000-000000000004");
    private Guid productVisaId = Guid.Parse("00000000-0000-0000-0000-000000000005");
    private Guid categoryGoFamilyId = Guid.Parse("00000000-0000-0000-0000-000000000001");
    private readonly Guid productBpBundle = Guid.NewGuid();
    private readonly Guid productBpService = Guid.NewGuid();
    private readonly IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;
    private readonly ICounterpartyProvider counterpartyProvider;

    public ProductRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<DataContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        context = new DataContext(options, new CounterpartyProvider());
        softposEgyptFeatureToggle = Substitute.For<IOptions<SoftposEgyptFeatureToggle>>();
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = true });
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();

        productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);

        context.Products.AddRange(new List<ProductEntity>
            {
                new ProductEntity
                {
                    Id=productGoSmartId,
                    Availability="Bundle",
                    Code="GO_SMART",
                    CreatedBy=Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.Now,
                    Description="Go smart bundle",
                    DisplayOrder=0,
                    Parts = new List<ProductPartEntity>
                    {
                        new ProductPartEntity
                        {
                            Part = new ProductEntity
                            {
                                Id=productWebsiteBuilderId,
                                Availability="PartOf",
                                DisplayOrder=0,
                                Code="WEBSITE_BUILDER",
                                Type="WSB"
                            }
                        }
                    },
                    Prices = new List<PriceEntity>
                    {
                        new PriceEntity
                        {
                            PerItemPrice=100
                        }
                    },
                    ProductCategories=new List<ProductCategoriesEntity>
                    {
                        new ProductCategoriesEntity
                        {
                            Category=new CategoryEntity
                            {
                                Id = categoryGoFamilyId,
                                Code="GO_FAMILY"
                            }
                        }
                    },
                    Type="Bundle",
                    Version=0
                },
                new ProductEntity
                {
                    Id=productSoftPosId,
                    Availability="Normal",
                    DisplayOrder=1,
                    Code="SOFT_POS",
                    Type="TERMINAL"
                },
                new ProductEntity
                {
                    Id=productTillPointId,
                    Availability="Normal",
                    DisplayOrder=1,
                    Code="TILL_POINT",
                    Type="MINI_ECR"
                },
                new ProductEntity
                {
                    Id=productVisaId,
                    Availability="Obsolete",
                    DisplayOrder=1,
                    Code="VISA",
                    Type="SCHEME"
                },
                new()
                {
                    Id=productBpBundle,
                    Availability="Live",
                    DisplayOrder=1,
                    Code=Constants.ProductCodes.GoSmartWithBP,
                    Type="Bundle"
               },
                new()
                {
                    Id=productBpService,
                    Availability="Live",
                    DisplayOrder=1,
                    Code=Constants.ProductCodes.BillPayment,
                    Type="Service"
                }
                ,new()
                {
                    Id=new Guid(),
                    Availability="Live",
                    DisplayOrder=1,
                    Code=Constants.ProductCodes.GoAir,
                    Type="BUNDLE"
                }
                ,new()
                {
                    Id=new Guid(),
                    Availability="Live",
                    DisplayOrder=1,
                    Code=Constants.ProductCodes.Softpos,
                    Type="BUNDLE"
                }
            });
        context.SaveChanges();
    }

    [Fact]
    public async Task HardDelete()
    {
        var priceId = Guid.NewGuid();
        var product = new ProductEntity
        {
            Prices = new List<PriceEntity>
                {
                    new PriceEntity
                    {
                        Id = priceId,
                        ChargeFrequency="Recurring",
                        CreatedBy=Guid.NewGuid().ToString(),
                        CreatedDate = DateTime.UtcNow
                    }
                },
            Parts = new List<ProductPartEntity>
                {
                    new ProductPartEntity
                    {
                        Part = new ProductEntity
                        {
                            Type="SCHEME",
                            CreatedBy=Guid.NewGuid().ToString(),
                            CreatedDate = DateTime.UtcNow
                        }
                    }
                }
        };

        context.Products.Add(product);
        await context.SaveChangesAsync();

        await productRepository.HardDeleteProductAsync(product);

        var retrievedProduct = await context.Products.FirstOrDefaultAsync(p => p.Id == product.Id);
        retrievedProduct.Should().BeNull();

        var price = await context.Prices.FirstOrDefaultAsync(p => p.Id == priceId);
        price.Should().BeNull();

        var part = await context.ProductParts.FirstOrDefaultAsync(p => p.ProductId == product.Id);
        part.Should().BeNull();
    }

    [Fact]
    public async Task FindNotValid()
    {
        var products = await productRepository.FindAsync(new FindProductRequest { OnlyValid = false }, false);
        products.Should().NotBeNull();
        products.Should().HaveCount(9);
    }
    [Fact]
    public async Task FindWithInfoNotValid()
    {
        var products = await productRepository.FindWithInfoAsync(new FindProductRequest { OnlyValid = false }, false);
        products.Should().NotBeNull();
        products.Should().HaveCount(9);
    }

    [Fact]
    public async Task FindById()
    {
        var products = await productRepository.FindAsync(new FindProductRequest { Id = Guid.Parse("00000000-0000-0000-0000-000000000001") }, false);
        products.Should().NotBeNull();
        products.Should().HaveCount(1);
        products[0].Type.Should().Be("Bundle");
        products[0].Code.Should().Be("GO_SMART");
    }

    [Fact]
    public async Task FindByAvailability()
    {
        var products = await productRepository.FindAsync(new FindProductRequest { Availability = "Normal" }, false);
        products.Should().NotBeNull();
        products.Should().HaveCount(2);
    }

    [Fact]
    public async Task FindWithInfoByAvailability()
    {
        var products = await productRepository.FindWithInfoAsync(new FindProductRequest { Availability = "Normal" }, false);
        products.Should().NotBeNull();
        products.Should().HaveCount(2);
    }

    [Fact]
    public async Task FindByCode()
    {
        var products = await productRepository.FindAsync(new FindProductRequest { Code = "TILL_POINT" }, false);
        products.Should().NotBeNull();
        products.Should().HaveCount(1);
        products[0].Type.Should().Be("MINI_ECR");
        products[0].Availability.Should().Be("Normal");
    }


    [Fact]
    public async Task FindWithInfoByCode()
    {
        var products = await productRepository.FindWithInfoAsync(new FindProductRequest { Code = "TILL_POINT" }, false);
        products.Should().NotBeNull();
        products.Should().HaveCount(1);
        products[0].Type.Should().Be("MINI_ECR");
        products[0].Availability.Should().Be("Normal");
    }

    [Fact]
    public async Task FindByDescription()
    {
        var products = await productRepository.FindAsync(new FindProductRequest { Description = "bundle" }, false);
        products.Should().NotBeNull();
        products.Should().HaveCount(1);
        products[0].Type.Should().Be("Bundle");
        products[0].Code.Should().Be("GO_SMART");
    }


    [Fact]
    public async Task FindWithInfoByDescription()
    {
        var products = await productRepository.FindWithInfoAsync(new FindProductRequest { Description = "bundle" }, false);
        products.Should().NotBeNull();
        products.Should().HaveCount(1);
        products[0].Type.Should().Be("Bundle");
        products[0].Code.Should().Be("GO_SMART");
    }

    [Fact]
    public async Task FindByType()
    {
        var products = await productRepository.FindAsync(new FindProductRequest { Type = "TERMINAL" }, false);
        products.Should().NotBeNull();
        products.Should().HaveCount(1);
        products[0].Code.Should().Be("SOFT_POS");
    }

    [Fact]
    public async Task FindWithInfoByType()
    {
        var products = await productRepository.FindWithInfoAsync(new FindProductRequest { Type = "TERMINAL" }, false);
        products.Should().NotBeNull();
        products.Should().HaveCount(1);
        products[0].Code.Should().Be("SOFT_POS");
    }


    [Fact]
    public async Task FindByCategory()
    {
        var products = await productRepository.FindAsync(new FindProductRequest { CategoryId = categoryGoFamilyId }, false);
        products.Should().NotBeNull();
        products.Should().HaveCount(1);
        products[0].Id.Should().Be(productGoSmartId);
    }

    [Fact]
    public async Task FindWithInfoByCategory()
    {
        var products = await productRepository.FindWithInfoAsync(new FindProductRequest { CategoryId = categoryGoFamilyId }, false);
        products.Should().NotBeNull();
        products.Should().HaveCount(1);
        products[0].Id.Should().Be(productGoSmartId);
    }

    [Fact]
    public void GetRelatedProductsIds_WhenNoProductIsFound_ReturnsEmptyArray()
    {
        var productIds = productRepository.GetRelatedProductsIds(new ProductCodesRequest { ProductCodes = new[] { "NotACode" } });
        productIds.Length.Should().Be(0);
    }

    [Fact]
    public void GetRelatedProductsIds_WhenProductsWithCodeExists_ReturnsProductIds()
    {
        var productIds = productRepository.GetRelatedProductsIds(new ProductCodesRequest { ProductCodes = new[] { "WEBSITE_BUILDER", "VISA" } });

        productIds.Length.Should().Be(3);
        productIds.Should().Contain(new Guid[] { productVisaId, productWebsiteBuilderId, productGoSmartId });
    }

    [Fact]
    public async Task GetProductIdsFromCodes_WhenNoProductCodeFound_ReturnsEmptyArray()
    {
        var products = await productRepository.GetProductIdsFromCodes(new ProductCodesRequest { ProductCodes = new[] { "NotACode" } });
        Assert.True(products.Count == 0);
    }

    [Fact]
    public async Task GetProductIdsFromCodes_WhenProductsWithCodeExists_ReturnsProducts()
    {
        var prodLive = new ProductEntity { Availability = "Live", Code = "TEST1", Type = "BUNDLE" };
        var prodObsolete = new ProductEntity { Availability = "Obsoletge", Code = "TEST1", Type = "BUNDLE" };
        context.Products.Add(prodLive);
        context.Products.Add(prodObsolete);
        await context.SaveChangesAsync();

        var products = await productRepository.GetProductIdsFromCodes(new ProductCodesRequest { ProductCodes = new[] { "TEST1" } });

        context.Products.Remove(prodLive);
        context.Products.Remove(prodObsolete);
        await context.SaveChangesAsync();

        Assert.True(products.Count == 1);


    }

    [Fact]
    public async Task ProductsContainBillPaymentServiceOrBundleAsync_ReturnsTrue()
    {
        var result = await productRepository.ProductsContainBillPaymentServiceOrBundleAsync(new IdsRequest
        {
            Ids = new[] { productBpBundle, productBpService, productGoSmartId }
        });

        result.Should().BeTrue();
    }

    [Fact]
    public async Task ProductsContainBillPaymentServiceOrBundleAsync_WhenOnlyBundle_ReturnsTrue()
    {
        var result = await productRepository.ProductsContainBillPaymentServiceOrBundleAsync(new IdsRequest
        {
            Ids = new[] { productBpBundle, productGoSmartId }
        });

        result.Should().BeTrue();
    }

    [Fact]
    public async Task ProductsContainBillPaymentServiceOrBundleAsync_WhenOnlyService_ReturnsTrue()
    {
        var result = await productRepository.ProductsContainBillPaymentServiceOrBundleAsync(new IdsRequest
        {
            Ids = new[] { productGoSmartId, productBpService }
        });

        result.Should().BeTrue();
    }

    [Fact]
    public async Task ProductsContainBillPaymentServiceOrBundleAsync_ReturnsFalse()
    {
        var result = await productRepository.ProductsContainBillPaymentServiceOrBundleAsync(new IdsRequest
        {
            Ids = new[] { productVisaId, productGoSmartId }
        });

        result.Should().BeFalse();
    }

    [Fact]
    public async Task ProductsContainBillPaymentTypeAsync_ReturnsTrue()
    {
        var result = await productRepository.ProductsContainBillPaymentTypeAsync(new IdsRequest
        {
            Ids = new[] { productBpService, productBpBundle, productSoftPosId }
        });

        result.HasBillPaymentBundle.Should().BeTrue();
        result.HasBillPaymentService.Should().BeTrue();
    }

    [Fact]
    public async Task ProductsContainBillPaymentTypeAsync_ReturnsFalse()
    {
        var result = await productRepository.ProductsContainBillPaymentTypeAsync(new IdsRequest
        {
            Ids = new[] { productTillPointId, productSoftPosId }
        });

        result.HasBillPaymentBundle.Should().BeFalse();
        result.HasBillPaymentService.Should().BeFalse();
    }

    [Fact]
    public async Task ProductsContainBillPaymentTypeAsync_ReturnsTrueForService()
    {
        var result = await productRepository.ProductsContainBillPaymentTypeAsync(new IdsRequest
        {
            Ids = new[] { productTillPointId, productBpService }
        });

        result.HasBillPaymentBundle.Should().BeFalse();
        result.HasBillPaymentService.Should().BeTrue();
    }

    [Fact]
    public async Task FindByBundleType_WhenSoftposEgyptEnabled_ShouldReturnGoAirBundle()
    {
        var products = await productRepository.FindAsync(new FindProductRequest { Type = "BUNDLE" }, false);
        products.Should().NotBeNull();
        products.Should().HaveCount(2);
        products[0].Code.Should().Be("GO_AIR");
    }

    [Fact]
    public async Task FindByBundleType_WhenSoftposEgyptNotEnabled_ShouldNotReturnGoAirBundle()
    {
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = false });

        counterpartyProvider.SetCode(Geidea.Utils.Common.Constants.CounterpartyEgypt);
        var productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);
        var products = await productRepository.FindAsync(new FindProductRequest { Type = "BUNDLE" }, false);
        products.Should().NotBeNull();
    }
    [Fact]
    public async Task GetProductsList_Should_ReturnProductsList()
    {
        var request = new GetProductsListRequest()
        {
            Page = 1,
            Size = 10
        };
        var products = await productRepository.GetProductsList(request);
        products.Should().NotBeNull();
    }
    [Fact]
    public async Task GetProductIdsAndNamesAsync_Should_Return_List_Of_ProductIds_And_Names()
    {
        // Arrange: Setup the context with sample data
        var product1 = new ProductEntity { Id = Guid.NewGuid(), Name = "Product 1" };
        var product2 = new ProductEntity { Id = Guid.NewGuid(), Name = "Product 2" };

        context.Products.Add(product1);
        context.Products.Add(product2);
        await context.SaveChangesAsync();

        // Act: Call the method under test
        var result = await productRepository.GetProductsNamesAsync();

        // Assert: Verify the result
        result.Should().NotBeNull();
        result.Should().HaveCount(11);

        result.Should().Contain(p => p.Id == product1.Id && p.Name == "Product 1");
        result.Should().Contain(p => p.Id == product2.Id && p.Name == "Product 2");
    }
    [Fact]
    public async Task GetProductDetails_Returns_Product_For_Valid_Id()
    {
        var Product = await productRepository.GetProductDetails(productGoSmartId);
        Product.Should().NotBeNull();
    }
    [Fact]
    public async Task GetProductDetails_Returns_Null_For_Non_Valid_Id()
    {
        var ProductId = Guid.NewGuid();
        var Product = await productRepository.GetProductDetails(ProductId);
        Product.Should().BeNull();
    }
}
