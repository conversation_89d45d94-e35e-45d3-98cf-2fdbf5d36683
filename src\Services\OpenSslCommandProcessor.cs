﻿using System.Diagnostics;
using Common.Services;

namespace Services;

public class OpenSslCommandProcessor : IOpenSslCommandProcessor
{
    private const int WaitTimeLimit = 10000;

    public void Execute(string command)
    {
        using var process = new Process
        {
            StartInfo =
                    {
                        FileName = "openssl",
                        UseShellExecute = false,
                        RedirectStandardError = true,
                        RedirectStandardOutput = true,
                        CreateNoWindow = true
                    }

        };
        process.StartInfo.ArgumentList.Add(command);
        process.Start();
        process.WaitForExit(WaitTimeLimit);

        if (!process.HasExited)
            process.Kill();
    }
}
