﻿using System;
using Geidea.ProductService.Models;
using Models;

namespace Common.Models;

public class DefaultProductInstanceRequest
{
    public string? Mid { get; set; }
    public Guid ProductId { get; set; }
    public Guid CompanyId { get; set; }
    public Guid StoreId { get; set; }
    public string MerchantName { get; set; } = string.Empty;
    public string? MerchantNameAr { get; set; }
    public string DefaultOperation { get; set; } = string.Empty;
    public string? MpgsMerchantId { get; set; }
    public string? MpgsApiKey { get; set; }
    public SubMerchantInformation SubMerchantInformation { get; set; } = new SubMerchantInformation();
    public string? TenantCode { get; set; }
    public string? AccountNumber { get; set; }
}
