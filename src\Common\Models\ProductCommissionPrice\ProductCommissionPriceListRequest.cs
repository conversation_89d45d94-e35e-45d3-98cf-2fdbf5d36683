﻿using Common.Enums;
using Common.Enums.ProductCommisssionPrices;
using Common.Enums.UnitPrice;
using Common.Models.UnitPrice;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.ProductCommissionPrice;
public class ProductCommissionPriceListRequest
{
    public Dictionary<ProductCommissionPriceSearchKey, string> SearchTerms { get; set; } = new Dictionary<ProductCommissionPriceSearchKey, string>();
    public List<Guid>? FilterByProducts { get; set; }
    public List<Guid>? FilterByMccType { get; set; }
    public List<Guid>? FilterByBusinessType { get; set; }
    public List<Guid>? FilterByCommissionFee { get; set; }
    public List<Status>? FilterByFeeStatus { get; set; }
    public List<CommisssionPricesBillingType>? FilterByBillingType { get; set; }
    public List<PriceBillingFrequency>? FilterByBillingFrequency { get; set; }
    [DefaultValue("desc")]
    public string OrderType { get; set; } = SortType.desc.ToString();
    [DefaultValue("CreatedDate")]
    public string OrderFieldName { get; set; } = "CreatedDate";

    [DefaultValue(1)]
    public int Page { get; set; } = 1;
    [DefaultValue(10)]
    public int Size { get; set; } = 10;
}
public enum ProductCommissionPriceSearchKey
{
    All,
    ProductName,
    ProductCode,
    MccName,
    MccCode,
    FeeName,
    FeeCode
}
