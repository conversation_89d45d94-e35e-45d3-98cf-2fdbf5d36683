﻿ DECLARE @ProductIds TABLE(ID UNIQUEIDENTIFIER)
 DECLARE @Geidea_A920 UNIQUEIDENTIFIER
 DECLARE @Geidea_MPOS UNIQUEIDENTIFIER
 DECLARE @MPOS_SP530 UNIQUEIDENTIFIER
 DECLARE @SmartPOS UNIQUEIDENTIFIER
 DECLARE @GeideaGoApp UNIQUEIDENTIFIER
 DECLARE @GoFamilyCategoryId UNIQUEIDENTIFIER
 DECLARE @Mastercard UNIQUEIDENTIFIER
 DECLARE @Mada UNIQUEIDENTIFIER
 DECLARE @Visa UNIQUEIDENTIFIER

 SELECT TOP 1 @GoFamilyCategoryId = ID FROM Category where  Code = 'GO_FAMILY'
 SELECT TOP 1 @SmartPOS = ID FROM Products where  Code = 'SMARTPOS_A920'
 SELECT TOP 1 @GeideaGoApp = ID FROM Products where  Code = 'GEIDEA_GO_APP'
 SELECT TOP 1 @MPOS_SP530 = ID FROM Products where  Code = 'MOBILE_POS_SP530'
 SELECT TOP 1 @Mastercard = ID FROM Products where  Code = 'MC'
 SELECT TOP 1 @Mada = ID FROM Products where  Code = 'MADA'
 SELECT TOP 1 @Visa = ID FROM Products where  Code = 'VISA'

 --Geidea A920
 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle', 'GEIDEA_A920', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @Geidea_A920 = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@Geidea_A920, @GoFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('ONE_OFF','SETUP_CHARGE', 0, @Geidea_A920, 240000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Geidea_A920, @SmartPOS)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Geidea_A920, @GeideaGoApp)

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Geidea_A920, @Mastercard)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Geidea_A920, @Mada)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Geidea_A920, @Visa)

 --Geidea MPOS
 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle', 'GEIDEA_MPOS', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE())
 
 SELECT TOP 1 @Geidea_MPOS = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@Geidea_MPOS, @GoFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('ONE_OFF','SETUP_CHARGE',0, @Geidea_MPOS, 75000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Geidea_MPOS, @MPOS_SP530)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Geidea_MPOS, @GeideaGoApp)

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Geidea_MPOS, @Mastercard)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Geidea_MPOS, @Mada)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@Geidea_MPOS, @Visa)