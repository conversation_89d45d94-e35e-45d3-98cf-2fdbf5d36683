﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Common.Entities;

public class GatewayInstanceEntity
{
    public Guid ProductInstanceId { get; set; }
    public Guid? CompanyId { get; set; }
    public Guid? StoreId { get; set; }
    public bool? IsTest { get; set; }
    public Guid? MerchantGatewayKey { get; set; }
    public string? ShahryCpBnplMerchantCode { get; set; }
    public string? ShahryCpBnplBranchCode { get; set; }

    [ForeignKey("ProductInstanceId")]
    public ProductInstanceEntity ProductInstance { get; set; } = null!;
}