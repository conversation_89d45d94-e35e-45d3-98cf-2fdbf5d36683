﻿using Common.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models;
[ExcludeFromCodeCoverage]

public class GetProductsListRequest
{
    [DefaultValue(Constants.ProductsSearchDictionary)]
    public Dictionary<ProductSearchKey, string> SearchTerms { get; set; } = new Dictionary<ProductSearchKey, string>();
    public List<string>? FilterByCategory { get; set; }
    public List<string>? FilterBySalesChannel { get; set; }
    public string OrderType { get; set; } = SortType.desc.ToString();
    public string OrderFieldName { get; set; } = "CreatedDate";
    public int Page { get; set; } = 1;
    public int Size { get; set; } = 10;
}
