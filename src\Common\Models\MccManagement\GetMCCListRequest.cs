﻿using Common.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.MccManagement;
public class GetMccListRequest
{
    [DefaultValue(Constants.MCCSearchDictionary)]
    public Dictionary<MccSearchKey, string> SearchTerms { get; set; } = new Dictionary<MccSearchKey, string>();
    public List<Status>? FilterByStatus { get; set; }
    public List<string>? FilterByCategory { get; set; }
    public string OrderType { get; set; } = SortType.desc.ToString();
    [DefaultValue("CreatedDate")]
    public string OrderFieldName { get; set; } = "CreatedDate";
    public int Page { get; set; } = 1;
    public int Size { get; set; } = 10;
}
public enum MccSearchKey
{
    All,
    Code,
    Name,
    Category,
}