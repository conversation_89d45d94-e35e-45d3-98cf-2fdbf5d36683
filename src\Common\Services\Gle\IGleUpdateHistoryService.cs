﻿using Common.Entities.Gle;
using Common.Models.Gle;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services.Gle;
public interface IGleUpdateHistoryService
{
    Task AddGleUpdateHistoryAsync(List<GleUpdateHistoryRequest> requests);
    Task<GleUpdateHistory?> GetGleUpdateHistoryByMerchantIdAsync(Guid merchantId);
    Task UpdateGleUpdateHistoryAsync(List<GleUpdateHistoryEditRequest> gleUpdateHistoryEditRequest);
}