﻿using FluentMigrator;

namespace Migrations.Tables.Products;

[Migration(2021_03_14_1000)]
public class ReminingCRMProductsMapping : ForwardOnlyMigration
{
    public override void Up()
    {
        Update.Table("Products").Set(new { CRMProductId = "GEIDEA_APP" }).Where(new { Code = "GEIDEA_GO_APP" });
        Update.Table("Products").Set(new { CRMProductId = "Softpos" }).Where(new { Code = "SOFT_POS" });
        Update.Table("Products").Set(new { CRMProductId = "E-INVOICING" }).Where(new { Code = "E_INVOICING" });
        Update.Table("Products").Set(new { CRMProductId = "D135" }).Where(new { Code = "D135_READER" });
        Update.Table("Products").Set(new { CRMProductId = "A920" }).Where(new { Code = "SMARTPOS_A920" });
        Update.Table("Products").Set(new { CRMProductId = "POS REOCKET" }).Where(new { Code = "POS_ROCKET" });
        Update.Table("Products").Set(new { CRMProductId = "LINGA_POS" }).Where(new { Code = "LINGA_POS" });
        Update.Table("Products").Set(new { CRMProductId = "GATEWAY" }).Where(new { Code = "PAYMENT_GATEWAY" });
        Update.Table("Products").Set(new { CRMProductId = "WEB-SITE BUILDER" }).Where(new { Code = "WEBSITE_BUILDER" });
        Update.Table("Products").Set(new { CRMProductId = "E-INVOICING GATEWAY" }).Where(new { Code = "E_INVOICING_GW" });
    }
}
