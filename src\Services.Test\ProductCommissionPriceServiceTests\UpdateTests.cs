﻿using AutoMapper;
using Common.Entities;
using Common.Models.ProductCommissionPrice;
using DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace Services.Test.ProductCommissionPriceServiceTests;
public class UpdateTests
{
    private readonly Mock<ILogger<ProductCommissionPriceService>> logger;
    private readonly Mock<IProductCommissionPriceRepository> CommissionPriceRepositoryMock;
    private readonly Mock<IMapper> mapperMock;
    private readonly ProductCommissionPriceService productCommissionPriceService;

    public UpdateTests()
    {
        logger = new Mock<ILogger<ProductCommissionPriceService>>();
        CommissionPriceRepositoryMock = new Mock<IProductCommissionPriceRepository>();
        mapperMock = new Mock<IMapper>();
        productCommissionPriceService = new ProductCommissionPriceService(logger.Object, CommissionPriceRepositoryMock.Object, mapperMock.Object);
    }
    [Fact]
    public async Task UpdateAsync_ShouldReturn_UnitPriceDetailsResponse_With_Created_And_Existing_Prices()
    {
        // Arrange
        var Id = Guid.NewGuid();
        var request = new ProductComissionPriceUpdateRequest
        {
            FeeType = Common.Enums.ProductCommisssionPrices.FeeType.Flat,
            FeeValue = 50,
            BillingType = Common.Enums.ProductCommisssionPrices.CommisssionPricesBillingType.PostPaid,
            BillingFrequency = Common.Enums.ProductCommisssionPrices.PriceBillingFrequency.Monthly
        };
        var existingEntity = new ProductCommissionPriceEntity { Id = Id };
        var updatedResponse = new ProductCommissionPriceDetailsResponse { };
        // Mock the repository to return an existing entity
        CommissionPriceRepositoryMock.Setup(r => r.GetByIdAsync(Id))
                                     .ReturnsAsync(existingEntity);
        mapperMock.Setup(mapper => mapper.Map<ProductCommissionPriceDetailsResponse>(It.IsAny<ProductCommissionPriceEntity>()))
                  .Returns(updatedResponse);
        // Act
        var result = await productCommissionPriceService.UpdateAsync(Id, request);
        // Assert
        Assert.NotNull(result);
        Assert.Equal(updatedResponse, result);
    }
}
