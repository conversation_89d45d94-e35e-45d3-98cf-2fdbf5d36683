﻿using System;
using System.Collections;
using System.Collections.Generic;
using Common.Models;
using Microsoft.AspNetCore.JsonPatch;

namespace Services.Test.TestData;

public class InvalidPatchData : IEnumerable<object[]>
{
    public IEnumerator<object[]> GetEnumerator()
    {
        var patch1 = new JsonPatchDocument<ProductRequest>();
        patch1.Replace(x => x.Availability, "AVAILABILITY");
        yield return new object[] { patch1 };

        var patch2 = new JsonPatchDocument<ProductRequest>();
        patch2.Replace(x => x.Code, "");
        yield return new object[] { patch2 };

        var patch3 = new JsonPatchDocument<ProductRequest>();
        patch3.Replace(x => x.Version, -1);
        yield return new object[] { patch3 };

        var patch4 = new JsonPatchDocument<ProductRequest>();
        patch4.Replace(x => x.Type, "");
        yield return new object[] { patch4 };

        var patch5 = new JsonPatchDocument<ProductRequest>();
        patch5.Replace(x => x.Type, "test");
        yield return new object[] { patch5 };

        var patch6 = new JsonPatchDocument<ProductRequest>();

        yield return new object[] { patch6 };

        var patch7 = new JsonPatchDocument<ProductRequest>();

        yield return new object[] { patch7 };
    }

    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }
}
