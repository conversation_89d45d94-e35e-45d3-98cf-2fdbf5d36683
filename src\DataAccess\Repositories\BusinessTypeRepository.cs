﻿using AutoMapper;
using Common.Data.Extensions;
using Common.Entities;
using Common.Enums;
using Common.Models;
using Common.Models.businessType;
using Common.Models.MccManagement;
using Common.Repositories;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Repositories;
public class BusinessTypeRepository : AuditableRepository<Guid, BusinessTypeEntity>, IBusinessTypeRepository
{
    private readonly IMapper mapper;
    public BusinessTypeRepository(DataContext context, IHttpContextAccessor contextAccessor, IMapper mapper) : base(context, contextAccessor)
    {
        this.mapper = mapper;
    }
    public async Task<GetBusinessTypesListResponse> GetBusinessTypesList(GetBusinessTypesListRequest request)
    {
        var BusinessTypesQuery = GetAllBusinessTypes();
        BusinessTypesQuery = SearchBusinessTypes(request, BusinessTypesQuery);
        BusinessTypesQuery = FilterBusinessTypes(request, BusinessTypesQuery);
        BusinessTypesQuery = SortBusinessTypes(request, BusinessTypesQuery);

        return new GetBusinessTypesListResponse
        {
            TotalCount = await BusinessTypesQuery.CountAsync(),
            TotalPages = (int)Math.Ceiling((double)BusinessTypesQuery.Count() / request.Size),
            BusinessTypeList = await BusinessTypesQuery.Page(request.Page, request.Size).ToArrayAsync()
        };
    }
    public IQueryable<BusinessTypesListResponse> GetAllBusinessTypes()
    {
        var BusinessTypesList = context.Set<BusinessTypeEntity>().AsNoTracking();
        return mapper.ProjectTo<BusinessTypesListResponse>(BusinessTypesList);
    }
    public static IQueryable<BusinessTypesListResponse> SearchBusinessTypes(GetBusinessTypesListRequest request, IQueryable<BusinessTypesListResponse> BusinessTypesList)
    {
        var SearchTerm = request.SearchTerms.FirstOrDefault(s => !string.IsNullOrEmpty(s.Value));
        if (!string.IsNullOrEmpty(SearchTerm.Value))
        {
            switch (SearchTerm.Key)
            {
                case BusinessTypesSearchKey.All:
                    BusinessTypesList = BusinessTypesList.Where(s => s.Code.Contains(SearchTerm.Value) ||
                                                                s.Name != null && s.Name.Contains(SearchTerm.Value));
                    break;
                case BusinessTypesSearchKey.Id:
                    BusinessTypesList = BusinessTypesList.Where(s => s.Code.Contains(SearchTerm.Value));
                    break;
                case BusinessTypesSearchKey.Type:
                    BusinessTypesList = BusinessTypesList.Where(s => s.Name != null && s.Name.Contains(SearchTerm.Value));
                    break;
                default:
                    break;
            }
        }
        return BusinessTypesList;
    }
    public static IQueryable<BusinessTypesListResponse> FilterBusinessTypes(GetBusinessTypesListRequest request, IQueryable<BusinessTypesListResponse> BusinessTypesList)
    {
        if (request.FilterByStatus != null && request.FilterByStatus.Any())
            BusinessTypesList = BusinessTypesList.Where(s => request.FilterByStatus.Contains(s.Status));

        return BusinessTypesList;
    }
    public static IQueryable<BusinessTypesListResponse> SortBusinessTypes(GetBusinessTypesListRequest request, IQueryable<BusinessTypesListResponse> BusinessTypesList)
    {
        SortType orderType;

        if (request.OrderType.ToLower() == SortType.asc.ToString())
            orderType = SortType.asc;
        else if (request.OrderType.ToLower() == SortType.desc.ToString())
            orderType = SortType.desc;
        else
            throw new ArgumentException("Invalid value for OrderType. Allowed values are 'asc' and 'desc'.", nameof(request));

        if (!string.IsNullOrEmpty(request.OrderFieldName))
            BusinessTypesList = BusinessTypesList.OrderBy(request.OrderFieldName, orderType);

        return BusinessTypesList;
    }

    public async Task<BusinessTypeEntity?> GetByIdAsync(Guid id)
    {
        return await context.Set<BusinessTypeEntity>().FindAsync(id);
    }
    public async Task<BusinessTypeDetailsResponse?> GetBusinessTypeDetails(Guid Id)
    {
        var BusinessTypeDetailsObj = await context.Set<BusinessTypeEntity>()
                                            .AsNoTracking()
                                            .Where(s => s.Id == Id)
                                            .Select(s => new BusinessTypeDetailsResponse()
                                            {
                                                Id = s.Id,
                                                Code = s.Code,
                                                Name = s.Name,
                                                NameAr = s.NameAr,
                                                Status = s.Status,
                                            }).SingleOrDefaultAsync();
        return BusinessTypeDetailsObj;
    }
    public async Task<List<BasicBusinessTypesInfo>> GetBusinessTypesNamesAsync()
    {
        return await context.Set<BusinessTypeEntity>()
        .AsNoTracking()
        .Select(p => new BasicBusinessTypesInfo
        {
            Id = p.Id,
            Name = p.Name,
            NameAR = p.NameAr,
            Code = p.Code
        })
        .ToListAsync();
    }
}