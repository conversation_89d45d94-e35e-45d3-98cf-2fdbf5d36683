﻿using System;
using System.Diagnostics.CodeAnalysis;

namespace Common.Models;

[ExcludeFromCodeCoverage]
public class StoreProductInstanceResponse
{
    public Guid ProductInstanceId { get; set; }

    public Guid? StoreId { get; set; }

    public Guid? ParentId { get; set; }

    public DateTime? CreatedDate { get; set; }

    public ProductShortResponse Product { get; set; } = null!;

    public object? Data { get; set; }

    public string? EPosTicketId { get; set; }
}
