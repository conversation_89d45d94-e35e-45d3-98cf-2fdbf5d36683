﻿using System;
using System.Linq;
using System.Text.Json;
using Common;
using Common.Data;
using Common.Data.ProductType;
using Common.Entities;
using Common.Services;
using Geidea.Messages.TerminalConfiguration;
using Geidea.Messages.TerminalConfiguration.Messages;
using GeideaPaymentGateway.Utils.RabbitMQ;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Generic;

namespace Messaging;

public class TerminalConfigurationPublisher : ConfigurationPublisherPublisher, IProductInstancePublisher
{
    private const string TerminalConfigurationCreated = "terminal.configuration.created.event";
    private const string TerminalConfigurationUpdated = "terminal.configuration.updated.event";
    private const string TerminalConfigurationDeleted = "terminal.configuration.deleted.event";

    public List<ProductTypes> ProductTypes => new List<ProductTypes> { Common.Data.ProductType.ProductTypes.TERMINAL, Common.Data.ProductType.ProductTypes.M_POS };

    public TerminalConfigurationPublisher(IOptionsMonitor<RabbitMqConfig> options,
        ILogger<TerminalConfigurationPublisher> logger,
        IHttpContextAccessor contextAccessor,
        IConnectionFactory factory)
        : base(options, logger, contextAccessor, factory, "TerminalConfiguration.Fanout")
    {
    }

    public void PublishCreatedEvent(ProductInstanceEntity productInstance)
    {
        if (!IsConfigValid)
            return;

        var message = new TerminalConfigurationCreatedEvent
        {
            Header = BuildHeader(TerminalConfigurationCreated),
            TerminalConfiguration = BuildTerminalConfiguration(productInstance)
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(TerminalConfigurationCreated, messageAsByteArray);
    }

    public void PublishUpdatedEvent(ProductInstanceEntity productInstance)
    {
        if (!IsConfigValid)
            return;

        var message = new TerminalConfigurationUpdatedEvent
        {
            Header = BuildHeader(TerminalConfigurationUpdated),
            TerminalConfiguration = BuildTerminalConfiguration(productInstance)
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(TerminalConfigurationUpdated, messageAsByteArray);
    }

    public void PublishDeletedEvent(Guid productInstanceId)
    {
        if (!IsConfigValid)
            return;

        var message = new TerminalConfigurationDeletedEvent()
        {
            Header = BuildHeader(TerminalConfigurationDeleted),
            TerminalConfigurationId = productInstanceId
        };

        var messageAsByteArray = JsonSerializer.SerializeToUtf8Bytes(message);
        Send(TerminalConfigurationDeleted, messageAsByteArray);
    }

    private TerminalConfiguration BuildTerminalConfiguration(ProductInstanceEntity terminalProductInstance)
    {
        if (!(terminalProductInstance.Data is TerminalData terminalData))
        {
            logger.LogError("Expected terminal product instance.");
            throw new ServiceException(System.Net.HttpStatusCode.BadRequest, Errors.ExpectedTerminal);
        }

        var result = new TerminalConfiguration
        {
            TerminalConfigurationId = terminalProductInstance.Id,
            CompanyId = terminalProductInstance.CompanyId ?? Guid.Empty,
            MerchantId = terminalProductInstance.StoreId ?? Guid.Empty,
            PaymentMethods = terminalProductInstance.Children.Where(c => c.Product.Type == Common.Data.ProductType.ProductTypes.SCHEME.ToString()).Select(c => c.Product.Code)
                .ToList(),
            ShortNameEN = terminalData.ShortName_EN,
            ShortNameAR = terminalData.ShortName_AR,
            InstallDate = terminalData.InstallDate,
            ActiveDate = terminalData.InstallDate,
            TerminationDate = terminalData.TerminationDate,
            TerminalPassword = terminalData.TerminalPassword,
            ProductStatus = terminalData.ProductStatus,
            TerminalSerialNumber = terminalData.TerminalSerialNumber,
            ActivationType = terminalData.ActivationType,
            EodCutoff = terminalData.EODCutoff,
            Mcc = terminalData.Mcc,
            MidMerchantReference = terminalData.MIDMerchantReference,
            GsdkKey = terminalData.GSDKKey,
            DailyVolumeCap = terminalData.DailyVolumeCap.ToString(),
            ActivationCode = terminalData.ActivationCode,
            GpSLatitude = terminalData.GPS_Latitude,
            GpSLongitude = terminalData.GPS_Longitude,
            TradingCurrency = terminalData.TradingCurrency,
            PosDataCode = terminalData.POSDataCode,
            TId = terminalData.TId,
            FullTId = terminalData.FullTId,
            ProviderBank = terminalData.ProviderBank,
            AccountNo = terminalData.AccountNo,
            CountryPrefix = terminalData.CountryPrefix,
            PhoneNumber = terminalData.PhoneNumber,
            ConnectionType = terminalData.ConnectionType,
            ChannelType = terminalData.ChannelType,
            MPGSKEY = terminalData.MPGSKEY,
            MPGSMID = terminalData.MPGSMID
        };

        return result;
    }
}
