﻿using FluentMigrator;

namespace Migrations.Tables.Products;

[Migration(2021_07_29_1342)]
public class Products_AddProductsIndex_Counterparty : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(
            $@"IF NOT EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'IDX_Products_Counterparty' AND object_id = OBJECT_ID('[dbo].[Products]'))
                 BEGIN
                    CREATE NONCLUSTERED INDEX [IDX_Products_Counterparty]
                                ON [dbo].[Products] ([Counterparty])
                                INCLUDE ([CreatedDate])
                 END");
    }
}
