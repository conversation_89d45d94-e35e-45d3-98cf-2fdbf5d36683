﻿using Common.Models.ProductCommissionPrice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Services;
public interface IProductCommissionPriceService
{
    Task<ProductCommissionPriceResponse> CreateAsync(ProductCommissionPriceCreateRequest request);
    Task<ProductCommissionPriceDetailsResponse> UpdateAsync(Guid Id, ProductComissionPriceUpdateRequest request);
    Task<ProductCommissionPriceListResponse> GetProductCommissionPriceList(ProductCommissionPriceListRequest request);

    Task DeleteProductCommissionPricesAsync(List<Guid> ids, string deletedBy);
    Task<ProductCommissionPriceDetailsResponse> GetCommissionPriceByIdAsync(Guid id);
    Task<ProductCommissionPriceResponse> CreateOrUpdateAsync(ProductCommissionPriceCreateRequest request);
    Task<List<ProductCommissionPriceDetailsResponse>> GetProductCommissionPricesByIdsAsync(List<Guid> ids);
}
