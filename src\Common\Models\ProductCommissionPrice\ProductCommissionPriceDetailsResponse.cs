﻿using Common.Enums.ProductCommisssionPrices;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.ProductCommissionPrice;
public class ProductCommissionPriceDetailsResponse
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public Guid MccId { get; set; }
    public Guid BusinessTypeId { get; set; }
    public Guid CommissionFeeId { get; set; }
    public FeeType FeeType { get; set; }
    public decimal FeeValue { get; set; }
    public CommisssionPricesBillingType BillingType { get; set; }
    public PriceBillingFrequency BillingFrequency { get; set; }
}
