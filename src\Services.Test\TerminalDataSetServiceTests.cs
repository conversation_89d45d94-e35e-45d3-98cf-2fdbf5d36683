﻿using Common;
using Common.Entities;
using Common.Models.Search;
using Common.Models.TerminalDataSets;
using Common.Repositories;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Mvc.TagHelpers.Cache;
using Services.Test.Helpers;
using Common.Services.Acquirers;
using Moq;
using static Common.Constants;
using Services.Acquirers;

namespace Services.Test;

public class TerminalDataSetServiceTests
{
    private TerminalDataSetService terminalDataSetService;
    private readonly ITerminalDataSetRepository terminalDataSetRepositoryMock = Substitute.For<ITerminalDataSetRepository>();
    private readonly ILogger<TerminalDataSetService> loggerMock = Substitute.For<ILogger<TerminalDataSetService>>();
    private readonly ICounterpartyProvider counterpartyProvider = Substitute.For<ICounterpartyProvider>();
    private readonly IMapper mapperMock = Substitute.For<IMapper>();
    private readonly Func<string, IAcquirer> acquirerProviderMock = Substitute.For<Func<string, IAcquirer>>();

    private readonly List<TerminalDataSet> repositoryResponse = new()
    {
        new()
        {
            AcquiringLedger = "test",
            Availability = "test",
            FullTid = "***********",
            Tid = "111111",
            Mid = "test",
            Trsm = "test",
        }
    };
    private static Guid guid = Guid.NewGuid();
    private string counterPartyProvider = string.Empty;
    private readonly List<TerminalDataSetValidationRequiredFields> repositoryRequiredFieldsResponse = new()
    {
        new TerminalDataSetValidationRequiredFields()
        {
            Data = guid.ToString(),
            Label = "TID"
        }
    };

    [SetUp]
    public void Setup()
    {
        terminalDataSetRepositoryMock.ClearReceivedCalls();

        terminalDataSetService = new TerminalDataSetService(terminalDataSetRepositoryMock, acquirerProviderMock, loggerMock, mapperMock, counterpartyProvider);
        counterPartyProvider = counterpartyProvider.GetCode();
    }

    [Test]
    public async Task AdvancedSearchAsync_WhenRepositoryReturnsData_ShouldReturnData()
    {
        var request = new TerminalDataSetSearchRequest() { Skip = 0, Take = 1 };

        var searchResultMock = new SearchResponse<TerminalDataSetSearchResponse>()
        {
            TotalRecordCount = 1,
            ReturnedRecordCount = 1,
            Records = new List<TerminalDataSetSearchResponse>()
                {
                    new TerminalDataSetSearchResponse()
                    {
                        Id = Guid.NewGuid(),
                        Availability = "test"
                    }
                }
        };

        terminalDataSetRepositoryMock.AdvancedSearchAsync(Arg.Any<TerminalDataSetSearchRequest>()).Returns(searchResultMock);

        var result = await terminalDataSetService.AdvancedSearchAsync(request);

        result.Should().BeEquivalentTo(searchResultMock);
        await terminalDataSetRepositoryMock.Received(1).AdvancedSearchAsync(request);
    }

    [Test]
    public async Task AdvancedSearchAsync_WhenRepositoryThrowsException_ShouldThrowException()
    {
        var request = new TerminalDataSetSearchRequest() { Skip = 0, Take = 1 };
        terminalDataSetRepositoryMock.AdvancedSearchAsync(Arg.Any<TerminalDataSetSearchRequest>()).Throws(new Exception("Ex message"));

        Assert.ThrowsAsync<Exception>(() => terminalDataSetRepositoryMock.AdvancedSearchAsync(request));
        await terminalDataSetRepositoryMock.Received(1).AdvancedSearchAsync(request);
    }

    [Test]
    public async Task CreateAsync_WhenRepositoryReturnsData_ShouldThrowException()
    {
        var createRequest = new List<TerminalDataSet>()
        {
                new TerminalDataSet()
                {
                    AcquiringLedger="test",
                    Availability="test",
                    FullTid = "***********",
                    Tid = "111111",
                    Mid = "test",
                    Trsm="test",
                }
            };

        terminalDataSetRepositoryMock.CreateAsync(Arg.Any<List<TerminalDataSet>>()).Throws(new Exception("Ex message"));

        Assert.ThrowsAsync<Exception>(() => terminalDataSetRepositoryMock.CreateAsync(createRequest));
        await terminalDataSetRepositoryMock.Received(1).CreateAsync(createRequest);
    }

    [Test]
    public async Task CreateAsync_WhenRepositoryReturnsData_ShouldReturnData()
    {
        var createRequest = new List<TerminalDataSet>() {
                new TerminalDataSet()
                {
                    AcquiringLedger="test",
                    Availability="test",
                    FullTid = "***********",
                    Tid = "111111",
                    Mid = "test",
                    Trsm = "test",
                }
            };

        terminalDataSetRepositoryMock.CreateAsync(Arg.Any<List<TerminalDataSet>>()).Returns(Task.FromResult(repositoryResponse));

        var result = await terminalDataSetService.CreateAsync(createRequest);
        result.Should().BeEquivalentTo(repositoryResponse);

        await terminalDataSetRepositoryMock.Received(1).CreateAsync(createRequest);
    }

    [Test]
    public async Task PatchAsync_WhenRepositoryReturnsData_ShouldReturnData()
    {
        const string jsonData = "{\"MID\":\"123456789012345\"}";
        var instanceId = Guid.NewGuid();
        var patchDocument = new JsonPatchDocument<TerminalDataSet>();
        patchDocument.Replace(x => x.Mid, jsonData);

        var patchRequest = new List<TerminalDataSetPatchRequest>()
            {
                new TerminalDataSetPatchRequest()
                {
                    TerminalDataSetId = instanceId,
                    TerminalDataSetPatchDocument = patchDocument
                }
            };

        var repositoryPatchResponse = new TerminalDataSet()
        {
            AcquiringLedger = "test",
            Availability = "test",
            FullTid = "***********",
            Tid = "111111",
            Mid = "123456789012345",
            Trsm = "test",
        };

        var repositoryResponseEntity = TestsHelper.repositoryResponseEntity;

        terminalDataSetRepositoryMock.GetTerminalDataByIdAsync(Arg.Any<Guid>())
                                        .Returns(repositoryResponseEntity);
        terminalDataSetRepositoryMock.PatchAsync(Arg.Any<List<TerminalDataSetPatchRequest>>())
                                        .Returns(new List<TerminalDataSet> { repositoryPatchResponse });

        var result = await terminalDataSetService.PatchAsync(patchRequest);

        Assert.AreEqual(123456789012345, long.Parse(result[0].Mid!));
    }
    [Test]
    public async Task PatchAsync_WhenRepositoryReturnsData_ShouldThrowError()
    {
        const string jsonData = "{\"MID\":\"123456789012345\"}";
        var instanceId = Guid.NewGuid();
        var patchDocument = new JsonPatchDocument<TerminalDataSet>();
        patchDocument.Replace(x => x.Mid, jsonData);

        var patchRequest = new List<TerminalDataSetPatchRequest>()
            {
                new()
                {
                    TerminalDataSetId = instanceId,
                    TerminalDataSetPatchDocument = patchDocument
                }
            };

        var repositoryResponseEntity = TestsHelper.repositoryResponseEntity;

        terminalDataSetRepositoryMock.GetTerminalDataByIdAsync(Arg.Any<Guid>())
                                        .Returns(repositoryResponseEntity);
        terminalDataSetRepositoryMock.PatchAsync(Arg.Any<List<TerminalDataSetPatchRequest>>())
                                        .Throws(new Exception("Ex message"));

        Assert.ThrowsAsync<Exception>(() => terminalDataSetRepositoryMock.PatchAsync(patchRequest));
        await terminalDataSetRepositoryMock.Received(1).PatchAsync(patchRequest);
    }

    [Test]
    public async Task GetAvailableTerminalDataSetCountAsync_ShouldReturn_Successfully()
    {
        var acquiringLedger = "test";

        var localRepositoryResponse = 1;

        terminalDataSetRepositoryMock.GetAvailableTerminalDataSetCountAsync(Arg.Any<string>()).Returns(localRepositoryResponse);

        var result = await terminalDataSetService.GetAvailableTerminalDataSetCountAsync(acquiringLedger);

        Assert.AreEqual(result.ValidTerminalDataSetCount, localRepositoryResponse);
    }
    [Test]
    public async Task GetAvailableTerminalDataSetCountAsync_ShouldThrowException()
    {
        var acquiringLedger = "";

        await terminalDataSetService
            .Invoking(async x => await x.GetAvailableTerminalDataSetCountAsync(acquiringLedger))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                x.ProblemDetails.Type == Errors.GetAvailableTerminalDataSetCountBadRequest.Code);
    }

    [Test]
    public async Task GetValidationRequiredFieldsAsync_WhenRepositoryThrowException_ShouldThrowException()
    {
        var request = new List<TerminalDataSet>();
        terminalDataSetRepositoryMock.GetValidationRequiredFieldsAsync(Arg.Any<List<TerminalDataSet>>()).Throws(new Exception("Exception message"));

        Assert.ThrowsAsync<Exception>(() => terminalDataSetRepositoryMock.GetValidationRequiredFieldsAsync(request));
        await terminalDataSetRepositoryMock.Received(1).GetValidationRequiredFieldsAsync(request);
    }

    [Test]
    public async Task GetValidationRequiredFieldsAsync_WhenRepositoryReturnsData_ShouldReturnData()
    {
        var request = new List<TerminalDataSet>()
        {
            new TerminalDataSet()
            {
                AcquiringLedger = "test",
                Availability = "test",
                FullTid = "***********",
                Tid = "111111",
                Mid = "test",
                Trsm = "test",
            }
        };

        terminalDataSetRepositoryMock.GetValidationRequiredFieldsAsync(Arg.Any<List<TerminalDataSet>>()).Returns(Task.FromResult(repositoryRequiredFieldsResponse));

        var result = await terminalDataSetService.GetValidationRequiredFieldsAsync(request);
        result.Should().BeEquivalentTo(repositoryRequiredFieldsResponse);

        await terminalDataSetRepositoryMock.Received(1).GetValidationRequiredFieldsAsync(request);
    }

    [Test]
    public async Task GetOrderMigrationValidationRequiredFieldsAsync_WhenRepositoryThrowException_ShouldThrowException()
    {
        var request = new List<TerminalDataSet>();
        terminalDataSetRepositoryMock.GetOrderMigrationValidationRequiredFieldsAsync(Arg.Any<List<TerminalDataSet>>()).Throws(new Exception("Exception message"));

        Assert.ThrowsAsync<Exception>(() => terminalDataSetRepositoryMock.GetOrderMigrationValidationRequiredFieldsAsync(request));
        await terminalDataSetRepositoryMock.Received(1).GetOrderMigrationValidationRequiredFieldsAsync(request);
    }

    [Test]
    public async Task GetOrderMigrationValidationRequiredFieldsAsync_WhenRepositoryReturnsData_ShouldReturnData()
    {
        var request = new List<TerminalDataSet>()
        {
            new TerminalDataSet()
            {
                AcquiringLedger = "test",
                Availability = "test",
                FullTid = "***********",
                Tid = "111111",
                Mid = "test",
                Trsm = "test",
            }
        };

        terminalDataSetRepositoryMock.GetOrderMigrationValidationRequiredFieldsAsync(Arg.Any<List<TerminalDataSet>>()).Returns(Task.FromResult(repositoryResponse));

        var result = await terminalDataSetService.GetOrderMigrationValidationRequiredFieldsAsync(request);
        result.Should().BeEquivalentTo(repositoryResponse);

        await terminalDataSetRepositoryMock.Received(1).GetOrderMigrationValidationRequiredFieldsAsync(request);
    }

    [Test]
    public async Task GetTerminalDataByIdAsync_ShouldReturnData()
    {
        var terminalDataSetId = Guid.NewGuid();

        var terminalDataSetEntity = new TerminalDataSetEntity()
        {
            Id = terminalDataSetId,
            Counterparty = "GEIDEA_SAUDI",
            TID = "123",
            MID = "123",
            FullTID = "123",
            AcquiringLedger = "DEFAULT_BANK",
            Availability = Constants.TerminalDataSetAvailability.Available
        };

        var terminalDataSetSearchResponse = new TerminalDataSetSearchResponse()
        {
            Id = terminalDataSetId,
            Counterparty = "GEIDEA_SAUDI",
            Tid = "123",
            Mid = "123",
            FullTid = "123",
            AcquiringLedger = "DEFAULT_BANK",
            Availability = Constants.TerminalDataSetAvailability.Available
        };

        terminalDataSetRepositoryMock.GetTerminalDataByIdAsync(Arg.Any<Guid>())
            .Returns(Task.FromResult(terminalDataSetEntity));
        mapperMock.Map<TerminalDataSetSearchResponse>(Arg.Any<TerminalDataSetEntity>())
            .Returns(terminalDataSetSearchResponse);

        var result = await terminalDataSetService.GetTerminalDataByIdAsync(terminalDataSetId);
        result.Id.Should().Be(terminalDataSetId);

        await terminalDataSetRepositoryMock.Received(1).GetTerminalDataByIdAsync(terminalDataSetId);
    }

    [Test]
    public async Task GetTerminalDataByProductInstanceId_ShouldReturnCorrect()
    {
        var terminalDataSetResponse = new List<TerminalDataSet>
        {
            new() {
            AcquiringLedger = "test",
            Availability = "test",
            ConfigDate = DateTime.UtcNow,
            FullTid = "***********",
            Mid = "123456789012345",
            OrderNumber = "test",
            ProductInstanceId = Guid.NewGuid(),
            Tid = "111111",
            Trsm = "test"
            }
        };

        var instanceIds = new Guid[] { Guid.NewGuid() };

        terminalDataSetRepositoryMock.GetTerminalDataByProductInstanceId(Arg.Any<Guid[]>())
            .Returns(Task.FromResult(terminalDataSetResponse));

        var result = await terminalDataSetService.GetTerminalDataByProductInstanceId(instanceIds);
        result.Should().BeEquivalentTo(terminalDataSetResponse);

        await terminalDataSetRepositoryMock.Received(1).GetTerminalDataByProductInstanceId(instanceIds);

    }

    [TestCase(AcquiringLedger.NBEBank)]
    public async Task AddOrUpdateTerminalDataSets_ShouldReturnCorrect(string acquirer)
    {
        var terminalDataSetRequest = new TerminalDataSetsRequest
        {
            OrderNumber = "test",
            ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.NewGuid(), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
            AcquiringLedger = acquirer
        };

        var acquirerProvider = MockAcquirer(acquirer);

        await terminalDataSetService.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSetRequest);

        await terminalDataSetRepositoryMock.Received(1)
            .GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSetRequest, acquirerProvider);
    }

    [TestCase(AcquiringLedger.NBEBank)]
    public async Task AddOrUpdateNBEOrderTerminalDataSets_ShouldReturnCorrect(string acquirer)
    {
        var terminalDataSetRequest = new TerminalDataSetsMidTidRequest
        {
            TerminalDataSets = new List<TerminalDataSet> { new TerminalDataSet { AcquiringLedger = acquirer } },
            StoresIds = new List<Guid>(),
        };

        var acquirerProvider = MockAcquirer(acquirer);

        await terminalDataSetService.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(terminalDataSetRequest);

        await terminalDataSetRepositoryMock.Received(1).GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(terminalDataSetRequest, acquirerProvider);
    }

    [TestCase(AcquiringLedger.NBEBank)]
    [TestCase(AcquiringLedger.ALXBank)]

    public async Task GenerateTIDAndAddEditNBETerminalDataSet_ShouldReturnCorrect(string acquirer)
    {
        var terminalDataSetRes = new TerminalDataSetsResponse
        {
            TId = "tid",
            FullTId = "FullTId",
            Trsm = "Trsm"
        };
        var terminalDataSet = new TerminalDataSet
        {
            OrderNumber = "test",
            ProductInstanceId = Guid.NewGuid(),
            AcquiringLedger = acquirer
        };

        var acquirerProvider = MockAcquirer(acquirer);

        terminalDataSetRepositoryMock.GenerateTIDAndAddEditTerminalDataSet(Arg.Any<TerminalDataSet>(), acquirerProvider)
            .Returns(Task.FromResult(terminalDataSetRes));

        var result = await terminalDataSetService.GenerateTIDAndAddEditTerminalDataSet(terminalDataSet);

        result.Should().BeEquivalentTo(terminalDataSetRes);

        await terminalDataSetRepositoryMock.Received(1).GenerateTIDAndAddEditTerminalDataSet(terminalDataSet, acquirerProvider);
    }

    [TestCase(AcquiringLedger.NBEBank)]
    public async Task GenerateMIDAndAddEditTerminalDataSet_ShouldReturnCorrect(string acquirer)
    {
        var mid = "mid";
        var terminalDataSet = new TerminalDataSet
        {
            OrderNumber = "test",
            ProductInstanceId = Guid.NewGuid(),
            AcquiringLedger = acquirer
        };

        TerminalDataSetMidRequest terminalDataSetMid = new TerminalDataSetMidRequest
        {
            TerminalDataSet = terminalDataSet,
            StoresIds = new List<Guid>()
        };

        var acquirerProvider = MockAcquirer(acquirer);

        terminalDataSetRepositoryMock.GenerateMIDAndAddEditTerminalDataSet(Arg.Any<TerminalDataSetMidRequest>(), acquirerProvider)
            .Returns(Task.FromResult(mid));

        var result = await terminalDataSetService.GenerateMIDAndAddEditTerminalDataSet(terminalDataSetMid);

        result.Should().BeEquivalentTo(mid);

        await terminalDataSetRepositoryMock.Received(1).GenerateMIDAndAddEditTerminalDataSet(terminalDataSetMid, acquirerProvider);
    }

    [Test]
    public async Task AddUpdateTerminalDataSetByMcc_ShouldReturnCorrect()
    {
        var terminalDataSetRequest = new TerminalDataSetsMidTidRequest
        {
            TerminalDataSets = new List<TerminalDataSet>(),
            StoresIds = new List<Guid>()
        };

        await terminalDataSetService.AddUpdateTerminalDataSetMcc(terminalDataSetRequest);

        await terminalDataSetRepositoryMock.Received(1).AddUpdateTerminalDataSetMcc(terminalDataSetRequest);
    }

    [Test]
    public async Task GenerateNewTID_ShouldRecieveOneCall()
    {
        await terminalDataSetService.GetNewTIDSequence();

        await terminalDataSetRepositoryMock.Received(1).GetTerminalIdSequenceAsync();
    }

    private IAcquirer MockAcquirer(string acquirer)
    {

        var provider = Substitute.For<IAcquirer>();
        if (acquirer == AcquiringLedger.NBEBank)
            provider = Substitute.For<NbeAcquirer>();

        else if (acquirer == AcquiringLedger.ALXBank)
            provider = Substitute.For<NbeAcquirer>();

        acquirerProviderMock(acquirer).Returns(provider);
        return provider;

    }

}
