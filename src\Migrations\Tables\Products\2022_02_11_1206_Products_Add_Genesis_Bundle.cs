﻿using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.Products;

[Migration(2022_02_11_1500)]
public class AddReferralChannel : ForwardOnlyMigration
{
    public override void Up()
    {
        Alter.Table("Products").AddColumn("ReferralChannel").AsString().WithDefaultValue("UNASSIGNED");
    }
}

[Migration(2022_02_11_1540)]
public class ProductsAddGenesisBundle : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "Products_GenesisSmart_Bundles.sql"));
    }
}
