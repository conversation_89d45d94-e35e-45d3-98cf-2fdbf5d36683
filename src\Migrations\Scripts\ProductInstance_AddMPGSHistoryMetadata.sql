﻿
GO
create or alter function AddMPGSAccountCreationDateAndHistory
(
 @OldMetaData nvarchar(MAX) , @CreationDate Datetime2
)
RETURNS nvarchar(MAX)
    AS
    BEGIN
​
	Declare @UpdatedMetaData Nvarchar(MAX) ;
​
	WITH AddCreatedDataToEachAccountQuery AS (
	    SELECT JSON_MODIFY(t.[value], '$.CreatedDate', convert( nvarchar, @CreationDate) ) UpdatedMpgsAccount
	    FROM OPENJSON((SELECT JSON_QUERY(@OldMetaData, 'lax $.MpgsAccounts'))) t
	), UpdateMpgsAccountsQuery AS (
	    SELECT CONCAT('[',STRING_AGG(UpdatedMpgsAccount, ','),']') UpdatedMpgsAccounts
	    FROM AddCreatedDataToEachAccountQuery
	)
​
	SELECT @UpdatedMetaData = JSON_MODIFY(JSON_MODIFY(@OldMetaData,'$.MpgsHistory', JSON_QUERY(N'[]')), '$.MpgsAccounts', JSON_QUERY(UpdatedMpgsAccounts))
	FROM UpdateMpgsAccountsQuery 
	​
	Return @UpdatedMetaData​​
END



