﻿BEGIN TRANSACTION;

DECLARE @ProductIds TABLE(ID UNIQUEIDENTIFIER)

--Categories
DECLARE @GoFamilyCategoryId UNIQUEIDENTIFIER
SELECT TOP 1 @GoFamilyCategoryId = ID FROM [PRODUCTS].[dbo].[Category] WHERE Counterparty = 'GEIDEA_EGYPT' AND Code = 'GO_FAMILY'
UPDATE [PRODUCTS].[dbo].[Category] SET DeletedFlag = 0 WHERE Id = @GoFamilyCategoryId 

-- <PERSON><PERSON>
DECLARE @GoSmartBundleId UNIQUEIDENTIFIER
INSERT INTO [PRODUCTS].[dbo].[Products](Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'GO_SMART', 'BUNDLE', GETUTCDATE(), '00000000-0000-0000-0000-000000000000', GETUTCDATE(), 1, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @GoSmartBundleId = ID FROM @ProductIds 
DELETE FROM @ProductIds

-- Products
DECLARE @SunmiId UNIQUEIDENTIFIER
SELECT TOP 1 @SunmiId = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_EGYPT' AND Code = 'SUNMI_P2'
UPDATE [PRODUCTS].[dbo].[Products] SET DisplayOrder = 1 WHERE Id = @SunmiId 

DECLARE @GeideaGoAppId UNIQUEIDENTIFIER
SELECT TOP 1 @GeideaGoAppId = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_EGYPT' AND Code = 'GEIDEA_GO_APP'
UPDATE [PRODUCTS].[dbo].[Products] SET DisplayOrder = 2 WHERE Id = @GeideaGoAppId 

-- Card schemes
DECLARE @MeezaScheme UNIQUEIDENTIFIER
SELECT TOP 1 @MeezaScheme = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_EGYPT' AND Code = 'MEEZA'

DECLARE @VisaScheme UNIQUEIDENTIFIER
SELECT TOP 1 @VisaScheme = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_EGYPT' AND Code = 'VISA'

DECLARE @MasterCardScheme UNIQUEIDENTIFIER
SELECT TOP 1 @MasterCardScheme = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_EGYPT' AND Code = 'MC'

-- Associations
INSERT INTO [PRODUCTS].[dbo].[Prices](ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
VALUES('MONTH', 'RECCURRING_CHARGE', 0, @GoSmartBundleId, 0, NULL, NULL, 1, GETUTCDATE(), 0, '00000000-0000-0000-0000-000000000000', GETUTCDATE(), 'EGP', 12, NULL)

INSERT INTO [PRODUCTS].[dbo].[ProductCategories](ProductId, CategoryId) VALUES(@GoSmartBundleId, @GoFamilyCategoryId)

INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@GoSmartBundleId, @SunmiId)
INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@GoSmartBundleId, @GeideaGoAppId)

INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@GoSmartBundleId, @MeezaScheme)
INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@GoSmartBundleId, @VisaScheme)
INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId) VALUES(@GoSmartBundleId, @MasterCardScheme)

COMMIT;