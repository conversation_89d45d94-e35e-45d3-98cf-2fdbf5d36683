﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Common;
using Common.Entities.Gle;
using Common.Repositories.Gle;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DataAccess.Repositories.Gle;

public class GleTerminalRepository : GleBaseRepository<GleTerminalEntity>, IGleTerminalRepository
{
    private readonly ILogger<GleTerminalRepository> logger;

    public GleTerminalRepository(DbContext context,
        IHttpContextAccessor contextAccessor,
        ILogger<GleBaseRepository<GleTerminalEntity>> baseLogger,
        ILogger<GleTerminalRepository> logger)
        : base(context, contextAccessor, baseLogger)
    {
        this.logger = logger;
    }

    public async Task<GleTerminalEntity?> GetGleTerminalByProductInstanceIdAsync(Guid productInstanceId)
    {
        var result = await context.Set<GleTerminalEntity>()
            .AsQueryable()
            .AsNoTracking()
            .SingleOrDefaultAsync(x => x.ProductInstanceId == productInstanceId && !x.IsTerminalUser);

        logger.LogInformation("Returning GLE terminal for product instance ID: {id}", productInstanceId);
        return result;
    }

    public async Task<IReadOnlyCollection<GleTerminalEntity>> GetGleTerminalListByOrderIdAsync(Guid orderId)
    {
        var result = await context.Set<GleTerminalEntity>()
            .AsQueryable()
            .AsNoTracking()
            .Where(x => x.OrderId == orderId)
            .ToListAsync();

        logger.LogInformation("Returning GLE terminal list (with users) for order ID: {id}", orderId);
        return result;
    }

    public async Task<bool?> OrderHasAllInstancesRegisteredToGle(Guid orderId)
    {
        var order = await context.Set<GleTerminalEntity>()
            .AsQueryable()
            .AsNoTracking()
            .Where(x => x.OrderId == orderId)
            .Select(x => new { x.Id, x.GleRegistrationStatus })
            .ToListAsync();

        if (!order.Any())
        {
            return null;
        }

        return order.All(x => x.GleRegistrationStatus == Constants.GleRegistrationStatus.Success);
    }
}