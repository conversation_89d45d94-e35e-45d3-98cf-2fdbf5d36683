﻿using Common.Validators;
using Geidea.Utils.Exceptions;
using GatewayDataModel = Geidea.ProductService.Models.GatewayData;

namespace Common.Data;

public class GatewayData : GatewayDataModel, IData
{
    public void ValidateAndThrow()
    {
        var dataValidationResult = new GatewayDataValidator().Validate(this);
        if (!dataValidationResult.IsValid)
        {
            throw new ValidationException(dataValidationResult);
        }
    }
}
