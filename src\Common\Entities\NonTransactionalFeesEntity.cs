﻿using Common.Enums;
using Geidea.Utils.DataAccess.Entities;
using System;
using System.Collections.Generic;

namespace Common.Entities;
public class NonTransactionalFeesEntity : AuditableEntity<Guid>
{
    public string? Code { get; set; }
    public string? Name { get; set; }
    public string? NameAr { get; set; }
    public Status Status { get; set; }
    public ICollection<NonTransactionalPriceEntity>? NonTransactionalPrice { get; set; }

}