﻿using Common.Entities;
using Common.Models.ValueAddedServices;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Services;
public interface IValueAddedServiceService
{
    Task<ValueAddedServiceDetailsResponse> GetValueAddedServicesDetailsAsync(Guid id);
    Task<ValueAddedServicesListResponse> GetValueAddedServicesListAsync(GetValueAddedServicesListRequest request);
    Task<ValueAddedServiceResponse> CreateAsync(ValueAddedServiceRequest valueAddedServiceRequest);
    Task<ValueAddedServiceResponse> UpdateAsync(Guid id, ValueAddedServiceRequest valueAddedServiceRequest);
    Task<bool> SetStatusAsync(Guid id, bool isActive);
    Task<List<BasicValueAddedServiceInfo>> GetValueAddedServiceNamesAsync();
}
