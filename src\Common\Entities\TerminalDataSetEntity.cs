﻿using Common.Attributes;
using Geidea.Utils.DataAccess.Entities;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Common.Entities;

public class TerminalDataSetEntity : AuditableEntity<Guid>
{
    [Queryable(canSearch: true, canOrder: true)]
    public string? AcquiringLedger { get; set; } = string.Empty;

    [Queryable(canSearch: true, canOrder: true)]
    public string? MID { get; set; }

    [Queryable(canSearch: true, canOrder: true)]
    public string? TID { get; set; } = string.Empty;

    [Queryable(canSearch: true, canOrder: true)]
    public string? FullTID { get; set; } = string.Empty!;

    [Queryable(canSearch: true, canOrder: true)]
    public string? TRSM { get; set; }

    [Queryable(canSearch: true, canOrder: true)]
    public string? MCC { get; set; } = string.Empty;

    [Queryable(canSearch: true, canOrder: true)]
    public string? OrderNumber { get; set; }

    [Queryable(canSearch: true, canOrder: true)]
    public DateTime? ConfigDate { get; set; }

    [Queryable(canSearch: true, canOrder: true)]
    public string Availability { get; set; } = string.Empty;

    [Queryable(canSearch: true, canOrder: true)]
    public string? Counterparty { get; set; }

    [Queryable(canSearch: false, canOrder: false)]
    public Guid? StoreId { get; set; }

    [Queryable(canSearch: true, canOrder: false)]
    public Guid? ProductInstanceId { get; set; }

    [Queryable(canSearch: true, canOrder: true)]
    public DateTime? ExtractionDate { get; set; }

    [Queryable(canSearch: true, canOrder: false)]
    public bool Extracted { get; set; }

    [Queryable(canSearch: false, canOrder: false)]
    public string? CountryPrefix { get; set; }

    [Queryable(canSearch: false, canOrder: false)]
    public string? PhoneNumber { get; set; }

    [Queryable(canSearch: false, canOrder: false)]
    public string? ChannelType { get; set; }

    [Queryable(canSearch: false, canOrder: false)]
    public string? ConnectionType { get; set; }

    [Queryable(canSearch: false, canOrder: false)]
    public string? MPGSMID { get; set; }

    [Queryable(canSearch: false, canOrder: false)]
    public string? MPGSKEY { get; set; }

    [Queryable(canSearch: false, canOrder: false)]
    public Guid? VendorId { get; set; }

    public VendorEntity? Vendor { get; set; }
}
