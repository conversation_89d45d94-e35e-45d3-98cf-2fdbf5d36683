﻿using AutoMapper;
using Common;
using Common.Entities;
using Common.Models;
using Common.Models.CommissionFees;
using Common.Models.ValueAddedServices;
using Common.Repositories;
using Common.Services;
using Common.Validators;
using DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Services;
public class CommissionFeesService : ICommissionFeesService
{
    #region Fields
    private readonly ILogger<CommissionFeesService> logger;
    private readonly ICommissionFeesRepository commissionFeesRepository;
    private readonly IMapper mapper;
    #endregion

    #region Constructor
    public CommissionFeesService(ILogger<CommissionFeesService> logger, ICommissionFeesRepository commissionFeesRepository, IMapper mapper)
    {
        this.logger = logger;
        this.commissionFeesRepository = commissionFeesRepository;
        this.mapper = mapper;
    }
    #endregion

    #region Methods

    #region CreateAsync
    public async Task<CommissionFeesResponse> CreateAsync(CommissionFeesRequest commissionFeesRequest)
    {

        var codeExists = await commissionFeesRepository.ExistsAsync(v => v.Code == commissionFeesRequest.Code);
        var commissionFees = mapper.Map<CommissionFeesEntity>(commissionFeesRequest);
        if (codeExists)
        {
            logger.LogError("CreateAsync: Invalid commissionFees Code '{Code}' already exists", commissionFeesRequest.Code);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.CodeAlreadyExist);
        }
        try
        {
            commissionFeesRepository.Add(commissionFees);
            await commissionFeesRepository.SaveChangesAsync();

            logger.LogInformation("Added commission Fees with id '{commissionFeesId}' and Name '{commissionFeesName}'.", commissionFees.Id, commissionFees.Name);
            return mapper.Map<CommissionFeesResponse>(commissionFees);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating commission fees with Name '{Name}'.", commissionFeesRequest.Name);
            throw new ServiceException(HttpStatusCode.BadRequest, $"Error creating commission fees: {ex.Message}");
        }
    }
    #endregion

    #region UpdateAsync
    public async Task<CommissionFeesResponse> UpdateAsync(Guid id, CommissionFeesRequest commissionFeesRequest)
    {

        var existingCommissionFees = await commissionFeesRepository.GetByIdAsync(id);

        if (existingCommissionFees == null)
        {
            logger.LogWarning("Commission Fees with id '{id}' not found.", id);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductNotFound);
        }

        if (existingCommissionFees.Code != commissionFeesRequest.Code &&
         await commissionFeesRepository.AnyAsync(x => x.Code == commissionFeesRequest.Code))
        {
            logger.LogError("A Commission Fees with code '{Code}' already exists.", commissionFeesRequest.Code);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.CodeAlreadyExist);
        }

        mapper.Map(commissionFeesRequest, existingCommissionFees);

        commissionFeesRepository.Update(existingCommissionFees);
        await commissionFeesRepository.SaveChangesAsync();

        logger.LogInformation("Updated Commission Fees with id '{CommissionFeesId}' and Code '{CommissionFeesCode}'.", existingCommissionFees.Id, existingCommissionFees.Code);

        return mapper.Map<CommissionFeesResponse>(existingCommissionFees);

    }
    #endregion

    #region GetCommissionFeesList
    public async Task<GetCommissionFeesListResponse> GetCommissionFeesList(GetCommissionFeesListRequest request)
    {
        if (request == null)
        {
            logger.LogError("Invalid Get Commission Fees List request.");
            throw new ServiceException(HttpStatusCode.BadRequest, "Invalid Get Commission Fees List request.");
        }
        try
        {
            return await commissionFeesRepository.GetCommissionFeesList(request);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "something has went wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, "something has went wrong!");
        }
    }

    #endregion

    #region ToggleStatus
    public async Task<bool> ToggleStatus(Guid CommissionFeeId, bool Status)
    {
        var result = false;
        if (CommissionFeeId == Guid.Empty)
        {
            logger.LogError("Invalid CommissionFee Id.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidIdentifier);
        }

        var CommissionFeeObj = await commissionFeesRepository.GetByIdAsync(CommissionFeeId);
        if (CommissionFeeObj == null)
            return result;

        CommissionFeeObj.Status = Status ? Common.Enums.Status.Active : Common.Enums.Status.Inactive;
        commissionFeesRepository.Update(CommissionFeeObj);
        await commissionFeesRepository.SaveChangesAsync();
        return true;
    }
    #endregion

    #region GetCommissionFeeDetails
    public async Task<CommissionFeeDetailsResponse> GetCommissionFeeDetails(Guid Id)
    {
        if (Id == Guid.Empty)
        {
            logger.LogError("Invalid CommissionFee Id.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidIdentifier);
        }

        var CommissionFeeObj = await commissionFeesRepository.GetByIdAsync(Id);
        if (CommissionFeeObj == null)
        {
            logger.LogError("Commission fee with Id '{Id}' not found.", Id);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.InvalidCommissionFeeId);
        }

        return mapper.Map<CommissionFeeDetailsResponse>(CommissionFeeObj);
    }
    #endregion

    public async Task<List<CommissionFees>> GetCommissionFees()
    {
        try
        {
            return await commissionFeesRepository.GetCommissionFees();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "GetCommissionFees: something has went wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, "something has went wrong!");
        }
    }
    #endregion
}