﻿ DECLARE @ProductIds TABLE(ID UNIQUEIDENTIFIER)
 DECLARE @GoFamilyCategoryId UNIQUEIDENTIFIER
 DECLARE @Pax UNIQUEIDENTIFIER
 DECLARE @Genesis_Smart UNIQUEIDENTIFIER
 DECLARE @VISA_GENESIS UNIQUEIDENTIFIER
 DECLARE @MC_GENESIS UNIQUEIDENTIFIER
 DECLARE @MADA_GENESIS UNIQUEIDENTIFIER

 DECLARE @SCHEME VARCHAR(10) SET @SCHEME = 'SCHEME'
 DECLARE @ChargeType VARCHAR(20) SET @ChargeType = 'PURCHASE_CP'
 DECLARE @CounterParty VARCHAR(20) SET @CounterParty = 'GEIDEA_SAUDI' 
 DECLARE @ChargeFrequency VARCHAR(10) SET @ChargeFrequency = 'ONE_OFF'
 DECLARE @Flow VARCHAR(10) SET @Flow= 'Normal'


 SELECT TOP 1 @GoFamilyCategoryId = ID FROM [PRODUCTS].[dbo].[Category] where  Code = 'GO_FAMILY' and CounterParty = @CounterParty
 SELECT TOP 1 @Pax = ID FROM [PRODUCTS].[dbo].[Products] where  Code = 'PAX_A920'

 --add visa scheme
 INSERT INTO [PRODUCTS].[dbo].[Products](Availability, Code, Type, Description, CreatedBy, CreatedDate, ValidFrom, [Version], Counterparty, Flow) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Live', 'VISA_GENESIS', @SCHEME, 'VISA_GENESIS', 'n/a', GETUTCDATE(), GETUTCDATE(),  0, @CounterParty, @Flow)
 
 SELECT TOP 1 @VISA_GENESIS = ID from @ProductIds
 DELETE FROM @ProductIds
 
 INSERT INTO [PRODUCTS].[dbo].[Prices] ([ChargeFrequency],[ChargeType],[ExemptFromVAT],[ProductId],[PercentagePrice],[Priority],[ValidFrom],[DeletedFlag],[CreatedBy],[CreatedDate],[UpdatedBy],[Currency])
 VALUES (@ChargeFrequency, @ChargeType, 0, @VISA_GENESIS, 250, 1, GETUTCDATE(), 0, '00000000-0000-0000-0000-000000000000', GETUTCDATE(), '00000000-0000-0000-0000-000000000000', 'SAR')

 --add mc scheme
 INSERT INTO [PRODUCTS].[dbo].[Products](Availability, Code, Type, Description, CreatedBy, CreatedDate, ValidFrom,  [Version], Counterparty, Flow) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Live','MC_GENESIS',@SCHEME, 'MC_GENESIS','n/a', GETUTCDATE(),GETUTCDATE(), 0, @CounterParty, @Flow)

 SELECT TOP 1 @MC_GENESIS = ID from @ProductIds
 DELETE FROM @ProductIds
 
 INSERT INTO [PRODUCTS].[dbo].[Prices] ([ChargeFrequency],[ChargeType],[ExemptFromVAT],[ProductId],[PercentagePrice],[Priority],[ValidFrom],[DeletedFlag],[CreatedBy],[CreatedDate],[UpdatedBy],[Currency])
 VALUES (@ChargeFrequency, @ChargeType, 0, @MC_GENESIS, 250, 1, GETUTCDATE(), 0, '00000000-0000-0000-0000-000000000000', GETUTCDATE(), '00000000-0000-0000-0000-000000000000', 'SAR')

 --add mada scheme
 INSERT INTO [PRODUCTS].[dbo].[Products](Availability, Code, Type, Description, CreatedBy, CreatedDate, ValidFrom, [Version], Counterparty, Flow) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Live','MADA_GENESIS',@SCHEME, 'MADA_GENESIS','n/a', GETUTCDATE(),GETUTCDATE(), 0, @CounterParty,@Flow)
 
 SELECT TOP 1 @MADA_GENESIS = ID from @ProductIds
 DELETE FROM @ProductIds
 
 INSERT INTO [PRODUCTS].[dbo].[Prices] ([ChargeFrequency],[ChargeType],[ExemptFromVAT],[ProductId],[PercentagePrice],[Priority],[ValidFrom],[DeletedFlag],[CreatedBy],[CreatedDate],[UpdatedBy],[Currency])
 VALUES (@ChargeFrequency, @ChargeType, 0, @MADA_GENESIS, 70, 1, GETUTCDATE(), 0, '00000000-0000-0000-0000-000000000000', GETUTCDATE(), '00000000-0000-0000-0000-000000000000', 'SAR')
 
 --Genesis smart
 INSERT INTO [PRODUCTS].[dbo].[Products](Availability, Code, Type, Description, CreatedBy, CreatedDate, ValidFrom, DisplayOrder, [Version], Counterparty, Flow, SalesChannel, ReferralChannel) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Live', 'GENESIS_SMART', 'BUNDLE', 'GENESIS_SMART','n/a', GETUTCDATE(), GETUTCDATE(), 1, 0, @CounterParty, @Flow, 'Shop', 'SABB_GENESIS')
 
 SELECT TOP 1 @Genesis_Smart = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO [PRODUCTS].[dbo].[ProductCategories](ProductId, CategoryId) VALUES(@Genesis_Smart, @GoFamilyCategoryId)

INSERT INTO [PRODUCTS].[dbo].[Prices] ([ChargeFrequency],[ChargeType],[ExemptFromVAT],[ProductId],[PerItemPrice],[Priority],[ValidFrom],[DeletedFlag],[CreatedBy],[CreatedDate],[UpdatedBy],[Currency])
     VALUES (@ChargeFrequency, 'RETAIL_PRICE' ,0 ,@Genesis_Smart,0 ,1 ,GETUTCDATE() ,0 ,'00000000-0000-0000-0000-000000000000',GETUTCDATE(),'00000000-0000-0000-0000-000000000000','SAR')
 
 --add part for bundle
 INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId, Quantity) VALUES(@Genesis_Smart, @Pax, 1)
 INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId, Quantity) VALUES(@Genesis_Smart, @VISA_GENESIS, 1)
 INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId, Quantity) VALUES(@Genesis_Smart, @MC_GENESIS, 1)
 INSERT INTO [PRODUCTS].[dbo].[ProductParts](ProductId, PartId, Quantity) VALUES(@Genesis_Smart, @MADA_GENESIS, 1)

 --add Sarie fee
 INSERT INTO [PRODUCTS].[dbo].[Prices] ([ChargeType],[ExemptFromVAT],[ProductId],[PerItemPrice],[Priority],[ValidFrom],[DeletedFlag],[CreatedBy],[CreatedDate],[UpdatedBy],[Currency])
 VALUES ('SARIE_CHARGE', 0, @Genesis_Smart, 0, 1, GETUTCDATE(), 0, '00000000-0000-0000-0000-000000000000', GETUTCDATE(), '00000000-0000-0000-0000-000000000000', 'SAR')