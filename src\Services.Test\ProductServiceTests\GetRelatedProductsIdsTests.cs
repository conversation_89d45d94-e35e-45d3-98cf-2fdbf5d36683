﻿using AutoMapper;
using Common.Models;
using Common.Repositories;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using Xunit;

namespace Services.Test.ProductServiceTests;

public class GetRelatedProductsIdsTests
{
    private readonly Mock<ILogger<ProductService>> logger = new Mock<ILogger<ProductService>>();
    private readonly Mock<IProductInstanceRepository> productInstanceRepository = new Mock<IProductInstanceRepository>();
    private readonly Mock<IProductRepository> productRepository = new Mock<IProductRepository>();
    private readonly Mock<ICategoryRepository> categoryRepository = new Mock<ICategoryRepository>();
    private readonly Mock<IMapper> mapper = new Mock<IMapper>();
    private readonly ProductService productService;

    public GetRelatedProductsIdsTests()
    {
        productService = new ProductService(logger.Object, productRepository.Object, mapper.Object, categoryRepository.Object, productInstanceRepository.Object);
    }

    [Fact]
    public void GetRelatedProductsIds_CallsProductRepository()
    {
        var productCodesRequest = new ProductCodesRequest() { ProductCodes = new[] { "GO_AIR", "GO_SMART" } };
        productRepository.Setup(x => x.GetRelatedProductsIds(productCodesRequest)).Returns(new Guid[] { Guid.NewGuid() });

        var productIds = productService.GetRelatedProductsIds(productCodesRequest);

        productIds.Length.Should().Be(1);
        productRepository.Verify(x => x.GetRelatedProductsIds(productCodesRequest), Times.Once());
    }


    [Fact]
    public void GetRelatedProductsIds_WhenRepositoryThrowsException_ThrowsException()
    {
        var productCodesRequest = new ProductCodesRequest() { ProductCodes = new[] { "GO_AIR" } };
        productRepository.Setup(x => x.GetRelatedProductsIds(productCodesRequest)).Throws(new Exception());

        productService.Invoking(x => x.GetRelatedProductsIds(productCodesRequest))
            .Should().Throw<Exception>();

        productRepository.Verify(x => x.GetRelatedProductsIds(productCodesRequest), Times.Once());
    }
}
