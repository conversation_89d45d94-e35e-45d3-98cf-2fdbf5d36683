﻿using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.Products;

[Migration(2022_03_18_1000)]
public class Prices_UpdateGoSmartBundlePriceAndPeriod : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scrip<PERSON>", "PriceUpdates_Egypt_GoSmartBundle.sql"));
    }
}
