﻿using System;
using System.Threading.Tasks;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ProductService.Controllers;

[Route("api/v1/[controller]")]
[ApiController]
public class MetaDataMigrationFilesController : ControllerBase
{
    private readonly IMetaDataMigrationService metaDataMigrationService;
    private readonly ILogger<MetaDataMigrationFilesController> logger;

    public MetaDataMigrationFilesController(IMetaDataMigrationService metaDataMigrationService, ILogger<MetaDataMigrationFilesController> logger)
    {
        this.metaDataMigrationService = metaDataMigrationService;
        this.logger = logger;
    }

    [HttpPost("UpdateMetaDataMigrations")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [Produces("application/json")]
    public async Task<IActionResult> UpdateMetaDataMigrations()
    {
        try
        {
            logger.LogInformation("Start UpdateMetaDataMigrations");

            await metaDataMigrationService.MigrateMetaDataScripts();

            logger.LogInformation("UpdateMetaDataMigrations finished successfully");
            return Ok("Migration successful");
        }
        catch (Exception ex)
        {
            logger.LogError("Error happened in UpdateMetaDataMigrations ,\n {message}", ex.Message);
            return StatusCode(500, $"Migration failed: {ex.Message}");
        }
    }
}