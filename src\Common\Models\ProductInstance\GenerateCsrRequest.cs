﻿using System;
using Common.Validators;
using Geidea.Utils.Exceptions;

namespace Common.Models.ProductInstance;

public class GenerateCsrRequest
{
    public Guid ProductInstanceId { get; set; }
    public string CommonName { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Organization { get; set; } = string.Empty;
    public string OrganizationalUnit { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;

    public void ValidateAndThrow()
    {
        var dataValidationResult = new GenerateCsrValidator().Validate(this);
        if (!dataValidationResult.IsValid)
        {
            throw new ValidationException(dataValidationResult);
        }
    }
}
