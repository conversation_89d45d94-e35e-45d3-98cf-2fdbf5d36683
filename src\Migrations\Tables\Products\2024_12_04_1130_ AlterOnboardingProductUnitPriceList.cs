﻿using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.Products;
[Migration(2024_12_04_1130)]
public class AlterOnboardingProductUnitPriceList : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "OnboardingUnitPriceList.sql"));
    }
}
[Migration(2025_01_09_6006)]
public class AlterOnboardingProductUnitPriceList_AddDisplayOrder : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "OnboardingUnitPriceList.sql"));
    }
}
[Migration(2025_01_28_1331)]
public class AlterOnboardingProductUnitPriceList_AddFilterByProductType : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "OnboardingUnitPriceList.sql"));
    }
}
