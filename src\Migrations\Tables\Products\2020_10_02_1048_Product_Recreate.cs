﻿using Common;
using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2020_10_02_1048)]
public class Product_Recreate : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("Products")
          .WithColumn("Id").AsGuid().NotNullable().PrimaryKey().WithDefault(SystemMethods.NewGuid)
          .WithColumn("Availability").AsString(255).NotNullable().WithDefaultValue(Constants.Availability.Live)
          .WithColumn("Code").AsString(255).NotNullable()
          .WithColumn("Type").AsString(255).NotNullable()
          .WithColumn("Description").AsString(int.MaxValue).Nullable()
          .WithColumn("ValidFrom").AsDateTime2().Nullable()
          .WithColumn("ValidTo").AsDateTime2().Nullable()
          .WithColumn("DeletedFlag").AsBoolean().NotNullable().WithDefaultValue(false)
          .WithColumn("CreatedBy").AsString(150).NotNullable()
          .WithColumn("CreatedDateUtc").AsDateTime2().NotNullable()
          .WithColumn("UpdatedBy").AsString(150).Nullable()
          .WithColumn("UpdatedDateUtc").AsDateTime2().Nullable()
          .WithColumn("Order").AsInt32().Nullable()
          ;

    }
}
