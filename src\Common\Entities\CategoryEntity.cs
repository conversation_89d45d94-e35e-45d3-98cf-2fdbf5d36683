﻿using Geidea.Utils.DataAccess.Entities;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace Common.Entities;

public class CategoryEntity : AuditableEntity<Guid>
{
    public string Code { get; set; } = string.Empty;
    public int Type { get; set; }
    public bool DeletedFlag { get; set; }
    public int? DisplayOrder { get; set; }

    public Guid? ParentId { get; set; }
    [ForeignKey("ParentId")]
    [JsonIgnore]
    public CategoryEntity? Parent { get; set; }
    public string? SalesChannel { get; set; }
    public string? Flow { get; set; }

    public List<CategoryEntity> Subcategories { get; set; } = new List<CategoryEntity>();

    public string? Counterparty { get; set; }
    public string? Name { get; set; }
}
