﻿using AutoMapper;
using Common.Entities;
using Common.Models.NonTransactionalPrice;
using Common.Models.ProductCommissionPrice;
using Common.Models.UnitPrice;
using Common.Models.ValueAddedSerivcePricing;
using Common.Repositories;
using Common.Services;
using DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Services;
public class ProductCommissionPriceService : IProductCommissionPriceService
{
    private readonly ILogger<ProductCommissionPriceService> logger;
    private readonly IProductCommissionPriceRepository productCommissionPriceRepository;
    private readonly IMapper mapper;
    public ProductCommissionPriceService(ILogger<ProductCommissionPriceService> logger, IProductCommissionPriceRepository productCommissionPriceRepository, IMapper mapper)
    {
        this.productCommissionPriceRepository = productCommissionPriceRepository;
        this.mapper = mapper;
        this.logger = logger;
    }
    #region Create
    public async Task<ProductCommissionPriceResponse> CreateAsync(ProductCommissionPriceCreateRequest request)
    {
        try
        {
            var ProcessResponse = await ProcessCommissionPricesAsync(request);
            await productCommissionPriceRepository.SaveCommissionPricesAsync(ProcessResponse.NewCommissionPriceList);

            return new ProductCommissionPriceResponse
            {
                NewCommissionPriceList = ProcessResponse.NewCommissionPriceList,
                NewExistedCommissionPriceList = ProcessResponse.NewExistedCommissionPriceList,
            };
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "CreateAsync: An error occured while saving product commission price.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An an error occured while saving product commission price.");
        }

    }
    public async Task<Dictionary<ProductCommissionPriceCombinationKey, ProductCommissionPriceEntity>> GetAllExistedCommisssionPrices(ProductCommissionPriceCreateRequest request)
    {
        return (await productCommissionPriceRepository.GetExistCommissionPrices(pc =>
                                              request.ProductIds.Contains(pc.ProductID) &&
                                              request.MccIds.Contains(pc.MCCID) &&
                                              request.BusinessTypeIds.Contains(pc.BusinessTypeID) &&
                                              request.CommissionFeeIds.Contains(pc.CommissionFeeID)))
                                             .ToDictionary(pc => new ProductCommissionPriceCombinationKey(pc.ProductID, pc.MCCID, pc.BusinessTypeID, pc.CommissionFeeID));
    }
    private async Task<ProductCommissionPriceResponse> ProcessCommissionPricesAsync(ProductCommissionPriceCreateRequest request)
    {
        var NewCommissionPricesList = new List<ProductCommissionPriceEntity>();
        var ExistedCommissionPricesList = new List<ProductCommissionPriceEntity>();
        var AllExistedCommisssionPrices = await GetAllExistedCommisssionPrices(request);

        var combinations = from productId in request.ProductIds
                           from mccId in request.MccIds
                           from businessTypeId in request.BusinessTypeIds
                           from CommissionId in request.CommissionFeeIds
                           select new ProductCommissionPriceCombinationKey(productId, mccId, businessTypeId, CommissionId);

        foreach (var combination in combinations)
        {
            if (AllExistedCommisssionPrices.TryGetValue(combination, out var existingCommissionPrice))
            {
                ExistedCommissionPricesList.Add(existingCommissionPrice);
            }
            else
            {
                var CommissionPrice = mapper.Map<ProductCommissionPriceEntity>(request);
                CommissionPrice.ProductID = combination.ProductID;
                CommissionPrice.MCCID = combination.MCCID;
                CommissionPrice.BusinessTypeID = combination.BusinessTypeID;
                CommissionPrice.CommissionFeeID = combination.CommissionID;
                NewCommissionPricesList.Add(CommissionPrice);
            }
        }

        return new ProductCommissionPriceResponse
        {
            NewCommissionPriceList = NewCommissionPricesList,
            NewExistedCommissionPriceList = ExistedCommissionPricesList
        };
    }
    #endregion

    #region Update
    public async Task<ProductCommissionPriceDetailsResponse> UpdateAsync(Guid Id, ProductComissionPriceUpdateRequest request)
    {

        var ExistedCommissionPrice = await productCommissionPriceRepository.GetByIdAsync(Id);
        if (ExistedCommissionPrice == null)
        {
            logger.LogWarning("No commission price was found with Id {Id}", Id);
            throw new ServiceException(HttpStatusCode.NotFound, "Commission price not found!");
        }

        try
        {
            ExistedCommissionPrice.FeeType = request.FeeType;
            ExistedCommissionPrice.FeeValue = request.FeeValue;
            ExistedCommissionPrice.BillingType = request.BillingType;
            ExistedCommissionPrice.BillingFrequency = request.BillingFrequency;

            productCommissionPriceRepository.Update(ExistedCommissionPrice);
            await productCommissionPriceRepository.SaveChangesAsync();

            return mapper.Map<ProductCommissionPriceDetailsResponse>(ExistedCommissionPrice);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "UpdateAsync: An error occured while updating product commission price.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "UpdateAsync: An error occured while updating product commission price.");
        }

    }
    #endregion

    #region Listing
    public async Task<ProductCommissionPriceListResponse> GetProductCommissionPriceList(ProductCommissionPriceListRequest request)
    {
        if (request == null)
        {
            logger.LogError("Invalid Get Product Commission Price List request.");
            throw new ServiceException(HttpStatusCode.BadRequest, "GetProductCommissionPriceList: Invalid Get Product Commission Price List request.");
        }
        try
        {
            return await productCommissionPriceRepository.GetProductCommissionPriceList(request);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "something has went wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, "GetProductCommissionPriceList: something has went wrong!");
        }
    }
    #endregion


    #region Delete ProductCommissionPrices 
    public async Task DeleteProductCommissionPricesAsync(List<Guid> ids, string deletedBy)
    {
        if (!ids.Any())
        {
            logger.LogWarning("DeleteProductCommissionPricesAsync: No IDs were provided for deletion.");
            throw new ServiceException(HttpStatusCode.BadRequest, "No IDs provided for deletion.");
        }
        var productCommissionPricesToDelete = await productCommissionPriceRepository.GetProductCommissionPricesByIdsAsync(ids);

        if (!productCommissionPricesToDelete.Any())
        {
            logger.LogWarning("DeleteProductCommissionPricesAsync: No matching records found for the provided IDs.");
            throw new ServiceException(HttpStatusCode.NotFound, "No matching records found for deletion.");
        }
        try
        {
            var logs = productCommissionPricesToDelete.Select(up =>
            {
                var log = mapper.Map<ProductCommissionPriceLogEntity>(up);
                log.DeletedBy = deletedBy;
                log.DeletedDate = DateTime.UtcNow;
                return log;
            }).ToList();

            int affectedRows = await productCommissionPriceRepository.DeleteBulkAsync(ids);
            await productCommissionPriceRepository.AddLogsAsync(logs);

            logger.LogInformation($"DeleteProductCommissionPricesAsync: Successfully deleted {affectedRows} Product Commission Prices and logged the actions.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "DeleteProductCommissionPricesAsync: An error occurred while deleting commission prices.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An error occurred while deleting commission prices.");
        }
    }

    #endregion
    #region Get By Id
    public async Task<ProductCommissionPriceDetailsResponse> GetCommissionPriceByIdAsync(Guid id)
    {
        try
        {
            var commissionPricePriceEntity = await productCommissionPriceRepository.GetCommissionPriceByIdAsync(id);

            if (commissionPricePriceEntity == null)
            {
                logger.LogWarning("No commission price found with Id {Id}.", id);
                throw new ServiceException(HttpStatusCode.NotFound, "commission price not found.");
            }

            var commissionPriceDetails = mapper.Map<ProductCommissionPriceDetailsResponse>(commissionPricePriceEntity);
            return commissionPriceDetails;
        }

        catch (Exception ex)
        {
            logger.LogError(ex, "something has went wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, "something has went wrong!");
        }
    }
    #endregion
    #region CreateOrUpdate
    public async Task<ProductCommissionPriceResponse> CreateOrUpdateAsync(ProductCommissionPriceCreateRequest request)
    {

        var (newCommissionPrices, oldExistingCommissionPricesToUpdate, existingCommissionPricesToUpdate) = await ProcessCommissionPricesForCreateOrUpdateAsync(request);

        // Save the new and updated commission prices
        if (newCommissionPrices.Any())
        {
            await productCommissionPriceRepository.SaveCommissionPricesAsync(newCommissionPrices);
        }

        if (existingCommissionPricesToUpdate.Any())
        {
            await productCommissionPriceRepository.UpdateCommissionPricesAsync(existingCommissionPricesToUpdate);
        }

        return new ProductCommissionPriceResponse
        {
            NewCommissionPriceList = newCommissionPrices,
            NewExistedCommissionPriceList = existingCommissionPricesToUpdate,
            OldExistedCommissionPriceList = oldExistingCommissionPricesToUpdate
        };
    }

    private async Task<(List<ProductCommissionPriceEntity> newCommissionPrices, List<ProductCommissionPriceEntity> oldExistingCommissionPricesToUpdate, List<ProductCommissionPriceEntity> newExistingCommissionPricesToUpdate)> ProcessCommissionPricesForCreateOrUpdateAsync(ProductCommissionPriceCreateRequest request)
    {
        var newCommissionPricesList = new List<ProductCommissionPriceEntity>();
        var existingCommissionPricesToUpdateList = new List<ProductCommissionPriceEntity>();
        var oldValue = new List<ProductCommissionPriceEntity>();

        var allExistedCommissionPricesList = await GetAllExistedCommisssionPrices(request);

        var combinations = from productId in request.ProductIds
                           from mccId in request.MccIds
                           from businessTypeId in request.BusinessTypeIds
                           from commissionFeeId in request.CommissionFeeIds
                           select new ProductCommissionPriceCombinationKey(productId, mccId, businessTypeId, commissionFeeId);

        foreach (var combination in combinations)
        {
            var existingCommissionPrice = allExistedCommissionPricesList.FirstOrDefault(up =>
                up.Key.ProductID == combination.ProductID &&
                up.Key.MCCID == combination.MCCID &&
                up.Key.BusinessTypeID == combination.BusinessTypeID &&
                up.Key.CommissionID == combination.CommissionID);

            if (existingCommissionPrice.Value != null)
            {
                var oldExistingPrice = JsonConvert.DeserializeObject<ProductCommissionPriceEntity>(
                    JsonConvert.SerializeObject(existingCommissionPrice.Value));

                if (oldExistingPrice != null)
                {
                    oldValue.Add(oldExistingPrice);
                }

                UpdateExistingCommissionPrice(existingCommissionPrice.Value, request);
                existingCommissionPricesToUpdateList.Add(existingCommissionPrice.Value);
            }
            else
            {
                var commissionPrice = CreateNewCommissionPrice(request, combination.ProductID, combination.MCCID, combination.BusinessTypeID, combination.CommissionID);
                newCommissionPricesList.Add(commissionPrice);
            }
        }

        return (newCommissionPricesList, oldValue, existingCommissionPricesToUpdateList);
    }

    private ProductCommissionPriceEntity CreateNewCommissionPrice(ProductCommissionPriceCreateRequest request, Guid productId, Guid mccId, Guid businessTypeId, Guid commissionFeeId)
    {
        var commissionPrice = mapper.Map<ProductCommissionPriceEntity>(request);
        commissionPrice.ProductID = productId;
        commissionPrice.MCCID = mccId;
        commissionPrice.BusinessTypeID = businessTypeId;
        commissionPrice.CommissionFeeID = commissionFeeId;
        return commissionPrice;
    }

    private void UpdateExistingCommissionPrice(ProductCommissionPriceEntity existingCommissionPrice, ProductCommissionPriceCreateRequest request)
    {
        // Update fields that are allowed to be changed
        existingCommissionPrice.FeeType = request.FeeType;
        existingCommissionPrice.FeeValue = request.FeeValue;
        existingCommissionPrice.BillingType = request.BillingType;
        existingCommissionPrice.BillingFrequency = request.BillingFrequency;
    }
    #endregion
    public async Task<List<ProductCommissionPriceDetailsResponse>> GetProductCommissionPricesByIdsAsync(List<Guid> ids)
    {
        var productCommissionPriceEntities = await productCommissionPriceRepository.GetProductCommissionPricesByIdsAsync(ids);
        return mapper.Map<List<ProductCommissionPriceDetailsResponse>>(productCommissionPriceEntities);
    }
}

