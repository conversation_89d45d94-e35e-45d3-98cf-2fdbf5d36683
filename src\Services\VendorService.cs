﻿using Common;
using Common.Models.Vendor;
using Common.Repositories;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;

namespace Services;
public class VendorService : IVendorService
{
    private readonly ILogger<VendorService> logger;
    private readonly IVendorRepository vendorRepository;

    public VendorService(ILogger<VendorService> logger, IVendorRepository vendorRepository)
    {
        this.logger = logger;
        this.vendorRepository = vendorRepository;
    }

    public async Task<VendorResponse> CreateAsync(VendorRequest request)
    {
        logger.LogInformation("Creating new vendor with name {@request.Name} , terminal type {@request.TerminalType}", request.Name, request.TerminalType);

        await ValidateVendorExistWithSameNameAndType(request);

        var vendor = await vendorRepository.CreateAsync(request);

        logger.LogInformation("Added vendor with Name '{@vendor.Name}' and Prefixes '{@vendor.TerminalType}' successfully!.", vendor.Name, vendor.TerminalType);

        return vendor;
    }

    public async Task<VendorResponse?> GetByIdAsync(Guid id)
    {
        logger.LogInformation("Getting vendor by id {@id}", id);

        var vendor = await vendorRepository.GetByIdAsync(id);

        if (vendor != null)
            logger.LogInformation("Got vendor with Name '{@vendor.Name}' and Prefixes '{@vendor.Prefix}' for id {@id}.", vendor.Name, vendor.Prefix, id);
        else
            logger.LogInformation("There is no vendor with id {@id}", id);

        return vendor;
    }

    public async Task<List<VendorResponse>?> GetAsync(string? terminalType = null)
    {
        if (terminalType == null)
            logger.LogInformation("Getting all vendors");
        else
            logger.LogInformation("Getting all vendors with terminal type {terminalType}", terminalType);

        var vendors = await vendorRepository.GetAsync(terminalType);

        if (vendors != null)
            logger.LogInformation("The vendors are {vendors}", vendors);
        else
            logger.LogInformation("There are no vendors available");

        return vendors;
    }

    public async Task<VendorResponse?> GetDefaultVendorAsync()
    {
        logger.LogInformation("Getting default vendor");

        var vendor = await vendorRepository.GetDefaultVendorAsync();

        if (vendor != null)
            logger.LogInformation("Got default vendor with Name '{@vendor.Name}' and Prefixes '{@vendor.Prefix}' for id {@vendor.Id}.", vendor.Name, vendor.Prefix, vendor.Id);
        else
            logger.LogInformation("There is no default vendor");

        return vendor;
    }

    public async Task<VendorResponse?> SetDefaultVendorAsync(Guid vendorId)
    {
        return await vendorRepository.SetDefaultVendorAsync(vendorId);
    }

    public async Task<VendorSearchResponse> AdvancedSearch(VendorSearchRequest searchRequest)
    {
        var result = await vendorRepository.AdvancedSearch(searchRequest!);
        return result;
    }

    private async Task ValidateVendorExistWithSameNameAndType(VendorRequest vendor)
    {
        if (await vendorRepository.IsVendorExistWithSameNameAndType(vendor))
        {
            logger.LogInformation("Can not add vendor with '{vendor.Name}' and terminal type '{vendor.TerminalType}', as there is another one with the same data!.", vendor.Name, vendor.TerminalType);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.VendorExistBefore);
        }
    }
}
