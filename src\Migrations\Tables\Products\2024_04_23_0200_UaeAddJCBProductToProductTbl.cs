﻿using FluentMigrator;

namespace Migrations.Tables.Products;
[Migration(2024_04_23_0200)]
public class UaeAddJCBProductToProductTbl : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
            DECLARE @JCBProductId_UAE AS uniqueidentifier=NEWID();
            --add new product with type JCB_GW to UAE
 
            INSERT INTO [dbo].[Products]
            ([Id]
            ,[Availability]
            ,[Code]
            ,[Type]
            ,[ValidFrom]
            ,[CreatedBy]
            ,[CreatedDate]
            ,[UpdatedBy]
            ,[Version]
            ,[Counterparty],
            [DisplayOrder],
            [SalesChannel])
            VALUES
            (@JCBProductId_UAE
            ,'Live'
            ,'JCB_GW'
            ,'SCHEME'
            ,GETUTCDATE()
            ,'00000000-0000-0000-0000-000000000000'
            ,GETUTCDATE()
            ,'00000000-0000-0000-0000-000000000000'
            ,0
            ,'GEIDEA_UAE',NULL,NULL)
 
            --add new JCB_UAE product as part of BUNDLE
 
            DECLARE @ProductId uniqueidentifier;

            SET @ProductId = (SELECT TOP 1 Id FROM Products WHERE type = 'BUNDLE' AND Counterparty = 'GEIDEA_UAE' AND Availability = 'Live' AND Code = 'PAYMENT_GATEWAY_BUNDLE')

            IF NOT EXISTS (SELECT 1 FROM ProductParts WHERE  ProductId = @ProductId  AND  PartId = @JCBProductId_UAE)

	            INSERT INTO dbo.ProductParts (ProductId, PartId, Quantity) VALUES (@ProductId, @JCBProductId_UAE, 1);
 
            ");
    }
}
