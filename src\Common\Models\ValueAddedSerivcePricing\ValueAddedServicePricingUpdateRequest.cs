﻿using Common.Enums.UnitPrice;
using Common.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Common.Enums.ProductCommisssionPrices;

namespace Common.Models.ValueAddedSerivcePricing;
public class ValueAddedServicePricingUpdateRequest
{
    public decimal SubscriptionFee { get; set; }

    public VatType FeeType { get; set; }

    public BillingType BillingType { get; set; }

    public PriceBillingFrequency BillingFrequency { get; set; }
}
