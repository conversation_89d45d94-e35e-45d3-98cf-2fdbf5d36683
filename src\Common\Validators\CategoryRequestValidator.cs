﻿using Common.Models.CategoryRequests;
using FluentValidation;
using System.Linq;
using System.Reflection;

namespace Common.Validators;

public class CategoryRequestValidator : AbstractValidator<CategoryRequest>
{
    public CategoryRequestValidator()
    {
        var allowedSalesChannels = typeof(Constants.SalesChannel).GetFields(BindingFlags.Public | BindingFlags.Static |
            BindingFlags.FlattenHierarchy)
            .Where(fi => fi.IsLiteral && !fi.IsInitOnly).Select(a => a.GetRawConstantValue()).ToList();

        var allowedFlows = typeof(Constants.Flow).GetFields(BindingFlags.Public | BindingFlags.Static |
            BindingFlags.FlattenHierarchy)
            .Where(fi => fi.IsLiteral && !fi.IsInitOnly).Select(a => a.GetRawConstantValue()).ToList();

        RuleFor(x => x.SalesChannel).Must(x => allowedSalesChannels.Contains(x!))
            .When(x => x.SalesChannel != null)
            .WithErrorCode(Errors.InvalidSalesChannel.Code).WithMessage(Errors.InvalidSalesChannel.Message);

        RuleFor(x => x.Flow).Must(x => allowedFlows.Contains(x!))
            .When(x => x.Flow != null)
            .WithErrorCode(Errors.InvalidFlow.Code).WithMessage(Errors.InvalidFlow.Message);
    }
}
