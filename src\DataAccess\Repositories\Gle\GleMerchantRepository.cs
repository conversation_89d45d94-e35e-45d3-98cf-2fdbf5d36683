﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Common.Entities.Gle;
using Common.Repositories.Gle;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Constants = Common.Constants;

namespace DataAccess.Repositories.Gle;

public class GleMerchantRepository : GleBaseRepository<GleMerchantEntity>, IGleMerchantRepository
{
    private readonly ILogger<GleMerchantRepository> logger;

    public GleMerchantRepository(DbContext context,
        IHttpContextAccessor contextAccessor,
        ILogger<GleBaseRepository<GleMerchantEntity>> baseLogger,
        ILogger<GleMerchantRepository> logger)
        : base(context, contextAccessor, baseLogger)
    {
        this.logger = logger;
    }

    public async Task<GleMerchantEntity?> GetGleHierarchyByMerchantIdAsync(Guid merchantId)
    {
        var result = await context.Set<GleMerchantEntity>()
            .AsQueryable()
            .AsNoTracking()
            .Include(x => x.GleStoreEntities)
            .ThenInclude(x => x.GleTerminalEntities)
            .Where(x => x.MerchantId == merchantId)
            .SingleOrDefaultAsync();

        logger.LogInformation("Returning the entire GLE hierarchy based on the merchant ID: {id}", merchantId);
        return result;
    }

    public async Task<GleMerchantEntity?> GetGleMerchantByMerchantIdAsync(Guid merchantId)
    {
        var result = await context.Set<GleMerchantEntity>()
            .AsQueryable()
            .AsNoTracking()
            .SingleOrDefaultAsync(x => x.MerchantId == merchantId);

        logger.LogInformation("Returning GLE merchant for merchant ID: {id}", merchantId);
        return result;
    }

    public async Task<bool> IsMerchantRegisteredInGleAsync(Guid merchantId)
    {
        return await context.Set<GleMerchantEntity>()
            .AnyAsync(x => x.MerchantId == merchantId &&
                           x.GleRegistrationStatus == Constants.GleRegistrationStatus.Success);
    }
}