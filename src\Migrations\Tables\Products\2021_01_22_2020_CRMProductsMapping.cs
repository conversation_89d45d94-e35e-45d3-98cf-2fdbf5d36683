﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_01_22_2020)]
public class CRMProductsMapping : ForwardOnlyMigration
{
    public override void Up()
    {
        Alter.Table("Products").AddColumn("CRMProductId").AsString().Nullable();
        Update.Table("Products").Set(new { CRMProductId = "50000001" }).Where(new { Code = "SMARTPOS_A920" });
        Update.Table("Products").Set(new { CRMProductId = "50000002" }).Where(new { Code = "GEIDEA_GO_APP" });
    }
}
