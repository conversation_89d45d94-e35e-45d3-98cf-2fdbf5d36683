﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Common;
using Common.Models;
using Common.Models.Gle;
using Common.Services.Gle;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;

namespace ProductService.Controllers;

[Route("api/v1/[controller]")]
[ApiController]
public class GleController : ControllerBase
{
    private readonly IGleMerchantService gleMerchantService;
    private readonly IGleStoreService gleStoreService;
    private readonly IGleTerminalService gleTerminalService;
    private readonly IGleUpdateHistoryService gleUpdateHistoryService;

    public GleController(IGleMerchantService gleMerchantService,
        IGleStoreService gleStoreService,
        IGleTerminalService gleTerminalService,
        IGleUpdateHistoryService gleUpdateHistoryService)
    {
        this.gleMerchantService = gleMerchantService;
        this.gleStoreService = gleStoreService;
        this.gleTerminalService = gleTerminalService;
        this.gleUpdateHistoryService = gleUpdateHistoryService;
    }

    /// <summary>
    /// Creates a new GLE merchant registration.
    /// </summary>
    /// <response code="200">GLE merchant was created.</response>
    /// <response code="400">Returns the error.</response> 
    [HttpPost("merchant")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> AddGleMerchant([FromBody] GleMerchantRequest gleMerchant)
    {
        await gleMerchantService.AddGleMerchantAsync(gleMerchant);
        return Ok();
    }

    /// <summary>
    /// Updates an existing GLE merchant registration based on GLE merchant ID. 
    /// </summary>
    /// <remarks>
    /// Sample request:
    ///
    ///     PATCH /order
    ///     [
    ///         { "op": "replace", "path": "GleRegistrationStatus", "value": "Success"},
    ///     	{ "op": "replace", "path": "GleRegistrationResponse", "value": "Merchant was registered."}
    ///     ]
    ///
    /// </remarks>
    /// <response code="204">Returns no content if the registration has been updated</response>
    /// <response code="400"></response> 
    /// <response code="404">If there is no record with that given ID</response> 
    [HttpPatch("merchant/{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateGleMerchant(Guid id, [FromBody] JsonPatchDocument<UpdateGleMerchantRequest> updateDocument)
    {
        await gleMerchantService.UpdateGleMerchantAsync(id, updateDocument);
        return NoContent();
    }

    /// <summary>
    /// Get the GLE hierarchy created for a certain merchant id
    /// </summary>
    /// <param name="merchantId">Merchant id</param>
    /// <response code="200">Returns the GLE registrations for a merchant id</response>
    /// <response code="400">Returns the error</response> 
    [Produces("application/json")]
    [HttpGet("merchant/hierarchyByMerchantId/{merchantId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetGleHierarchyByMerchantId(Guid merchantId)
    {
        return Ok(await gleMerchantService.GetGleHierarchyByMerchantIdAsync(merchantId));
    }

    /// <summary>
    /// Returns true if the merchant is registered in GLE. Returns false otherwise.
    /// </summary>
    /// <param name="merchantId">Merchant id</param>
    /// <response code="200">Returns true/false if the merchant is registered or not in GLE</response>
    /// <response code="400">Returns the error</response> 
    [Produces("application/json")]
    [HttpGet("merchant/isRegisteredInGle/{merchantId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> IsMerchantRegisteredInGle(Guid merchantId)
    {
        return Ok(await gleMerchantService.IsMerchantRegisteredInGleAsync(merchantId));
    }

    /// <summary>
    /// Get the GLE hierarchy created for a certain store id
    /// </summary>
    /// <param name="storeId">Store id</param>
    /// <response code="200">Returns the GLE registrations for a store id</response>
    /// <response code="400">Returns the error</response> 
    [Produces("application/json")]
    [HttpGet("store/hierarchyByStoreId/{storeId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetGleHierarchyByStoreId(Guid storeId)
    {
        return Ok(await gleStoreService.GetGleHierarchyByStoreIdAsync(storeId));
    }

    /// <summary>
    /// Creates a new GLE store registration.
    /// </summary>
    /// <response code="200">GLE store was created.</response>
    /// <response code="400">Returns the error.</response> 
    [HttpPost("store")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> AddGleStore([FromBody] GleStoreRequest gleStore)
    {
        await gleStoreService.AddGleStoreAsync(gleStore);
        return Ok();
    }

    /// <summary>
    /// Updates an existing GLE store registration based on GLE store ID. 
    /// </summary>
    /// <remarks>
    /// Sample request:
    ///
    ///     PATCH /order
    ///     [
    ///         { "op": "replace", "path": "GleRegistrationStatus", "value": "Success"},
    ///     	{ "op": "replace", "path": "GleRegistrationResponse", "value": "Store was registered."}
    ///     ]
    ///
    /// </remarks>
    /// <response code="204">Returns no content if the registration has been updated</response>
    /// <response code="400"></response> 
    /// <response code="404">If there is no record with that given ID</response> 
    [HttpPatch("store/{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateGleStore(Guid id, [FromBody] JsonPatchDocument<UpdateGleStoreRequest> updateDocument)
    {
        await gleStoreService.UpdateGleStoreAsync(id, updateDocument);
        return NoContent();
    }

    /// <summary>
    /// Creates a new GLE terminal registration.
    /// </summary>
    /// <response code="200">GLE terminal was created.</response>
    /// <response code="400">Returns the error.</response> 
    [HttpPost("terminal")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> AddGleTerminal([FromBody] GleTerminalRequest gleTerminal)
    {
        await gleTerminalService.AddGleTerminalAsync(gleTerminal);
        return Ok();
    }

    /// <summary>
    /// Updates an existing GLE terminal registration based on GLE terminal ID. 
    /// </summary>
    /// <remarks>
    /// Sample request:
    ///
    ///     PATCH /order
    ///     [
    ///         { "op": "replace", "path": "GleRegistrationStatus", "value": "Success"},
    ///     	{ "op": "replace", "path": "GleRegistrationResponse", "value": "Store was registered."}
    ///     ]
    ///
    /// </remarks>
    /// <response code="204">Returns no content if the registration has been updated</response>
    /// <response code="400"></response> 
    /// <response code="404">If there is no record with that given ID</response> 
    [HttpPatch("terminal/{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateGleTerminal(Guid id, [FromBody] JsonPatchDocument<UpdateGleTerminalRequest> updateDocument)
    {
        await gleTerminalService.UpdateGleTerminalAsync(id, updateDocument);
        return NoContent();
    }

    /// <summary>
    /// Gets a GLE terminal registration list based on order id.
    /// </summary>
    /// <response code="200">Returns the GLE terminal registration list</response>
    /// <response code="400">Returns the error</response> 
    /// <response code="404">If there is no GLE terminal registrations for given order id</response> 
    [Produces("application/json")]
    [HttpGet("terminal/registrationList/{orderId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetGleTerminalListByOrderId(Guid orderId)
    {
        return Ok(await gleTerminalService.GetGleTerminalListByOrderIdAsync(orderId));
    }

    /// <summary>
    /// Returns the calculated bill payment status on a order level
    /// </summary>
    /// <param name="orderId">Order ID</param>
    /// <param name="productIdsRequest">List of product IDs</param>
    /// <response code="200">Returns the status</response>
    [HttpPost("terminal/{orderId}/billPaymentStatus")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    public async Task<IActionResult> CalculateBillPaymentsStatus(Guid orderId, [FromBody] IdsRequest productIdsRequest)
    {
        return Ok(await gleTerminalService.CalculateBillPaymentsStatusAsync(orderId, productIdsRequest));
    }

    /// <summary>
    /// Returns the calculated GLE registration status for an order with BP products
    /// </summary>
    /// <param name="orderId">Order ID</param>
    /// <response code="200">Returns the status</response>
    [HttpGet("terminal/{orderId}/gleRegistrationStatus")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    public async Task<IActionResult> CalculateGleRegistrationStatusForOrderWithBp(Guid orderId)
    {
        return Ok(await gleTerminalService.CalculateGleRegistrationStatusForOrderWithBpAsync(orderId));
    }

    /// <summary>
    /// Gets a GLE registration based on type (merchant/store/terminal) and id.
    /// </summary>
    /// <param name="type">GLE type: merchant/store/terminal</param>
    /// <param name="id">Can be merchant id/store id/product instance id</param>
    /// <response code="200">Returns the GLE registration</response>
    /// <response code="400">Returns the error</response> 
    /// <response code="404">If there is no GLE registration with given instance id and type</response> 
    [Produces("application/json")]
    [HttpGet("type/{type}/id/{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetGleRegistration(string type, Guid id)
    {
        var allowedTypes = new List<string>
        {
            Constants.GleRegistrationType.Merchant,
            Constants.GleRegistrationType.Store,
            Constants.GleRegistrationType.Terminal
        };
        if (!allowedTypes.Contains(type))
        {
            return BadRequest(Errors.InvalidGleRegistrationType.Message);
        }

        return type.ToLower() switch
        {
            Constants.GleRegistrationType.Merchant => Ok(await gleMerchantService.GetGleMerchantByMerchantIdAsync(id)),
            Constants.GleRegistrationType.Store => Ok(await gleStoreService.GetGleStoreByStoreIdAsync(id)),
            _ => Ok(await gleTerminalService.GetGleTerminalByProductInstanceIdAsync(id))
        };
    }

    /// <summary>
    /// Gets status history for a GLE registration
    /// </summary>
    /// <param name="gleHistoryLogRequest">Merchant Id, Order Id, Parent Merchant Id</param>
    /// <response code="200">Returns status history for gle registration</response>
    /// <response code="400">Returns the error</response> 
    /// <response code="404">If there is no GLE registration with given ids</response> 
    [Produces("application/json")]
    [HttpGet("gleHistoryLog")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetGleRegistrationStatusHistory([FromQuery] GleHistoryLogRequest gleHistoryLogRequest)
    {
        return Ok(await gleTerminalService.GetGleRegistrationStatusHistoryAsync(gleHistoryLogRequest));
    }

    [Produces("application/json")]
    [HttpPost("updateHistory")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> AddGleUpdateHistory([FromBody] List<GleUpdateHistoryRequest> gleUpdateHistoryRequests)
    {
        await gleUpdateHistoryService.AddGleUpdateHistoryAsync(gleUpdateHistoryRequests);
        return Ok();
    }

    [Produces("application/json")]
    [HttpGet("updateHistory/merchant/{merchantId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetGleUpdateHistoryByMerchantId(Guid merchantId)
    {
        return Ok(await gleUpdateHistoryService.GetGleUpdateHistoryByMerchantIdAsync(merchantId));
    }

    [Produces("application/json")]
    [HttpPut("updateHistory")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateGleUpdateHistory([FromBody] List<GleUpdateHistoryEditRequest> gleUpdateHistoryEditRequest)
    {
        await gleUpdateHistoryService.UpdateGleUpdateHistoryAsync(gleUpdateHistoryEditRequest);
        return Ok();
    }
}