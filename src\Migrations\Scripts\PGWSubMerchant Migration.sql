BEGIN Transaction

DECLARE @ProductInstanceId UNIQUEIDENTIFIER;
DECLARE @Metadata NVARCHAR(MAX);
DECLARE @Country NVARCHAR(255);
DECLARE @Governorate NVARCHAR(255);
DECLARE @City NVARCHAR(255);
DECLARE @AddressLine1 NVARCHAR(255);
DECLARE @CountryPrefix NVARCHAR(50);
DECLARE @PhoneNumber NVARCHAR(50);
DECLARE @Email NVARCHAR(50);
DECLARE @SubMerchantInformation NVARCHAR(MAX);

DECLARE subMerchantInformations_Cursor CURSOR FOR 
SELECT 
	pi.ProductInstanceId,
	pi.Metadata,
	a.Country,
	a.Governorate,
	a.City,
	a.AddressLine1,
	c.CountryPrefix,
	c.PhoneNumber,
	c.Email
FROM  [MERCHANTS].[dbo].[Merchant] m
LEFT JOIN [MERCHANTS].[dbo].[MerchantAddresses] ma ON m.Id = ma.[MerchantId] and ma.Purpose = 'BUSINESS'
LEFT JOIN [MERCHANTS].[dbo].[Address] a ON a.Id = ma.AddressId
LEFT JOIN [MERCHANTS].[dbo].[MerchantPersonOfInterest] mpi ON m.Id = mpi.[MerchantId] and mpi.OrganizationRole = 'OWNER'
LEFT JOIN [MERCHANTS].[dbo].[PersonOfInterest] poi ON poi.Id = mpi.[PersonOfInterestId]
LEFT JOIN [MERCHANTS].[dbo].[PersonOfInterestContacts] poic ON poi.Id = poic.[PersonOfInterestId]
LEFT JOIN [MERCHANTS].[dbo].[Contact] c ON c.Id = poic.ContactId 
INNER JOIN (SELECT DISTINCT pi.Id as ProductInstanceId, pi.[Metadata], m.[ParentMerchantId] CompanyId 
			FROM [PRODUCTS].[dbo].[ProductInstances] pi
			INNER JOIN [PRODUCTS].[dbo].[Products] p ON p.id = pi.ProductId
			INNER JOIN [MERCHANTS].[dbo].[MerchantHierarchy] m ON m.[MerchantId] = pi.StoreID and [HierarchyType] = 'STRUCTURAL_HIERARCHY'
			WHERE  [Metadata] IS NOT NULL
			  AND p.Type = 'GWAY' 
			  AND ISJSON([Metadata]) > 0 
			  AND p.Counterparty = 'GEIDEA_EGYPT'
			  AND (JSON_QUERY(pi.metadata, '$.SubMerchantInformation') IS NULL
				   OR (JSON_VALUE(pi.metadata, '$.SubMerchantInformation.Id') IS NULL 
					   AND JSON_VALUE(pi.metadata, '$.SubMerchantInformation.RegisteredName') IS NULL
					   AND JSON_VALUE(pi.metadata, '$.SubMerchantInformation.TradingName') IS NULL 
					   AND JSON_VALUE(pi.metadata, '$.SubMerchantInformation.City') IS NULL 
					   AND JSON_VALUE(pi.metadata, '$.SubMerchantInformation.Country') IS NULL 
					   AND JSON_VALUE(pi.metadata, '$.SubMerchantInformation.Governorate') IS NULL 
					   AND JSON_VALUE(pi.metadata, '$.SubMerchantInformation.StreetAndNumber') IS NULL 
					   AND JSON_VALUE(pi.metadata, '$.SubMerchantInformation.ZipCode') IS NULL 
					   AND JSON_VALUE(pi.metadata, '$.SubMerchantInformation.Email') IS NULL 
					   AND JSON_VALUE(pi.metadata, '$.SubMerchantInformation.PhoneNumber') IS NULL ))) pi ON pi.CompanyId = m.id;

OPEN subMerchantInformations_Cursor;  

FETCH NEXT FROM subMerchantInformations_Cursor
INTO @ProductInstanceId, @Metadata , @Country , @Governorate , @City , @AddressLine1 , @CountryPrefix , @PhoneNumber, @Email;

WHILE @@FETCH_STATUS = 0  
BEGIN  
   SET @SubMerchantInformation = '{}';

   SET @SubMerchantInformation = JSON_MODIFY(@SubMerchantInformation, '$.City', @City);
   SET @SubMerchantInformation = JSON_MODIFY(@SubMerchantInformation, '$.Country', @Country);
   SET @SubMerchantInformation = JSON_MODIFY(@SubMerchantInformation, '$.Governorate', @Governorate);
   SET @SubMerchantInformation = JSON_MODIFY(@SubMerchantInformation, '$.StreetAndNumber', @AddressLine1);
   SET @SubMerchantInformation = JSON_MODIFY(@SubMerchantInformation, '$.ZipCode', '12345');
   SET @SubMerchantInformation = JSON_MODIFY(@SubMerchantInformation, '$.Email', @Email);
   SET @SubMerchantInformation = JSON_MODIFY(@SubMerchantInformation, '$.PhoneNumber', @PhoneNumber);
   SET @SubMerchantInformation = JSON_MODIFY(@SubMerchantInformation, '$.CountryPrefix', @CountryPrefix);

   SET @Metadata = JSON_MODIFY(@Metadata, '$.SubMerchantInformation', JSON_QUERY(@SubMerchantInformation));

   UPDATE [PRODUCTS].[dbo].[ProductInstances]
	  SET [Metadata] = @Metadata		 
   WHERE [Id] = @ProductInstanceId;

  FETCH NEXT FROM subMerchantInformations_Cursor
INTO @ProductInstanceId, @Metadata , @Country , @Governorate , @City , @AddressLine1 , @CountryPrefix , @PhoneNumber, @Email;
END  


CLOSE subMerchantInformations_Cursor;
DEALLOCATE subMerchantInformations_Cursor;

Commit Transaction;