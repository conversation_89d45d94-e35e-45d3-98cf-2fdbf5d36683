﻿using Common.Models.ProductCommissionPrice;
using Common.Models.UnitPrice;
using Common.Services;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProductService.Controllers;
[Route("api/v1/[controller]")]
[ApiController]
public class ProductCommissionPriceController : ControllerBase
{
    private readonly IProductCommissionPriceService commissionPriceService;
    public ProductCommissionPriceController(IProductCommissionPriceService commissionPriceService)
    {
        this.commissionPriceService = commissionPriceService;
    }
    /// <summary>
    /// Create a commission price for specific product
    /// </summary>
    /// <param name="request">An object containing the details of the product to be created.</param>
    /// <returns>
    ///  An <see cref="IActionResult"/> indicating the outcome of the operation.
    /// Returns a 200 OK status if the operation is successful.
    /// Returns a 400 Bad Request status if the input data is invalid.
    /// Returns a 500 Internal Server Error status if an unexpected error occurs.
    /// </returns>
    [HttpPost("CreateProductCommissionPrices")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreateAsync([FromBody] ProductCommissionPriceCreateRequest request)
    {
        var result = await commissionPriceService.CreateAsync(request);
        return Ok(result);
    }
    /// <summary>
    /// Updates the commission prices for a specific product.
    /// </summary>
    /// <param name="id">The unique identifier of the product to be updated.</param>
    /// <param name="request">An object containing the updated commission price details.</param>
    /// <returns>
    /// An <see cref="IActionResult"/> indicating the outcome of the operation.
    /// Returns a 200 OK status with the updated commission price if the operation is successful.
    /// Returns a 400 Bad Request status if the input data is invalid.
    /// Returns 404 not found status if the id us wronh and not existed.
    /// Returns a 500 Internal Server Error status if an unexpected error occurs.
    /// </returns>
    [HttpPut("UpdateProductCommissionPrices/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> UpdateAsync(Guid id, [FromBody] ProductComissionPriceUpdateRequest request)
    {
        var result = await commissionPriceService.UpdateAsync(id, request);
        return Ok(result);
    }
    /// <summary>
    /// Get product commission prices list.
    /// </summary>
    /// <param name="request">An object containing pagination, search filter, and sort data.</param>
    /// <returns>
    /// An <see cref="IActionResult"/> indicating the outcome of the operation.
    /// Returns a 200 OK status if the operation is successful.
    /// Returns a 400 Bad Request status if the request or input data is invalid.
    /// Returns a 500 Internal Server Error status if an unexpected error occurs.
    /// </returns>
    [HttpPost("GetProductCommissionPriceList")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> ListAsync([FromBody] ProductCommissionPriceListRequest request)
    {
        var result = await commissionPriceService.GetProductCommissionPriceList(request);
        return Ok(result);
    }
    /// <summary>
    /// Delete addons prices by a list of IDs.
    /// </summary>
    /// <param name="deletedBy"></param>
    /// <param name="ids">List of addons price IDs to delete.</param>
    /// <returns>Returns a status indicating success or failure.</returns>
    [HttpDelete("DeleteProductCommissionPrices")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DeleteProductCommissionPricesAsync([FromQuery] string deletedBy, [FromBody] List<Guid> ids)
    {
        await commissionPriceService.DeleteProductCommissionPricesAsync(ids, deletedBy);

        return Ok(new { Message = "deleted successfully" });
    }
    /// <summary>
    /// Get product commission price details by ID.
    /// </summary>
    /// <param name="id">The ID of the  commission price.</param>
    /// <returns>
    /// Returns the commission price details if found.
    /// </returns>
    [HttpGet("GetCommissionPriceById/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetCommissionPriceByIdAsync(Guid id)
    {
        var productCommissionPrice = await commissionPriceService.GetCommissionPriceByIdAsync(id);
        return Ok(productCommissionPrice);
    }
    /// <summary>
    /// Create or Update product commission prices based on the request data.
    /// </summary>
    /// <param name="request">An object containing the details of the product to be created or updated.</param>
    /// <returns>
    ///  An <see cref="IActionResult"/> indicating the outcome of the operation.
    /// Returns a 200 OK status if the operation is successful.
    /// Returns a 400 Bad Request status if the input data is invalid.
    /// Returns a 500 Internal Server Error status if an unexpected error occurs.
    /// </returns>
    [HttpPost("CreateOrUpdateProductCommissionPrices")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreateOrUpdateAsync([FromBody] ProductCommissionPriceCreateRequest request)
    {
        var result = await commissionPriceService.CreateOrUpdateAsync(request);
        return Ok(result);
    }
    [HttpPost("GetProductCommissionPricesByIdsAsync")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetProductCommissionPricesByIdsAsync([FromBody] List<Guid> ids)
    {
        var commissionPrice = await commissionPriceService.GetProductCommissionPricesByIdsAsync(ids);
        return Ok(commissionPrice);
    }
}
