﻿using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.TerminalDataSets;

[Migration(2024_10_28_1300)]
public class TIDsStockViewScript : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "TIDsStockViewScript.sql"));
    }
}


[Migration(2024_10_28_1400)]
public class DropTIDsStockViewScript : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "DropTIDsStockViewScript.sql"));
    }
}