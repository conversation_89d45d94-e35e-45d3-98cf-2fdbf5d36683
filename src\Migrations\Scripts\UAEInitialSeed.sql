﻿BEGIN TRANSACTION;

DECLAR<PERSON> @CategoryIds TABLE(ID UNIQUEIDENTIFIER)
DECLARE @CategoryId UNIQUEIDENTIFIER

DECLARE @ProductIds TABLE(ID UNIQUEIDENTIFIER)
DECLARE @ProductId UNIQUEIDENTIFIER

DECLARE @PartIds TABLE(ID UNIQUEIDENTIFIER)
DECLAR<PERSON> @PartId UNIQUEIDENTIFIER

DECLARE @PayFamilyCategoryId UNIQUEIDENTIFIER 

DECLARE @PaymentGatewayBundleId UNIQUEIDENTIFIER

DECLARE @MobilePosId UNIQUEIDENTIFIER
DECLARE @SmartPosId UNIQUEIDENTIFIER
DECLARE @SunmiId UNIQUEIDENTIFIER
DECLARE @VerifoneId UNIQUEIDENTIFIER
DECLARE @PaymentGatewayId UNIQUEIDENTIFIER
DECLARE @GeideaGoAppId UNIQUEIDENTIFIER
DECLARE @PosRocketId UNIQUEIDENTI<PERSON>ER
DECLARE @LingaPosId UNIQUEIDENTIFIER
DECLARE @TillPointId UNIQUEIDENTIFIER
DECLARE @SoftPosId UNIQUEIDENTIFIER
DECLARE @ReaderPosId UNIQUEIDENTIFIER
DECLARE @EInvoicingGwId UNIQUEIDENTIFIER
DECLARE @GenerateReferenceId UNIQUEIDENTIFIER
DECLARE @AcceptReferenceId UNIQUEIDENTIFIER
DECLARE @PayByLinkId UNIQUEIDENTIFIER
DECLARE @QrPaymentId UNIQUEIDENTIFIER
DECLARE @CashoutVoucherCreationId UNIQUEIDENTIFIER
DECLARE @CashoutVoucherRedemptionId UNIQUEIDENTIFIER
DECLARE @BillPaymentId UNIQUEIDENTIFIER

--Categories
INSERT INTO CATEGORY(Code, Type, ParentId, DeletedFlag, CreatedBy, CreatedDate, UpdatedBy, UpdatedDate, DisplayOrder, Counterparty) OUTPUT inserted.Id INTO @CategoryIds
VALUES('PAY_FAMILY', 0, NULL, 0, 'n/a', GETUTCDATE(), NULL, NULL, 3, 'GEIDEA_UAE') 

SELECT TOP 1 @PayFamilyCategoryId = ID FROM @CategoryIds 
DELETE FROM @CategoryIds

--Products
INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'MOBILE_POS_SP530', 'M_POS', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @MobilePosId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'SMARTPOS_A920', 'TERMINAL', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @SmartPosId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'SUNMI_P2', 'M_POS', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @SunmiId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'VERIFONE_ANDROID', 'M_POS', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @VerifoneId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'PAYMENT_GATEWAY', 'GWAY', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @PaymentGatewayId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'GEIDEA_GO_APP', 'MINI_ECR', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @GeideaGoAppId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'POS_ROCKET', 'MINI_ECR', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @PosRocketId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'LINGA_POS', 'MINI_ECR', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @LingaPosId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'TILL_POINT', 'MINI_ECR', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @TillPointId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'SOFT_POS', 'TERMINAL', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @SoftPosId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'E_INVOICING_GW', 'GWAY_ADDON', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @EInvoicingGwId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'D135_READER', 'M_POS', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @ReaderPosId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'GENERATE_REFERENCE', 'SERVICES', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @GenerateReferenceId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'ACCEPT_REFERENCE', 'SERVICES', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @AcceptReferenceId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'PAY_BY_LINK', 'SERVICES', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @PayByLinkId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'QR_PAYMENT', 'SERVICES', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @QrPaymentId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'CASHOUT_VOUCHER_CREATION', 'SERVICES', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @CashoutVoucherCreationId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'CASHOUT_VOUCHER_REDEMPTION', 'SERVICES', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @CashoutVoucherRedemptionId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
VALUES('Live', 'BILL_PAYMENT', 'SERVICES', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

SELECT TOP 1 @BillPaymentId = ID FROM @ProductIds 
DELETE FROM @ProductIds

--Bundles
INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Live', 'PAYMENT_GATEWAY_BUNDLE', 'BUNDLE', GETUTCDATE(), 'n/a', GETUTCDATE(), 1, 0, 'GEIDEA_UAE','Normal','All')
 
 SELECT TOP 1 @PaymentGatewayBundleId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

--Associations

------------------------------------------------------------------------------

--Pay Family

 ---Payment Gateway
 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@PaymentGatewayBundleId, @PayFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @PaymentGatewayBundleId, 0, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', 24, NULL)

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @PaymentGatewayId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @GeideaGoAppId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @GenerateReferenceId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @PayByLinkId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @QrPaymentId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @CashoutVoucherCreationId)

--card schemes
INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Live', 'MEEZA', 'SCHEME', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CP', 1, @ProductId, NULL, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CNP', 1, @ProductId, NULL, 180, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CNP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)


 INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Live', 'MEEZA_GW', 'SCHEME', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @ProductId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CP', 1, @ProductId, NULL, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CNP', 1, @ProductId, 200, 250, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CNP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)


INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Live', 'VISA', 'SCHEME', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CP', 1, @ProductId, NULL, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CNP', 1, @ProductId, NULL, 180, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CNP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Live', 'VISA_GW', 'SCHEME', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @ProductId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CP', 1, @ProductId, NULL, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CNP', 1, @ProductId, 200, 250, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CNP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)


INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Live', 'MC', 'SCHEME', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CP', 1, @ProductId, NULL, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CNP', 1, @ProductId, NULL, 180, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CNP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)


INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDate, DisplayOrder, [Version], Counterparty, Flow, SalesChannel) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Live', 'MC_GW', 'SCHEME', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_UAE','Normal',NULL)

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @ProductId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CP', 1, @ProductId, NULL, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CNP', 1, @ProductId, 200, 250, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDate, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CNP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'AED', NULL, NULL)

COMMIT;