﻿using System;
using System.Threading.Tasks;
using Common.Models.CategoryRequests;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;

namespace ProductService.Controllers;

[Route("api/v1/[controller]")]
[ApiController]
public class CategoryController : ControllerBase
{
    private readonly ICategoryService categoryService;

    public CategoryController(ICategoryService categoryService)
    {
        this.categoryService = categoryService;
    }

    /// <summary>
    /// Search categories based on criteria.
    /// </summary>
    /// <response code="200">Returns list of categories</response>

    [HttpPost("Search")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [Produces("application/json")]
    public async Task<IActionResult> SearchCategories([FromBody] FindCategoryRequest findCategoryRequest)
    {
        var categories = await categoryService.FindAsync(findCategoryRequest);
        return Ok(categories);
    }
    /// <summary>
    /// Get Categories List {Id,Name}
    /// </summary>
    /// <returns>Returns Categories List {Id,Name} </returns>
    [HttpGet("GetCategoriesList")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [Produces("application/json")]

    public async Task<IActionResult> GetCategoriesList()
    {
        var CategoriesList = await categoryService.GetCategoriesList();
        return Ok(CategoriesList);
    }
}
