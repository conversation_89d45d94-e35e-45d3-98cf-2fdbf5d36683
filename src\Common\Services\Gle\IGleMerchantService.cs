﻿using System;
using System.Threading.Tasks;
using Common.Models.Gle;
using Microsoft.AspNetCore.JsonPatch;

namespace Common.Services.Gle;

public interface IGleMerchantService
{
    public Task AddGleMerchantAsync(GleMerchantRequest createGleMerchantRequest);
    public Task UpdateGleMerchantAsync(Guid gleMerchantId, JsonPatchDocument<UpdateGleMerchantRequest> updateDocument);
    public Task<GleMerchant?> GetGleMerchantByMerchantIdAsync(Guid merchantId);
    Task<GleMerchant?> GetGleHierarchyByMerchantIdAsync(Guid merchantId);
    Task<bool> IsMerchantRegisteredInGleAsync(Guid merchantId);
}