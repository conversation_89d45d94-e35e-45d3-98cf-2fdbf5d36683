﻿using Common.Data;
using Common.Entities;
using Common.Repositories;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DataAccess.Repositories;
public class MetaDataMigrationFilesRepository : IMetaDataMigrationFilesRepository
{
    private readonly DbContext context;
    public MetaDataMigrationFilesRepository(DbContext Context)
    {
        context = Context;
    }

    public async Task<List<MetaDataMigrationFilesEntity>> GetAllMigrationFiles()
    {
        var query = context.Set<MetaDataMigrationFilesEntity>().AsQueryable().AsNoTracking();
        var MetaDataMigrationFiles = await query.AsNoTracking().ToListAsync();
        return MetaDataMigrationFiles;
    }

    public async Task InsertAsync(MetaDataMigrationFilesEntity migrationFile)
    {
        await context.Set<MetaDataMigrationFilesEntity>().AddAsync(migrationFile);
        await context.SaveChangesAsync();
    }

    public async Task UpdateMigrationFile(Guid metaDataMigrationId, MetaDataMigrationFilesEntity newMigrationFile)
    {
        var metaDataMigrationFilesEntity = context.Set<MetaDataMigrationFilesEntity>().Find(metaDataMigrationId);

        if (metaDataMigrationFilesEntity != null)
        {
            context.Set<MetaDataMigrationFilesEntity>().Remove(metaDataMigrationFilesEntity);

            await InsertAsync(newMigrationFile);
        }
    }

    public async Task<List<MetaDataMigrationFilesEntity>> GetFailedAndPartiallyFailedMigrationFiles()
    {
        var query = context.Set<MetaDataMigrationFilesEntity>().AsQueryable().AsNoTracking();
        var MetaDataMigrationFiles = await query.Where(x => x.Status == MetadataStatus.Failed.ToString() || x.Status == MetadataStatus.PartiallySucceeded.ToString()).ToListAsync();
        return MetaDataMigrationFiles;
    }
}