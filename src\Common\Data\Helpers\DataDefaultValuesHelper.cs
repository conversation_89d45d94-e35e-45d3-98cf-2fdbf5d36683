﻿using Common.Entities;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Models.Rules.AuthenticationRules;
using static Common.Data.Helpers.CounterpartyHelper;
using GatewayDataModel = Geidea.ProductService.Models.GatewayData;

namespace Common.Data.Helpers;

public static class DataDefaultValuesHelper
{
    public static bool IsMerchantCountrySaudi(string? merchantCountry) => merchantCountry is not null && merchantCountry.Equals(Constants.MerchantCountries.Sau);

    public static bool IsMerchantCountryEgypt(string? merchantCountry) => merchantCountry is not null && merchantCountry.Equals(Constants.MerchantCountries.Egy);

    public static bool IsMerchantCountryUae(string? merchantCountry) => merchantCountry is not null && merchantCountry.Equals(Constants.MerchantCountries.Are);

    public static object? SetDefaultValues(object? data) => data is not GatewayData gatewayData ? data : SetDefaultsValuesForGatewayData(gatewayData);

    public static object? SetDefaultValues(object? data, OperationType operationType) => data is not GatewayData gatewayData ? data : SetDefaultsValuesForGatewayData(gatewayData, operationType);

    public static object? SetDefaultValues(ProductInstanceEntity productInstance, string counterparty)
    {
        if (productInstance.Data is not GatewayData gatewayData)
            return productInstance.Data;

        if (string.IsNullOrEmpty(gatewayData.MerchantCountry))
            gatewayData.MerchantCountry = GetGatewayMerchantCountry(counterparty);

        SetDefaultsValuesForGatewayData(gatewayData);

        return productInstance.Data = gatewayData;
    }

    private static object? SetDefaultsValuesForGatewayData(GatewayDataModel gatewayData, OperationType operationType = OperationType.Add)
    {
        SetDefaultIs3dsRequiredForTokenPayments(gatewayData);
        SetDefaultIsCvvRequiredForTokenPayments(gatewayData);
        SetDefaultRules(gatewayData, operationType);

        return gatewayData;
    }

    private static void SetDefaultIs3dsRequiredForTokenPayments(GatewayDataModel gatewayData) =>
        gatewayData.Is3dsRequiredForTokenPayments ??= false;

    private static void SetDefaultIsCvvRequiredForTokenPayments(GatewayDataModel gatewayData) =>
        gatewayData.IsCvvRequiredForTokenPayments ??= false;

    private static void SetDefaultRules(GatewayDataModel gatewayData, OperationType operationType)
    {
        if (!IsMerchantCountryEgypt(gatewayData.MerchantCountry))
            return;

        if (operationType != OperationType.Add)
            return;

        if (gatewayData.Rules.RuleAttemptedAuth?.CardTypes is not null)
            return;

        gatewayData.Rules.RuleAttemptedAuth = new AttemptedAuthenticationRule();
        gatewayData.Rules.RuleAttemptedAuth.SetDefaultValues();
    }
}
