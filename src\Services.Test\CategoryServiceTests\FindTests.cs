﻿using AutoMapper;
using Common.Entities;
using Common.Repositories;
using Microsoft.Extensions.Logging;
using Moq;
using ProductService;
using System;
using System.Threading.Tasks;
using Xunit;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using DataAccess;
using DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Common.Models.CategoryRequests;
using Common;
using Geidea.Utils.Counterparty.Providers;
using Common.Options;
using Microsoft.Extensions.Options;
using NSubstitute;

namespace Services.Test.CategoryServiceTests;

public class FindTests
{
    private readonly Mock<ILogger<CategoryService>> logger = new Mock<ILogger<CategoryService>>();
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly CategoryService categoryService;
    private readonly Guid categoryId = Guid.NewGuid();
    private readonly IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;
    private readonly ICounterpartyProvider counterpartyProvider;
    public FindTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
            .UseInMemoryDatabase(databaseName: "CategoryFindTests" + Guid.NewGuid().ToString())
            .Options;

        var context = new DataContext(options, new CounterpartyProvider());
        ICategoryRepository categoryRepository = new CategoryRepository(context, httpContext.Object);

        context.Categories.AddRange(new List<CategoryEntity>
            {
                new CategoryEntity
                {
                    Id=categoryId,
                    Code="BBB",
                    Type=1,
                    DisplayOrder=1,
                    SalesChannel = Constants.SalesChannel.Shop
                },
                new CategoryEntity
                {
                    Code="AAA",
                    Type=1,
                    DisplayOrder=1,
                    SalesChannel = Constants.SalesChannel.Onboarding
                },
                new CategoryEntity
                {
                    Code="CCC",
                    Type=3,
                    DisplayOrder=0
                }
            });
        context.SaveChanges();
        softposEgyptFeatureToggle = Substitute.For<IOptions<SoftposEgyptFeatureToggle>>();
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = true });
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();
        IProductRepository productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);

        categoryService = new CategoryService(categoryRepository, logger.Object, mapper, productRepository);
    }

    [Fact]
    public async Task FindById()
    {
        var categories = await categoryService.FindAsync(new FindCategoryRequest { CategoryId = categoryId });
        categories.Should().NotBeNull();
        categories.Should().HaveCount(1);
        categories[0].Code.Should().Be("BBB");
    }

    [Fact]
    public async Task FindByCode()
    {
        var categories = await categoryService.FindAsync(new FindCategoryRequest { Code = "AAA" });
        categories.Should().NotBeNull();
        categories.Should().HaveCount(1);
        categories[0].Code.Should().Be("AAA");
    }

    [Fact]
    public async Task FindByType()
    {
        var categories = await categoryService.FindAsync(new FindCategoryRequest { Type = 1 });
        categories.Should().NotBeNull();
        categories.Should().HaveCount(2);
    }

    [Fact]
    public async Task FindByAvailability()
    {
        var categories = await categoryService.FindAsync(new FindCategoryRequest { SalesChannel = Constants.SalesChannel.Onboarding });
        categories.Should().NotBeNull();
        categories.Should().HaveCount(1);
        categories[0].Code.Should().Be("AAA");
    }

    [Fact]
    public async Task FindByMultiple()
    {
        var categories = await categoryService.FindAsync(new FindCategoryRequest { CategoryId = categoryId, Type = 1, Code = "BBB", SalesChannel = Constants.SalesChannel.Shop });
        categories.Should().NotBeNull();
        categories.Should().HaveCount(1);
        categories[0].Code.Should().Be("BBB");
    }

    [Fact]
    public async Task Find_IsOrdered()
    {
        var categories = await categoryService.FindAsync(new FindCategoryRequest());
        categories.Should().NotBeNull();
        categories.Should().HaveCount(3);
        categories[0].Code.Should().Be("CCC");
        categories[1].Code.Should().Be("AAA");
        categories[2].Code.Should().Be("BBB");
    }
}
