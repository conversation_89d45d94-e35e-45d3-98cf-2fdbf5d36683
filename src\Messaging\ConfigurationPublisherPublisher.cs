﻿using System;
using Geidea.Messages.Base;
using GeideaPaymentGateway.Utils.RabbitMQ;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;
using IConnectionFactory = GeideaPaymentGateway.Utils.RabbitMQ.IConnectionFactory;

namespace Messaging;

public abstract class ConfigurationPublisherPublisher
{
    protected readonly IOptionsMonitor<RabbitMqConfig> options;
    protected readonly IHttpContextAccessor contextAccessor;
    protected readonly IConnectionFactory factory;
    protected readonly string exchangeName;
    protected readonly ILogger<ConfigurationPublisherPublisher> logger;

    protected bool IsConfigValid => options?.CurrentValue?.IsValid ?? false;

    protected ConfigurationPublisherPublisher(
        IOptionsMonitor<RabbitMqConfig> options, ILogger<ConfigurationPublisherPublisher> logger, IHttpContextAccessor contextAccessor, IConnectionFactory factory, string exchangeName)
    {
        this.options = options;
        this.contextAccessor = contextAccessor;
        this.exchangeName = exchangeName;
        this.factory = factory;
        this.logger = logger;

        if (!IsConfigValid)
            logger.LogWarning("Configuration for RabbitMQ is missing or incorrect. Updates about added/deleted/updated will not be sent");
    }

    protected Header BuildHeader(string type, string counterparty = "") => new Header
    {
        Type = type,
        Id = Guid.NewGuid(),
        Version = 3,
        Sender = "ProductService",
        CreatedDate = DateTime.UtcNow,
        CorrelationId = contextAccessor.GetCorrelationId(),
        Counterparty = counterparty
    };

    protected void Send(string routingKey, byte[] messageBody)
    {
        try
        {
            using var connection = factory.CreateConnection();
            using var channel = connection.CreateModel();
            channel.ExchangeDeclare(exchangeName, ExchangeType.Fanout, true);
            channel.BasicPublish(exchangeName, routingKey, null, messageBody);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Unable to send configuration updates to RabbitMQ");
        }
    }
}
