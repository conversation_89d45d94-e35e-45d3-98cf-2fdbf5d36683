﻿BEGIN TRANSACTION;

DECLAR<PERSON> @CategoryIds TABLE(ID UNIQUEIDENTIFIER)
DECLARE @CategoryId UNIQUEIDENTIFIER

DECLARE @ProductIds TABLE(ID UNIQUEIDENTIFIER)
DECLARE @ProductId UNIQUEIDENTIFIER

DECLARE @PartIds TABLE(ID UNIQUEIDENTIFIER)
DECLAR<PERSON> @PartId UNIQUEIDENTIFIER

DECLARE @GoFamilyCategoryId UNIQUEIDENTIFIER
DECLARE @PayFamilyCategoryId UNIQUEIDENTIFIER 

DECLARE @GoAirId UNIQUEIDENTIFIER
DECLARE @GoLiteId UNIQUEIDENTIFIER
DECLARE @GoPosId UNIQUEIDENTIFIER
DECLARE @WebsiteBuilderBundleId UNIQUEIDENTIFIER
DECLARE @PaymentGatewayBundleId UNIQUEIDENTIFIER

DECLARE @MobilePosId UNIQUEIDENTIFIER
DECLARE @SmartPosId UNIQUEIDEN<PERSON>FIER
DECLARE @SunmiId UNIQUEIDENTIFIER
DECLARE @VerifoneId UNIQUEIDENTIFIER
DECLARE @PaymentGatewayId UNIQUEIDENTIFIER
DECLARE @GeideaGoAppId UNIQUEIDENTIFIER
DECLARE @PosRocketId UNIQUEIDENTIFIER
DECLARE @LingaPosId UNIQUEIDENTIFIER
DECLARE @TillPointId UNIQUEIDENTIFIER
DECLARE @WebsiteBuilderId UNIQUEIDENTIFIER
DECLARE @SoftPosId UNIQUEIDENTIFIER
DECLARE @ReaderPosId UNIQUEIDENTIFIER
DECLARE @EInvoicingGwId UNIQUEIDENTIFIER
DECLARE @GenerateReferenceId UNIQUEIDENTIFIER
DECLARE @AcceptReferenceId UNIQUEIDENTIFIER
DECLARE @PayByLinkId UNIQUEIDENTIFIER
DECLARE @QrPaymentId UNIQUEIDENTIFIER
DECLARE @CashoutVoucherCreationId UNIQUEIDENTIFIER
DECLARE @CashoutVoucherRedemptionId UNIQUEIDENTIFIER
DECLARE @BillPaymentId UNIQUEIDENTIFIER

--Categories
INSERT INTO CATEGORY(Code, Type, ParentId, DeletedFlag, CreatedBy, CreatedDateUtc, UpdatedBy, UpdatedDateUtc, DisplayOrder, Counterparty) OUTPUT inserted.Id INTO @CategoryIds
VALUES('GO_FAMILY', 0, NULL, 0, 'n/a', GETUTCDATE(), NULL, NULL, 1, 'GEIDEA_EGYPT') 

SELECT TOP 1 @GoFamilyCategoryId = ID FROM @CategoryIds 
DELETE FROM @CategoryIds

INSERT INTO CATEGORY(Code, Type, ParentId, DeletedFlag, CreatedBy, CreatedDateUtc, UpdatedBy, UpdatedDateUtc, DisplayOrder, Counterparty) OUTPUT inserted.Id INTO @CategoryIds
VALUES('PAY_FAMILY', 0, NULL, 0, 'n/a', GETUTCDATE(), NULL, NULL, 3, 'GEIDEA_EGYPT') 

SELECT TOP 1 @PayFamilyCategoryId = ID FROM @CategoryIds 
DELETE FROM @CategoryIds

--Products
INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'MOBILE_POS_SP530', 'M_POS', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @MobilePosId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'SMARTPOS_A920', 'TERMINAL', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @SmartPosId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'SUNMI_P2', 'M_POS', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @SunmiId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'VERIFONE_ANDROID', 'M_POS', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @VerifoneId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'PAYMENT_GATEWAY', 'GWAY', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @PaymentGatewayId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'GEIDEA_GO_APP', 'MINI_ECR', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @GeideaGoAppId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'POS_ROCKET', 'MINI_ECR', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @PosRocketId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'LINGA_POS', 'MINI_ECR', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @LingaPosId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'TILL_POINT', 'MINI_ECR', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @TillPointId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'WEBSITE_BUILDER', 'WSB', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @WebsiteBuilderId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'SOFT_POS', 'TERMINAL', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @SoftPosId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'E_INVOICING_GW', 'GWAY_ADDON', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @EInvoicingGwId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'D135_READER', 'M_POS', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @ReaderPosId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'GENERATE_REFERENCE', 'SERVICES', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @GenerateReferenceId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'ACCEPT_REFERENCE', 'SERVICES', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @AcceptReferenceId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'PAY_BY_LINK', 'SERVICES', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @PayByLinkId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'QR_PAYMENT', 'SERVICES', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @QrPaymentId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'CASHOUT_VOUCHER_CREATION', 'SERVICES', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @CashoutVoucherCreationId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'CASHOUT_VOUCHER_REDEMPTION', 'SERVICES', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @CashoutVoucherRedemptionId = ID FROM @ProductIds 
DELETE FROM @ProductIds

INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
VALUES('PartOf', 'BILL_PAYMENT', 'SERVICES', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

SELECT TOP 1 @BillPaymentId = ID FROM @ProductIds 
DELETE FROM @ProductIds

--Bundles
 INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle_Preorder', 'GO_AIR', 'BUNDLE', GETUTCDATE(), 'n/a', GETUTCDATE(), 1, 0, 'GEIDEA_EGYPT')
 
 SELECT TOP 1 @GoAirId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle_Preorder', 'GO_LITE', 'BUNDLE', GETUTCDATE(), 'n/a', GETUTCDATE(), 2, 0, 'GEIDEA_EGYPT')
 
 SELECT TOP 1 @GoLiteId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle_Preorder', 'GO_POS', 'BUNDLE', GETUTCDATE(), 'n/a', GETUTCDATE(), 3, 0, 'GEIDEA_EGYPT')
 
 SELECT TOP 1 @GoPosId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle', 'WEBSITE_BUILDER_BUNDLE', 'BUNDLE', GETUTCDATE(), 'n/a', GETUTCDATE(), 2, 0, 'GEIDEA_EGYPT')
 
 SELECT TOP 1 @WebsiteBuilderBundleId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Bundle', 'PAYMENT_GATEWAY_BUNDLE', 'BUNDLE', GETUTCDATE(), 'n/a', GETUTCDATE(), 1, 0, 'GEIDEA_EGYPT')
 
 SELECT TOP 1 @PaymentGatewayBundleId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

--Associations

 ---Go Air
 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@GoAirId, @GoFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @GoAirId, 0, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', 24, NULL)
 
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @SoftPosId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @GeideaGoAppId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @AcceptReferenceId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @QrPaymentId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @CashoutVoucherRedemptionId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @BillPaymentId)

 ---Go Lite
 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@GoLiteId, @GoFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @GoLiteId, 0, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', 24, NULL)
 
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @ReaderPosId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @GeideaGoAppId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @AcceptReferenceId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @QrPaymentId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @CashoutVoucherRedemptionId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @BillPaymentId)

 ---Go POS
 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@GoPosId, @GoFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @GoPosId, 0, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', 24, NULL)

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoPosId, @SmartPosId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoPosId, @SunmiId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoPosId, @VerifoneId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoPosId, @GeideaGoAppId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoPosId, @AcceptReferenceId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoPosId, @QrPaymentId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoPosId, @CashoutVoucherRedemptionId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoPosId, @BillPaymentId)

------------------------------------------------------------------------------

--Pay Family
 ---Website Builder
 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@WebsiteBuilderBundleId, @PayFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @WebsiteBuilderBundleId, 39900, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', 24, NULL)
 
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @EInvoicingGwId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @PaymentGatewayId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @GeideaGoAppId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @WebsiteBuilderId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @GenerateReferenceId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @PayByLinkId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @QrPaymentId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @CashoutVoucherCreationId)

 ---Payment Gateway
 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@PaymentGatewayBundleId, @PayFamilyCategoryId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @PaymentGatewayBundleId, 0, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', 24, NULL)

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @PaymentGatewayId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @GeideaGoAppId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @GenerateReferenceId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @PayByLinkId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @QrPaymentId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @CashoutVoucherCreationId)

--card schemes
 INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Normal', 'MEEZA', 'SCHEME', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoPosId, @ProductId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CP', 1, @ProductId, NULL, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CNP', 1, @ProductId, NULL, 180, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CNP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)


 INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Normal', 'MEEZA_GW', 'SCHEME', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @ProductId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CP', 1, @ProductId, NULL, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CNP', 1, @ProductId, 200, 250, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CNP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)


 INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Normal', 'VISA', 'SCHEME', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoPosId, @ProductId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CP', 1, @ProductId, NULL, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CNP', 1, @ProductId, NULL, 180, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CNP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Normal', 'VISA_GW', 'SCHEME', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @ProductId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CP', 1, @ProductId, NULL, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CNP', 1, @ProductId, 200, 250, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CNP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)


 INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Normal', 'MC', 'SCHEME', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoPosId, @ProductId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CP', 1, @ProductId, NULL, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CNP', 1, @ProductId, NULL, 180, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CNP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)


 INSERT INTO PRODUCTS(Availability, Code, Type, ValidFrom, CreatedBy, CreatedDateUtc, DisplayOrder, [Version], Counterparty) OUTPUT inserted.Id INTO @ProductIds
 VALUES('Normal', 'MC_GW', 'SCHEME', GETUTCDATE(), 'n/a', GETUTCDATE(), NULL, 0, 'GEIDEA_EGYPT')

 SELECT TOP 1 @ProductId = ID FROM @ProductIds 
 DELETE FROM @ProductIds

 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @ProductId)
 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @ProductId)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CP', 1, @ProductId, NULL, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'PURCHASE_CNP', 1, @ProductId, 200, 250, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, PercentagePrice, Threshold, [Priority], ValidFrom, DeletedFlag, CreatedBy, CreatedDateUtc, Currency, RentalPeriod, MaxPrice)
 VALUES(NULL, 'REFUND_CNP', 1, @ProductId, 1000, NULL, NULL, 1, GETUTCDATE(), 0, 'n/a', GETUTCDATE(), 'EGP', NULL, NULL)

COMMIT;