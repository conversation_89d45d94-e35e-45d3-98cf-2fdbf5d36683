﻿using System;

namespace Common.Models.ProductInstance;

public class SearchGatewayConfigurationsRequest
{
    public Guid? MerchantGatewayKey { get; init; }
    public Guid? CompanyId { get; init; }
    public Guid? StoreId { get; init; }
    public string? ShahryCpBnplMerchantCode { get; init; }
    public string? ShahryCpBnplBranchCode { get; init; }
    public bool? IsTest { get; init; }
    public string? MeezaMerchantId { get; init; }
    public int Skip { get; init; } = 0;
    public int Take { get; init; } = 100;
}