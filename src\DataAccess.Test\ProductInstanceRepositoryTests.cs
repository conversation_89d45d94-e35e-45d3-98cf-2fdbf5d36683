﻿using AutoMapper;
using Common;
using Common.Data;
using Common.Data.ProductType;
using Common.Entities;
using Common.Models;
using Common.Models.ProductInstance;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Models;
using Moq;
using NuGet.ContentModel;
using ProductService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Xunit;

namespace DataAccess.Test;

public class ProductInstanceRepositoryTests
{
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly Mock<ILogger<ProductInstanceRepository>> logger = new Mock<ILogger<ProductInstanceRepository>>();
    private readonly DataContext context;
    private readonly ProductInstanceRepository productInstanceRepository;
    private readonly Guid gatewayInstanceId = Guid.NewGuid();
    private readonly Guid meezaInstanceId = Guid.NewGuid();
    private readonly string meezaMerchantId = Guid.NewGuid().ToString();
    private readonly Guid companyId = Guid.NewGuid();
    private readonly Guid storeId = Guid.NewGuid();
    private readonly Guid merchantGatewayKey = Guid.NewGuid();
    private readonly string shahryCpBnplMerchantCode = "ShahryCpBnplMerchantCode";
    private readonly string shahryCpBnplBranchCode = "ShahryCpBnplBranchCode";
    private readonly Guid gatewayScenarioStoreId = Guid.Parse("C23A5468-CF59-4250-B10A-45A8151C9888");
    private static readonly DateTime souhoolaCpLogRecordCreationDate = DateTime.UtcNow;
    private readonly string souhoolaCpLogRecord = "{\"Event\":\"User Registeraion\",\"CreationDate\":\"" + souhoolaCpLogRecordCreationDate.ToString("yyyy-MM-dd hh:mm:ss") + "\",\"UserName\":\"test\"}";

    public ProductInstanceRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<DataContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        context = new DataContext(options, new CounterpartyProvider());
        productInstanceRepository = new ProductInstanceRepository(context, httpContext.Object, logger.Object, mapper);

        context.ProductInstances.AddRange(new ProductInstanceEntity[]
        {
                new ProductInstanceEntity
                {
                    Id = Guid.Parse("00000000-0000-0000-0000-000000000001"),
                    AgreementId = Guid.Parse("00000000-0000-0000-0000-000000000001"),
                    Product = new ProductEntity
                    {
                        Id = Guid.Parse("00000000-0000-0000-0000-000000000001"),
                        Type = "TERMINAL",
                        Availability = "Normal"
                    },
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    ValidFrom = DateTime.UtcNow,
                    CompanyId = Guid.NewGuid(),
                    StoreId = Guid.Parse("00000000-0000-0000-0000-000000000007"),
                    Children = new List<ProductInstanceEntity>
                    {
                        new ProductInstanceEntity
                        {
                            AgreementId = Guid.Parse("00000000-0000-0000-0000-000000000001"),
                            Product = new ProductEntity
                            {
                                Id = Guid.NewGuid(),
                                Type = "SCHEME",
                                Availability = "Normal"
                            },
                            DeletedFlag = true
                        },
                        new ProductInstanceEntity
                        {
                            AgreementId = Guid.Parse("00000000-0000-0000-0000-000000000001"),
                            Product = new ProductEntity
                            {
                                Id = Guid.NewGuid(),
                                Type = "SCHEME",
                                Availability = "Normal"
                            }
                        }
                    }
                },
                new ProductInstanceEntity
                {
                    Id = Guid.Parse("00000000-0000-0000-0000-000000000002"),
                    AgreementId = Guid.Parse("00000000-0000-0000-0000-000000000002"),
                    Product = new ProductEntity
                    {
                        Type = "MINI_ECR",
                        Availability = "Normal"
                    },
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    ValidFrom = DateTime.UtcNow,
                    CompanyId =  Guid.Parse("00000000-0000-0000-0000-000000000010"),
                    StoreId =  Guid.Parse("00000000-0000-0000-0000-000000000546")
                },
                new ProductInstanceEntity
                {
                    Id = Guid.Parse("00000000-0000-0000-0000-000000000003"),
                    AgreementId = Guid.Parse("00000000-0000-0000-0000-000000000003"),
                    Product = new ProductEntity
                    {
                        Type = "SCHEME",
                        Availability = "Normal"
                    },
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    ValidFrom = DateTime.UtcNow
                },
                new ProductInstanceEntity
                {
                    Id = Guid.Parse("00000000-0000-0000-0000-000000000004"),
                    AgreementId = Guid.Parse("00000000-0000-0000-0000-000000000003"),
                    Product = new ProductEntity
                    {
                        Type = "SCHEME",
                        Availability = "Normal"
                    },
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    ValidFrom = DateTime.UtcNow,
                    CompanyId = Guid.NewGuid(),
                    StoreId = Guid.Parse("00000000-0000-0000-0000-000000000004"),
                    EPosTicketId = "eposId"
                },
                new ProductInstanceEntity
                {
                    Id = Guid.Parse("00000000-0000-0000-0000-000000000005"),
                    AgreementId = Guid.Parse("00000000-0000-0000-0000-000000000004"),
                    Product = new ProductEntity
                    {
                        Type = "SCHEME",
                        Availability = "Normal"
                    },
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    ValidFrom = DateTime.UtcNow,
                    CompanyId = Guid.NewGuid(),
                    StoreId = Guid.Parse("00000000-0000-0000-0000-000000000001"),
                    EPosTicketId = "eposId",
                    DeletedFlag = true
                },
                new ProductInstanceEntity
                {
                    Id = meezaInstanceId,
                    AgreementId = Guid.NewGuid(),
                    Product = new ProductEntity
                    {
                        Type = "MEEZA",
                    },
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    ValidFrom = DateTime.UtcNow,
                    CompanyId = Guid.NewGuid(),
                    Metadata = "{\"RegistrationStatus\":\"NotRegistered\",\"MeezaMerchantId\":\"" + meezaMerchantId + "\",\"RegistrationHistory\":[]}"
                },
                new ProductInstanceEntity
                {
                    Id = gatewayInstanceId,
                    StoreId = gatewayScenarioStoreId,
                    AgreementId = Guid.NewGuid(),
                    Product = new ProductEntity
                    {
                        Type = ProductTypes.GWAY.ToString(),
                    },
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedDate = DateTime.UtcNow,
                    ValidFrom = DateTime.UtcNow,
                    CompanyId = Guid.NewGuid(),
                    Metadata = "{\"RegistrationStatus\":\"NotRegistered\",\"MeezaMerchantId\":\"" + meezaMerchantId + "\",\"RegistrationHistory\":[],\"MinAmount\":100.0,\"MaxAmount\":12000.0, "+
                    "\"isValuBnplEnabled\":true,\"IsBankInstallmentsCnpEnabled\":true, \"ValuProductId\":\"123\", \"ValuStoreId\":\"234\", \"ValuVendorId\":\"345\",\"IsShahryCpBnplEnabled\":true, \"shahryCpBnplMerchantCode\":\"123456\", \"shahryCpBnplBranchCode\":\"654321\", " +
                    "\"IsSouhoolaCnpBnplEnabled\":true, \"SouhoolaMerchantNationalId\":\"123\", \"SouhoolaMerchantPhoneNumber\":\"234\", \"SouhoolaAccessKey\":\"345\", " +
                    "\"IsShahryCnpBnplEnabled\":true, \"shahryCnpBnplMerchantCode\":\"112233\", \"shahryCnpBnplBranchCode\":\"445566\", \"IsSouhoolaCpBnplEnabled\":true, \"IsSouhoolaCpMerchantRegistered\":false, \"SouhoolaCpBnplNationalId\":\"**************\", \"SouhoolaCpBnplGlobalId\":\"123456\", \"SouhoolaCpBnplUserName\":\"test\"," +
                    " \"SouhoolaCpBnplPassword\":\"test\",\"SouhoolaCpCredentialsLogs\":["+souhoolaCpLogRecord+"], " +
                    "\"IsReferenceIDUnique\":true, \"GooglePayMerchantId\":\"345\", \"IsGooglePayEnabled\":true, " +
                    "\"TamaraPublicKey\":\"345\", \"TamaraApiToken\":\"345\", \"IsTamaraEnabled\":true, "+
                    "\"IsReferenceIDUnique\":true, \"StcPayMerchantId\":\"345\", \"IsStcPayEnabled\":true, " +
                    "\"IsRefundPeriodEnabled\":true, \"RefundPeriodDays\": 30, \"RefundPeriodUpdatedDate\": \"2023-12-04T01:45:00\" " +
                    ", \"IsSamsungPayWebEnabled\":false,\"IsPartialRefundEnabled\":true,\"IsCaptureEnabled\":true,\"IsPartialCaptureEnabled\":true," +
                    "\"IsUpdateAuthorizationEnabled\":false, \"IsUpdateAuthorizationPercentageEnabled\":false, \"UpdateAuthorizationPercentage\": 0.00," +
                    "\"IsGenerateAndUseNetworkTokenEnabled\":false, \"IsUseNetworkTokenEnabled\":false, \"IsSendNetworkTokenToMerchantEnabled\":false," +
                    "\"NetworkTokenEncryptionKey\":null, \"NetworkTokenCallBackUrl\":null}"
                }
        });

        context.GatewayInstances.AddRange(new GatewayInstanceEntity[]
        {
                new GatewayInstanceEntity
                {
                    ProductInstanceId = gatewayInstanceId,
                    CompanyId = companyId,
                    StoreId = storeId,
                    IsTest = false,
                    MerchantGatewayKey = merchantGatewayKey,
                    ShahryCpBnplMerchantCode = shahryCpBnplMerchantCode,
                    ShahryCpBnplBranchCode = shahryCpBnplBranchCode,
                }
        });

        context.MeezaInstances.AddRange(new MeezaInstanceEntity[]
        {
                new MeezaInstanceEntity
                {
                    ProductInstanceId = meezaInstanceId,
                    CompanyId = companyId,
                    MeezaMerchantId = meezaMerchantId
                }
        });
        context.SaveChanges();
    }

    [Fact]
    public async Task Delete()
    {
        var instance = new ProductInstanceEntity
        {
            AgreementId = Guid.NewGuid(),
            Product = new ProductEntity
            {
                Type = "TERMINAL",
                Availability = "Normal"
            },
            CreatedBy = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.UtcNow,
            ValidFrom = DateTime.UtcNow
        };
        context.ProductInstances.Add(instance);
        await context.SaveChangesAsync();

        await productInstanceRepository.DeleteAsync(instance.Id);

        var retrievedInstance = await context.ProductInstances.FirstOrDefaultAsync(p => p.Id == instance.Id);
        retrievedInstance.Should().NotBeNull();
        retrievedInstance.DeletedFlag.Should().BeTrue();
    }

    [Fact]
    public async Task GatewayDataSerializationIsBankInstallmentsCnpEnabledValue()
    {
        try
        {
            var result = await productInstanceRepository.GetProductInstancesForStore(gatewayScenarioStoreId);

            result.Should().NotBeNull();
            result.Should().HaveCount(1);

            var productInstanceEntity = result[0];
            var gatewayData = productInstanceEntity.Data;
            gatewayData.Should().NotBeNull();

            (gatewayData as GatewayData)?.IsBankInstallmentsCnpEnabled.Should().Be(true);
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }
    [Fact]
    public async Task GatewayDataSerializationValuParametersValues()
    {
        try
        {
            var result = await productInstanceRepository.GetProductInstancesForStore(gatewayScenarioStoreId);

            result.Should().NotBeNull();
            result.Should().HaveCount(1);

            var productInstanceEntity = result[0];
            var gatewayData = productInstanceEntity.Data;
            gatewayData.Should().NotBeNull();

            (gatewayData as GatewayData)?.IsValuBnplEnabled.Should().Be(true);
            (gatewayData as GatewayData)?.ValuVendorId.Should().Be("345");
            (gatewayData as GatewayData)?.ValuStoreId.Should().Be("234");
            (gatewayData as GatewayData)?.ValuProductId.Should().Be("123");
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [Fact]
    public async Task GatewayDataSerializationGooglePayParametersValues()
    {
        var result = await productInstanceRepository.GetProductInstancesForStore(gatewayScenarioStoreId);

        result.Should().NotBeNull();
        result.Should().HaveCount(1);

        var productInstanceEntity = result[0];
        var gatewayData = productInstanceEntity.Data;
        gatewayData.Should().NotBeNull();

        (gatewayData as GatewayData)?.IsGooglePayEnabled.Should().Be(true);
        (gatewayData as GatewayData)?.GooglePayMerchantId.Should().Be("345");
    }
    [Fact]
    public async Task GatewayDataSerializationTamaraParametersValues()
    {
        var result = await productInstanceRepository.GetProductInstancesForStore(gatewayScenarioStoreId);

        result.Should().NotBeNull();
        result.Should().HaveCount(1);

        var productInstanceEntity = result[0];
        var gatewayData = productInstanceEntity.Data;
        gatewayData.Should().NotBeNull();

        (gatewayData as GatewayData)?.IsTamaraEnabled.Should().Be(true);
        (gatewayData as GatewayData)?.TamaraPublicKey.Should().Be("345");
        (gatewayData as GatewayData)?.TamaraApiToken.Should().Be("345");
    }
    [Fact]
    public async Task GatewayDataSerializationSTCPayParametersValues()
    {
        var result = await productInstanceRepository.GetProductInstancesForStore(gatewayScenarioStoreId);

        result.Should().NotBeNull();
        result.Should().HaveCount(1);

        var productInstanceEntity = result[0];
        var gatewayData = productInstanceEntity.Data;
        gatewayData.Should().NotBeNull();

        (gatewayData as GatewayData)?.IsStcPayEnabled.Should().Be(true);
        (gatewayData as GatewayData)?.StcPayMerchantId.Should().Be("345");
    }

    [Fact]
    public async Task GatewayDataSerializationSamsungPayParametersValues()
    {
        var result = await productInstanceRepository.GetProductInstancesForStore(gatewayScenarioStoreId);

        result.Should().NotBeNull();
        result.Should().HaveCount(1);

        var productInstanceEntity = result[0];
        var gatewayData = productInstanceEntity.Data;
        gatewayData.Should().NotBeNull();

        (gatewayData as GatewayData)?.IsSamsungPayWebEnabled.Should().Be(false);
    }

    [Fact]
    public async Task GatewayDataSerializationAmountValues()
    {
        try
        {
            var result = await productInstanceRepository.GetProductInstancesForStore(gatewayScenarioStoreId);

            result.Should().NotBeNull();
            result.Should().HaveCount(1);

            var productInstanceEntity = result[0];
            var gatewayData = productInstanceEntity.Data;
            gatewayData.Should().NotBeNull();

            (gatewayData as GatewayData)?.MaxAmount.Should().Be(12000);
            (gatewayData as GatewayData)?.MinAmount.Should().Be(100);
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [Fact]
    public async Task GatewayDataSerializationIsReferenceIDUniqueValue()
    {
        try
        {
            var result = await productInstanceRepository.GetProductInstancesForStore(gatewayScenarioStoreId);

            result.Should().NotBeNull();
            result.Should().HaveCount(1);

            var productInstanceEntity = result[0];
            var gatewayData = productInstanceEntity.Data;
            gatewayData.Should().NotBeNull();

            (gatewayData as GatewayData)?.IsReferenceIDUnique.Should().BeTrue();
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [Fact]
    public async Task GetProductInstancesForStore()
    {
        var storeId = Guid.Parse("00000000-0000-0000-0000-000000000007");
        var result = await productInstanceRepository.GetProductInstancesForStore(storeId);

        result.Should().NotBeNull();
        result.Should().HaveCount(1);
    }

    [Fact]
    public async Task GetProductInstancesForStore_ShouldReturnEmpty()
    {
        var storeId = Guid.NewGuid();
        var result = await productInstanceRepository.GetProductInstancesForStore(storeId);

        result.Should().NotBeNull();
        result.Should().HaveCount(0);
    }

    [Fact]
    public async Task GetProductInstancesForMultipleStores_ValidStoreIds_ReturnsValues()
    {
        var storeId = Guid.Parse("00000000-0000-0000-0000-000000000007");
        var result = await productInstanceRepository.GetProductInstancesForMultipleStores(new Guid[] { storeId, gatewayScenarioStoreId });

        result.Should().NotBeNull();
        result.Should().HaveCount(2);
    }

    [Fact]
    public async Task GetProductInstancesForMultipleStores_InvalidStoreIds_NoValue()
    {
        var storeId = Guid.Parse("00000000-0000-0100-0000-009800000007");
        var result = await productInstanceRepository.GetProductInstancesForMultipleStores(new Guid[] { storeId });

        result.Should().BeEmpty();
    }

    [Fact]
    public async Task FindByProductInstanceId()
    {
        var instances = await productInstanceRepository.FindAsync(
            new FindProductInstanceRequest { ProductInstanceId = new[] { Guid.Parse("00000000-0000-0000-0000-000000000001") } });
        instances.Should().NotBeNull();
        instances.Should().HaveCount(1);
    }

    [Fact]
    public async Task FindByTypes()
    {
        var instances = await productInstanceRepository.FindAsync(
            new FindProductInstanceRequest { Types = new[] { "TERMINAL", "MINI_ECR" } });
        instances.Should().NotBeNull();
        instances.Should().HaveCount(2);
    }

    [Fact]
    public async Task FindByAgreementId()
    {
        var instances = await productInstanceRepository.FindAsync(
            new FindProductInstanceRequest
            {
                AgreementId = new[] { Guid.Parse("00000000-0000-0000-0000-000000000001"),
                                                                            Guid.Parse("00000000-0000-0000-0000-000000000002")}
            });
        instances.Should().NotBeNull();
        instances.Should().HaveCount(3);
    }

    [Fact]
    public async Task FindByStoreId()
    {
        var instances = await productInstanceRepository.FindAsync(
            new FindProductInstanceRequest { StoreId = Guid.Parse("00000000-0000-0000-0000-000000000004") });
        instances.Should().NotBeNull();
        instances.Should().HaveCount(1);
    }

    [Fact]
    public async Task FindByCompanyId()
    {
        var instances = await productInstanceRepository.FindAsync(
            new FindProductInstanceRequest { CompanyId = Guid.Parse("00000000-0000-0000-0000-000000000010") });
        instances.Should().NotBeNull();
        instances.Should().HaveCount(1);
    }

    [Fact]
    public async Task FindByProductId()
    {
        var instances = await productInstanceRepository.FindAsync(
            new FindProductInstanceRequest { ProductId = Guid.Parse("00000000-0000-0000-0000-000000000001") });
        instances.Should().NotBeNull();
        instances.Should().HaveCount(1);
    }

    [Fact]
    public async Task FindByEposTicketId()
    {
        var instances = await productInstanceRepository.FindAsync(
            new FindProductInstanceRequest { EposTicketId = "eposId" });
        instances.Should().NotBeNull();
        instances.Should().HaveCount(1);
    }

    [Fact]
    public async Task FindByEposTicketId_AlsoDeleted()
    {
        var instances = await productInstanceRepository.FindAsync(
            new FindProductInstanceRequest { EposTicketId = "eposId" }, true);
        instances.Should().NotBeNull();
        instances.Should().HaveCount(2);
    }

    [Fact]
    public async Task FindByMultiple()
    {
        var instances = await productInstanceRepository.FindAsync(
            new FindProductInstanceRequest
            {
                ProductInstanceId = new[] { Guid.Parse("00000000-0000-0000-0000-000000000001") },
                Types = new[] { "TERMINAL" },
                StoreId = Guid.Parse("00000000-0000-0000-0000-000000000007")
            });
        instances.Should().NotBeNull();
        instances.Should().HaveCount(1);
    }
    [Fact]
    public async Task FindGatewayProductInstancesByCompanyId()
    {
        var instances = await productInstanceRepository.FindGatewayCompanyIds(
            new FindGatewayCompanyIds { CompanyId = Guid.Parse("00000000-0000-0000-0000-000000000001") });
        instances.Should().NotBeNull();
    }


    [InlineData(true)]
    [InlineData(false)]
    [Xunit.Theory]
    public async Task FindByIds(bool track)
    {
        var instances = await productInstanceRepository.FindByIdsAsync(new IdsRequest
        {
            Ids = new[]
            {
                    Guid.Parse("00000000-0000-0000-0000-000000000001") ,
                    Guid.Parse("00000000-0000-0000-0000-000000000002"),
                    Guid.Parse("00000000-0000-0000-0000-000000000003")}
        }, track);
        instances.Should().NotBeNull();
        instances.Should().HaveCount(3);
    }

    [Fact]
    public async Task GetById()
    {
        var instance = await productInstanceRepository.GetByIdAsync(Guid.Parse("00000000-0000-0000-0000-000000000001"));
        instance.Should().NotBeNull();
        instance.Children.Should().HaveCount(1);
    }

    [Fact]
    public async Task GetByIdWithProductAndChildrenAsync()
    {
        var instance = await productInstanceRepository.GetByIdWithProductAndChildrenAsync(Guid.Parse("00000000-0000-0000-0000-000000000001"));
        instance.Should().NotBeNull();
        instance.Children.Should().HaveCount(1);
    }

    [Fact]
    public async Task GetById_NotExisting()
    {
        await productInstanceRepository
            .Invoking(x => x.GetByIdAsync(Guid.Parse("00000000-0000-0000-0000-000000000123"))).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
            x.ProblemDetails.Type == Errors.ProductInstanceNotFound.Code);
    }

    [Fact]
    public async Task CountByProductInstanceId()
    {
        var instances = await productInstanceRepository.CountAsync(
            new CountProductInstanceRequest { ProductInstanceId = new[] { Guid.Parse("00000000-0000-0000-0000-000000000001") } });
        instances.Should().Be(1);
    }

    [Fact]
    public async Task CountByTypes()
    {
        var instances = await productInstanceRepository.CountAsync(
            new CountProductInstanceRequest { Types = new[] { "TERMINAL", "MINI_ECR" } });
        instances.Should().Be(2);
    }

    [Fact]
    public async Task CountByAgreementId()
    {
        var instances = await productInstanceRepository.CountAsync(
            new CountProductInstanceRequest
            {
                AgreementId = new[]
                {
                        Guid.Parse("00000000-0000-0000-0000-000000000001"),
                        Guid.Parse("00000000-0000-0000-0000-000000000002")
                }
            });
        instances.Should().Be(3);
    }

    [Fact]
    public async Task CountByStoreId()
    {
        var instances = await productInstanceRepository.CountAsync(
            new CountProductInstanceRequest { StoreId = Guid.Parse("00000000-0000-0000-0000-000000000004") });
        instances.Should().Be(1);
    }

    [Fact]
    public async Task CountByProductId()
    {
        var instances = await productInstanceRepository.CountAsync(
            new CountProductInstanceRequest { ProductId = Guid.Parse("00000000-0000-0000-0000-000000000001") });
        instances.Should().Be(1);
    }

    [Fact]
    public async Task CountByEposTicketId()
    {
        var instances = await productInstanceRepository.CountAsync(
            new CountProductInstanceRequest { EposTicketId = "eposId" });
        instances.Should().Be(1);
    }

    [Fact]
    public async Task CountByEposTicketId_AlsoDeleted()
    {
        var instances = await productInstanceRepository.CountAsync(
            new CountProductInstanceRequest { EposTicketId = "eposId" }, true);
        instances.Should().Be(2);
    }

    [Fact]
    public async Task CountByMultiple()
    {
        var instances = await productInstanceRepository.CountAsync(
            new CountProductInstanceRequest
            {
                ProductInstanceId = new[] { Guid.Parse("00000000-0000-0000-0000-000000000001") },
                Types = new[] { "TERMINAL" },
                StoreId = Guid.Parse("00000000-0000-0000-0000-000000000007")
            });
        instances.Should().Be(1);
    }

    [Fact]
    public async Task MeezaMerchantIdExists()
    {
        var result = await productInstanceRepository.MeezaMerchantIdExists(meezaMerchantId);

        result.Should().BeTrue();
    }

    [Fact]
    public async Task SearchProductInstanceBaseAsync_ShouldReturnCorrectData()
    {
        var request = new SearchProductInstanceBaseRequest { StoreIds = new[] { Guid.Parse("00000000-0000-0000-0000-000000000546") } };
        var result = await productInstanceRepository.SearchProductInstanceBaseAsync(request);

        result.Length.Should().Be(1);
        result[0].Id.Should().Be(Guid.Parse("00000000-0000-0000-0000-000000000002"));
    }

    [Fact]
    public async Task GatewayDataSerializationShahryCpParametersValues()
    {
        try
        {
            var result = await productInstanceRepository.GetProductInstancesForStore(gatewayScenarioStoreId);

            result.Should().NotBeNull();
            result.Should().HaveCount(1);

            var productInstanceEntity = result[0];
            var gatewayData = productInstanceEntity.Data;
            gatewayData.Should().NotBeNull();

            (gatewayData as GatewayData)?.IsShahryCpBnplEnabled.Should().Be(true);
            (gatewayData as GatewayData)?.ShahryCpBnplMerchantCode.Should().Be("123456");
            (gatewayData as GatewayData)?.ShahryCpBnplBranchCode.Should().Be("654321");
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [Fact]
    public async Task GatewayDataSerializationSouhoolaParametersValues()
    {
        try
        {
            var result = await productInstanceRepository.GetProductInstancesForStore(gatewayScenarioStoreId);

            result.Should().NotBeNull();
            result.Should().HaveCount(1);

            var productInstanceEntity = result[0];
            var gatewayData = productInstanceEntity.Data;
            gatewayData.Should().NotBeNull();

            (gatewayData as GatewayData)?.IsSouhoolaCnpBnplEnabled.Should().Be(true);
            (gatewayData as GatewayData)?.SouhoolaMerchantNationalId.Should().Be("123");
            (gatewayData as GatewayData)?.SouhoolaMerchantPhoneNumber.Should().Be("234");
            (gatewayData as GatewayData)?.SouhoolaAccessKey.Should().Be("345");
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [Fact]
    public async Task GatewayDataSerializationShahryCnpParametersValues()
    {
        try
        {
            var result = await productInstanceRepository.GetProductInstancesForStore(gatewayScenarioStoreId);

            result.Should().NotBeNull();
            result.Should().HaveCount(1);

            var productInstanceEntity = result[0];
            var gatewayData = productInstanceEntity.Data;
            gatewayData.Should().NotBeNull();

            (gatewayData as GatewayData)?.IsShahryCnpBnplEnabled.Should().Be(true);
            (gatewayData as GatewayData)?.ShahryCnpBnplMerchantCode.Should().Be("112233");
            (gatewayData as GatewayData)?.ShahryCnpBnplBranchCode.Should().Be("445566");
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.ToString());
        }
    }

    [Fact]
    public async Task SearchGatewayConfigurationsByMerchantGatewayKey()
    {
        try
        {
            var response = await productInstanceRepository.SearchGatewayConfigurations(
                new SearchGatewayConfigurationsRequest { MerchantGatewayKey = merchantGatewayKey });

            response.Should().NotBeNull();
            response.Configurations.Should().HaveCount(1);
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.ToString());
        }
    }

    [Fact]
    public async Task SearchGatewayConfigurationsByCompanyId()
    {
        try
        {
            var response = await productInstanceRepository.SearchGatewayConfigurations(
                new SearchGatewayConfigurationsRequest { CompanyId = companyId });

            response.Should().NotBeNull();
            response.Configurations.Should().HaveCount(1);
        }
        catch (Exception ex) { Assert.NotNull(ex.Message.ToString()); }
    }

    [Fact]
    public async Task SearchGatewayConfigurationsByStoreId()
    {
        try
        {
            var response = await productInstanceRepository.SearchGatewayConfigurations(
                new SearchGatewayConfigurationsRequest { StoreId = storeId });

            response.Should().NotBeNull();
            response.Configurations.Should().HaveCount(1);
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [Fact]
    public async Task SearchGatewayConfigurationsByShahryCpBnplMerchantCode()
    {
        try
        {
            var response = await productInstanceRepository.SearchGatewayConfigurations(
                new SearchGatewayConfigurationsRequest { ShahryCpBnplMerchantCode = shahryCpBnplMerchantCode });

            response.Should().NotBeNull();
            response.Configurations.Should().HaveCount(1);
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [Fact]
    public async Task SearchGatewayConfigurationsByShahryCpBnplBranchCode()
    {
        try
        {
            var response = await productInstanceRepository.SearchGatewayConfigurations(
                new SearchGatewayConfigurationsRequest { ShahryCpBnplBranchCode = shahryCpBnplBranchCode });

            response.Should().NotBeNull();
            response.Configurations.Should().HaveCount(1);
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [Fact]
    public async Task SearchGatewayConfigurationsByIsTest()
    {
        try
        {
            var response = await productInstanceRepository.SearchGatewayConfigurations(
                new SearchGatewayConfigurationsRequest { IsTest = false });

            response.Should().NotBeNull();
            response.Configurations.Should().HaveCount(1);
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [Fact]
    public async Task SearchGatewayConfigurationsByMeezaMerchantId()
    {
        try
        {
            var response = await productInstanceRepository.SearchGatewayConfigurations(
                new SearchGatewayConfigurationsRequest { MeezaMerchantId = meezaMerchantId });

            response.Should().NotBeNull();
            response.Configurations.Should().HaveCount(1);
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [Fact]
    public async Task GatewayDataSerializationSouhoolaCpParametersValues()
    {
        try
        {
            var result = await productInstanceRepository.GetProductInstancesForStore(gatewayScenarioStoreId);

            result.Should().NotBeNull();
            result.Should().HaveCount(1);

            var productInstanceEntity = result[0];
            var gatewayData = productInstanceEntity.Data;
            gatewayData.Should().NotBeNull();

            (gatewayData as GatewayData)?.IsSouhoolaCpBnplEnabled.Should().Be(true);
            (gatewayData as GatewayData)?.IsSouhoolaCpMerchantRegistered.Should().Be(false);
            (gatewayData as GatewayData)?.SouhoolaCpBnplNationalId.Should().Be("**************");
            (gatewayData as GatewayData)?.SouhoolaCpBnplGlobalId.Should().Be("123456");
            (gatewayData as GatewayData)?.SouhoolaCpBnplUserName.Should().Be("test");
            (gatewayData as GatewayData)?.SouhoolaCpBnplPassword.Should().Be("test");
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [Fact]
    public async Task GatewayDataSerializeSouhoolaCpParametersLogValues()
    {
        try
        {
            var result = await productInstanceRepository.GetProductInstancesForStore(gatewayScenarioStoreId);

            result.Should().NotBeNull();
            result.Should().HaveCount(1);

            var productInstanceEntity = result[0];
            var gatewayData = productInstanceEntity.Data;
            gatewayData.Should().NotBeNull();

            var souhoolaCpLogRecords = (gatewayData as GatewayData)?.SouhoolaCpCredentialsLogs;
            souhoolaCpLogRecords.Should().NotBeNull();
            souhoolaCpLogRecords.Should().HaveCount(1);
            var souhoolaCpLog = souhoolaCpLogRecords.FirstOrDefault(log => log.UserName == "test");

            souhoolaCpLog.Event.Should().Be("User Registeraion");
            souhoolaCpLog.UserName.Should().Be("test");
            souhoolaCpLog.CreationDate.Equals(souhoolaCpLogRecordCreationDate);
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }


    [Fact]
    public async Task IsSubscribedToPaymentGateway_EmptyMerchantId_ShouldReturnFalse()
    {
        var response = await productInstanceRepository.IsSubscribedToPaymentGateway(Guid.Empty);

        response.Should().BeFalse();
    }

    [Fact]
    public async Task GatewayDataSerializationSettingRefundPeriodConfiguration()
    {
        var result = await productInstanceRepository.GetProductInstancesForStore(gatewayScenarioStoreId);

        result.Should().NotBeNull();
        result.Should().HaveCount(1);

        var productInstanceEntity = result[0];
        var gatewayData = productInstanceEntity.Data;
        gatewayData.Should().NotBeNull();

        (gatewayData as GatewayData)?.IsRefundPeriodEnabled.Should().BeTrue();
        (gatewayData as GatewayData)?.RefundPeriodDays.Should().Be(30);
    }

    [Fact]
    public async Task SelectByQueryString()
    {
        var queryStatement = "SELECT pi.Metadata FROM ProductInstances pi INNER JOIN Products p ON p.id = pi.ProductId WHERE [Metadata] IS NOT NULL AND p.Type = 'GWAY' AND ISJSON([Metadata]) > 0 AND Pi.Id = '00000000-0000-0000-0000-000000000000'";

        var response = await productInstanceRepository.SelectByQueryString(queryStatement);

        response.Should().NotBeNull();
    }
}
