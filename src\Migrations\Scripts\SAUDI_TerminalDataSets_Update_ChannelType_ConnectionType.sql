BEGIN TRANSACTION;

--Update Values to New Columns [ChannelType]
UPDATE tds
SET
    tds.[ChannelType] = 'GO_AIR'
FROM 
	[PRODUCTS].[dbo].[TerminalDataSets] Tds
	JOIN [PRODUCTS].[dbo].[ProductInstances] AS ChildPis ON ChildPis.Id = Tds.ProductInstanceId
	JOIN [PRODUCTS].[dbo].[ProductInstances] AS ParentPis ON ParentPis.Id = ChildPis.ParentId 
	JOIN [PRODUCTS].[dbo].[Products] AS P ON P.Id = ParentPis.ProductId
	WHERE P.Counterparty = Tds.Counterparty AND P.Counterparty = 'GEIDEA_SAUDI' 
	AND P.[Type] = 'BUNDLE' AND P.Code = 'GO_AIR';
GO

UPDATE tds
SET
    tds.[ChannelType] = 'GO_SMART'
FROM 
	[PRODUCTS].[dbo].[TerminalDataSets] Tds
	JOIN [PRODUCTS].[dbo].[ProductInstances] AS Child<PERSON><PERSON> ON ChildPis.Id = Tds.ProductInstanceId
	JOIN [PRODUCTS].[dbo].[ProductInstances] AS ParentPis ON ParentPis.Id = ChildPis.ParentId 
	JOIN [PRODUCTS].[dbo].[Products] AS P ON P.Id = ParentPis.ProductId
	WHERE P.Counterparty = Tds.Counterparty AND P.Counterparty = 'GEIDEA_SAUDI' 
	AND P.[Type] = 'BUNDLE' AND P.Code = 'GO_SMART';
GO

UPDATE tds
SET
    tds.[ChannelType] = 'GO_SMART'
FROM 
	[PRODUCTS].[dbo].[TerminalDataSets] tds
	WHERE  tds.Counterparty = 'GEIDEA_SAUDI'  AND tds.[ProductInstanceId] IS NULL  
	AND tds.[Availability] = 'AVAILABLE';
GO

COMMIT;