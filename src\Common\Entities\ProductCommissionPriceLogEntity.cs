﻿using Common.Enums.ProductCommisssionPrices;
using Geidea.Utils.DataAccess.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Entities;
public class ProductCommissionPriceLogEntity : Entity<Guid>
{
    public Guid ProductId { get; set; }
    public Guid MccId { get; set; }
    public Guid BusinessTypeId { get; set; }
    public Guid CommissionFeeId { get; set; }
    public FeeType FeeType { get; set; }
    public decimal FeeValue { get; set; }
    public CommisssionPricesBillingType BillingType { get; set; }
    public PriceBillingFrequency BillingFrequency { get; set; }
    public string? DeletedBy { get; set; }
    public DateTime DeletedDate { get; set; }
}
