﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Common.Entities;
using Common.Entities.Gle;
using Common.Views;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.DataAccess.Entities;
using Microsoft.EntityFrameworkCore;

namespace DataAccess;

public class DataContext : DbContext
{
    public DataContext(DbContextOptions<DataContext> options,
        ICounterpartyProvider counterpartyProvider) : base(options)
    {
        this.counterpartyProvider = counterpartyProvider;
    }

    public DbSet<ProductEntity> Products { get; set; } = null!;
    public DbSet<ProductImage> ProductImages { get; set; } = null!;
    public DbSet<ValueAddedService> ValueAddedServices { get; set; } = null!;
    public DbSet<ProductPartEntity> ProductParts { get; set; } = null!;
    public DbSet<PriceEntity> Prices { get; set; } = null!;
    public DbSet<ProductInstanceEntity> ProductInstances { get; set; } = null!;
    public DbSet<GatewayInstanceEntity> GatewayInstances { get; set; } = null!;
    public DbSet<MeezaInstanceEntity> MeezaInstances { get; set; } = null!;
    public DbSet<CategoryEntity> Categories { get; set; } = null!;
    public DbSet<ProductCategoriesEntity> ProductCategories { get; set; } = null!;
    public DbSet<TerminalDataSetEntity> TerminalDataSets { get; set; } = null!;
    public DbSet<OrderGeneratedReportEntity> OrderGeneratedReports { get; set; } = null!;

    public DbSet<GleMerchantEntity> GleMerchant { get; set; } = null!;
    public DbSet<GleStoreEntity> GleStore { get; set; } = null!;
    public DbSet<GleTerminalEntity> GleTerminal { get; set; } = null!;
    public DbSet<GleUpdateHistoryEntity> GleUpdateHistory { get; set; } = null!;

    public DbSet<MetaDataMigrationFilesEntity> MetaDataMigrationFiles { get; set; } = null!;
    public DbSet<TerminalDetailsEntity> TerminalDetails { get; set; } = null!;
    public DbSet<VendorEntity> Vendors { get; set; } = null!;


    public DbSet<CommissionFeesEntity> CommissionFees { get; set; } = null!;
    public DbSet<NonTransactionalFeesEntity> NonTransactionalFees { get; set; } = null!;
    public DbSet<Mcc> Mcc { get; set; } = null!;
    public DbSet<MccCategory> MccCategory { get; set; } = null!;
    public DbSet<BusinessTypeEntity> BusinessTypeEntities { get; set; } = null!;
    public DbSet<UnitPriceEntity> UnitPriceEntitys { get; set; } = null!;
    public DbSet<ProductCommissionPriceEntity> ProductCommissionPrice { get; set; } = null!;
    public DbSet<NonTransactionalPriceEntity> NonTransactionalFeesPrice { get; set; } = null!;
    public DbSet<UnitPriceLogsEntity> UnitPriceLogs { get; set; } = null!;
    public DbSet<ValueAddedServicePricingEntity> ValueAddedServicePricingEntities { get; set; } = null!;
    public DbSet<ValueAddedServicePricingLogEntity> ValueAddedServicePricingLogs { get; set; } = null!;
    public DbSet<ProductCommissionPriceLogEntity> ProductCommissionPriceLogs { get; set; } = null!;
    public DbSet<NonTransactionalPriceLogEntity> NonTransactionalPriceLog { get; set; } = null!;
    public DbSet<PendingUnitPriceEntity> PendingUnitPrice { get; set; } = null!;
    public DbSet<PendingComissionPriceEntity> PendingComissionPrice { get; set; } = null!;


    //Views
    public virtual DbSet<UnitPriceListView> UnitPriceList { get; set; } = null!;
    public virtual DbSet<ProductCommissionPriceListView> ProductCommissionPriceListView { get; set; } = null!;
    public virtual DbSet<ValueAddedServicePricingListView> ValueAddedServicePricingList { get; set; } = null!;
    public virtual DbSet<NonTransactionalFeesPriceListView> NonTransactionalFeesPriceList { get; set; } = null!;
    public virtual DbSet<ProductCategoryView> ProductCategoryView { get; set; } = null!;
    public virtual DbSet<OnboardingUnitPriceListView> OnboardingUnitPriceList { get; set; } = null!;
    public virtual DbSet<PendingUnitPriceListView> PendingUnitPriceList { get; set; } = null!;


    private readonly ICounterpartyProvider counterpartyProvider;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<VendorEntity>().ToTable("Vendor");
        modelBuilder.Entity<ProductEntity>().ToTable("Products");
        modelBuilder.Entity<ProductEntity>().HasMany(a => a.Prices).WithOne(a => a.Product).HasForeignKey(a => a.ProductId);
        modelBuilder.Entity<ProductEntity>().HasQueryFilter(p => p.Counterparty == counterpartyProvider.GetCode());

        modelBuilder.Entity<ProductPartEntity>().ToTable("ProductParts").HasKey(a => new { a.ProductId, a.PartId });
        modelBuilder.Entity<ProductPartEntity>().HasOne(a => a.Product).WithMany(a => a.Parts).HasForeignKey(a => a.ProductId);
        modelBuilder.Entity<ProductPartEntity>().HasOne(a => a.Part);

        modelBuilder.Entity<PriceEntity>().ToTable("Prices");

        modelBuilder.Entity<ProductInstanceEntity>().ToTable("ProductInstances");
        modelBuilder.Entity<ProductInstanceEntity>()
                    .HasOne(e => e.Product);
        modelBuilder.Entity<ProductInstanceEntity>()
                    .HasMany(x => x.Children)
#pragma warning disable CS8603 // Possible null reference return.
                        .WithOne(x => x.Parent)
#pragma warning restore CS8603 // Possible null reference return.
                        .HasForeignKey(x => x.ParentId);

        modelBuilder.Entity<GatewayInstanceEntity>().ToTable("GatewayInstances").HasKey(a => a.ProductInstanceId);
        modelBuilder.Entity<ProductImage>().ToTable("ProductImages").HasKey(a => a.Id);
        modelBuilder.Entity<ValueAddedService>().ToTable("ValueAddedServices").HasKey(a => a.Id);
        modelBuilder.Entity<GatewayInstanceEntity>().HasOne(a => a.ProductInstance).WithOne(a => a.GatewayInstance);

        modelBuilder.Entity<MeezaInstanceEntity>().ToTable("MeezaInstances").HasKey(a => a.ProductInstanceId);
        modelBuilder.Entity<MeezaInstanceEntity>().HasOne(a => a.ProductInstance).WithOne(a => a.MeezaInstance);

        modelBuilder.Entity<CategoryEntity>().ToTable("Category")
                    .HasMany(x => x.Subcategories)
#pragma warning disable CS8603 // Possible null reference return.
                        .WithOne(x => x.Parent)
#pragma warning restore CS8603 // Possible null reference return.
                        .HasForeignKey(x => x.ParentId);
        modelBuilder.Entity<CategoryEntity>().HasQueryFilter(p => p.Counterparty == counterpartyProvider.GetCode());

        modelBuilder.Entity<ProductCategoriesEntity>().ToTable("ProductCategories").HasKey(a => new { a.CategoryId, a.ProductId });
        modelBuilder.Entity<ProductCategoriesEntity>().HasOne(a => a.Category);
        modelBuilder.Entity<ProductEntity>().HasMany(p => p.Images).WithOne(i => i.Product).HasForeignKey(i => i.ProductId);

        modelBuilder.Entity<TerminalDetailsEntity>().ToTable("TerminalDetails");
        modelBuilder.Entity<TerminalDataSetEntity>(entity =>
        {
            entity.ToTable("TerminalDataSets");

            entity.HasQueryFilter(p => p.Counterparty == counterpartyProvider.GetCode());

            entity.HasIndex(t => new { t.StoreId, t.Counterparty, t.MID }).HasDatabaseName("IX_TerminalDataSet_MID");

            entity.HasIndex(t => new { t.Counterparty, t.StoreId, t.OrderNumber, t.TID }).HasDatabaseName("IX_TerminalDataSet_TID");
        });

        modelBuilder.Entity<OrderGeneratedReportEntity>().ToTable("OrderGeneratedReports");
        modelBuilder.Entity<OrderGeneratedReportEntity>().HasQueryFilter(p => p.Counterparty == counterpartyProvider.GetCode());
        modelBuilder.Entity<OrderGeneratedReportEntity>().HasIndex(p => new { p.ReportName, p.Counterparty, p.AcquiringBank, p.OrderNumber }, "IX_OrderGeneratedReports_ReportOrder");

        modelBuilder.Entity<GleMerchantEntity>().ToTable("GleMerchant").HasKey(a => a.Id);

        modelBuilder.Entity<GleStoreEntity>().ToTable("GleStore").HasKey(a => a.Id);
        modelBuilder.Entity<GleStoreEntity>().HasOne(a => a.GleMerchant).WithMany(a => a.GleStoreEntities);

        modelBuilder.Entity<GleTerminalEntity>().ToTable("GleTerminal").HasKey(a => a.Id);
        modelBuilder.Entity<GleTerminalEntity>().HasOne(a => a.GleStore).WithMany(a => a.GleTerminalEntities);
        modelBuilder.Entity<GleTerminalEntity>().HasOne(a => a.ProductInstance).WithMany(a => a.GleTerminalEntities);

        modelBuilder.Entity<GleUpdateHistoryEntity>().ToTable("GleUpdateHistory").HasKey(a => a.Id);

        modelBuilder.Entity<MetaDataMigrationFilesEntity>()
            .HasIndex(b => b.MigrationFileName)
            .IsUnique();
        modelBuilder.Entity<MetaDataMigrationFilesEntity>()
            .Property(b => b.Id).HasDefaultValueSql("newsequentialid()");

        modelBuilder.Entity<CommissionFeesEntity>().ToTable("CommissionFees").HasKey(c => c.Id);
        modelBuilder.Entity<NonTransactionalFeesEntity>().ToTable("NonTransactionalFees").HasKey(c => c.Id);
        modelBuilder.Entity<Mcc>().HasKey(c => c.Id);
        modelBuilder.Entity<MccCategory>().HasKey(c => c.Id);
        modelBuilder.Entity<UnitPriceLogsEntity>().ToTable("UnitPriceLogs");
        modelBuilder.Entity<ValueAddedServicePricingLogEntity>().ToTable("ValueAddedServicePricingLogs");
        modelBuilder.Entity<ProductCommissionPriceLogEntity>().ToTable("ProductCommissionPriceLog");

        modelBuilder.Entity<BusinessTypeEntity>(entity =>
        {
            entity.ToTable("BusinessTypes");
            entity.HasKey(c => c.Id);
            entity.Property(c => c.Code);
        });
        modelBuilder.Entity<Mcc>().HasOne(m => m.MccCategory).WithMany(c => c.Mccs);

        modelBuilder.Entity<UnitPriceListView>(entity =>
        {
            entity.ToView("UnitPriceList");
            entity.HasKey(u => u.Id);
        });
        modelBuilder.Entity<ProductCommissionPriceListView>(entity =>
        {
            entity.ToView("ProductCommissionPriceList");
            entity.HasKey(u => u.Id);
        });
        modelBuilder.Entity<NonTransactionalFeesPriceListView>(entity =>
        {
            entity.ToView("NonTransactionalFeesPriceList");
            entity.HasKey(u => u.Id);
        });
        modelBuilder.Entity<ProductCategoryView>(entity =>
        {
            entity.ToView("ProductCategoryView");
            entity.HasNoKey();
        });
        modelBuilder.Entity<OnboardingUnitPriceListView>(entity =>
        {
            entity.ToView("OnboardingUnitPriceList");
            entity.HasKey(u => u.Id);
        });
        modelBuilder.Entity<PendingUnitPriceListView>(entity =>
        {
            entity.ToView("PendingUnitPriceList");
            entity.HasKey(u => u.Id);
        });
        modelBuilder.Entity<ValueAddedServicePricingListView>(entity =>
        {
            entity.ToView("ValueAddedServicePricingList");
            entity.HasKey(u => u.Id);
        });
        modelBuilder.Entity<UnitPriceEntity>(entity =>
        {
            entity.ToTable("UnitPrice");
            entity.HasKey(c => c.Id);

            entity.HasOne(up => up.Product)
                .WithMany(p => p.UnitPrices)
                .HasForeignKey(up => up.ProductID)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(up => up.Mcc)
                .WithMany(m => m.UnitPrices)
                .HasForeignKey(up => up.MCCID);

            entity.HasOne(up => up.BusinessType)
                .WithMany(bt => bt.UnitPrices)
                .HasForeignKey(up => up.BusinessTypeID);
        });

        modelBuilder.Entity<PendingUnitPriceEntity>(entity =>
        {
            entity.ToTable("PendingUnitPrice");
            entity.HasKey(c => c.Id);
        });
        modelBuilder.Entity<PendingComissionPriceEntity>(entity =>
        {
            entity.ToTable("PendingComissionPrice");
            entity.HasKey(c => c.Id);
        });
        modelBuilder.Entity<ProductCommissionPriceEntity>(entity =>
        {
            entity.ToTable("ProductCommissionPrice");
            entity.HasKey(c => c.Id);

            entity.HasOne(up => up.Product)
                .WithMany(p => p.CommissionPrices)
                .HasForeignKey(up => up.ProductID)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(up => up.Mcc)
                .WithMany(m => m.CommissionPrices)
                .HasForeignKey(up => up.MCCID);

            entity.HasOne(up => up.BusinessType)
                .WithMany(bt => bt.CommissionPrices)
                .HasForeignKey(up => up.BusinessTypeID);

            entity.HasOne(up => up.CommissionFee)
                .WithMany(bt => bt.CommissionPrices)
                .HasForeignKey(up => up.CommissionFeeID);
        });
        modelBuilder.Entity<NonTransactionalPriceEntity>(entity =>
        {
            entity.ToTable("NonTransactionalFeesPrice");
            entity.HasKey(c => c.Id);

            entity.HasOne(up => up.Product)
                .WithMany(p => p.NonTransactionalPrices)
                .HasForeignKey(up => up.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(up => up.Mcc)
                .WithMany(m => m.NonTransactionalPrices)
                .HasForeignKey(up => up.MccId);

            entity.HasOne(up => up.BusinessType)
                .WithMany(bt => bt.NonTransactionalPrices)
                .HasForeignKey(up => up.BusinessTypeId);

            entity.HasOne(up => up.NonTransactionalFees)
                .WithMany(bt => bt.NonTransactionalPrice)
                .HasForeignKey(up => up.NonTransFeeId);
        });
        modelBuilder.Entity<ValueAddedServicePricingEntity>(entity =>
        {
            entity.ToTable("ValueAddedServicePricing");
            entity.HasKey(c => c.Id);

            entity.HasOne(up => up.Product)
                .WithMany(p => p.ValueAddedServicePricings)
                .HasForeignKey(up => up.ProductID)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(up => up.Mcc)
                .WithMany(m => m.ValueAddedServicePricings)
                .HasForeignKey(up => up.MCCID);

            entity.HasOne(up => up.BusinessType)
                .WithMany(bt => bt.ValueAddedServicePricings)
                .HasForeignKey(up => up.BusinessTypeID);
            entity.HasOne(up => up.ValueAddedService)
             .WithMany(bt => bt.ValueAddedServicePricings)
             .HasForeignKey(up => up.VASID);
        });

        base.OnModelCreating(modelBuilder);
    }

    public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default)
    {
        Save();

        return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
    }
    public override int SaveChanges()
    {
        Save();

        return base.SaveChanges();
    }

    private void Save()
    {
        var utcNow = DateTime.UtcNow;
        const string createdDatePropertyName = nameof(AuditableEntity<Guid>.CreatedDate);
        const string updatedDatePropertyName = nameof(AuditableEntity<Guid>.UpdatedDate);
        const string counterpartyPropertyName = nameof(ProductEntity.Counterparty);

        var addedEntities = ChangeTracker.Entries().Where(e => e.State == EntityState.Added).ToList();
        var editedEntities = ChangeTracker.Entries().Where(e => e.State == EntityState.Modified).ToList();

        addedEntities.ForEach(e =>
        {
            if (e.Properties.Any(p => p.Metadata.Name == counterpartyPropertyName))
                e.Property(counterpartyPropertyName).CurrentValue = counterpartyProvider.GetCode();

            if (!e.Properties.ToList().Exists(p => p.Metadata.Name == createdDatePropertyName)) return;
            e.Property(createdDatePropertyName).CurrentValue = utcNow;
        });

        editedEntities.ForEach(e =>
        {
            if (e.Properties.Any(p => p.Metadata.Name == counterpartyPropertyName))
                e.Property(counterpartyPropertyName).CurrentValue = counterpartyProvider.GetCode();

            if (!e.Properties.ToList().Exists(p => p.Metadata.Name == createdDatePropertyName)) return;
            e.Property(updatedDatePropertyName).CurrentValue = utcNow;
        });
    }
}
