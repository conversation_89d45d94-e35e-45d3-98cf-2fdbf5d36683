﻿using Common.Enums;
using Geidea.Utils.DataAccess.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Entities;
[ExcludeFromCodeCoverage]
public class BusinessTypeEntity : AuditableEntity<Guid>
{
    public string Code { get; set; } = null!;
    public string? Name { get; set; }
    public string? NameAr { get; set; }
    public Status Status { get; set; }
    public ICollection<UnitPriceEntity>? UnitPrices { get; set; }
    public ICollection<ProductCommissionPriceEntity>? CommissionPrices { get; set; }
    public ICollection<ValueAddedServicePricingEntity>? ValueAddedServicePricings { get; set; }
    public ICollection<NonTransactionalPriceEntity>? NonTransactionalPrices { get; set; }
}
