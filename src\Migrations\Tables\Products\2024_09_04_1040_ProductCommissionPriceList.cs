﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.Products;
[Migration(2024_11_04_1040)]
public class ProductCommissionPriceList : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "ProductCommissionPriceListingView.sql")
            );
    }
}
[Migration(2025_02_09_0427)]
public class ProductCommissionPriceList_AddBusinessTypeCode : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "ProductCommissionPriceListingView.sql")
            );
    }
}
[Migration(2025_02_09_0554)]
public class ProductCommissionPriceList_AddBusinessTypeCodeToDto : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "ProductCommissionPriceListingView.sql")
            );
    }
}