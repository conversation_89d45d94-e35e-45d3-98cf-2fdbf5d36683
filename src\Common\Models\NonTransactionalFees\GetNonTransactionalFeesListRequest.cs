﻿using Common.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.NonTransactionalFees;
public class GetNonTransactionalFeesListRequest
{
    [DefaultValue(Constants.NonTransactionalFeesDictionary)]
    public Dictionary<NonTransactionalFeesSearchKey, string> SearchTerms { get; set; } = new Dictionary<NonTransactionalFeesSearchKey, string>();
    public string OrderType { get; set; } = SortType.desc.ToString();
    public string OrderFieldName { get; set; } = "CreatedDate";
    public List<Status>? FilterByStatus { get; set; }
    public int Page { get; set; } = 1;
    public int Size { get; set; } = 10;
}
public enum NonTransactionalFeesSearchKey
{
    Id,
    Name,
    All
}

