﻿using Common.Entities;
using Common.Models.UnitPrice;
using Common.Views;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Common.Repositories;
public interface IUnitPriceRepository : IRepository<Guid, UnitPriceEntity>
{
    Task<int> DeleteBulkAsync(List<Guid> ids);
    void AddRange(IEnumerable<UnitPriceEntity> entities);
    Task SaveUnitPricesAsync(List<UnitPriceEntity> unitPrices);
    Task<List<UnitPriceEntity>> GetExistUnitPrices(Expression<Func<UnitPriceEntity, bool>> predicate);
    Task<UnitPriceEntity?> GetByIdAsync(Guid id);
    Task<UnitPricesListResponse> GetUnitPricesList(UnitPricesListRequest request);
    Task<List<UnitPriceEntity>> GetUnitPricesByIdsAsync(List<Guid> ids);
    Task AddLogsAsync(List<UnitPriceLogsEntity> logs);
    Task<UnitPriceEntity?> GetUnitPriceByIdAsync(Guid id);
    Task<List<ProductCategoryView>> ProductCategoryList(Guid MccId, Guid BusinessTypeId);
    Task UpdateUnitPricesAsync(List<UnitPriceEntity> unitPriceEntities);

}
