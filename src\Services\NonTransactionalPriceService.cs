﻿using AutoMapper;
using Common.Entities;
using Common.Models.NonTransactionalPrice;
using Common.Models.UnitPrice;
using Common.Repositories;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Services;
[ExcludeFromCodeCoverage]
public class NonTransactionalPriceService : INonTransactionalPriceService
{
    private readonly ILogger<NonTransactionalPriceService> logger;
    private readonly INonTransactionalPriceRepository nonTransactionalPriceRepository;
    private readonly IMapper mapper;
    public NonTransactionalPriceService(ILogger<NonTransactionalPriceService> logger, INonTransactionalPriceRepository nonTransactionalPriceRepository, IMapper mapper)
    {
        this.nonTransactionalPriceRepository = nonTransactionalPriceRepository;
        this.mapper = mapper;
        this.logger = logger;
    }

    #region Create
    public async Task<NonTransactionalFeePriceResponse> CreateAsync(NonTransactionalFeePriceCreateRequest request)
    {
        try
        {
            var ProcessResponse = await ProcessNonTransactionalPricesAsync(request);
            await nonTransactionalPriceRepository.SaveNonTransactionalPricesAsync(ProcessResponse.NewNonTransactionalPriceList);

            return new NonTransactionalFeePriceResponse
            {
                NewNonTransactionalPriceList = ProcessResponse.NewNonTransactionalPriceList,
                NewExistedNonTransactionalPriceList = ProcessResponse.NewNonTransactionalPriceList,
            };
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "CreateAsync: An error occured while saving NonTransactional price.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An an error occured while saving NonTransactional price.");
        }

    }
    public async Task<Dictionary<NonTransactionalPriceCombinationKey, NonTransactionalPriceEntity>> GetAllExistedNonTransactionalPrices(NonTransactionalFeePriceCreateRequest request)
    {
        return (await nonTransactionalPriceRepository.GetExistNonTransactionalPrices(pc =>
                                              request.ProductIds.Contains(pc.ProductId) &&
                                              request.MccIds.Contains(pc.MccId) &&
                                              request.BusinessTypeIds.Contains(pc.BusinessTypeId) &&
                                              request.NonTransFeeId.Contains(pc.NonTransFeeId)))
                                             .ToDictionary(pc => new NonTransactionalPriceCombinationKey(pc.ProductId, pc.MccId, pc.BusinessTypeId, pc.NonTransFeeId));
    }
    private async Task<NonTransactionalFeePriceResponse> ProcessNonTransactionalPricesAsync(NonTransactionalFeePriceCreateRequest request)
    {
        var NewNonTransactionalPricesList = new List<NonTransactionalPriceEntity>();
        var ExistedNonTransactionalPricesList = new List<NonTransactionalPriceEntity>();
        var AllExistedNonTransactionalPrices = await GetAllExistedNonTransactionalPrices(request);

        var combinations = from productId in request.ProductIds
                           from mccId in request.MccIds
                           from businessTypeId in request.BusinessTypeIds
                           from NonTransFeeId in request.NonTransFeeId
                           select new NonTransactionalPriceCombinationKey(productId, mccId, businessTypeId, NonTransFeeId);

        foreach (var combination in combinations)
        {
            if (AllExistedNonTransactionalPrices.TryGetValue(combination, out var existingNonTransPrice))
            {
                ExistedNonTransactionalPricesList.Add(existingNonTransPrice);
            }
            else
            {
                var NonTransPrice = mapper.Map<NonTransactionalPriceEntity>(request);
                NonTransPrice.ProductId = combination.ProductId;
                NonTransPrice.MccId = combination.MccId;
                NonTransPrice.BusinessTypeId = combination.BusinessTypeId;
                NonTransPrice.NonTransFeeId = combination.NonTransFeeId;
                NewNonTransactionalPricesList.Add(NonTransPrice);
            }
        }

        return new NonTransactionalFeePriceResponse
        {
            NewNonTransactionalPriceList = NewNonTransactionalPricesList,
            NewExistedNonTransactionalPriceList = ExistedNonTransactionalPricesList,
            OldExistedNonTransactionalPriceList = AllExistedNonTransactionalPrices.Values.ToList()
        };
    }
    #endregion

    #region Update
    public async Task<NonTransactionalPriceDetailsResponse> UpdateAsync(Guid Id, NonTransactionalFeePriceUpdateRequest request)
    {

        var ExistedNonTransactionalPrice = await nonTransactionalPriceRepository.GetByIdAsync(Id);
        if (ExistedNonTransactionalPrice == null)
        {
            logger.LogWarning("No NonTransactional price was found with Id {Id}", Id);
            throw new ServiceException(HttpStatusCode.NotFound, "NonTransactional price not found!");
        }

        try
        {
            ExistedNonTransactionalPrice.FeeValue = request.FeeValue;
            ExistedNonTransactionalPrice.FeeType = request.FeeType;
            ExistedNonTransactionalPrice.BillingType = request.BillingType;
            ExistedNonTransactionalPrice.BillingFrequency = request.BillingFrequency;

            nonTransactionalPriceRepository.Update(ExistedNonTransactionalPrice);
            await nonTransactionalPriceRepository.SaveChangesAsync();

            return mapper.Map<NonTransactionalPriceDetailsResponse>(ExistedNonTransactionalPrice);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "UpdateAsync: An error occured while updating product NonTransactional price.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "UpdateAsync: An error occured while updating product NonTransactional price.");
        }

    }
    #endregion

    #region Get By Id
    public async Task<NonTransactionalPriceDetailsResponse> GetNonTransactionalPriceByIdAsync(Guid id)
    {
        try
        {
            var nonTransactionalPriceEntity = await nonTransactionalPriceRepository.GetNonTransactionalPriceByIdAsync(id);

            if (nonTransactionalPriceEntity == null)
            {
                logger.LogWarning("No nonTransactional price found with Id {Id}.", id);
                throw new ServiceException(HttpStatusCode.NotFound, "nonTransactional price not found.");
            }

            var nonTransactionalPriceDetails = mapper.Map<NonTransactionalPriceDetailsResponse>(nonTransactionalPriceEntity);
            return nonTransactionalPriceDetails;
        }

        catch (Exception ex)
        {
            logger.LogError(ex, "something has went wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, "something has went wrong!");
        }
    }
    #endregion

    #region Listing
    public async Task<NonTransactionalFeesPriceListResponse> GetNonTransactionalPriceList(NonTransactionalFeesPriceListRequest request)
    {
        if (request == null)
        {
            logger.LogError("Invalid Get NonTransactional Price List request.");
            throw new ServiceException(HttpStatusCode.BadRequest, "GetNonTransactionalPriceList: Invalid Get NonTransactional  Price List request.");
        }
        try
        {
            return await nonTransactionalPriceRepository.GetNonTransactionalPriceList(request);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "something has went wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, "GetNonTransactionalPriceList: something has went wrong!");
        }
    }
    #endregion

    #region Delete NonTransactionalPrices 
    public async Task DeleteNonTransactionalPricesAsync(List<Guid> ids, string deletedBy)
    {
        if (!ids.Any())
        {
            logger.LogWarning("DeleteNonTransactionalPricesAsync: No IDs were provided for deletion.");
            throw new ServiceException(HttpStatusCode.BadRequest, "No IDs provided for deletion.");
        }
        var nonTransactionalPricesToDelete = await nonTransactionalPriceRepository.GetNonTransactionalPricesByIdsAsync(ids);

        if (!nonTransactionalPricesToDelete.Any())
        {
            logger.LogWarning("DeleteNonTransactionalPricesAsync: No matching records found for the provided IDs.");
            throw new ServiceException(HttpStatusCode.NotFound, "No matching records found for deletion.");
        }
        try
        {
            var logs = nonTransactionalPricesToDelete.Select(up =>
            {
                var log = mapper.Map<NonTransactionalPriceLogEntity>(up);
                log.DeletedBy = deletedBy;
                log.DeletedDate = DateTime.UtcNow;
                return log;
            }).ToList();

            int affectedRows = await nonTransactionalPriceRepository.DeleteBulkAsync(ids);
            await nonTransactionalPriceRepository.AddLogsAsync(logs);

            logger.LogInformation($"DeleteNonTransactionalPricesAsync: Successfully deleted {affectedRows} NonTransactionalPrices and logged the actions.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "DeleteNonTransactionalPricesAsync: An error occurred while deleting NonTransactionalPrices.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An error occurred while deleting NonTransactionalPrices.");
        }
    }

    #endregion

    #region CreateOrUpdate
    public async Task<NonTransactionalFeePriceResponse> CreateOrUpdateAsync(NonTransactionalFeePriceCreateRequest request)
    {
        var processResponse = await ProcessNonTransactionalPricesForCreateOrUpdateAsync(request);

        // Save new and updated prices to the repository
        if (processResponse.NewNonTransactionalPriceList.Any())
        {
            await nonTransactionalPriceRepository.SaveNonTransactionalPricesAsync(processResponse.NewNonTransactionalPriceList);
        }

        if (processResponse.NewExistedNonTransactionalPriceList.Any())
        {
            await nonTransactionalPriceRepository.UpdateNonTransactionalPricesAsync(processResponse.NewExistedNonTransactionalPriceList);
        }

        return new NonTransactionalFeePriceResponse
        {
            NewNonTransactionalPriceList = processResponse.NewNonTransactionalPriceList,
            NewExistedNonTransactionalPriceList = processResponse.NewExistedNonTransactionalPriceList,
            OldExistedNonTransactionalPriceList = processResponse.OldExistedNonTransactionalPriceList,
        };
    }

    private async Task<NonTransactionalFeePriceResponse> ProcessNonTransactionalPricesForCreateOrUpdateAsync(NonTransactionalFeePriceCreateRequest request)
    {
        var newNonTransactionalPricesList = new List<NonTransactionalPriceEntity>();
        var existedNonTransactionalPricesList = new List<NonTransactionalPriceEntity>();
        var oldExistedNonTransactionalPricesList = new List<NonTransactionalPriceEntity>();

        var allExistedNonTransactionalPrices = await GetAllExistedNonTransactionalPrices(request);

        var combinations = from productId in request.ProductIds
                           from mccId in request.MccIds
                           from businessTypeId in request.BusinessTypeIds
                           from nonTransFeeId in request.NonTransFeeId
                           select new NonTransactionalPriceCombinationKey(productId, mccId, businessTypeId, nonTransFeeId);

        foreach (var combination in combinations)
        {
            if (allExistedNonTransactionalPrices.TryGetValue(combination, out var existingNonTransPrice))
            {
                var oldExistingPrice = JsonConvert.DeserializeObject<NonTransactionalPriceEntity>(
                    JsonConvert.SerializeObject(existingNonTransPrice));

                if (oldExistingPrice != null)
                    oldExistedNonTransactionalPricesList.Add(oldExistingPrice);

                UpdateExistingNonTransactionalPrice(existingNonTransPrice, request);
                existedNonTransactionalPricesList.Add(existingNonTransPrice);
            }
            else
            {
                var nonTransPrice = CreateNewNonTransactionalPrice(request, combination.ProductId, combination.MccId, combination.BusinessTypeId, combination.NonTransFeeId);
                newNonTransactionalPricesList.Add(nonTransPrice);
            }
        }

        return new NonTransactionalFeePriceResponse
        {
            NewNonTransactionalPriceList = newNonTransactionalPricesList,
            NewExistedNonTransactionalPriceList = existedNonTransactionalPricesList,
            OldExistedNonTransactionalPriceList = oldExistedNonTransactionalPricesList
        };
    }
    private void UpdateExistingNonTransactionalPrice(NonTransactionalPriceEntity existingNonTransPrice, NonTransactionalFeePriceCreateRequest request)
    {
        // Update the existing non-transactional price entity with the new data
        existingNonTransPrice.FeeValue = request.FeeValue;
        existingNonTransPrice.FeeType = request.FeeType;
        existingNonTransPrice.BillingType = request.BillingType;
        existingNonTransPrice.BillingFrequency = request.BillingFrequency;
    }
    private NonTransactionalPriceEntity CreateNewNonTransactionalPrice(NonTransactionalFeePriceCreateRequest request, Guid productId, Guid mccId, Guid businessTypeId, Guid nonTransFeeId)
    {
        var nonTransactionalPrice = mapper.Map<NonTransactionalPriceEntity>(request);
        nonTransactionalPrice.ProductId = productId;
        nonTransactionalPrice.MccId = mccId;
        nonTransactionalPrice.BusinessTypeId = businessTypeId;
        nonTransactionalPrice.NonTransFeeId = nonTransFeeId;
        return nonTransactionalPrice;
    }
    #endregion
    public async Task<List<NonTransactionalPriceDetailsResponse>> GetNonTransactionalPricesByIdsAsync(List<Guid> ids)
    {
        var nonTransactionalPriceEntities = await nonTransactionalPriceRepository.GetNonTransactionalPricesByIdsAsync(ids);
        return mapper.Map<List<NonTransactionalPriceDetailsResponse>>(nonTransactionalPriceEntities);
    }
}
