﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.CommissionFees;
[ExcludeFromCodeCoverage]

public class GetCommissionFeesListResponse
{
    public CommissionFeesListResponse[]? CommissionFeesList { get; set; }
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }
}
