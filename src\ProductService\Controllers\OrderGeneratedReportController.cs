﻿using Common.Models.OrderGeneratedReport;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ProductService.Controllers;

[Route("api/v1/[controller]")]
[ApiController]
public class OrderGeneratedReportController : ControllerBase
{
    private readonly IOrderGeneratedReportService orderReportService;

    public OrderGeneratedReportController(IOrderGeneratedReportService orderReportService)
    {
        this.orderReportService = orderReportService;
    }

    [HttpPost]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AddOrdersReport(OrdersReportRequest reportOrdersRequest)
    {
        await orderReportService.AddOrdersReport(reportOrdersRequest);
        return NoContent();
    }
}
