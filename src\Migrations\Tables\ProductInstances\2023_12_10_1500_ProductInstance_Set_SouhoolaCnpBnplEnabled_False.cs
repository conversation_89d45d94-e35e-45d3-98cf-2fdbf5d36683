﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2023_12_10_1500)]
public class ProductInstance_Set_SouhoolaCnpBnplEnabled_False : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
                UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsSouhoolaCnpBnplEnabled', CAST(0 as BIT))
	            FROM ProductInstances pi
                INNER JOIN Products p ON p.id = pi.ProductId
                WHERE [Metadata] IS NOT NULL
                AND p.Type = 'GWAY' 
                AND ISJSON([Metadata]) > 0 
            ");
    }
}
