﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Models;
using Common.Models.Gle;
using Microsoft.AspNetCore.JsonPatch;

namespace Common.Services.Gle;

public interface IGleTerminalService
{
    public Task AddGleTerminalAsync(GleTerminalRequest createGleTerminalRequest);
    public Task UpdateGleTerminalAsync(Guid gleTerminalId, JsonPatchDocument<UpdateGleTerminalRequest> updateDocument);
    public Task<GleTerminal?> GetGleTerminalByProductInstanceIdAsync(Guid productInstanceId);
    public Task<IReadOnlyCollection<GleTerminalRequest>> GetGleTerminalListByOrderIdAsync(Guid orderId);
    Task<IReadOnlyCollection<GleRegistrationStatusInfo>> GetGleRegistrationStatusHistoryAsync(GleHistoryLogRequest gleHistoryLogRequest);
    Task<string> CalculateBillPaymentsStatusAsync(Guid orderId, IdsRequest productIdsRequest);
    Task<string?> CalculateGleRegistrationStatusForOrderWithBpAsync(Guid orderId);
}