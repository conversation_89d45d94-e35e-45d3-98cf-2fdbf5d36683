﻿using Common.Entities;
using Common.Models.CommissionFees;
using Common.Models.ValueAddedServices;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Services;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;

namespace ProductService.Controllers;
[Route("api/v1/[controller]")]
[ApiController]

public class CommissionFeesController : ControllerBase
{
    private readonly ICommissionFeesService commissionFeesService;
    public CommissionFeesController(ICommissionFeesService commissionFeesService)
    {
        this.commissionFeesService = commissionFeesService;
    }
    /// <summary>
    /// Get Commission Fees List, with search, sort and filter functionalities.
    /// </summary>
    [HttpPost("GetCommissionFeesList")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetCommissionFeesList(GetCommissionFeesListRequest request)
    {
        var CommissionFeesList = await commissionFeesService.GetCommissionFeesList(request);
        return Ok(CommissionFeesList);
    }

    /// <summary>
    /// Create new commission fees to be added in AdminPanel
    /// </summary>
    /// <returns>Returns the created commission fees </returns>
    [HttpPost("CreateCommissionFees")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateAsync([FromBody] CommissionFeesRequest request)
    {
        var createdcommissionFees = await commissionFeesService.CreateAsync(request);
        return Ok(createdcommissionFees);
    }
    /// <summary>
    /// Toggle commission fee record status, active or inactive
    /// </summary>
    /// <param name="Id">The Id of the commission fee record, to change its status, if existed.</param>
    /// <param name="Status">boolean parameter to set the status, 0 for in active, and 1 for active</param>
    [HttpPost("ToggleStatus/{Id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ToggleStatus(Guid Id, [FromHeader] bool Status)
    {
        var result = await commissionFeesService.ToggleStatus(Id, Status);
        if (!result)
            return NotFound();

        return Ok(result);
    }
    /// <summary>
    /// Update an existing Commission Fees in AdminPanel
    /// </summary>
    /// <param name="id">The ID of the commission fees to update</param>
    /// <param name="request">The updated commission fees details</param>
    /// <returns>Returns the updated commission fees</returns>
    [HttpPut("UpdateCommissionFees/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateAsync(Guid id, [FromBody] CommissionFeesRequest request)
    {
        var updatedCommissionFees = await commissionFeesService.UpdateAsync(id, request);
        return Ok(updatedCommissionFees);
    }
    /// <summary>
    /// Get commission fe details by Id
    /// </summary>
    /// <param name="Id">A passed parameter to check if it is existed in database, or not</param>
    /// <returns>Commission fee details of type [CommissionFeeDetailsResponse]</returns>
    [HttpGet("GetCommissionFeeDetails/{Id}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(ProductEntity), statusCode: StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DetailsAsync(Guid Id)
    {
        var CommissionFeeDetails = await commissionFeesService.GetCommissionFeeDetails(Id);
        return Ok(CommissionFeeDetails);
    }
    /// <summary>
    /// Get commission fees Id, Name
    /// </summary>
    /// <returns>Commission fees list Id, Name</returns>
    [HttpGet("GetCommissionFees")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(CommissionFees), statusCode: StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetCommissionFees()
    {
        var CommissionFeeDetails = await commissionFeesService.GetCommissionFees();
        return Ok(CommissionFeeDetails);
    }
}
