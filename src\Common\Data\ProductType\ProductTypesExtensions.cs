﻿using System;

namespace Common.Data.ProductType;

public static class ProductTypesExtensions
{
    public static Type GetObjectType(this ProductTypes type)
    {
        return type switch
        {
            ProductTypes.TERMINAL => typeof(TerminalData),
            ProductTypes.M_POS => typeof(TerminalData),
            ProductTypes.MINI_ECR => typeof(MiniEcrData),
            ProductTypes.MEEZA => typeof(MeezaData),
            ProductTypes.GWAY => typeof(GatewayData),
            _ => typeof(object)
        };

    }
}
