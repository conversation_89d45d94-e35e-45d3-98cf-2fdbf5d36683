﻿using Common.Enums;
using Geidea.Utils.DataAccess.Entities;
using System;
using System.Collections.Generic;

namespace Common.Entities;

public class ProductEntity : AuditableEntity<Guid>
{
    /// <summary>
    /// "Normal" or "Bundle"
    /// </summary>
    public string Availability { get; set; } = Constants.Availability.Live;
    public string Flow { get; set; } = Constants.Flow.Normal;
    public string? SalesChannel { get; set; }

    /// <summary>
    /// Uniquely identifies a product so versioning is made possible
    /// </summary>
    public string Code { get; set; } = string.Empty;
    public int Version { get; set; } = 0;

    /// <summary>
    /// MINI_ECR, WSB, GW, etc
    /// </summary>
    public string Type { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? DisplayOrder { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public List<PriceEntity> Prices { get; set; } = new List<PriceEntity>();
    public List<ProductPartEntity> Parts { get; set; } = new List<ProductPartEntity>();
    public List<ProductCategoriesEntity> ProductCategories { get; set; } = new List<ProductCategoriesEntity>();
    public string? Counterparty { get; set; }
    public string? CRMProductId { get; set; }
    public string ReferralChannel { get; set; } = Constants.ReferralChannel.Unassigned;
    public bool QuickOnboarding { get; set; }

    //New fileds 
    public string? NameAr { get; set; } = null!; // Arabic Name
    public string? Name { get; set; } = null!; // English Name
    public string? Subname { get; set; } = null!; // English Subname
    public string? SubnameAr { get; set; } = null!; // Arabic Subname
    public string? DescriptionAr { get; set; } = null!; // Arabic Description
    public string? ProductLink { get; set; } = null!; // Product Link
    public bool? IsCNP { get; set; }
    // Navigation property to ProductImages
    public List<ProductImage> Images { get; set; } = new List<ProductImage>();
    public ICollection<UnitPriceEntity>? UnitPrices { get; set; }
    public ICollection<ProductCommissionPriceEntity>? CommissionPrices { get; set; }
    public ICollection<ValueAddedServicePricingEntity>? ValueAddedServicePricings { get; set; }
    public ICollection<NonTransactionalPriceEntity>? NonTransactionalPrices { get; set; }
}
