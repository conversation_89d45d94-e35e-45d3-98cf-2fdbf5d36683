﻿using AutoMapper;
using Common.Entities;
using Common.Enums;
using Common.Enums.ProductCommisssionPrices;
using Common.Models.ValueAddedSerivcePricing;
using Common.Repositories;
using FluentValidation;
using FluentValidation.Results;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using Services;
using System;
using System.Net;
using System.Threading.Tasks;
using Xunit;

namespace Services.Test.ValueAddedServicePricingServiceTests;

public class UpdateTests
{
    private readonly Mock<ILogger<ValueAddedServicePricingService>> loggerMock;
    private readonly Mock<IValueAddedServicePricingRepository> valueAddedServicePricingRepositoryMock;
    private readonly Mock<IMapper> mapperMock;
    private readonly ValueAddedServicePricingService valueAddedServicePricingService;

    public UpdateTests()
    {
        loggerMock = new Mock<ILogger<ValueAddedServicePricingService>>();
        valueAddedServicePricingRepositoryMock = new Mock<IValueAddedServicePricingRepository>();
        mapperMock = new Mock<IMapper>();
        valueAddedServicePricingService = new ValueAddedServicePricingService(loggerMock.Object, valueAddedServicePricingRepositoryMock.Object, mapperMock.Object);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturn_UpdatedVASPriceDetails_When_Successful()
    {
        // Arrange
        var id = Guid.NewGuid();
        var updateRequest = new ValueAddedServicePricingUpdateRequest
        {
            SubscriptionFee = 150,
            FeeType = Common.Enums.UnitPrice.VatType.Percentage,
            BillingType = Common.Enums.UnitPrice.BillingType.PostPaid,
            BillingFrequency = PriceBillingFrequency.Monthly
        };

        var existingEntity = new ValueAddedServicePricingEntity
        {
            Id = id,
            SubscriptionFee = 100,
            FeeType = Common.Enums.UnitPrice.VatType.Flat,
            BillingType = Common.Enums.UnitPrice.BillingType.PrePaid,
            BillingFrequency = PriceBillingFrequency.Annual
        };

        valueAddedServicePricingRepositoryMock.Setup(r => r.GetByIdAsync(id))
            .ReturnsAsync(existingEntity);

        valueAddedServicePricingRepositoryMock.Setup(r => r.SaveChangesAsync())
            .Returns(Task.CompletedTask);

        mapperMock.Setup(m => m.Map<ValueAddedServicesPricingDetails>(existingEntity))
            .Returns(new ValueAddedServicesPricingDetails
            {
                Id = existingEntity.Id,
                SubscriptionFee = updateRequest.SubscriptionFee,
                FeeType = updateRequest.FeeType,
                BillingType = updateRequest.BillingType,
                BillingFrequency = updateRequest.BillingFrequency
            });

        // Act
        var result = await valueAddedServicePricingService.UpdateAsync(id, updateRequest);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(updateRequest.SubscriptionFee, result.SubscriptionFee);
        Assert.Equal(updateRequest.FeeType, result.FeeType);
        Assert.Equal(updateRequest.BillingType, result.BillingType);
        Assert.Equal(updateRequest.BillingFrequency, result.BillingFrequency);

        valueAddedServicePricingRepositoryMock.Verify(r => r.Update(existingEntity), Times.Once);
        valueAddedServicePricingRepositoryMock.Verify(r => r.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_ServiceException_When_VASPriceNotFound()
    {
        // Arrange
        var id = Guid.NewGuid();
        var updateRequest = new ValueAddedServicePricingUpdateRequest
        {
            SubscriptionFee = 150,
            FeeType = Common.Enums.UnitPrice.VatType.Percentage,
            BillingType = Common.Enums.UnitPrice.BillingType.PostPaid,
            BillingFrequency = PriceBillingFrequency.Monthly
        };

        valueAddedServicePricingRepositoryMock.Setup(r => r.GetByIdAsync(id))
            .ReturnsAsync((ValueAddedServicePricingEntity)null);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ServiceException>(() => valueAddedServicePricingService.UpdateAsync(id, updateRequest));

        Assert.NotNull(exception);
        Assert.Equal(HttpStatusCode.InternalServerError, exception.StatusCode);
        valueAddedServicePricingRepositoryMock.Verify(r => r.Update(It.IsAny<ValueAddedServicePricingEntity>()), Times.Never);
        valueAddedServicePricingRepositoryMock.Verify(r => r.SaveChangesAsync(), Times.Never);
    }
}
