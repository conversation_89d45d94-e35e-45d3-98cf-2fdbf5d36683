﻿using AutoMapper;
using Common;
using Common.Entities;
using Common.Models.TerminalDataSets;
using DataAccess.Repositories;
using DataAccess.Test.TestData;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;
using ProductService;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using Common.Models;
using static Common.Constants;
using Common.Services.Acquirers;
using Services.Acquirers;
using System.Diagnostics;
using System.Linq.Expressions;
using System.Linq;

namespace DataAccess.Test;

public class TerminalDataSetRepositoryTests
{
    private DataContext dataContext;
    private TerminalDataSetRepository repository = null!;
    private IMapper mapper;
    private ILogger<TerminalDataSetRepository> logger;
    private IHttpContextAccessor contextAccessor;
    private ICounterpartyProvider counterpartyProvider;
    private readonly Func<string, IAcquirer> acquirerProviderMock = Substitute.For<Func<string, IAcquirer>>();


    private const string TerminalDataSetCountErrorMessage = "Wrong terminal data set count was returned from database.";
    private const string Trsm = "TRSM";
    private static string MCC = "1324";
    private const string CounterPartyProvider = "GEIDEA_SAUDI";

    private readonly TerminalDataSetEntity existingTerminalData1 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0001"),
        AcquiringLedger = "test",
        Availability = Constants.TerminalDataSetAvailability.Available,
        ConfigDate = DateTime.UtcNow,
        Counterparty = "GEIDEA_UAE",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = "**********",
        MID = "test",
        OrderNumber = "test",
        TID = "11111",
        MCC = MCC,
        TRSM = "test",
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow
    };
    private readonly TerminalDataSetEntity existingTerminalData2 = new TerminalDataSetEntity
    {
        AcquiringLedger = "acquiring",
        Availability = Constants.TerminalDataSetAvailability.Used,
        ConfigDate = DateTime.UtcNow - TimeSpan.FromDays(1),
        Counterparty = "GEIDEA_SAUDI",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = "1111111112",
        MID = "test23",
        OrderNumber = "test",
        TID = "11112",
        TRSM = "test24",
        MCC = MCC,
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0551"),
        CountryPrefix = "+20",
        PhoneNumber = "1175491578",
        ChannelType = "test",
        ConnectionType = "ConnectionTest",
        MPGSMID = "MPGSMID",
        MPGSKEY = "MPGSKEY",
        VendorId = Guid.NewGuid()
    };
    private readonly TerminalDataSetEntity existingTerminalData3 = new TerminalDataSetEntity
    {
        AcquiringLedger = "test",
        Availability = Constants.TerminalDataSetAvailability.Available,
        ConfigDate = DateTime.UtcNow + TimeSpan.FromDays(1),
        Counterparty = "GEIDEA_SAUDI",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = "**********",
        MID = "test3456",
        OrderNumber = "test",
        TID = "11113",
        TRSM = Trsm,
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow
    };
    private readonly TerminalDataSetEntity existingTerminalData4 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0004"),
        AcquiringLedger = "test",
        Availability = Constants.TerminalDataSetAvailability.Available,
        ConfigDate = DateTime.UtcNow,
        Counterparty = "GEIDEA_UAE",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = "9999999",
        MID = "test",
        OrderNumber = "test",
        TID = "9999",
        TRSM = "test",
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow
    };
    private readonly TerminalDataSetEntity existingTerminalData5 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0005"),
        AcquiringLedger = "NBE",
        Availability = Constants.TerminalDataSetAvailability.Available,
        ConfigDate = DateTime.UtcNow,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = "9999999",
        MID = "test",
        OrderNumber = "test",
        TID = "9999",
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0015"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow
    };
    private readonly TerminalDataSetEntity existingTerminalData6 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0006"),
        AcquiringLedger = "NBE",
        Availability = Constants.TerminalDataSetAvailability.Available,
        ConfigDate = DateTime.UtcNow,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = "9999999",
        MID = "test",
        OrderNumber = "test",
        TID = null,
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0016"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow
    };
    private readonly TerminalDataSetEntity existingTerminalData7 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0007"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Available,
        ConfigDate = DateTime.UtcNow,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = "9999999",
        MID = "************",
        OrderNumber = "test",
        TID = "9999",
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0030"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0020")
    };
    private readonly TerminalDataSetEntity existingTerminalData8 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0008"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Available,
        ConfigDate = null,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = "9999999",
        MID = null,
        OrderNumber = "test",
        TID = "9999",
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0031"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0021")
    };
    private readonly TerminalDataSetEntity existingTerminalData9 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0009"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Available,
        ConfigDate = null,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = "9999999",
        MID = null,
        OrderNumber = "test",
        TID = "9999",
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0032"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0023")
    };
    private readonly TerminalDataSetEntity existingTerminalData10 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0010"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Available,
        ConfigDate = DateTime.UtcNow,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = "9999999",
        MID = "************",
        OrderNumber = "test",
        TID = "9999",
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0033"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0021")
    };
    private readonly TerminalDataSetEntity existingTerminalData11 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0011"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Available,
        ConfigDate = null,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = "9999999",
        MID = null,
        OrderNumber = "test",
        TID = "9999",
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0034"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0023")
    };
    private readonly TerminalDataSetEntity existingTerminalData12 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0012"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Available,
        ConfigDate = DateTime.UtcNow,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = "9999999",
        MID = "00177099900k",
        OrderNumber = "test",
        TID = "9999",
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0035"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0021")
    };
    private readonly TerminalDataSetEntity existingTerminalData13 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0013"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Available,
        ConfigDate = DateTime.UtcNow,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = "9999999",
        MID = "************",
        OrderNumber = "test",
        TID = "9999",
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0036"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0021")
    };
    private readonly TerminalDataSetEntity existingTerminalData14 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0014"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Invalid,
        ConfigDate = null,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = null,
        MID = null,
        OrderNumber = "test",
        TID = null,
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0037"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0023")
    };
    private readonly TerminalDataSetEntity existingTerminalData15 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0015"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Invalid,
        ConfigDate = null,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = null,
        MID = null,
        OrderNumber = "test",
        TID = null,
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0038"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0023")
    };
    private readonly TerminalDataSetEntity existingTerminalData16 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0016"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Invalid,
        ConfigDate = null,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = null,
        MID = null,
        OrderNumber = "test",
        TID = null,
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0039"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0023")
    };
    private readonly TerminalDataSetEntity existingTerminalData17 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0017"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Invalid,
        ConfigDate = null,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = null,
        MID = null,
        OrderNumber = "test",
        TID = null,
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0040"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0023")
    };
    private readonly TerminalDataSetEntity existingTerminalData18 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0018"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Invalid,
        ConfigDate = null,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = null,
        MID = null,
        OrderNumber = "test",
        TID = null,
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0041"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0023")
    };
    private readonly TerminalDataSetEntity existingTerminalData19 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0019"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Invalid,
        ConfigDate = null,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = null,
        MID = null,
        OrderNumber = "test",
        TID = null,
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0042"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0023")
    };
    private readonly TerminalDataSetEntity existingTerminalData20 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0020"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Invalid,
        ConfigDate = null,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = null,
        MID = null,
        OrderNumber = "test",
        TID = null,
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0043"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0023")
    };
    private readonly TerminalDataSetEntity existingTerminalData21 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0021"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Invalid,
        ConfigDate = null,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = null,
        MID = null,
        OrderNumber = "test",
        TID = null,
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0043"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0023")
    };
    private readonly TerminalDataSetEntity existingTerminalData22 = new TerminalDataSetEntity
    {
        Id = Guid.Parse("********-0000-0000-0000-********0022"),
        AcquiringLedger = "NBE_BANK",
        Availability = Constants.TerminalDataSetAvailability.Invalid,
        ConfigDate = null,
        Counterparty = "GEIDEA_EGYPT",
        CreatedBy = Guid.NewGuid().ToString(),
        CreatedDate = DateTime.UtcNow,
        FullTID = null,
        MID = null,
        OrderNumber = "test",
        TID = null,
        TRSM = "NBEtest",
        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0044"),
        UpdatedBy = Guid.NewGuid().ToString(),
        UpdatedDate = DateTime.UtcNow,
        StoreId = new Guid("********-0000-0000-0000-********0023")
    };

    [SetUp]
    public void Setup()
    {
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();
        counterpartyProvider.GetCode().Returns(CounterPartyProvider);

        dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider);

        logger = Substitute.For<ILogger<TerminalDataSetRepository>>();
        contextAccessor = Substitute.For<IHttpContextAccessor>();

        var profile = new AutoMapping();
        var config = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        mapper = new Mapper(config);

        repository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

        dataContext.Add(existingTerminalData1);
        dataContext.Add(existingTerminalData2);
        dataContext.Add(existingTerminalData3);
        dataContext.Add(existingTerminalData5);

        dataContext.SaveChanges();
    }

    [Test]
    public async Task GetTerminalDataSetByIdAsync_ShouldBeSuccessfuly()
    {
        var result = await repository.GetTerminalDataByIdAsync(existingTerminalData1.Id);
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(existingTerminalData1.Id));
    }

    [Test]
    public async Task GetTerminalDataSetByIdAsync_TerminalDataSetNotFound()
    {
        await repository
            .Invoking(x => x.GetTerminalDataByIdAsync(Guid.Parse("********-0000-0000-0000-********0404")))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
                x.ProblemDetails.Type == Errors.TerminalDataSetNotFound.Code);
    }

    [Test]
    public async Task PatchTerminalDataSet_ShouldBeSuccessfuly()
    {
        var newAvailability = Constants.TerminalDataSetAvailability.Invalid;
        var patch = new JsonPatchDocument<TerminalDataSet>();
        patch.Add(x => x.Availability, newAvailability);

        var terminalDataSetPatchRequest = new List<TerminalDataSetPatchRequest>()
            {
               new TerminalDataSetPatchRequest
               {
                   TerminalDataSetId = existingTerminalData1.Id,
                   TerminalDataSetPatchDocument = patch
               }
            };

        var updatedTerminalDataSet = await repository.PatchAsync(terminalDataSetPatchRequest);

        updatedTerminalDataSet[0].Availability.Should().Be(newAvailability);
    }

    [Test]
    public async Task PatchTerminalDataSetInDb_TerminalDataSetNotFound()
    {
        var newAvailability = Constants.TerminalDataSetAvailability.Invalid;
        var patch = new JsonPatchDocument<TerminalDataSet>();
        patch.Add(x => x.Availability, newAvailability);

        var terminalDataSetPatchRequest = new List<TerminalDataSetPatchRequest>()
            {
               new TerminalDataSetPatchRequest
               {
                   TerminalDataSetId = Guid.Parse("********-0000-0000-0000-********0404"),
                   TerminalDataSetPatchDocument = patch
               }
            };

        await repository
            .Invoking(x => x.PatchAsync(terminalDataSetPatchRequest))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.ProblemDetails.Type == Errors.InvalidPatchRequest.Code);
    }

    [Test]
    public async Task CreateTerminalDataSet_ShouldBeSuccessfuly()
    {
        var availableStatusForAvailability = Constants.TerminalDataSetAvailability.Available;
        var terminalDataSet1 = new TerminalDataSet
        {
            AcquiringLedger = "Riyadh",
            Mid = "test12",
            Tid = "12334",
            FullTid = "12333444",
            Trsm = "e0123"
        };
        var terminalDataSet2 = new TerminalDataSet
        {
            AcquiringLedger = "Riyadh",
            Mid = "test13",
            Tid = "13344",
            FullTid = "133444444",
            Trsm = "e0123",
            MCC = "1324"
        };

        var terminalDataSetRequest = new List<TerminalDataSet>() { terminalDataSet1, terminalDataSet2 };

        var createdTerminalDataSet = await repository.CreateAsync(terminalDataSetRequest);
        Assert.That(createdTerminalDataSet, Is.Not.Null);

        Assert.IsTrue(!createdTerminalDataSet.Exists(x =>
            x.Availability != availableStatusForAvailability));
    }

    [TestCase("1111111112", null, null, null, null, null)]
    [TestCase(null, "test23", null, null, null, null)]
    [TestCase(null, null, "11112", null, null, null)]
    [TestCase(null, null, null, "test24", null, null)]
    [TestCase(null, null, null, null, new[] { "acquiring" }, null)]
    [TestCase(null, null, null, null, new[] { "acquiring" }, new[] { Constants.TerminalDataSetAvailability.Used })]
    [Test]
    public async Task ValidateMultipleInput_ForAdvanceSearchTerminalData_ReturnsCorrectNumberOfTerminalData(
        string fullTid, string mid, string tid, string trsm, string[] acquiring, string[] available)
    {
        var searchResult = await repository.AdvancedSearchAsync(new TerminalDataSetSearchRequest()
        {
            FullTid = fullTid,
            Mid = mid,
            Tid = tid,
            Trsm = trsm,
            AcquiringLedger = acquiring,
            Availability = available
        });

        Assert.That(searchResult.TotalRecordCount, Is.EqualTo(1), TerminalDataSetCountErrorMessage);
        Assert.That(searchResult.Records[0].FullTid, Is.EqualTo("1111111112"), "There should have been 1 result");
        Assert.That(searchResult.Records[0].Mid, Is.EqualTo("test23"), "There should have been 1 result");
        Assert.That(searchResult.Records[0].Tid, Is.EqualTo("11112"), "There should have been 1 result");
        Assert.That(searchResult.Records[0].Trsm, Is.EqualTo("test24"), "There should have been 1 result");
        Assert.That(searchResult.Records[0].AcquiringLedger, Is.EqualTo("acquiring"), "There should have been 1 result");
        Assert.That(searchResult.Records[0].Availability, Is.EqualTo("USED"), "There should have been 1 result");
    }

    [Test]
    public async Task SearchTerminalDataSet_WhenSearchingBasedOnConfigDate_ReturnsCorrectNumberOfTerminalData()
    {
        DateTime configDate = DateTime.UtcNow;
        GenerateNewEntryInDb(configDate);
        var searchResult = await repository.AdvancedSearchAsync(new TerminalDataSetSearchRequest() { ConfigDate = configDate });

        Assert.That(searchResult.TotalRecordCount, Is.EqualTo(2), TerminalDataSetCountErrorMessage);
        Assert.That(searchResult.Records[0].ConfigDate, Is.EqualTo(configDate), "There should have been 1 result");
    }

    [Test]
    public async Task Search_WhenUsingOrderBy_ShouldReturnOrderedData()
    {
        var searchParameter = new TerminalDataSetSearchRequest() { Skip = 0, Take = 10, OrderBy = "TID", Sort = "desc", SearchIn = new[] { "TRSM" }, Keyword = "test" };

        var searchResult = await repository.AdvancedSearchAsync(searchParameter);

        searchResult.TotalRecordCount.Should().Be(2);
        searchResult.ReturnedRecordCount.Should().Be(2);
        searchResult.Records[0].Tid.Should().Be("11112");
        searchResult.Records[1].Tid.Should().Be("11111");
    }

    [Test]
    public async Task Search_ByProductInstanceId_ShouldReturn_SingleTerminalDataSet()
    {
        var productInstanceId = Guid.Parse("********-0000-0000-0000-********0551");
        var searchParameter = new TerminalDataSetSearchRequest()
        {
            Skip = 0,
            Take = 10,
            ProductInstanceId = productInstanceId
        };

        var searchResult = await repository.AdvancedSearchAsync(searchParameter);

        searchResult.TotalRecordCount.Should().Be(1);
        searchResult.ReturnedRecordCount.Should().Be(1);
        searchResult.Records[0].Tid.Should().Be("11112");
        searchResult.Records[0].ProductInstanceId.Should().Be(productInstanceId);
        searchResult.Records[0].AcquiringLedger.Should().Be("acquiring");
        searchResult.Records[0].Availability.Should().Be(Constants.TerminalDataSetAvailability.Used);
    }

    [Test]
    public async Task AdvancedSearchAsync_WhenSearchInAndKeyword_ShouldReturnMatchingTerminalDataSet()
    {
        var searchParameter = new TerminalDataSetSearchRequest() { SearchIn = new[] { "TRSM" }, Keyword = "TRSM" };
        var searchResult = await repository.AdvancedSearchAsync(searchParameter);

        var countResult = await dataContext.TerminalDataSets.CountAsync(o => o.TRSM.StartsWith("TRSM"));

        searchResult.TotalRecordCount.Should().Be(countResult);
        searchResult.ReturnedRecordCount.Should().Be(1);
        searchResult.Records.Count.Should().Be(1);
    }

    [Test]
    public async Task AdvancedSearchAsync_WhenOrderNumbers_ShouldReturnMatchingTerminalDataSet()
    {
        var ordernNumber = "test";
        var searchParameter = new TerminalDataSetSearchRequest() { OrderNumbers = new List<string>() { ordernNumber } };
        var searchResult = await repository.AdvancedSearchAsync(searchParameter);

        var countResult = await dataContext.TerminalDataSets.CountAsync(o => o.OrderNumber == ordernNumber);

        searchResult.TotalRecordCount.Should().Be(countResult);
        searchResult.ReturnedRecordCount.Should().Be(countResult);
        searchResult.Records.Count.Should().Be(countResult);
    }

    [Test]
    public async Task AdvancedSearchAsync_WhenMCCKeyword_ShouldReturnMatchingTerminalDataSet()
    {
        var searchParameter = new TerminalDataSetSearchRequest() { Keyword = MCC, SearchIn = new[] { "mcc" } };
        var searchResult = await repository.AdvancedSearchAsync(searchParameter);

        var countResult = await dataContext.TerminalDataSets.CountAsync(o => o.MCC.Contains(MCC));

        searchResult.TotalRecordCount.Should().Be(countResult);
        searchResult.ReturnedRecordCount.Should().Be(countResult);
        searchResult.Records.Count.Should().Be(countResult);
    }

    [Test]
    public async Task AdvancedSearchAsync_WhenConfigDateInterval_ShouldReturnMatchingTerminalDataSet()
    {
        DateInterval dateInterval = new DateInterval()
        {
            FromDate = DateTime.Now - TimeSpan.FromDays(2),
            ToDate = DateTime.Now + TimeSpan.FromDays(2)
        };
        var searchParameter = new TerminalDataSetSearchRequest() { ConfigDateInterval = dateInterval };
        var searchResult = await repository.AdvancedSearchAsync(searchParameter);

        var countResult = await dataContext.TerminalDataSets.CountAsync(o => o.ConfigDate <= dateInterval.ToDate && o.ConfigDate >= dateInterval.FromDate);

        searchResult.TotalRecordCount.Should().Be(countResult);
        searchResult.ReturnedRecordCount.Should().Be(countResult);
        searchResult.Records.Count.Should().Be(countResult);
    }

    [Test]
    public async Task AdvancedSearchAsync_WhenFromDateConfig_ShouldReturnMatchingTerminalDataSet()
    {
        DateInterval dateInterval = new DateInterval()
        {
            FromDate = DateTime.Now
        };
        var searchParameter = new TerminalDataSetSearchRequest() { ConfigDateInterval = dateInterval };
        var searchResult = await repository.AdvancedSearchAsync(searchParameter);

        var countResult = await dataContext.TerminalDataSets.CountAsync(o => o.ConfigDate >= dateInterval.FromDate);

        searchResult.TotalRecordCount.Should().Be(countResult);
        searchResult.ReturnedRecordCount.Should().Be(countResult);
        searchResult.Records.Count.Should().Be(countResult);
    }

    [Test]
    public async Task AdvancedSearchAsync_WhenToDateConfig_ShouldReturnMatchingTerminalDataSet()
    {
        DateInterval dateInterval = new DateInterval()
        {
            ToDate = DateTime.Now
        };
        var searchParameter = new TerminalDataSetSearchRequest() { ConfigDateInterval = dateInterval };
        var searchResult = await repository.AdvancedSearchAsync(searchParameter);

        var countResult = await dataContext.TerminalDataSets.CountAsync(o => o.ConfigDate <= dateInterval.ToDate);

        searchResult.TotalRecordCount.Should().Be(countResult);
        searchResult.ReturnedRecordCount.Should().Be(countResult);
        searchResult.Records.Count.Should().Be(countResult);
    }

    [Test]
    public async Task AdvancedSearchAsync_WhenSearchInAndKeyword_ShouldReturnNoMatchingTerminalDataSet()
    {
        var searchParameter = new TerminalDataSetSearchRequest() { SearchIn = new[] { "TRSM" }, Keyword = "84563" };
        var searchResult = await repository.AdvancedSearchAsync(searchParameter);

        var countResult = await dataContext.TerminalDataSets.CountAsync(o => o.TRSM.StartsWith("84563"));

        searchResult.TotalRecordCount.Should().Be(countResult);
        searchResult.ReturnedRecordCount.Should().Be(0);
        searchResult.Records.Count.Should().Be(0);
    }


    [Test]
    public async Task GetAvailableTerminalDataSetCountAsync_ShouldReturn_Count_Successfully()
    {
        var acquiringLedger = "test";
        var countResult = await repository.GetAvailableTerminalDataSetCountAsync(acquiringLedger);

        countResult.Should().Be(2);
    }

    [Test]
    public async Task GetAvailableTerminalDataSetCountForUAEAsync_ShouldReturn_Count_Successfully()
    {
        counterpartyProvider.GetCode().Returns("GEIDEA_UAE");
        using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
        {
            dataContext.Add(existingTerminalData4);
            dataContext.SaveChanges();

            repository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

            var acquiringLedger = "test";
            var countResult = await repository.GetAvailableTerminalDataSetCountAsync(acquiringLedger);

            countResult.Should().Be(1);
        }
    }

    [Test]
    public async Task GetValidationRequiredFieldsAsync_ShouldBeSuccessfuly()
    {
        DateTime configDate = DateTime.UtcNow;
        GenerateNewEntryInDb(configDate);

        var request = new List<TerminalDataSet>() {
                new TerminalDataSet()
                {
                    AcquiringLedger = "required",
                    Mid = "required",
                    Tid = "6543",
                    FullTid = "**********",
                    Trsm = "required",
                }
            };
        var result = await repository.GetValidationRequiredFieldsAsync(request);
        Assert.That(result.Count, Is.EqualTo(4), TerminalDataSetCountErrorMessage);
        Assert.NotNull(result);
    }

    [Test]
    public async Task GetValidationRequiredFieldsAsync_ShouldBeSuccessfuly_WhenPropertyIsMissing()
    {
        DateTime configDate = DateTime.UtcNow;
        GenerateNewEntryInDb(configDate);

        var request = new List<TerminalDataSet>() {
                new TerminalDataSet()
                {
                    AcquiringLedger = "required",
                    Tid = "6543",
                    Trsm = "required",
                }
            };
        var result = await repository.GetValidationRequiredFieldsAsync(request);
        Assert.That(result.Count, Is.EqualTo(2), TerminalDataSetCountErrorMessage);
        Assert.NotNull(result);
    }

    [Test]
    public async Task GetValidationRequiredFieldsAsync_ShouldBeSuccessfuly_WhenFullTidIsMissing()
    {
        DateTime configDate = DateTime.UtcNow;
        GenerateNewEntryInDb(configDate);

        var request = new List<TerminalDataSet>() {
                new TerminalDataSet()
                {
                    AcquiringLedger = "required",
                    Tid = "6543",
                    Mid = "required",
                    Trsm = "required",
                }
            };
        var result = await repository.GetValidationRequiredFieldsAsync(request);
        Assert.That(result.Count, Is.EqualTo(3), TerminalDataSetCountErrorMessage);
        Assert.NotNull(result);
    }

    [Test]
    public async Task GetValidationRequiredFieldsAsync_ShouldBeSuccessfuly_WhenAllPropertiesAreMissing()
    {
        DateTime configDate = DateTime.UtcNow;
        GenerateNewEntryInDb(configDate);

        var request = new List<TerminalDataSet>() {
                new TerminalDataSet()
            };
        var result = await repository.GetValidationRequiredFieldsAsync(request);
        Assert.That(result.Count, Is.EqualTo(0), TerminalDataSetCountErrorMessage);
        Assert.NotNull(result);
    }

    [Test]
    public async Task GetOrderMigrationValidationRequiredFieldsAsync_ShouldBeSuccessfuly()
    {
        DateTime configDate = DateTime.UtcNow;
        GenerateNewEntryInDb(configDate);

        var request = new List<TerminalDataSet>() {
                new TerminalDataSet()
                {
                    AcquiringLedger = "required",
                    Mid = "required",
                    Tid = "6543",
                    FullTid = "**********",
                    Trsm = "required",
                }
            };
        var result = await repository.GetOrderMigrationValidationRequiredFieldsAsync(request);
        Assert.That(result.Count, Is.EqualTo(3), TerminalDataSetCountErrorMessage);
        Assert.NotNull(result);
    }

    [Test]
    public async Task GetOrderMigrationValidationRequiredFieldsAsync_ShouldBeSuccessfuly_WhenPropertyIsMissing()
    {
        DateTime configDate = DateTime.UtcNow;
        GenerateNewEntryInDb(configDate);

        var request = new List<TerminalDataSet>() {
                new TerminalDataSet()
                {
                    AcquiringLedger = "required",
                    Tid = "6543",
                    Trsm = "required",
                }
            };
        var result = await repository.GetOrderMigrationValidationRequiredFieldsAsync(request);
        Assert.That(result.Count, Is.EqualTo(1), TerminalDataSetCountErrorMessage);
        Assert.NotNull(result);
    }

    [Test]
    public async Task GetOrderMigrationValidationRequiredFieldsAsync_ShouldBeSuccessfuly_WhenFullTidIsMissing()
    {
        DateTime configDate = DateTime.UtcNow;
        GenerateNewEntryInDb(configDate);

        var request = new List<TerminalDataSet>() {
                new TerminalDataSet()
                {
                    AcquiringLedger = "required",
                    Tid = "6543",
                    Mid = "required",
                    Trsm = "required",
                }
            };
        var result = await repository.GetOrderMigrationValidationRequiredFieldsAsync(request);
        Assert.That(result.Count, Is.EqualTo(1), TerminalDataSetCountErrorMessage);
        Assert.NotNull(result);
    }

    [Test]
    public async Task GetOrderMigrationValidationRequiredFieldsAsync_ShouldBeSuccessfuly_WhenAllPropertiesAreMissing()
    {
        DateTime configDate = DateTime.UtcNow;
        GenerateNewEntryInDb(configDate);

        var request = new List<TerminalDataSet>() {
                new TerminalDataSet()
            };
        var result = await repository.GetOrderMigrationValidationRequiredFieldsAsync(request);
        Assert.That(result.Count, Is.EqualTo(0), TerminalDataSetCountErrorMessage);
        Assert.NotNull(result);
    }

    [Test]
    public async Task GetTerminalDataByProductInstanceId_ShouldReturnCorrect()
    {
        var productInstanceIds = new Guid[] { existingTerminalData2.ProductInstanceId!.Value };

        var result = await repository.GetTerminalDataByProductInstanceId(productInstanceIds);
        Assert.AreEqual(1, result.Count, "Result list different from 1.");
        Assert.AreEqual(existingTerminalData2.AcquiringLedger, result[0].AcquiringLedger, $"{nameof(TerminalDataSet.AcquiringLedger)} does not match.");
        Assert.AreEqual(existingTerminalData2.Availability, result[0].Availability, $"{nameof(TerminalDataSet.Availability)} does not match.");
        Assert.AreEqual(existingTerminalData2.ConfigDate, result[0].ConfigDate, $"{nameof(TerminalDataSet.ConfigDate)} does not match.");
        Assert.AreEqual(existingTerminalData2.Extracted, result[0].Extracted, $"{nameof(TerminalDataSet.Extracted)} does not match.");
        Assert.AreEqual(existingTerminalData2.ExtractionDate, result[0].ExtractionDate, $"{nameof(TerminalDataSet.ExtractionDate)} does not match.");
        Assert.AreEqual(existingTerminalData2.FullTID, result[0].FullTid, $"{nameof(TerminalDataSet.FullTid)} does not match.");
        Assert.AreEqual(existingTerminalData2.MCC, result[0].MCC, $"{nameof(TerminalDataSet.MCC)} does not match.");
        Assert.AreEqual(existingTerminalData2.MID, result[0].Mid, $"{nameof(TerminalDataSet.Mid)} does not match.");
        Assert.AreEqual(existingTerminalData2.OrderNumber, result[0].OrderNumber, $"{nameof(TerminalDataSet.OrderNumber)} does not match.");
        Assert.AreEqual(existingTerminalData2.StoreId, result[0].StoreId, $"{nameof(TerminalDataSet.StoreId)} does not match.");
        Assert.AreEqual(existingTerminalData2.TID, result[0].Tid, $"{nameof(TerminalDataSet.Tid)} does not match.");
        Assert.AreEqual(existingTerminalData2.TRSM, result[0].Trsm, $"{nameof(TerminalDataSet.Trsm)} does not match.");
        Assert.AreEqual(existingTerminalData2.CountryPrefix, result[0].CountryPrefix, $"{nameof(TerminalDataSet.CountryPrefix)} does not match.");
        Assert.AreEqual(existingTerminalData2.PhoneNumber, result[0].PhoneNumber, $"{nameof(TerminalDataSet.PhoneNumber)} does not match.");
        Assert.AreEqual(existingTerminalData2.ChannelType, result[0].ChannelType, $"{nameof(TerminalDataSet.ChannelType)} does not match.");
        Assert.AreEqual(existingTerminalData2.ConnectionType, result[0].ConnectionType, $"{nameof(TerminalDataSet.ConnectionType)} does not match.");
        Assert.AreEqual(existingTerminalData2.MPGSMID, result[0].MPGSMID, $"{nameof(TerminalDataSet.MPGSMID)} does not match.");
        Assert.AreEqual(existingTerminalData2.MPGSKEY, result[0].MPGSKEY, $"{nameof(TerminalDataSet.MPGSKEY)} does not match.");

    }

    private void GenerateNewEntryInDb(DateTime date)
    {
        TerminalDataSetEntity existingTerminalData = new TerminalDataSetEntity
        {
            AcquiringLedger = "test",
            Availability = Constants.TerminalDataSetAvailability.Available,
            ConfigDate = date,
            Counterparty = "GEIDEA_UAE",
            CreatedBy = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.UtcNow,
            FullTID = "**********",
            MID = "test",
            OrderNumber = "test",
            TID = "11113",
            TRSM = Trsm,
            UpdatedBy = Guid.NewGuid().ToString(),
            UpdatedDate = DateTime.UtcNow
        };

        TerminalDataSetEntity localExistingTerminalData2 = new TerminalDataSetEntity
        {
            AcquiringLedger = "required",
            Availability = "test",
            ConfigDate = date,
            Counterparty = "GEIDEA_SAUDI",
            CreatedBy = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.UtcNow,
            FullTID = "**********",
            MID = "required",
            OrderNumber = "test",
            TID = "6543",
            TRSM = "required",
            UpdatedBy = Guid.NewGuid().ToString(),
            UpdatedDate = DateTime.UtcNow
        };

        dataContext.Add(localExistingTerminalData2);
        dataContext.Add(existingTerminalData);
        dataContext.SaveChanges();
    }

    [TestCase(AcquiringLedger.NBEBank)]
    [TestCase(AcquiringLedger.ALXBank)]

    public async Task AddOrUpdateTerminalDataSets_ShouldSuccess(string acquirer)
    {
        try
        {
            counterpartyProvider.GetCode().Returns("GEIDEA_UAE");

            List<ProductInstanceData> productInstanceData = new List<ProductInstanceData>
        {
            new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0015"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" },
            new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0016"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" },
            new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0017"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" },
            new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0030"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" },
            new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0031"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" }
        };

            var storeIdsHasMID = new List<Guid> { Guid.Parse("********-0000-0000-0000-********0020"), Guid.Parse("********-0000-0000-0000-********0021") };

            IAcquirer acquirerProvider = MockAcquirer(acquirer);

            using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
            {
                dataContext.Add(existingTerminalData1);
                dataContext.Add(existingTerminalData2);
                dataContext.Add(existingTerminalData3);
                dataContext.Add(existingTerminalData5);
                dataContext.Add(existingTerminalData6);
                dataContext.Add(existingTerminalData7);
                dataContext.Add(existingTerminalData8);
                dataContext.Add(existingTerminalData9);

                dataContext.SaveChanges();

                var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

                TerminalDataSetsRequest terminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "test",
                    ProductInstancesData = productInstanceData,
                    MerchantStoresIds = storeIdsHasMID,
                    AcquiringLedger = acquirer
                };

                await egyptrepository.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, acquirerProvider);
                Assert.IsTrue(true);

                TerminalDataSetsRequest emptyStoresterminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "test",
                    ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0032"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                    MerchantStoresIds = new List<Guid> { Guid.Parse("********-0000-0000-0000-********0023"), Guid.Parse("********-0000-0000-0000-********0024") },
                    AcquiringLedger = acquirer
                };

                await egyptrepository.GenerateTIDAndMIDAndAddEditTerminalDataSets(emptyStoresterminalDataSets, acquirerProvider);
                Assert.IsTrue(true);

                TerminalDataSetsRequest withoutStoresterminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "test",
                    ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0035"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                    AcquiringLedger = acquirer
                };

                await egyptrepository.GenerateTIDAndMIDAndAddEditTerminalDataSets(withoutStoresterminalDataSets, acquirerProvider);
                Assert.IsTrue(true);

                TerminalDataSetsRequest newOrderStoresterminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "newtest",
                    ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0035"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                    AcquiringLedger = acquirer
                };

                await egyptrepository.GenerateTIDAndMIDAndAddEditTerminalDataSets(newOrderStoresterminalDataSets, acquirerProvider);
                Assert.IsTrue(true);
            }

            using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
            {
                var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

                TerminalDataSetsRequest newOrderStoresterminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "newtest",
                    ProductInstancesData = productInstanceData,
                    MerchantStoresIds = storeIdsHasMID,
                    AcquiringLedger = acquirer
                };

                await egyptrepository.GenerateTIDAndMIDAndAddEditTerminalDataSets(newOrderStoresterminalDataSets, acquirerProvider);
                Assert.IsTrue(true);
            }
        }
        catch (Exception ex)
        {
            Assert.IsNotNull(ex.Message.ToString());
        }
    }

    [TestCase(AcquiringLedger.NBEBank)]
    [TestCase(AcquiringLedger.ALXBank)]

    public async Task AddOrUpdateTerminalDataSets_StartNewGroup_ShouldPass(string acquirer)
    {
        using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
        {
            dataContext.Add(existingTerminalData13);
            dataContext.Add(existingTerminalData11);

            dataContext.SaveChanges();

            var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

            TerminalDataSetsRequest newMidSetterminalDataSets = new TerminalDataSetsRequest()
            {
                OrderNumber = "test",
                ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0034"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                MerchantStoresIds = new List<Guid> { Guid.Parse("********-0000-0000-0000-********0023"), Guid.Parse("********-0000-0000-0000-********0024") },
                AcquiringLedger = acquirer
            };

            await egyptrepository.GenerateTIDAndMIDAndAddEditTerminalDataSets(newMidSetterminalDataSets, MockAcquirer(acquirer));
            Assert.IsTrue(true);
        }
    }

    [TestCase(AcquiringLedger.NBEBank)]
    [TestCase(AcquiringLedger.ALXBank)]

    public async Task AddOrUpdateTerminalDataSets_ShouldPass(string acquirer)
    {
        try
        {
            var storeIds = new List<Guid> { Guid.Parse("********-0000-0000-0000-********0020"), Guid.Parse("********-0000-0000-0000-********0021") };

            List<ProductInstanceData> productInstanceData = new List<ProductInstanceData>
        {
            new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0015"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" },
            new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0016"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" }
        };

            TerminalDataSetsRequest terminalDataSets = new TerminalDataSetsRequest()
            {
                OrderNumber = "test",
                ProductInstancesData = productInstanceData,
                MerchantStoresIds = storeIds,
                AcquiringLedger = acquirer
            };

            var acquirerProvider = MockAcquirer(acquirer);

            var result = await repository.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, acquirerProvider);

            result.Should().NotBeNull();
            result.Should().HaveCount(2);

            terminalDataSets.ProductInstancesData = null;

            var result2 = await repository.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, acquirerProvider);

            result2.Should().NotBeNull();
            result2.Should().HaveCount(0);
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [TestCase(AcquiringLedger.NBEBank)]
    [TestCase(AcquiringLedger.ALXBank)]

    public async Task AddOrUpdateTerminalDataSets_WithMerchantTag_ShouldPass(string acquirer)
    {
        try
        {
            using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
            {
                dataContext.Add(existingTerminalData14);
                dataContext.Add(existingTerminalData15);
                dataContext.Add(existingTerminalData16);
                dataContext.Add(existingTerminalData17);
                dataContext.Add(existingTerminalData18);
                dataContext.Add(existingTerminalData19);
                dataContext.Add(existingTerminalData20);
                dataContext.Add(existingTerminalData21);

                dataContext.SaveChanges();

                var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

                var storeIds = new List<Guid> { Guid.Parse("********-0000-0000-0000-********0023") };

                TerminalDataSetsRequest terminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "test",
                    ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0037"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                    MerchantStoresIds = storeIds,
                    AcquiringLedger = "NBE_BANK",
                    MerchantTag = MerchantTag.Chain,
                    Mid = "*********"
                };

                var acquirerProvider = MockAcquirer(acquirer);

                var chainResult = await egyptrepository.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, acquirerProvider);

                chainResult.Should().NotBeNull();
                chainResult.Should().HaveCount(1);
                chainResult.FirstOrDefault().MIDMerchantReference.Equals(terminalDataSets.Mid);

                terminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "test",
                    ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0038"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                    MerchantStoresIds = storeIds,
                    AcquiringLedger = acquirer,
                    MerchantTag = MerchantTag.Retial
                };

                var retailResult = await repository.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, acquirerProvider);

                retailResult.Should().NotBeNull();
                retailResult.Should().HaveCount(1);

                terminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "test",
                    ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0039"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                    MerchantStoresIds = storeIds,
                    AcquiringLedger = acquirer,
                    MerchantTag = MerchantTag.Wholesaler
                };

                var wholesalerResult = await repository.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, acquirerProvider);

                wholesalerResult.Should().NotBeNull();
                wholesalerResult.Should().HaveCount(1);

                terminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "test",
                    ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0040"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                    MerchantStoresIds = storeIds,
                    AcquiringLedger = acquirer,
                    MerchantTag = MerchantTag.SubWholesaler
                };

                var subWholesalerResult = await repository.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, acquirerProvider);

                subWholesalerResult.Should().NotBeNull();
                subWholesalerResult.Should().HaveCount(1);

                terminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "test",
                    ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0041"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                    MerchantStoresIds = storeIds,
                    AcquiringLedger = acquirer,
                    MerchantTag = MerchantTag.MasterBusiness
                };

                var masterBusinessResult = await repository.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, acquirerProvider);

                masterBusinessResult.Should().NotBeNull();
                masterBusinessResult.Should().HaveCount(1);

                terminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "test",
                    ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0042"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                    MerchantStoresIds = storeIds,
                    AcquiringLedger = acquirer,
                    MerchantTag = MerchantTag.SubBusiness
                };

                var subBusinessResult = await repository.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, acquirerProvider);

                subBusinessResult.Should().NotBeNull();
                subBusinessResult.Should().HaveCount(1);

                terminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "test",
                    ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0043"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                    MerchantStoresIds = storeIds,
                    AcquiringLedger = acquirer,
                    MerchantTag = MerchantTag.MultiStore
                };

                var multiStoreResult = await repository.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, acquirerProvider);

                multiStoreResult.Should().NotBeNull();
                multiStoreResult.Should().HaveCount(1);

                terminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "test",
                    ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0044"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                    MerchantStoresIds = storeIds,
                    AcquiringLedger = acquirer,
                };

                var result = await repository.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, acquirerProvider);

                result.Should().NotBeNull();
                result.Should().HaveCount(1);
            }
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [TestCase(AcquiringLedger.NBEBank, RandomSequenceGenerator.NBEMIDLastValue)]
    [TestCase(AcquiringLedger.ALXBank, RandomSequenceGenerator.ALXMIDLastValue)]

    public async Task AddOrUpdateTerminalDataSets_ShouldThrowMaxMIDException(string acquirer, string midLastValue)
    {
        try
        {
            counterpartyProvider.GetCode().Returns("GEIDEA_UAE");
            using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
            {
                existingTerminalData10.AcquiringLedger = acquirer;
                existingTerminalData10.MID = midLastValue;
                existingTerminalData11.AcquiringLedger = acquirer;

                dataContext.Add(existingTerminalData10);
                dataContext.Add(existingTerminalData11);

                dataContext.SaveChanges();

                var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

                var storeIdsEmptyMID = new List<Guid> { Guid.Parse("********-0000-0000-0000-********0023"), Guid.Parse("********-0000-0000-0000-********0024") };

                TerminalDataSetsRequest terminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "test",
                    ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0034"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                    MerchantStoresIds = storeIdsEmptyMID,
                    AcquiringLedger = acquirer
                };

                var acquirerProvider = MockAcquirer(acquirer);

                await egyptrepository.Invoking(x => x.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, acquirerProvider)).Should().ThrowAsync<ServiceException>()
                                .Where(x => x.ProblemDetails.Type == Errors.ReachedMaxMID.Code);
            }
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [TestCase((AcquiringLedger.NBEBank))]
    public async Task AddOrUpdateTerminalDataSets_ShouldThrowMIDMustBeNumbersException(string acquirer)
    {
        try
        {
            counterpartyProvider.GetCode().Returns("GEIDEA_UAE");
            using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
            {
                dataContext.Add(existingTerminalData12);
                dataContext.Add(existingTerminalData11);

                dataContext.SaveChanges();

                var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

                var storeIdsEmptyMID = new List<Guid> { Guid.Parse("********-0000-0000-0000-********0023"), Guid.Parse("********-0000-0000-0000-********0024") };

                TerminalDataSetsRequest terminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "test",
                    ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0034"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                    MerchantStoresIds = storeIdsEmptyMID,
                    AcquiringLedger = acquirer
                };

                var acquirerProvider = MockAcquirer(acquirer);

                await egyptrepository.Invoking(x => x.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, acquirerProvider)).Should().ThrowAsync<ServiceException>()
                                .Where(x => x.ProblemDetails.Type == Errors.MIDMustBeNumbers.Code);
            }
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [Test]
    public async Task AddOrUpdateTerminalDataSets_ShouldThrowInvalidAcquirerException()
    {
        try
        {
            counterpartyProvider.GetCode().Returns("GEIDEA_UAE");
            using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
            {
                dataContext.Add(existingTerminalData12);
                dataContext.Add(existingTerminalData11);

                dataContext.SaveChanges();

                var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

                TerminalDataSetsRequest terminalDataSets = new TerminalDataSetsRequest()
                {
                    OrderNumber = "test",
                    ProductInstancesData = new List<ProductInstanceData> { new ProductInstanceData { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0033"), ConnectionType = "ConnectionTest", ChannelType = "ChannelTest" } },
                };

                await egyptrepository.Invoking(x => x.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminalDataSets, null)).Should().ThrowAsync<ServiceException>()
                                .Where(x => x.ProblemDetails.Type == Errors.InvalidAutoGeneratedAcquirer.Code);

                await egyptrepository.Invoking(x => x.GenerateTIDAndMIDAndAddEditTerminalDataSets(null, null)).Should().ThrowAsync<NullReferenceException>();
            }
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [TestCase(AcquiringLedger.NBEBank)]
    [TestCase(AcquiringLedger.ALXBank)]
    public async Task AddOrUpdateOrderTerminalDataSets_ShouldSuccess(string acquirer)
    {
        try
        {
            counterpartyProvider.GetCode().Returns("GEIDEA_UAE");
            List<Guid?> productInstanceIds = new List<Guid?>
        {
            Guid.Parse("********-0000-0000-0000-********0015"),
            Guid.Parse("********-0000-0000-0000-********0016"),
            Guid.Parse("********-0000-0000-0000-********0017"),
            Guid.Parse("********-0000-0000-0000-********0030"),
            Guid.Parse("********-0000-0000-0000-********0031"),
        };

            var storeIdsHasMID = new List<Guid> { Guid.Parse("********-0000-0000-0000-********0020"), Guid.Parse("********-0000-0000-0000-********0021") };

            using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
            {
                dataContext.Add(existingTerminalData1);
                dataContext.Add(existingTerminalData2);
                dataContext.Add(existingTerminalData3);
                dataContext.Add(existingTerminalData5);
                dataContext.Add(existingTerminalData6);
                dataContext.Add(existingTerminalData7);
                dataContext.Add(existingTerminalData8);
                dataContext.Add(existingTerminalData9);

                dataContext.SaveChanges();

                var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

                TerminalDataSetsMidTidRequest terminalDataSets = new TerminalDataSetsMidTidRequest()
                {
                    StoresIds = storeIdsHasMID,
                    TerminalDataSets = new List<TerminalDataSet>
                {
                    new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0015"), AcquiringLedger = acquirer, OrderNumber = "test" },
                    new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0016"), AcquiringLedger = acquirer, OrderNumber = "test" },
                    new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0017"), AcquiringLedger = acquirer, OrderNumber = "test" },
                    new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0030"), AcquiringLedger = acquirer, OrderNumber = "test" },
                    new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0031"), AcquiringLedger = acquirer, OrderNumber = "test" }
                }
                };

                var acquirerProvider = MockAcquirer(acquirer);

                await egyptrepository.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(terminalDataSets, acquirerProvider);
                Assert.IsTrue(true);

                TerminalDataSetsMidTidRequest emptyStoresterminalDataSets = new TerminalDataSetsMidTidRequest()
                {
                    StoresIds = new List<Guid> { Guid.Parse("********-0000-0000-0000-********0023"), Guid.Parse("********-0000-0000-0000-********0024") },
                    TerminalDataSets = new List<TerminalDataSet>
                {
                    new TerminalDataSet {
                        ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0032"),
                        AcquiringLedger = acquirer,
                        OrderNumber = "test",
                        StoreId =  Guid.Parse("********-0000-0000-0000-********0023")
                    }
                }
                };

                await egyptrepository.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(emptyStoresterminalDataSets, acquirerProvider);
                Assert.IsTrue(true);

                TerminalDataSetsMidTidRequest withoutStoresterminalDataSets = new TerminalDataSetsMidTidRequest()
                {
                    TerminalDataSets = new List<TerminalDataSet>
                {
                    new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0035"), AcquiringLedger = acquirer, OrderNumber = "test" }
                }
                };

                await egyptrepository.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(withoutStoresterminalDataSets, acquirerProvider);
                Assert.IsTrue(true);

                TerminalDataSetsMidTidRequest newOrderStoresterminalDataSets = new TerminalDataSetsMidTidRequest()
                {
                    TerminalDataSets = new List<TerminalDataSet>
                {
                    new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0035"), AcquiringLedger = acquirer, OrderNumber = "newtest" }
                }
                };

                await egyptrepository.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(newOrderStoresterminalDataSets, acquirerProvider);
                Assert.IsTrue(true);
            }


            using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
            {
                var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

                TerminalDataSetsMidTidRequest newOrderStoresterminalDataSets = new TerminalDataSetsMidTidRequest()
                {
                    StoresIds = storeIdsHasMID,
                    TerminalDataSets = new List<TerminalDataSet>
                {
                    new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0015"), AcquiringLedger = acquirer, OrderNumber = "newtest" },
                    new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0016"), AcquiringLedger = acquirer, OrderNumber = "newtest" },
                    new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0017"), AcquiringLedger = acquirer, OrderNumber = "newtest" },
                    new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0030"), AcquiringLedger = acquirer, OrderNumber = "newtest" },
                    new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0031"), AcquiringLedger = acquirer, OrderNumber = "newtest" }
                }
                };

                var acquirerProvider = MockAcquirer(acquirer);

                await egyptrepository.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(newOrderStoresterminalDataSets, acquirerProvider);
                Assert.IsTrue(true);
            }
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }



    [TestCase(AcquiringLedger.NBEBank)]
    [TestCase(AcquiringLedger.ALXBank)]
    public async Task AddOrUpdateNBEOrderTerminalDataSets_ShouldPass(string acquirer)
    {
        try
        {
            var storeIds = new List<Guid> { Guid.Parse("********-0000-0000-0000-********0020"), Guid.Parse("********-0000-0000-0000-********0021") };
            List<Guid?> productInstanceIds = new List<Guid?>
            {
                Guid.Parse("********-0000-0000-0000-********0015"),
                Guid.Parse("********-0000-0000-0000-********0016")
            };

            TerminalDataSetsMidTidRequest terminalDataSets = new TerminalDataSetsMidTidRequest
            {
                StoresIds = storeIds,
                TerminalDataSets = new List<TerminalDataSet>
            {
                new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0015"), AcquiringLedger = acquirer, OrderNumber = "test" },
                new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0016"), AcquiringLedger = acquirer, OrderNumber = "test" }
            }
            };

            var acquirerProvider = MockAcquirer(acquirer);

            var result = await repository.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(terminalDataSets, acquirerProvider);

            result.Should().NotBeNull();
            result.Should().HaveCount(2);

            terminalDataSets.TerminalDataSets = null;

            var result2 = await repository.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(terminalDataSets, acquirerProvider);

            result2.Should().NotBeNull();
            result2.Should().HaveCount(0);

            terminalDataSets.TerminalDataSets = new List<TerminalDataSet>();

            var result3 = await repository.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(terminalDataSets, acquirerProvider);

            result3.Should().NotBeNull();
            result3.Should().HaveCount(0);

            var result4 = await repository.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(null, acquirerProvider);

            result4.Should().NotBeNull();
            result4.Should().HaveCount(0);
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [TestCase(AcquiringLedger.NBEBank, RandomSequenceGenerator.NBEMIDLastValue)]
    [TestCase(AcquiringLedger.ALXBank, RandomSequenceGenerator.ALXMIDLastValue)]
    public async Task AddOrUpdateOrderTerminalDataSets_ShouldThrowMaxMIDException(string acquirer, string midLastValue)
    {
        try
        {
            counterpartyProvider.GetCode().Returns("GEIDEA_UAE");
            using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
            {
                existingTerminalData10.AcquiringLedger = acquirer;
                existingTerminalData10.MID = midLastValue;
                existingTerminalData11.AcquiringLedger = acquirer;

                dataContext.Add(existingTerminalData10);
                dataContext.Add(existingTerminalData11);

                dataContext.SaveChanges();

                var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

                var storeIdsEmptyMID = new List<Guid> { Guid.Parse("********-0000-0000-0000-********0023"), Guid.Parse("********-0000-0000-0000-********0024") };

                TerminalDataSetsMidTidRequest terminalDataSets = new TerminalDataSetsMidTidRequest
                {
                    StoresIds = storeIdsEmptyMID,
                    TerminalDataSets = new List<TerminalDataSet>
                {
                    new TerminalDataSet { ProductInstanceId = new Guid("********-0000-0000-0000-********0034"), AcquiringLedger = acquirer, OrderNumber = "test" }
                }
                };

                var acquirerProvider = MockAcquirer(acquirer);

                await egyptrepository.Invoking(x => x.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(terminalDataSets, acquirerProvider)).Should().ThrowAsync<ServiceException>()
                                .Where(x => x.ProblemDetails.Type == Errors.ReachedMaxMID.Code);
            }
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [TestCase(AcquiringLedger.NBEBank)]
    public async Task AddOrUpdateOrderTerminalDataSets_ShouldThrowMIDMustBeNumbersException(string acquirer)
    {
        try
        {
            counterpartyProvider.GetCode().Returns("GEIDEA_UAE");
            using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
            {
                dataContext.Add(existingTerminalData12);
                dataContext.Add(existingTerminalData11);

                dataContext.SaveChanges();

                var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

                var storeIdsEmptyMID = new List<Guid> { Guid.Parse("********-0000-0000-0000-********0023"), Guid.Parse("********-0000-0000-0000-********0024") };

                TerminalDataSetsMidTidRequest terminalDataSets = new TerminalDataSetsMidTidRequest
                {
                    StoresIds = storeIdsEmptyMID,
                    TerminalDataSets = new List<TerminalDataSet>
                {
                    new TerminalDataSet { ProductInstanceId = new Guid("********-0000-0000-0000-********0034"), AcquiringLedger = "NBE_BANK", OrderNumber = "test" }
                }
                };

                var acquirerProvider = MockAcquirer(acquirer);

                await egyptrepository.Invoking(x => x.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(terminalDataSets, acquirerProvider)).Should().ThrowAsync<ServiceException>()
                                .Where(x => x.ProblemDetails.Type == Errors.MIDMustBeNumbers.Code);
            }
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [Test]
    public async Task AddOrUpdateOrderTerminalDataSets_ShouldThrowInvalidAcquirerException()
    {
        counterpartyProvider.GetCode().Returns("GEIDEA_EGYPT");
        using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
        {
            dataContext.Add(existingTerminalData12);
            dataContext.Add(existingTerminalData11);

            dataContext.SaveChanges();

            var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

            TerminalDataSetsMidTidRequest terminalDataSets = new TerminalDataSetsMidTidRequest
            {
                TerminalDataSets = new List<TerminalDataSet>
                {
                    new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0033"), OrderNumber = "test" }
                }
            };

            await egyptrepository.Invoking(x => x.GenerateTIDAndMIDAndAddEditOrderTerminalDataSets(terminalDataSets, null)).Should().ThrowAsync<ServiceException>()
                            .Where(x => x.ProblemDetails.Type == Errors.InvalidAutoGeneratedAcquirer.Code);
        }
    }

    [Test]
    public async Task GenerateTIDAndAddEditTerminalDataSet_ShouldThrowInvalidAcquirerException()
    {
        counterpartyProvider.GetCode().Returns("GEIDEA_EGYPT");
        using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
        {
            dataContext.Add(existingTerminalData12);
            dataContext.Add(existingTerminalData11);

            dataContext.SaveChanges();

            var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

            TerminalDataSet terminalDataSet = new TerminalDataSet()
            {
                OrderNumber = "test",
                ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0033")
            };

            await egyptrepository.Invoking(x => x.GenerateTIDAndAddEditTerminalDataSet(terminalDataSet, null)).Should().ThrowAsync<ServiceException>()
                 .Where(x => x.ProblemDetails.Type == Errors.InvalidAutoGeneratedAcquirer.Code);
        }
    }

    [Test]
    public async Task GenerateTIDAndAddEditTerminalDataSet_ShouldReturnEmpty()
    {
        counterpartyProvider.GetCode().Returns("GEIDEA_EGYPT");
        using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
        {
            dataContext.Add(existingTerminalData12);
            dataContext.Add(existingTerminalData11);

            dataContext.SaveChanges();

            var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

            var result = await egyptrepository.GenerateTIDAndAddEditTerminalDataSet(null, null);

            Assert.That(result, Is.Not.Null);
            Assert.That(result.TId, Is.Null);

        }
    }

    [TestCase(AcquiringLedger.NBEBank)]
    public async Task GenerateTIDAndAddEditTerminalDataSet_ShouldUpdateTidAndReturnValue(string acquirer)
    {
        try
        {
            counterpartyProvider.GetCode().Returns("GEIDEA_EGYPT");
            using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
            {
                dataContext.Add(existingTerminalData12);
                dataContext.Add(existingTerminalData11);

                await dataContext.SaveChangesAsync();

                var productInstance = Guid.Parse("********-0000-0000-0000-********0033");
                var orderNumber = "Ord1";

                var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

                TerminalDataSet terminalDataSet = new TerminalDataSet()
                {
                    OrderNumber = orderNumber,
                    ProductInstanceId = productInstance,
                    AcquiringLedger = acquirer
                };

                dataContext.TerminalDataSets.Add(new TerminalDataSetEntity
                {
                    ProductInstanceId = productInstance,
                    OrderNumber = orderNumber
                });

                await dataContext.SaveChangesAsync();

                var acquirerProvider = MockAcquirer(acquirer);

                var result = await egyptrepository.GenerateTIDAndAddEditTerminalDataSet(terminalDataSet, acquirerProvider);

                Assert.That(result, Is.Not.Null);
                Assert.IsTrue(true);
            }
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [TestCase(AcquiringLedger.NBEBank)]
    [TestCase(AcquiringLedger.ALXBank)]
    public async Task GenerateTIDAndAddEditTerminalDataSet_ShouldReturnExistingValue(string acquirer)
    {
        counterpartyProvider.GetCode().Returns("GEIDEA_EGYPT");
        using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
        {
            dataContext.Add(existingTerminalData12);
            dataContext.Add(existingTerminalData11);

            await dataContext.SaveChangesAsync();

            var productInstance = Guid.Parse("********-0000-0000-0000-********0033");
            var orderNumber = "Ord1";

            var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

            TerminalDataSet terminalDataSet = new TerminalDataSet()
            {
                OrderNumber = orderNumber,
                ProductInstanceId = productInstance,
                AcquiringLedger = acquirer,
                Tid = "Tid",
                FullTid = "FullTid",
                Trsm = "Trsm"
            };

            dataContext.TerminalDataSets.Add(new TerminalDataSetEntity
            {
                ProductInstanceId = productInstance,
                OrderNumber = orderNumber,
                TID = "Tid",
                FullTID = "FullTid",
                TRSM = "Trsm"
            });

            await dataContext.SaveChangesAsync();

            var result = await egyptrepository.GenerateTIDAndAddEditTerminalDataSet(terminalDataSet, MockAcquirer(acquirer));

            Assert.That(result, Is.Not.Null);
            Assert.AreEqual("Tid", result.TId);
            Assert.IsTrue(true);
        }
    }

    [TestCase(AcquiringLedger.NBEBank)]
    [TestCase(AcquiringLedger.ALXBank)]
    public async Task GenerateTIDAndAddEditTerminalDataSet_ShouldCreateDatasetAndReturnValue(string acquirer)
    {
        try
        {
            counterpartyProvider.GetCode().Returns("GEIDEA_EGYPT");
            using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
            {
                dataContext.Add(existingTerminalData12);
                dataContext.Add(existingTerminalData11);

                await dataContext.SaveChangesAsync();

                var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

                TerminalDataSet terminalDataSet = new TerminalDataSet()
                {
                    OrderNumber = "test",
                    ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0033"),
                    AcquiringLedger = acquirer
                };

                var acquirerProvider = MockAcquirer(acquirer);

                var result = await egyptrepository.GenerateTIDAndAddEditTerminalDataSet(terminalDataSet, acquirerProvider);

                Assert.That(result, Is.Not.Null);
                Assert.IsTrue(true);
            }
        }
        catch (Exception ex)
        {
            Assert.NotNull(ex.Message.ToString());
        }
    }

    [Test]
    public async Task GenerateMIDAndAddEditTerminalDataSet_ShouldThrowInvalidAcquirerException()
    {
        counterpartyProvider.GetCode().Returns("GEIDEA_EGYPT");
        using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
        {
            dataContext.Add(existingTerminalData12);
            dataContext.Add(existingTerminalData11);

            dataContext.SaveChanges();

            var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

            TerminalDataSet terminalDataSet = new TerminalDataSet()
            {
                OrderNumber = "test",
                ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0033")
            };

            TerminalDataSetMidRequest terminalDataSetMid = new TerminalDataSetMidRequest
            {
                TerminalDataSet = terminalDataSet,
                StoresIds = new List<Guid> { Guid.NewGuid() }
            };

            await egyptrepository.Invoking(x => x.GenerateMIDAndAddEditTerminalDataSet(terminalDataSetMid, null))
                .Should().ThrowAsync<ServiceException>()
                .Where(x => x.ProblemDetails.Type == Errors.InvalidAutoGeneratedAcquirer.Code);
        }
    }

    [TestCase(AcquiringLedger.NBEBank)]
    [TestCase(AcquiringLedger.ALXBank)]
    public async Task GenerateMIDAndAddEditTerminalDataSet_ShouldReturnEmpty(string acquirer)
    {
        counterpartyProvider.GetCode().Returns("GEIDEA_EGYPT");
        using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
        {
            dataContext.Add(existingTerminalData12);
            dataContext.Add(existingTerminalData11);

            dataContext.SaveChanges();

            var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

            var result = await egyptrepository.GenerateMIDAndAddEditTerminalDataSet(null, MockAcquirer(acquirer));

            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(""));

            TerminalDataSetMidRequest terminalDataSetMid = new TerminalDataSetMidRequest
            {
                TerminalDataSet = null
            };

            result = await egyptrepository.GenerateMIDAndAddEditTerminalDataSet(terminalDataSetMid, MockAcquirer(acquirer));

            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(""));

            terminalDataSetMid = new TerminalDataSetMidRequest
            {
                TerminalDataSet = new TerminalDataSet()
                {
                    OrderNumber = "test",
                    ProductInstanceId = Guid.NewGuid(),
                    AcquiringLedger = acquirer
                },
                StoresIds = null
            };

            result = await egyptrepository.GenerateMIDAndAddEditTerminalDataSet(terminalDataSetMid, null);

            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(""));

            terminalDataSetMid.StoresIds = new List<Guid>();

            result = await egyptrepository.GenerateMIDAndAddEditTerminalDataSet(terminalDataSetMid, null);

            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(""));
        }
    }

    [TestCase(AcquiringLedger.ALXBank)]
    [TestCase(AcquiringLedger.NBEBank)]
    public async Task GenerateMIDAndAddEditTerminalDataSet_ShouldUpdateMidAndReturnValue(string acquirer)
    {
        counterpartyProvider.GetCode().Returns("GEIDEA_EGYPT");
        using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
        {
            dataContext.Add(existingTerminalData12);
            dataContext.Add(existingTerminalData11);

            await dataContext.SaveChangesAsync();

            var productInstance = Guid.Parse("********-0000-0000-0000-********0033");
            var orderNumber = "Ord1";

            var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

            TerminalDataSet terminalDataSet = new TerminalDataSet()
            {
                OrderNumber = orderNumber,
                ProductInstanceId = productInstance,
                AcquiringLedger = acquirer
            };

            dataContext.TerminalDataSets.Add(new TerminalDataSetEntity
            {
                ProductInstanceId = productInstance,
                OrderNumber = orderNumber
            });

            TerminalDataSetMidRequest terminalDataSetMid = new TerminalDataSetMidRequest
            {
                TerminalDataSet = terminalDataSet,
                StoresIds = new List<Guid> { existingTerminalData12.StoreId.Value }
            };

            await dataContext.SaveChangesAsync();

            var result = await egyptrepository.GenerateMIDAndAddEditTerminalDataSet(terminalDataSetMid, MockAcquirer(acquirer));

            Assert.That(result, Is.Not.Null);
            Assert.IsTrue(true);
        }
    }

    [TestCase(AcquiringLedger.ALXBank)]
    [TestCase(AcquiringLedger.NBEBank)]
    public async Task GenerateMIDAndAddEditTerminalDataSet_ShouldReturnExistingValue(string acquirer)
    {
        counterpartyProvider.GetCode().Returns("GEIDEA_EGYPT");
        using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
        {
            dataContext.Add(existingTerminalData12);
            dataContext.Add(existingTerminalData11);

            await dataContext.SaveChangesAsync();

            var productInstance = Guid.Parse("********-0000-0000-0000-********0033");
            var orderNumber = "Ord1";

            var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

            TerminalDataSet terminalDataSet = new TerminalDataSet()
            {
                OrderNumber = orderNumber,
                ProductInstanceId = productInstance,
                AcquiringLedger = acquirer
            };

            dataContext.TerminalDataSets.Add(new TerminalDataSetEntity
            {
                ProductInstanceId = productInstance,
                OrderNumber = orderNumber,
                MID = "Mid"
            });

            await dataContext.SaveChangesAsync();

            TerminalDataSetMidRequest terminalDataSetMid = new TerminalDataSetMidRequest
            {
                TerminalDataSet = terminalDataSet,
                StoresIds = new List<Guid> { existingTerminalData12.StoreId.Value }
            };

            var result = await egyptrepository.GenerateMIDAndAddEditTerminalDataSet(terminalDataSetMid, MockAcquirer(acquirer));

            Assert.That(result, Is.Not.Null);
            Assert.AreEqual("Mid", result);
            Assert.IsTrue(true);
        }
    }

    [TestCase(AcquiringLedger.ALXBank)]
    [TestCase(AcquiringLedger.NBEBank)]
    public async Task GenerateMIDAndAddEditTerminalDataSet_ShouldCreateDatasetAndReturnValue(string acquirer)
    {
        counterpartyProvider.GetCode().Returns("GEIDEA_EGYPT");
        using (dataContext = DbContextHelper.CreateInMemoryDatabase(counterpartyProvider))
        {
            dataContext.Add(existingTerminalData12);
            dataContext.Add(existingTerminalData11);

            await dataContext.SaveChangesAsync();

            var egyptrepository = new TerminalDataSetRepository(dataContext, contextAccessor, mapper, logger, counterpartyProvider);

            TerminalDataSet terminalDataSet = new TerminalDataSet()
            {
                OrderNumber = "test",
                ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0033"),
                AcquiringLedger = acquirer
            };

            TerminalDataSetMidRequest terminalDataSetMid = new TerminalDataSetMidRequest
            {
                TerminalDataSet = terminalDataSet,
                StoresIds = new List<Guid> { existingTerminalData12.StoreId.Value }
            };

            var result = await egyptrepository.GenerateMIDAndAddEditTerminalDataSet(terminalDataSetMid, MockAcquirer(acquirer));

            Assert.That(result, Is.Not.Null);
            Assert.IsTrue(true);
        }
    }

    [Test]
    public async Task AddUpdateTerminalDataSetByMcc_WithExistingTerminalDataAndNewTerminalDataSet_ShouldAddAndUpdate()
    {
        counterpartyProvider.GetCode().Returns("GEIDEA_UAE");

        dataContext.Add(existingTerminalData6);
        dataContext.SaveChanges();

        var storeIds = new List<Guid> { Guid.Parse("********-0000-0000-0000-********0020"), Guid.Parse("********-0000-0000-0000-********0021") };


        TerminalDataSetsMidTidRequest terminalDataSets = new TerminalDataSetsMidTidRequest
        {
            StoresIds = storeIds,
            TerminalDataSets = new List<TerminalDataSet>
            {
                new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0020"), AcquiringLedger = "NBE_BANK", OrderNumber = "test1", StoreId = Guid.Parse("********-0000-0000-0000-********0020")},
                new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0015"), AcquiringLedger = "NBE_BANK", OrderNumber = "test", StoreId = Guid.Parse("********-0000-0000-0000-********0020")},
                new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0016"), AcquiringLedger = "NBE_BANK", OrderNumber = "test", StoreId = Guid.Parse("********-0000-0000-0000-********0021") }
            }
        };

        var result = await repository.AddUpdateTerminalDataSetMcc(terminalDataSets);

        result.Should().NotBeNull();
        result.Should().HaveCount(4);
    }


    [Test]
    public async Task AddUpdateTerminalDataSetByMcc_WithExistingTerminalDataSet_ShouldUpdateOnly()
    {
        counterpartyProvider.GetCode().Returns("GEIDEA_UAE");

        dataContext.Add(existingTerminalData22);
        dataContext.SaveChanges();

        var storeIds = new List<Guid> { Guid.Parse("********-0000-0000-0000-********0023") };


        TerminalDataSetsMidTidRequest terminalDataSets = new TerminalDataSetsMidTidRequest
        {
            StoresIds = storeIds,
            TerminalDataSets = new List<TerminalDataSet>
            {
                new TerminalDataSet { ProductInstanceId = Guid.Parse("********-0000-0000-0000-********0043"), AcquiringLedger = "NBE_BANK", OrderNumber = "test", StoreId = Guid.Parse("********-0000-0000-0000-********0023") }
            }
        };

        var result = await repository.AddUpdateTerminalDataSetMcc(terminalDataSets);

        result.Should().NotBeNull();
        result.Should().HaveCount(1);
    }
    [Test]
    public async Task GenerateNewTerminalIdSequence_WithEachCall()
    {
        try
        {
            var result = await repository.GetTerminalIdSequenceAsync();
        }
        catch (Exception ex)
        {
            Assert.That(ex.Message, Is.EqualTo("Relational-specific methods can only be used when the context is using a relational database provider."));
        }



    }

    private static IAcquirer MockAcquirer(string acquirer)
    {
        return acquirer switch
        {
            AcquiringLedger.NBEBank => Substitute.For<NbeAcquirer>(),
            AcquiringLedger.ALXBank => Substitute.For<AlxAcquirer>(),
            _ => throw new ServiceException(Errors.InvalidAutoGeneratedAcquirer)
        };
    }
}
