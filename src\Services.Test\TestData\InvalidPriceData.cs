﻿using System;
using System.Collections;
using System.Collections.Generic;
using Common.Models;

namespace Services.Test.TestData;

public class InvalidPriceData : IEnumerable<object[]>
{
    public IEnumerator<object[]> GetEnumerator()
    {
        yield return new object[]
        {
                new PriceRequest
                {
                    ChargeFrequency = "RECCURRING_CHARGE",
                    Currency="EUR",
                    ExemptFromVAT=true,
                    Group="group",
                    MaxPrice=100,
                    PercentagePrice=10,
                    PerItemPrice=20,
                    Priority=0,
                    ProductId=Guid.NewGuid(),
                    RentalPeriod=24,
                    Threshold=200,
                    ThresholdType="LT"
                }
        };
        yield return new object[]
        {
                new PriceRequest
                {
                    ChargeFrequency = "RECCURRING_CHARGE",
                    ChargeType="MONTH",
                    Currency="EUR",
                    ExemptFromVAT=true,
                    Group="group",
                    MaxPrice=100,
                    PercentagePrice=10,
                    PerItemPrice=20,
                    Priority=0,
                    RentalPeriod=24,
                    Threshold=200,
                    ThresholdType="LT"
                }
        };
        yield return new object[]
        {
                new PriceRequest
                {
                    ChargeFrequency = "RECCURRING_CHARGE",
                    ChargeType="MONTH",
                    Currency="EUR",
                    ExemptFromVAT=true,
                    Group="group",
                    MaxPrice=100,
                    PercentagePrice=10,
                    PerItemPrice=20,
                    Priority=0,
                    ProductId=Guid.NewGuid(),
                    RentalPeriod=24,
                    Threshold=200,
                    ThresholdType="type"
                }
        };
        yield return new object[]
        {
                new PriceRequest
                {
                    ChargeFrequency = "RECCURRING_CHARGE",
                    ChargeType="MONTH",
                    Currency="EUR",
                    ExemptFromVAT=true,
                    Group="group",
                    MaxPrice=100,
                    PercentagePrice=10,
                    PerItemPrice=20,
                    Priority=0,
                    ProductId=Guid.NewGuid(),
                    RentalPeriod=24,
                    ThresholdType="LT"
                }
        };
        yield return new object[]
        {
                new PriceRequest
                {
                    ChargeFrequency = "RECCURRING_CHARGE",
                    ChargeType="MONTH",
                    Currency="EUR",
                    ExemptFromVAT=true,
                    Group="group",
                    MaxPrice=100,
                    PercentagePrice=10,
                    PerItemPrice=20,
                    Priority=0,
                    ProductId=Guid.NewGuid(),
                    RentalPeriod=24,
                    Threshold=200,
                    ThresholdType="LT",
                    ValidTo=DateTime.Now
                }
        };
        yield return new object[]
        {
                new PriceRequest
                {
                    ChargeFrequency = "RECCURRING_CHARGE",
                    ChargeType="MONTH",
                    Currency="EUR",
                    ExemptFromVAT=true,
                    Group="group",
                    MaxPrice=100,
                    PercentagePrice=10,
                    PerItemPrice=20,
                    Priority=0,
                    ProductId=Guid.NewGuid(),
                    RentalPeriod=24,
                    Threshold=200,
                    ThresholdType="LT",
                    ValidFrom=DateTime.Now,
                    ValidTo=DateTime.Now.AddDays(-1)
                }
        };
    }

    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }
}
