﻿using System;
using System.Collections.Generic;

namespace Common.Models.PendingCommissionPrice;
public class PendingComissionPriceApprovalResponse
{
    public List<Guid> ReviewedComissionPrices { get; set; } = new();
    public List<FailedComissionPrices> FailedComissionPrices { get; set; } = new();
}

public class FailedComissionPrices
{
    public Guid Id { get; set; }
    public string? ErrorCode { get; set; }
}