﻿using System;
using System.Collections.Generic;

namespace Common.Models.TerminalDataSets;

public class TerminalDataSetSearchRequest
{
    public Guid? ProductInstanceId { get; set; }
    public string? Mid { get; set; }
    public string? Tid { get; set; }
    public string? FullTid { get; set; }
    public string? OrderNumber { get; set; }
    public List<string>? OrderNumbers { get; set; }
    public string[]? AcquiringLedger { get; set; }
    public string? Trsm { get; set; }
    public DateTime? ConfigDate { get; set; }
    public DateInterval? ConfigDateInterval { get; set; }
    public string[]? Availability { get; set; }
    public string? Counterparty { get; set; }
    public string? Keyword { get; set; }
    public string[]? SearchIn { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 10;
    public string Sort { get; set; } = "desc";
    public string OrderBy { get; set; } = "CreatedDate";
    public string[]? ChannelType { get; set; }
    public List<Guid?>? VendorIds { get; set; }
}
