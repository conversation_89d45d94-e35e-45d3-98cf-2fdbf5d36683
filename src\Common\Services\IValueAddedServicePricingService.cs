﻿using Common.Models.ValueAddedSerivcePricing;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services;
public interface IValueAddedServicePricingService
{
    Task<ValueAddedServicePriceResponse> CreateAsync(ValueAddedServicesPricingCreateRequest request);
    Task<ValueAddedServicesPricingDetails> UpdateAsync(Guid id, ValueAddedServicePricingUpdateRequest updateRequest);
    Task DeleteAddonsPricesAsync(List<Guid> ids, string deletedBy);
    Task<ValueAddedServicePriceListResponse> GetValueAddedServicesPricesList(ValueAddedServicePriceListRequest request);
    Task<ValueAddedServicesPricingDetails> GetAddOnsPriceByIdAsync(Guid id);
    Task<ValueAddedServicePriceResponse> CreateOrUpdateAsync(ValueAddedServicesPricingCreateRequest request);
    Task<List<ValueAddedServicesPricingDetails>> GetAddonsPricesByIdsAsync(List<Guid> ids);
}
