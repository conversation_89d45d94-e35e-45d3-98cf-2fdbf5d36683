﻿using Common.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Common.Enums.UnitPrice;
using Geidea.Utils.DataAccess.Entities;
using Common.Enums;
using Common.Enums.ProductCommisssionPrices;

namespace Common.Entities;
public class ValueAddedServicePricingEntity : AuditableEntity<Guid>
{
    public Guid ProductID { get; set; }
    public ProductEntity Product { get; set; } = null!;
    public Guid MCCID { get; set; }
    public Mcc Mcc { get; set; } = null!;
    public Guid BusinessTypeID { get; set; }
    public BusinessTypeEntity BusinessType { get; set; } = null!;
    public Guid VASID { get; set; }
    public ValueAddedService ValueAddedService { get; set; } = null!;

    public decimal SubscriptionFee { get; set; }

    public VatType FeeType { get; set; }
    public BillingType BillingType { get; set; }
    public PriceBillingFrequency BillingFrequency { get; set; }
}
