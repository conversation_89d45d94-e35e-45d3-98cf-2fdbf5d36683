﻿using System;
using System.Threading.Tasks;
using Common.Entities.Gle;

namespace Common.Repositories.Gle;

public interface IGleMerchantRepository : IGleBaseRepository<GleMerchantEntity>
{
    Task<GleMerchantEntity?> GetGleHierarchyByMerchantIdAsync(Guid merchantId);
    Task<GleMerchantEntity?> GetGleMerchantByMerchantIdAsync(Guid merchantId);
    Task<bool> IsMerchantRegisteredInGleAsync(Guid merchantId);
}