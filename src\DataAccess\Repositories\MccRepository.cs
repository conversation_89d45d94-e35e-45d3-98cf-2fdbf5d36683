﻿using AutoMapper;
using Common.Entities;
using Common.Enums;
using Common.Models.MccManagement;
using Common.Models.NonTransactionalFees;
using Common.Repositories;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using Common.Data.Extensions;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Dynamic.Core;
using EFCore.BulkExtensions;
using System.Collections.Concurrent;
using System.IO;
using ClosedXML.Excel;
using FluentValidation.Results;
using Common.Validators.MccManagement;
using System.ComponentModel.DataAnnotations;
using Geidea.Utils.Exceptions;
using System.Net;
using System.Threading;

namespace DataAccess.Repositories;
public class MccRepository : AuditableRepository<Guid, Mcc>, IMccRepository
{
    public MccRepository(DataContext context, IHttpContextAccessor contextAccessor)
        : base(context, contextAccessor)
    {
    }
    public async Task<Mcc?> GetByIdAsync(Guid id)
    {
        return await context.Set<Mcc>().FindAsync(id);
    }
    public async Task<GetMccListResponse> GetMCCList(GetMccListRequest request)
    {
        var MCCListQuery = GetAllMCCs();
        MCCListQuery = SearchMCC(request, MCCListQuery);
        MCCListQuery = FilterMCC(request, MCCListQuery);
        MCCListQuery = SortMCC(request, MCCListQuery);

        GetMccListResponse mccListResponse = new GetMccListResponse();
        mccListResponse.TotalCount = await MCCListQuery.CountAsync();
        mccListResponse.TotalPages = request.Size > 0 ? (int)Math.Ceiling((double)mccListResponse.TotalCount / request.Size) : 1;
        mccListResponse.MCCList = request.Size > 0 ? await MCCListQuery.Page(request.Page, request.Size).ToArrayAsync() : await MCCListQuery.ToArrayAsync();

        return mccListResponse;
    }
    public IQueryable<MccListResponse> GetAllMCCs()
    {
        var Query = from m in context.Set<Mcc>().AsNoTracking()

                    join mc in context.Set<MccCategory>().AsNoTracking()
                    on m.MccCategoryId equals mc.Id
                    into MccCategories
                    from mc in MccCategories.DefaultIfEmpty()

                    select new MccListResponse()
                    {
                        Id = m.Id,
                        Code = m.Code,
                        Name = m.Name,
                        Category = mc.Name,
                        Status = m.Status,
                        CreatedDate = m.CreatedDate,
                        NameAr = m.NameAr,
                    };

        return Query;
    }
    public static IQueryable<MccListResponse> SearchMCC(GetMccListRequest request, IQueryable<MccListResponse> MCCList)
    {
        var SearchTerm = request.SearchTerms.FirstOrDefault(s => !string.IsNullOrEmpty(s.Value));
        if (!string.IsNullOrEmpty(SearchTerm.Value))
        {
            switch (SearchTerm.Key)
            {
                case MccSearchKey.All:
                    MCCList = MCCList.Where(s => s.Code != null && s.Code.Contains(SearchTerm.Value) ||
                                            s.Name != null && s.Name.Contains(SearchTerm.Value) ||
                                            s.Category != null && s.Category.Contains(SearchTerm.Value));
                    break;

                case MccSearchKey.Code:
                    MCCList = MCCList.Where(s => s.Code != null && s.Code.Contains(SearchTerm.Value));
                    break;

                case MccSearchKey.Name:
                    MCCList = MCCList.Where(s => s.Name != null && s.Name.Contains(SearchTerm.Value));
                    break;

                case MccSearchKey.Category:
                    MCCList = MCCList.Where(s => s.Category != null && s.Category.Contains(SearchTerm.Value));
                    break;

                default:
                    break;
            }
        }
        return MCCList;
    }
    public static IQueryable<MccListResponse> FilterMCC(GetMccListRequest request, IQueryable<MccListResponse> MCCList)
    {
        if (request.FilterByStatus != null && request.FilterByStatus.Any())
            MCCList = MCCList.Where(s => request.FilterByStatus.Contains(s.Status));

        if (request.FilterByCategory != null && request.FilterByCategory.Any())
            MCCList = MCCList.Where(s => request.FilterByCategory.Contains(s.Category ?? ""));

        return MCCList;
    }
    public static IQueryable<MccListResponse> SortMCC(GetMccListRequest request, IQueryable<MccListResponse> MCCList)
    {
        SortType orderType;

        if (request.OrderType.ToLower() == SortType.asc.ToString())
            orderType = SortType.asc;
        else if (request.OrderType.ToLower() == SortType.desc.ToString())
            orderType = SortType.desc;
        else
            throw new ArgumentException("Invalid value for OrderType. Allowed values are 'asc' and 'desc'.", nameof(request));

        if (!string.IsNullOrEmpty(request.OrderFieldName))
            MCCList = MCCList.OrderBy(request.OrderFieldName, orderType);

        return MCCList;
    }
    public async Task<MccDetailsResponse?> GetMccDetails(Guid Id)
    {
        var MccDetails = await context.Set<Mcc>()
                                          .AsNoTracking()
                                          .Where(m => m.Id == Id)
                                          .Include(mc => mc.MccCategory)
                                          .Select(m => new MccDetailsResponse()
                                          {
                                              Id = m.Id,
                                              Code = m.Code,
                                              Name = m.Name,
                                              Status = m.Status,
                                              CategoryId = m.MccCategory == null ? Guid.Empty : m.MccCategory.Id,
                                              CategoryName = m.MccCategory == null ? "" : m.MccCategory.Name,
                                              NameAr = m.NameAr
                                          }).SingleOrDefaultAsync();

        return MccDetails;
    }
    public async Task<(int SuccessCount, int FailureCount, byte[] InvalidRecordsExcel)> ImportExcelToDatabase(Guid categoryId, Stream fileStream, CancellationToken cancellationToken)
    {
        var categoryEntity = await context.Set<MccCategory>().FindAsync(categoryId);
        if (categoryEntity == null)
        {
            throw new ServiceException(HttpStatusCode.BadRequest, "category not found");
        }

        var records = ReadExcelFile(fileStream, categoryEntity);

        var validRecords = new ConcurrentBag<Mcc>();
        var invalidRecords = new ConcurrentBag<(Mcc Record, string ErrorMessage)>();
        var duplicateCodes = new HashSet<string>();

        // Fetch all existing codes at once
        var allExistingCodes = new HashSet<string>(
            await context.Set<Mcc>()
                .AsNoTracking()
                .Where(m => m.Code != null)
                .Select(m => m.Code!)
                .ToListAsync()
        );

        // Parallel processing for validation and filtering
        Parallel.ForEach(records, new ParallelOptions { MaxDegreeOfParallelism = Environment.ProcessorCount, CancellationToken = cancellationToken }, record =>
        {
            cancellationToken.ThrowIfCancellationRequested();

            if (string.IsNullOrEmpty(record.Code))
            {
                invalidRecords.Add((record, "Code is null or empty."));
            }
            else if (duplicateCodes.Contains(record.Code) || allExistingCodes.Contains(record.Code))
            {
                invalidRecords.Add((record, $"Duplicate code found: {record.Code}"));
            }
            else
            {
                lock (duplicateCodes)
                {
                    duplicateCodes.Add(record.Code);
                }

                var result = new MccValidator().Validate(record);
                if (result.IsValid)
                {
                    validRecords.Add(record);
                }
                else
                {
                    var errorMessage = string.Join(", ", result.Errors.Select(e => e.ErrorMessage));
                    invalidRecords.Add((record, errorMessage));
                }
            }
        });


        int successCount = await SaveValidRecordsAsync(validRecords, invalidRecords, cancellationToken);

        var invalidRecordsExcel = CreateInvalidRecordsExcel(invalidRecords.ToList());

        return (successCount, invalidRecords.Count, invalidRecordsExcel);
    }

    private async Task<int> SaveValidRecordsAsync(ConcurrentBag<Mcc> validRecords, ConcurrentBag<(Mcc Record, string ErrorMessage)> invalidRecords, CancellationToken cancellationToken)
    {
        int successCount = 0;

        if (!validRecords.IsEmpty)
        {
            try
            {
                using (var transaction = await context.Database.BeginTransactionAsync(cancellationToken))
                {
                    await context.BulkInsertAsync(validRecords.ToList(), cancellationToken: cancellationToken);
                    successCount = validRecords.Count;
                    await transaction.CommitAsync(cancellationToken);
                }

            }
            catch (Exception)
            {
                // Fallback to batch insert
                successCount += await RetryBatchInsertsAsync(validRecords.ToList(), invalidRecords, cancellationToken);
            }
        }

        return successCount;
    }

    private async Task<int> RetryBatchInsertsAsync(List<Mcc> validRecords, ConcurrentBag<(Mcc Record, string ErrorMessage)> invalidRecords, CancellationToken cancellationToken)
    {
        int successCount = 0;
        const int batchSize = 100;

        for (int i = 0; i < validRecords.Count; i += batchSize)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var batch = validRecords.Skip(i).Take(batchSize).ToList();

            try
            {
                await context.BulkInsertAsync(batch, cancellationToken: cancellationToken);
                successCount += batch.Count;
            }
            catch (Exception)
            {
                foreach (var record in batch)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    try
                    {
                        context.Set<Mcc>().Add(record);
                        await context.SaveChangesAsync(cancellationToken);
                        successCount++;
                    }
                    catch (Exception innerEx)
                    {
                        var errorMessage = $"Failed to insert record with Code {record.Code}: {innerEx.Message}";
                        invalidRecords.Add((record, errorMessage));
                    }
                }
            }
        }

        return successCount;
    }

    private static List<Mcc> ReadExcelFile(Stream fileStream, MccCategory category)
    {
        var records = new List<Mcc>();

        using (var workbook = new ClosedXML.Excel.XLWorkbook(fileStream))
        {
            var worksheet = workbook.Worksheet(1);
            int rowCount = worksheet.LastRowUsed().RowNumber();

            for (int row = 2; row <= rowCount; row++)
            {
                var code = worksheet.Cell(row, 1).GetValue<string>();
                var name = worksheet.Cell(row, 2).GetValue<string>();
                var nameAr = worksheet.Cell(row, 3).GetValue<string>();
                var status = worksheet.Cell(row, 4).GetValue<string>();

                if (int.TryParse(status, out int parsedStatus))
                {
                    var record = new Mcc
                    {
                        Code = code,
                        Name = name,
                        NameAr = nameAr,
                        Status = (Status)parsedStatus,
                        MccCategory = category
                    };
                    records.Add(record);
                }
            }
        }

        return records;
    }

    private static byte[] CreateInvalidRecordsExcel(List<(Mcc Record, string ErrorMessage)> invalidRecords)
    {
        using (var workbook = new XLWorkbook())
        {
            var worksheet = workbook.Worksheets.Add("InvalidRecords");
            worksheet.Cell(1, 1).Value = "MCC Code";
            worksheet.Cell(1, 2).Value = "MCC Name";
            worksheet.Cell(1, 3).Value = "StatuQs";
            worksheet.Cell(1, 4).Value = "ErrorMessage";

            int row = 2;
            foreach (var invalidRecord in invalidRecords)
            {
                worksheet.Cell(row, 1).Value = invalidRecord.Record.Code;
                worksheet.Cell(row, 2).Value = invalidRecord.Record.Name;
                worksheet.Cell(row, 3).Value = invalidRecord.Record.Status.ToString();
                worksheet.Cell(row, 4).Value = invalidRecord.ErrorMessage;
                row++;
            }

            using (var memoryStream = new MemoryStream())
            {
                workbook.SaveAs(memoryStream);
                return memoryStream.ToArray();
            }
        }
    }
    public async Task<List<MccListCategoryResponse>> GetAllMccsWithCategoryAsync()
    {
        var query = from m in context.Set<Mcc>().AsNoTracking()
                    join mc in context.Set<MccCategory>().AsNoTracking()
                    on m.MccCategoryId equals mc.Id
                    select new MccListCategoryResponse
                    {
                        MccId = m.Id,
                        CategoryId = mc.Id,
                        CategoryName = mc.Name,
                        MccName = m.Name,
                        MccNameAr = m.NameAr
                    };

        return await query.ToListAsync();
    }

    public async Task<List<Mcc>> GetMCCsByCodesAsync(List<string?> codes)
    {
        return await context.Set<Mcc>()
                            .Where(mcc => codes.Contains(mcc.Code))
                            .ToListAsync();
    }
}
