﻿using Common.Entities;
using Common.Models.UnitPrice;
using Common.Models.ValueAddedSerivcePricing;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Common.Repositories;
public interface IValueAddedServicePricingRepository : IRepository<Guid, ValueAddedServicePricingEntity>
{
    void AddRange(IEnumerable<ValueAddedServicePricingEntity> entities);
    Task SaveVasPricesAsync(List<ValueAddedServicePricingEntity> vasPrices);
    Task<List<ValueAddedServicePricingEntity>> GetExistVasPrices(Expression<Func<ValueAddedServicePricingEntity, bool>> predicate);
    Task<ValueAddedServicePricingEntity?> GetByIdAsync(Guid id);
    Task<int> DeleteBulkAsync(List<Guid> ids);
    Task<List<ValueAddedServicePricingEntity>> GetAddonsPricesByIdsAsync(List<Guid> ids);
    Task AddLogsAsync(List<ValueAddedServicePricingLogEntity> logs);
    Task<ValueAddedServicePriceListResponse> GetValueAddedServicePricesList(ValueAddedServicePriceListRequest request);
    Task<ValueAddedServicePricingEntity?> GetAddOnsPriceByIdAsync(Guid id);
    Task UpdateVasPricesAsync(List<ValueAddedServicePricingEntity> vasPrices);
}
