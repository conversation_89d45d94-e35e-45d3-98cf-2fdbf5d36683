﻿using Common.Entities.Gle;
using Common.Models.Gle;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Repositories.Gle;
public interface IGleUpdateHistoryRepository : IRepository<Guid, GleUpdateHistoryEntity>
{
    Task<GleUpdateHistoryEntity?> FindGleUpdateHistoryAsync(Guid merchantId, string requestType);
    Task AddGleUpdateHistoryAsync(List<GleUpdateHistoryEntity> entities);
    Task UpdateGleUpdateHistoryAsync(List<GleUpdateHistoryEntity> entities);
    Task<List<GleUpdateHistoryEntity>?> FindGleUpdateHistoriesAsync(List<Guid> ids);
}
