﻿using AutoMapper;
using Common.Entities;
using Common.Enums;
using Common.Enums.ProductCommisssionPrices;
using Common.Enums.UnitPrice;
using Common.Models.ValueAddedSerivcePricing;
using Common.Repositories;
using FluentValidation;
using Geidea.Utils.Exceptions;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Threading.Tasks;
using Xunit;

namespace Services.Test.ValueAddedServicePricingServiceTests;

public class CreateTests
{
    private readonly Mock<ILogger<ValueAddedServicePricingService>> loggerMock;
    private readonly Mock<IValueAddedServicePricingRepository> valueAddedServicePricingRepositoryMock;
    private readonly Mock<IMapper> mapperMock;
    private readonly ValueAddedServicePricingService valueAddedServicePricingService;

    public CreateTests()
    {
        loggerMock = new Mock<ILogger<ValueAddedServicePricingService>>();
        valueAddedServicePricingRepositoryMock = new Mock<IValueAddedServicePricingRepository>();
        mapperMock = new Mock<IMapper>();
        valueAddedServicePricingService = new ValueAddedServicePricingService(loggerMock.Object, valueAddedServicePricingRepositoryMock.Object, mapperMock.Object);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturn_ValueAddedServicePriceResponse_With_Created_And_Existing_Prices()
    {
        // Arrange
        var request = new ValueAddedServicesPricingCreateRequest
        {
            ProductIDs = new List<Guid> { Guid.NewGuid() },
            MCCIDs = new List<Guid> { Guid.NewGuid() },
            BusinessTypeIDs = new List<Guid> { Guid.NewGuid() },
            VASIDs = new List<Guid> { Guid.NewGuid() },
            SubscriptionFee = 200,
            FeeType = VatType.Flat,
            BillingType = BillingType.PrePaid,
            BillingFrequency = PriceBillingFrequency.Annual
        };

        var existingEntity = new ValueAddedServicePricingEntity
        {
            Id = Guid.NewGuid(),
            ProductID = request.ProductIDs[0],
            MCCID = request.MCCIDs[0],
            BusinessTypeID = request.BusinessTypeIDs[0],
            VASID = request.VASIDs[0],
            SubscriptionFee = request.SubscriptionFee,
            FeeType = request.FeeType,
            BillingType = request.BillingType,
            BillingFrequency = request.BillingFrequency
        };

        var newEntity = new ValueAddedServicePricingEntity
        {
            Id = Guid.NewGuid(),
            ProductID = request.ProductIDs[0],
            MCCID = request.MCCIDs[0],
            BusinessTypeID = request.BusinessTypeIDs[0],
            VASID = request.VASIDs[0],
            SubscriptionFee = request.SubscriptionFee,
            FeeType = request.FeeType,
            BillingType = request.BillingType,
            BillingFrequency = request.BillingFrequency
        };

        // Mock the repository to return an existing entity
        valueAddedServicePricingRepositoryMock.Setup(r => r.GetExistVasPrices(It.IsAny<Expression<Func<ValueAddedServicePricingEntity, bool>>>()))
            .ReturnsAsync(new List<ValueAddedServicePricingEntity> { existingEntity });

        // Mock the mapper to return a new entity for the new VAS prices
        mapperMock.Setup(m => m.Map<ValueAddedServicePricingEntity>(request))
            .Returns(newEntity);

        mapperMock.Setup(m => m.Map<List<ValueAddedServicesPricingDetails>>(It.IsAny<List<ValueAddedServicePricingEntity>>()))
            .Returns((List<ValueAddedServicePricingEntity> src) => src?.Select(e => new ValueAddedServicesPricingDetails
            {
                Id = e.Id,
                ProductID = e.ProductID,
                MCCID = e.MCCID,
                BusinessTypeID = e.BusinessTypeID,
                VASID = e.VASID,
                SubscriptionFee = e.SubscriptionFee,
                FeeType = e.FeeType,
                BillingType = e.BillingType,
                BillingFrequency = e.BillingFrequency
            }).ToList());

        // Act
        var result = await valueAddedServicePricingService.CreateAsync(request);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(result.CreatedVASPrices);
        Assert.NotNull(result.NewExistingVASPrices);

    }
}
