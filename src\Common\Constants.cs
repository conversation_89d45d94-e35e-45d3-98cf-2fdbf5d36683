﻿using System.Reflection.Metadata;

namespace Common;

public static class Constants
{
    public static class SalesChannel
    {
        public const string Shop = "Shop";
        public const string Onboarding = "Onboarding";
        public const string All = "All";
    }

    public static class Flow
    {
        public const string Normal = "Normal";
        public const string HelpRequired = "HelpRequired";
        public const string Preorder = "Preorder";
    }

    public static class Availability
    {
        public const string Live = "Live";
        public const string Obsolete = "Obsolete";
    }

    public static class MerchantCountries
    {
        public const string Sau = "SAU";
        public const string Egy = "EGY";
        public const string Are = "ARE";
    }

    public static class MerchantTag
    {
        public const string Chain = "CHAIN";
        public const string Retial = "RETAIL";
        public const string Wholesaler = "WHOLESALER";
        public const string SubWholesaler = "SUB_WHOLESALER";
        public const string MasterBusiness = "MASTER_BUSINESS";
        public const string SubBusiness = "SUB_BUSINESS";
        public const string MultiStore = "MULTI_STORE";
    }
    public static class Model
    {
        public const string Sunmi = "Sunmi";
        public const string Ingenico = "Ingenico";
        public const string PGW = "PGW";
    }
    public static class CardChannelType
    {
        public const string POS = "POS";
        public const string PGW = "PGW";
    }
    public static class MIDChannalType
    {
        public const string POSMIDChannalType = "1";
        public const string PGWChannalType = "2";
        public const string DefaultChannalType = "0";
    }
    public static class ModelSequenceGenerator
    {
        public const string SunmiSequenceGenerator = "10";
        public const string IngenicoSequenceGenerator = "20";
        public const string PGWSequenceGenerator = "30";
        public const string DefaultSequenceGenerator = "0";
    }
    public static class ModelTIdSequenceGenerator
    {
        public const string NBETIDInitialValue = "000001";
        public const string NBETIDLastValue = "999999";
    }
    public static class ModelMIdSequenceGenerator
    {
        public const string GeIdeaMIDInitialValue = "001";
        public const string GeIdeaMIDLastValue = "999";
    }
    public static class GleUserCategoryCode
    {
        public const string Retailer = "RTLMER";
        public const string Chain = "CHNMER";
        public const string MultiStore = "MSTRMER";
        public const string MasterBusiness = "CHNMBS";
        public const string Wholesaler = "WSMBS";
        public const string MasterBusinessStore = "CHNMSTR";
        public const string WholesalerStore = "WSSTR";
    }

    public static class GleRegistrationType
    {
        public const string Merchant = "merchant";
        public const string Store = "store";
        public const string Terminal = "terminal";
    }

    public static class GleRegistrationStatus
    {
        public const string Success = "Success";
        public const string Failed = "Failed";
    }

    public static class OrderBillPaymentsStatus
    {
        public const string N_A = "N_A";
        public const string Success = "SUCCESS";
        public const string Fail = "FAIL";
        public const string Pending = "PENDING";
    }

    public static class MeezaRegistrationStatuses
    {
        public const string NotRegistered = "NotRegistered";
        public const string Registered = "Registered";
        public const string Unregistered = "Unregistered";
        public const string ForceUnregistered = "ForceUnregistered";
    }

    public static class AlphaCodes
    {
        public const string Sar = "SAR";
        public const string Egp = "EGP";
        public const string Aed = "AED";
    }

    public static class AcquiringProviders
    {
        public const string MPGS = "MPGS";
        public const string Postilion = "Postilion";
    }

    public static class ThreeDSecureProviders
    {
        public const string MPGS = "MPGS";
        public const string CyberSource = "CyberSource";
    }

    public static class ProductTypes
    {
        public const string Scheme = "SCHEME";
    }

    public const string InitiatedByInternet = "Internet";
    public const string InitiatedByMerchant = "Merchant";

    public const string UserNameHeader = "X-UserName";

    public static class TerminalDataSetAvailability
    {
        public const string Available = "AVAILABLE";
        public const string Used = "USED";
        public const string Invalid = "INVALID";
    }

    public static class ProductCodes
    {
        public const string PayByLinkBundle = "PAY_BY_LINK_BUNDLE";
        public const string PaymentGateway = "PAYMENT_GATEWAY";
        public const string PaymentGatewayBundle = "PAYMENT_GATEWAY_BUNDLE";
        public const string BillPayment = "BILL_PAYMENT";
        public const string GoSmartWithBP = "GO_SMART_BP";
        public const string GoAir = "GO_AIR";
        public const string Softpos = "SOFT_POS";
        public const string Diners = "DINERS";
        public const string DinersGw = "DINERS_GW";
        public const string Discover = "DISCOVER";
        public const string DiscoverGw = "DISCOVER_GW";
    }

    public static class ReferralChannel
    {
        public const string Unassigned = "UNASSIGNED";
    }

    public static class AcquiringLedger
    {
        public const string NBEBank = "NBE_BANK";
        public const string ALXBank = "ALX_BANK";
        public const string GeIdea = "GeIdea";
    }

    public static class RandomSequenceGenerator
    {
        public const string NBETIDAllowedChars = "**********";
        public const string NBETIDChainFormat = "30xxxxxx";
        public const string NBETIDRetailOrMultiStore = "31xxxxxx";
        public const string NBETIDWholesalerOrSubWholesaler = "38xxxxxx";
        public const string NBETIDMasterBusinessOrSubBusiness = "39xxxxxx";
        public const string NBEMIDStaticPart = "001770";
        public const string NBEMIDInitialValue = "************";
        public const string NBEMIDLastValue = "************";
        public const string NBEMaxMIDTempProduction = "************"; // This temp value for MAX MID to fix a production issue, don't remove it.
        public const int NBEMIDIncrementalValue = 1000;
        public const string ALXTIDAllowedChars = "**********";
        public const string ALXTIDChainFormat = "TGF0xxxx";
        public const string ALXTIDRetailOrMultiStore = "TGF1xxxx";
        public const string ALXTIDWholesalerOrSubWholesaler = "TGF8xxxx";
        public const string ALXTIDMasterBusinessOrSubBusiness = "TGF9xxxx";
        public const string ALXMIDStaticPart = "RGF";
        public const string ALXMIDInitialValue = "RGF00001";
        public const string ALXMIDLastValue = "RGF99999";
        public const int ALXMIDIncrementalValue = 1;
    }

    public const string OnlyAlphanumericRegex = "^[0-9a-zA-Z]+$";
    public const string OnlyNumericRegex = "^[0-9]+$";

    public const string DefaultAccountNumber = "*************";

    public const string DefaultTenantCode = "000001";

    public static class CounterParty
    {
        public const string Saudi = "GEIDEA_SAUDI";
        public const string Egypt = "GEIDEA_EGYPT";
        public const string Uae = "GEIDEA_UAE";
    }

    public static class ProductType
    {
        public const string Gateway = "GWAY";
        public const string Accessories = "ACCESSORIES";
        public const string Bundle = "BUNDLE";
        public const string Terminal = "TERMINAL";
        public const string Scheme = "SCHEME";
        public const string MiniEcr = "MINI_ECR";
        public const string Meeza = "MEEZA";
        public const string Services = "SERVICES";
        public const string Mpos = "M_POS";
    }

    public static class ProductCode
    {
        public const string BillPayment = "BILL_PAYMENT";
        public const string GoSmart = "GO_SMART";
        public const string GoSmartWithBP = "GO_SMART_BP";
        public const string GoAir = "GO_AIR";
        public const string SoftPos = "SOFT_POS";
    }

    public static class TerminalConnectionType
    {
        public const string MPGS = "MPGS";
        public const string HostToHost = "H2H";
    }

    public static class TerminalChannelType
    {
        public const string Softpos = "SOFT_POS";
        public const string Smartpos = "SMART_POS";
        public const string GoSmart = "GO_SMART";
        public const string GoAir = "GO_AIR";
    }
    public static class ProductAvailability
    {
        public const string Live = "Live";
        public const string Obsolete = "Obsolete";
    }

    public static class GleUpdateRequestType
    {
        public const string UpdateMerchantTag = "MerchantTag";
    }

    public const string ProductsSearchDictionary = " {\r\n    \"Name\": \"string\",\r\n    \"Subname\": \"string\",\r\n    \"Code\": \"string\"\r\n  }";
    public const string CommissionFeesDictionary = " {\r\n    \"All\": \"string\",\r\n    \"Id\": \"string\",\r\n    \"Name\": \"string\"\r\n  }";
    public const string NonTransactionalFeesDictionary = " {\r\n    \"All\": \"string\",\r\n    \"Id\": \"string\",\r\n    \"Name\": \"string\"\r\n  }";
    public const string MCCSearchDictionary = " {\r\n    \"All\": \"string\",\r\n    \"Code\": \"string\",\r\n    \"Name\": \"string\"\r\n, \r\n    \"Category\": \"string\"\r\n  }";
    public const string BusinessTypeSearchDictionary = " {\r\n    \"All\": \"string\",\r\n    \"Id\": \"string\",\r\n    \"Type\": \"string\"\r\n }";
    public const string UnitPriceSearchDictionary = " {\r\n    \"All\": \"string\",\r\n    \"ProductName\": \"string\",\r\n    \"ProductCode\": \"string\"\r\n, \r\n    \"MccName\": \"string\"\r\n, \r\n    \"MccCode\": \"string\"\r\n, \r\n    \"BusinessType\": \"string\"\r\n }";
    public const string AddOnsSearchDictionary = " {\r\n    \"All\": \"string\",\r\n    \"ProductName\": \"string\",\r\n    \"ProductCode\": \"string\"\r\n, \r\n    \"MccName\": \"string\"\r\n, \r\n    \"MccCode\": \"string\"\r\n, \r\n  \"AddOnsName\": \"string\"\r\n, \r\n  \"AddOnsCode\": \"string\"\r\n }";
}
