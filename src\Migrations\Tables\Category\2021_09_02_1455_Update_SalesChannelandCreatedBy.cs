﻿using FluentMigrator;

namespace Migrations.Tables.Category;

[Migration(2021_09_02_1455)]
public class Category_UpdateSalesChannelandCreatedBy : ForwardOnlyMigration
{
    public override void Up()
    {
        Update.Table("Category").Set(new { SalesChannel = "All" }).Where(new { Counterparty = "GEIDEA_UAE" });
        Update.Table("Category").Set(new { CreatedBy = "00000000-0000-0000-0000-000000000000" }).Where(new { Counterparty = "GEIDEA_UAE" });
    }
}
