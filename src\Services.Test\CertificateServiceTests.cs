using System;
using System.IO;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Common.Data;
using Common.Data.ProductType;
using Common.Entities;
using Common.Models.ProductInstance;
using Common.Repositories;
using Common.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using ProductService;
using Xunit;

namespace Services.Test;

public class CertificateServiceTests
{
    private static readonly Guid ProductInstanceId = Guid.NewGuid();
    private string keyFile = string.Empty;
    private string keyDerFile = string.Empty;
    private string csrFile = string.Empty;
    private string pfxFile = string.Empty;

    private readonly Mock<IProductInstanceRepository> productInstanceRepository;
    private readonly Mock<IOpenSslCommandProcessor> openSslCommandProcessor;
    private readonly Mock<IProductInstancePublisher> productInstancePublisher;
    private readonly CertificateService service;

    private readonly GenerateCsrRequest csrRequest = new GenerateCsrRequest
    {
        ProductInstanceId = ProductInstanceId,
        CommonName = "Name",
        Country = "US",
        Email = "<EMAIL>",
        Location = "Location",
        Organization = "Org",
        OrganizationalUnit = "OU",
        State = "State"
    };

    private readonly ProductInstanceEntity productInstance = new ProductInstanceEntity
    {
        Id = ProductInstanceId,
        Product = new ProductEntity
        {
            Type = ProductTypes.GWAY.ToString()
        },
        Metadata = "{\"IsSamsungPayAppEnabled\":true,\"AppleCertificatePrivateKey\":\"MIGHAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBG0wawIBAQQgbD3UqUqpz+vtQq2Wt+czxkVoFMFzKTZOkuJ5pj/U+uGhRANCAAQlndkKjYAw3VRaUpEQsMmbpKElbjaz0PuNjNFXddsoT4i2HkGOlioCwSFaUMIQENLAx/r4C1swKXPEuH5h0eoU\"}"
    };

    public CertificateServiceTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        var mapper = new Mapper(configuration);

        var applePaySettings = new Mock<IOptionsMonitor<ApplePaySettings>>();
        applePaySettings
            .Setup(x => x.CurrentValue)
            .Returns(new ApplePaySettings());

        var publisherSelector = new Mock<IProductInstancePublisherSelector>();
        var logger = new Mock<ILogger<CertificateService>>();

        openSslCommandProcessor = new Mock<IOpenSslCommandProcessor>();

        productInstancePublisher = new Mock<IProductInstancePublisher>();
        publisherSelector.Setup(x => x
                .GetProductInstancePublisher(ProductTypes.GWAY))
            .Returns(productInstancePublisher.Object);

        productInstanceRepository = new Mock<IProductInstanceRepository>();
        productInstanceRepository
            .Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<bool>()))
            .Returns(Task.FromResult(productInstance));

        service = new CertificateService(
            productInstanceRepository.Object,
            openSslCommandProcessor.Object,
            publisherSelector.Object,
            applePaySettings.Object,
            mapper,
            logger.Object);
    }


    [Fact]
    public async Task UploadSignedCertificateSuccessfully()
    {
        var certificate = new X509Certificate2("../../../certificate.der").RawData;

        var expectedDate = new DateTime(2022, 2, 2, 15, 48, 38);
        var pfxFileBytes = Encoding.UTF8.GetBytes("Sample pfx");

        openSslCommandProcessor
            .Setup(x => x.Execute(It.Is<string>(command => command.Contains("pkcs12 -export"))))
            .Callback((string command) =>
            {
                pfxFile = command.Split("-out ")[1];

                using var fileStream = File.Create(pfxFile);
                fileStream.Write(pfxFileBytes);
            });

        var result = await service.UploadSignedCertificate(ProductInstanceId, certificate);

        var gatewayData = result.Data as GatewayData;

        Assert.NotNull(gatewayData);
        Assert.True(gatewayData.IsApplePayMobileCertificateAvailable);
        Assert.Equal(Convert.ToBase64String(pfxFileBytes), gatewayData.ApplePaymentProcessingCertificate);
        Assert.Equal(expectedDate.ToString("G"), gatewayData.ApplePaymentProcessingCertificateExpiryDate?.ToString("G"));

        openSslCommandProcessor.Verify(x => x.Execute(It.IsAny<string>()), Times.Exactly(3));
        productInstanceRepository.Verify(x => x.GetByIdAsync(ProductInstanceId, true), Times.Once);
        productInstanceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        productInstancePublisher.Verify(x => x.PublishUpdatedEvent(productInstance), Times.Once);
    }

    [Fact]
    public async Task GenerateCsrShouldReturnTheGenerateSamsungCsr()
    {
        var response = await service.CreateSamsungCsrAndPrivateKey(csrRequest);

        Assert.NotNull(response);
    }
}
