﻿using Common.Models.Gle;
using FluentValidation;

namespace Common.Validators.Gle;

public class GleHistoryLogRequestValidator : AbstractValidator<GleHistoryLogRequest>
{
    public GleHistoryLogRequestValidator()
    {
        RuleFor(x => x.MerchantId)
            .NotEmpty()
            .WithErrorCode(Errors.InvalidMerchantId.Code)
            .WithMessage(Errors.InvalidMerchantId.Message);

        RuleFor(x => x.OrderId)
            .NotEmpty()
            .WithErrorCode(Errors.InvalidOrderId.Code)
            .WithMessage(Errors.InvalidOrderId.Message);
    }
}