﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Entities.Gle;

namespace Common.Repositories.Gle;

public interface IGleTerminalRepository : IGleBaseRepository<GleTerminalEntity>
{
    Task<GleTerminalEntity?> GetGleTerminalByProductInstanceIdAsync(Guid productInstanceId);
    public Task<IReadOnlyCollection<GleTerminalEntity>> GetGleTerminalListByOrderIdAsync(Guid orderId);
    Task<bool?> OrderHasAllInstancesRegisteredToGle(Guid orderId);
}