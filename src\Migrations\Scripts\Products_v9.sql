USE PRODUCTS
GO

DECLARE @Ids TABLE(ID UNIQUEIDENTIFIER)
DECLARE @InsertedId UNIQUEIDENTIFIER

INSERT INTO PRODUCTS([Availability], Flow, SalesChannel, Code, [Type], CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
VALUES('Live', 'Normal', 'Shop', 'PAY_BY_LINK', 'SERVICES', 'n/a', GETUTCDATE(), GETUTCDATE())

SELECT TOP 1 @InsertedId = ID FROM @Ids 
DELETE FROM @Ids
 
INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
VALUES('ONE_OFF','SETUP_CHARGE',0, @InsertedId, 0, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

