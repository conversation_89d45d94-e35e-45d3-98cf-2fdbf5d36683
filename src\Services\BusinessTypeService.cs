﻿using AutoMapper;
using Common;
using Common.Entities;
using Common.Models;
using Common.Models.businessType;
using Common.Models.CommissionFees;
using Common.Repositories;
using Common.Services;
using Common.Validators;
using DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Services;
public class BusinessTypeService : IBusinessTypeService
{
    private readonly ILogger<BusinessTypeService> logger;
    private readonly IBusinessTypeRepository businessTypeRepository;
    private readonly IMapper mapper;

    public BusinessTypeService(ILogger<BusinessTypeService> logger, IBusinessTypeRepository businessTypeRepository, IMapper mapper)
    {
        this.logger = logger;
        this.businessTypeRepository = businessTypeRepository;
        this.mapper = mapper;
    }

    #region Create
    public async Task<BusinessTypeResponse> CreateAsync(BusinessTypeRequest request)
    {
        var codeExists = await businessTypeRepository.ExistsAsync(v => v.Code == request.Code);
        if (codeExists)
        {
            logger.LogError("CreateAsync: Invalid BusinessType Code '{Code}' already exists", request.Code);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.CodeAlreadyExist);
        }
        var businessType = mapper.Map<BusinessTypeEntity>(request);
        try
        {
            businessTypeRepository.Add(businessType);
            await businessTypeRepository.SaveChangesAsync();

            logger.LogInformation("Added BusinessType with id '{ businessTypeId}' and Name '{ businessTypeName}'.", businessType.Id, businessType.Name);
            return mapper.Map<BusinessTypeResponse>(businessType);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating cBusinessType with Name '{Name}'.", request.Name);
            throw new ServiceException(HttpStatusCode.BadRequest, $"Error creating BusinessType: {ex.Message}");
        }


    }
    #endregion

    #region Update
    public async Task<BusinessTypeResponse> UpdateAsync(Guid id, BusinessTypeUpdateRequest businessTypeUpdateRequest)
    {
        if (businessTypeUpdateRequest == null)
        {
            logger.LogError("Invalid update business type request");
            throw new ServiceException(HttpStatusCode.BadRequest, "Invalid update business type request");
        }

        var existingBusinessType = await businessTypeRepository.GetByIdAsync(id);
        if (existingBusinessType == null)
        {
            logger.LogError("BusinessType with id '{id}' not found", id);
            throw new ServiceException(HttpStatusCode.NotFound, $"BusinessType with id '{id}' not found");
        }

        mapper.Map(businessTypeUpdateRequest, existingBusinessType);
        try
        {
            businessTypeRepository.Update(existingBusinessType);
            await businessTypeRepository.SaveChangesAsync();

            logger.LogInformation("Updated BusinessType with id '{businessTypeId}' and Name '{businessTypeName}'.", existingBusinessType.Id, existingBusinessType.Name);
            return mapper.Map<BusinessTypeResponse>(existingBusinessType);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating BusinessType with id '{id}' and Name '{Name}'.", id, businessTypeUpdateRequest.Name);
            throw new ServiceException(HttpStatusCode.BadRequest, $"Error updating BusinessType: {ex.Message}");
        }
    }
    #endregion

    #region Listing

    public async Task<GetBusinessTypesListResponse> GetBusinessTypesList(GetBusinessTypesListRequest request)
    {
        if (request == null)
        {
            logger.LogError("Invalid get business types list request");
            throw new ServiceException(HttpStatusCode.BadRequest, "Invalid get business types list request");
        }
        try
        {
            return await businessTypeRepository.GetBusinessTypesList(request);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "something has went wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, "something has went wrong!");
        }
    }
    #endregion

    #region ViewDetails
    public async Task<BusinessTypeDetailsResponse?> GetBusinessTypeDetails(Guid Id)
    {
        if (Id == Guid.Empty)
        {
            logger.LogError("Invalid business type Id");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidIdentifier);
        }
        var BusinessTypeDetailsObj = await businessTypeRepository.GetBusinessTypeDetails(Id);
        if (BusinessTypeDetailsObj == null)
        {
            logger.LogError("Business type with Id {Id} not found", Id);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductNotFound);
        }
        return BusinessTypeDetailsObj;
    }
    #endregion
    #region ToggleStatus
    public async Task<bool> ToggleStatus(Guid Id, bool Status)
    {
        var result = false;
        if (Id == Guid.Empty)
        {
            logger.LogError("Invalid business type Id.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidIdentifier);
        }

        var BusinessTypeObj = await businessTypeRepository.GetByIdAsync(Id);
        if (BusinessTypeObj == null)
            return result;

        BusinessTypeObj.Status = Status ? Common.Enums.Status.Active : Common.Enums.Status.Inactive;
        businessTypeRepository.Update(BusinessTypeObj);
        await businessTypeRepository.SaveChangesAsync();
        return true;
    }
    #endregion
    #region GetBusinessTypes Names and Ids
    public async Task<List<BasicBusinessTypesInfo>> GetBusinessTypesNamesAsync()
    {
        try
        {
            var getBusinessTypesNames = await businessTypeRepository.GetBusinessTypesNamesAsync();
            return getBusinessTypesNames;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while retrieving product IDs and names.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An error occurred while retrieving product data.");
        }
    }
    #endregion
}
