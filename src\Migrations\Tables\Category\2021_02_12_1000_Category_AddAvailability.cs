﻿using FluentMigrator;

namespace Migrations.Tables.Category;

[Migration(2021_02_12_1000)]
public class Category_AddAvailability : ForwardOnlyMigration
{
    public override void Up()
    {
        Alter.Table("Category").AddColumn("Availability").AsString().Nullable();

        Update.Table("Category").Set(new { Availability = "All" }).Where(new { Code = "GO_FAMILY" });
        Update.Table("Category").Set(new { Availability = "All" }).Where(new { Code = "PRO_FAMILY" });
        Update.Table("Category").Set(new { Availability = "All" }).Where(new { Code = "ONLINE_FAMILY" });
        Update.Table("Category").Set(new { Availability = "All" }).Where(new { Code = "RESTAURANT" });
        Update.Table("Category").Set(new { Availability = "All" }).Where(new { Code = "RETAIL" });
        Update.Table("Category").Set(new { Availability = "Shop" }).Where(new { Code = "ACCESSORIES" });
        Update.Table("Category").Set(new { Availability = "All" }).Where(new { Code = "PAY_FAMILY" });
    }
}
