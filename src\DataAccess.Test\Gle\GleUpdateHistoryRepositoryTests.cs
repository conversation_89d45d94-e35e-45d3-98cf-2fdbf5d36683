﻿using Common;
using Common.Entities.Gle;
using Common.Models.Gle;
using Common.Repositories.Gle;
using DataAccess.Repositories.Gle;
using DataAccess.Test.TestData;
using Geidea.Utils.Counterparty.Providers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Test.Gle;
public class GleUpdateHistoryRepositoryTests
{
    private readonly IHttpContextAccessor contextAccessor = Substitute.For<IHttpContextAccessor>();
    private DataContext context;
    private GleUpdateHistoryRepository gleUpdateHistoryRepository;

    private readonly Guid successMerchantId = Guid.NewGuid();
    private readonly Guid failedMerchantId = Guid.NewGuid();

    [SetUp]
    public async Task SetUp()
    {
        context = DbContextHelper.CreateInMemoryDatabase(Substitute.For<ICounterpartyProvider>());
        gleUpdateHistoryRepository = new GleUpdateHistoryRepository(context, contextAccessor);

        await context.GleUpdateHistory.AddRangeAsync(new List<GleUpdateHistoryEntity>
        {
            new()
            {
                Id = new Guid("7478F0A4-411F-4178-C41B-08DB8072FD41"),
                CreatedBy = Guid.NewGuid().ToString(),
                CreatedDate = DateTime.Now,
                GleUserId = "7639103",
                GleLoginId = "1230831",
                GleLoginId2 = "3245111",
                ParentGleUserId = null,
                MerchantId = successMerchantId,
                RequestType = Constants.GleUpdateRequestType.UpdateMerchantTag,
                OldValue = "Old",
                NewValue = "New",
                GleStatus = "Success",
                GleResponse = "N/A"
            },

            new()
            {
                Id = new Guid("7478F0A4-411F-4178-C41B-08DB8072FD40"),
                CreatedBy = Guid.NewGuid().ToString(),
                CreatedDate = DateTime.Now,
                GleUserId = "7639103x",
                GleLoginId = "1230831x",
                GleLoginId2 = "3245111x",
                ParentGleUserId = null,
                MerchantId = failedMerchantId,
                RequestType = Constants.GleUpdateRequestType.UpdateMerchantTag,
                OldValue = "Old",
                NewValue = "New",
                GleStatus = "Failed",
                GleResponse = "User Not Found"
            }
        });

        await context.SaveChangesAsync();
    }

    [Test]
    public async Task FindGleUpdateHistoryAsync_ShouldReturnData()
    {
        // Act
        var result = await gleUpdateHistoryRepository.FindGleUpdateHistoryAsync(successMerchantId, Constants.GleUpdateRequestType.UpdateMerchantTag);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(Constants.GleUpdateRequestType.UpdateMerchantTag, result.RequestType);
        Assert.AreEqual(successMerchantId, result.MerchantId);
    }

    [Test]
    public async Task FindGleUpdateHistoryAsync_ShouldNotReturnData()
    {
        // Act
        var result = await gleUpdateHistoryRepository.FindGleUpdateHistoryAsync(Guid.NewGuid(), Constants.GleUpdateRequestType.UpdateMerchantTag);

        // Assert
        Assert.Null(result);
    }

    [Test]
    public async Task UpdateGleUpdateHistory_ValidRequest_ShouldUpdate()
    {
        var gleUpdateHistory = await gleUpdateHistoryRepository.FindGleUpdateHistoryAsync(successMerchantId, Constants.GleUpdateRequestType.UpdateMerchantTag);

        gleUpdateHistory.NewValue = "00001";
        await gleUpdateHistoryRepository.UpdateGleUpdateHistoryAsync(new List<GleUpdateHistoryEntity>() { gleUpdateHistory });

        var gleUpdateHistoryAfterUpdate = await gleUpdateHistoryRepository.FindGleUpdateHistoryAsync(successMerchantId, Constants.GleUpdateRequestType.UpdateMerchantTag);

        Assert.AreEqual("00001", gleUpdateHistoryAfterUpdate.NewValue);
    }

    [Test]
    public async Task AddGleUpdateHistory_ValidRequest_ShouldAdd()
    {
        List<GleUpdateHistoryEntity> entities = new();

        GleUpdateHistoryEntity entity = new()
        {
            Id = Guid.NewGuid(),
            CreatedBy = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.Now,
            GleUserId = "UserId",
            GleLoginId = "LoginId",
            GleLoginId2 = "LoginId2",
            ParentGleUserId = null,
            MerchantId = Guid.NewGuid(),
            RequestType = Constants.GleUpdateRequestType.UpdateMerchantTag,
            OldValue = "Old",
            NewValue = "New",
            GleStatus = "Success",
            GleResponse = "N/A"
        };
        entities.Add(entity);

        await gleUpdateHistoryRepository.AddGleUpdateHistoryAsync(entities);

        var gleUpdateHistory = await gleUpdateHistoryRepository.FindGleUpdateHistoryAsync(entity.MerchantId, Constants.GleUpdateRequestType.UpdateMerchantTag);

        Assert.NotNull(gleUpdateHistory);
        Assert.AreEqual(Constants.GleUpdateRequestType.UpdateMerchantTag, gleUpdateHistory.RequestType);
        Assert.AreEqual(entity.MerchantId, gleUpdateHistory.MerchantId);
    }

    [Test]
    public async Task FindGleUpdateHistoriesAsync_ShouldReturnData()
    {
        // Act
        var result = await gleUpdateHistoryRepository.FindGleUpdateHistoriesAsync(new List<Guid> { new Guid("7478F0A4-411F-4178-C41B-08DB8072FD40"), new Guid("7478F0A4-411F-4178-C41B-08DB8072FD41") });

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(2, result.Count);
    }
}
