﻿using FluentMigrator;

namespace Migrations.Tables.Products;
public class UaeAddDinersProductToProductTbl : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
DECLARE @DinersProductId_UAE AS uniqueidentifier=NEWID();
 
--add new product with type DINERS_GW to UAE

INSERT INTO [dbo].[Products]
([Id]
,[Availability]
,[Code]
,[Type]
,[ValidFrom]
,[CreatedBy]
,[CreatedDate]
,[UpdatedBy]
,[Version]
,[Counterparty],
[DisplayOrder],
[SalesChannel])
VALUES
(@DinersProductId_UAE
,'Live'
,'DINERS_GW'
,'SCHEME'
,GETUTCDATE()
,'00000000-0000-0000-0000-000000000000'
,GETUTCDATE()
,'00000000-0000-0000-0000-000000000000'
,0
,'GEIDEA_UAE',NULL,NULL)

--add new DINERS_UAE product as part of BUNDLE

IF EXISTS (SELECT 1 Id FROM Products WHERE type='BUNDLE' AND Counterparty='GEIDEA_UAE' AND Availability='Live' AND Code='PAYMENT_GATEWAY_BUNDLE')
INSERT INTO ProductParts (ProductId, PartId, Quantity) VALUES
((SELECT TOP 1 Id FROM Products WHERE type='BUNDLE' AND Counterparty='GEIDEA_UAE' AND Availability='Live' AND Code='PAYMENT_GATEWAY_BUNDLE'), @DinersProductId_UAE, 1)

IF EXISTS (SELECT 1 Id FROM Products WHERE type='BUNDLE' AND Counterparty='GEIDEA_UAE' AND Availability='Live' AND Code='PAY_BY_LINK_BUNDLE')
INSERT INTO ProductParts (ProductId, PartId, Quantity) VALUES
((SELECT TOP 1 Id FROM Products WHERE type='BUNDLE' AND Counterparty='GEIDEA_UAE' AND Availability='Live' AND Code='PAY_BY_LINK_BUNDLE'), @DinersProductId_UAE, 1)

");
    }
}
