﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.Products;
[Migration(2024_12_24_1605)]
public class PendingUnitPriceList : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "PendingUnitPriceList.sql")
            );
    }
}
[Migration(2024_12_24_1630)]
public class PendingUnitPriceList_AddUnitPriceId : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "PendingUnitPriceList.sql")
            );
    }
}
[Migration(2025_01_14_1715)]
public class PendingUnitPriceList_AddUserCounterParty : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "PendingUnitPriceList.sql")
            );
    }
}
