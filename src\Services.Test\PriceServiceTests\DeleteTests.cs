﻿using System;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Common;
using Common.Entities;
using Common.Models;
using Common.Options;
using Common.Services;
using DataAccess;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NSubstitute;
using ProductService;
using Xunit;

namespace Services.Test.PriceServiceTests;

public class DeleteTests
{
    private readonly Mock<ILogger<PriceService>> logger = new Mock<ILogger<PriceService>>();
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly Mock<IProductService> productService = new Mock<IProductService>();
    private readonly PriceRepository priceRepository;
    private readonly PriceService priceService;
    private readonly IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;
    private readonly ICounterpartyProvider counterpartyProvider;

    private readonly PriceEntity priceEntity = new PriceEntity
    {
        ChargeFrequency = "RECCURRING_CHARGE",
        ChargeType = "MONTH",
        Currency = "EUR",
        ExemptFromVAT = true,
        Group = "group",
        MaxPrice = 100,
        PercentagePrice = 10,
        PerItemPrice = 20,
        Priority = 0,
        RentalPeriod = 24,
        Threshold = 200,
        ThresholdType = "LT",
        Product = new ProductEntity
        {
            Code = "TEST"
        }
    };
    private readonly PriceEntity price1 = new PriceEntity
    {
        ChargeFrequency = "RECCURRING_CHARGE",
        ChargeType = "MONTH",
        Currency = "EUR",
        ExemptFromVAT = true,
        Group = "group",
        MaxPrice = 100,
        PercentagePrice = 10,
        PerItemPrice = 20,
        Priority = 0,
        RentalPeriod = 24,
        Threshold = 200,
        ThresholdType = "LT",
        Product = new ProductEntity
        {
            Code = "TEST"
        }
    };

    public DeleteTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
           .UseInMemoryDatabase(databaseName: "PriceDeleteTests" + Guid.NewGuid().ToString())
           .Options;

        var context = new DataContext(options, new CounterpartyProvider());
        context.Prices.Add(priceEntity);
        context.Prices.Add(price1);
        context.SaveChanges();

        softposEgyptFeatureToggle = Substitute.For<IOptions<SoftposEgyptFeatureToggle>>();
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = true });
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();

        var productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);
        priceRepository = new PriceRepository(context, httpContext.Object);
        priceService = new PriceService(logger.Object, productRepository, priceRepository,
            mapper, productService.Object);
    }

    [Fact]
    public async Task PriceNotFound()
    {
        await priceService
            .Invoking(x => x.DeleteAsync(Guid.NewGuid()))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
            x.ProblemDetails.Type == Errors.PriceNotFound.Code);
    }

    [Fact]
    public async Task ProductIsUsed()
    {
        productService.Setup(x => x.IsProductUsedAsync(priceEntity.ProductId)).Returns(Task.FromResult(true));

        await priceService
            .Invoking(x => x.DeleteAsync(priceEntity.Id))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.InvalidProductPriceRequest.Code);
    }

    [Fact]
    public async Task Delete()
    {
        await priceService.DeleteAsync(price1.Id);

        var retrievedPrice = await priceRepository.FindAsync(new FindPriceRequest { Id = price1.Id }, false);
        retrievedPrice.Should().HaveCount(0);
    }
}
