﻿BEGIN TRANSACTION;

DECLARE @PayFamilyCategoryId UNIQUEIDENTIFIER
SELECT TOP 1 @PayFamilyCategoryId = ID FROM [PRODUCTS].[dbo].[Category] WHERE Code = 'PAY_FAMILY' AND Counterparty = 'GEIDEA_EGYPT'

DECLARE @PaymentGatewayId UNIQUEIDENTIFIER
SELECT TOP 1 @PaymentGatewayId = ID FROM [PRODUCTS].[dbo].[Products] WHERE Code = 'PAYMENT_GATEWAY_BUNDLE' AND Availability = 'Live' AND Counterparty = 'GEIDEA_EGYPT'
AND [Id] in (SELECT ProductId FROM [PRODUCTS].[dbo].[ProductCategories] pc where pc.[CategoryId] = @PayFamilyCategoryId)

DECLARE @MasterCardId UNIQUEIDENTIFIER
SELECT TOP 1 @MasterCardId = ID FROM [PRODUCTS].[dbo].[Products] WHERE Code = 'MC_GW' AND Availability = 'Live' AND Counterparty = 'GEIDEA_EGYPT'
AND [Id] in (SELECT PartId FROM [PRODUCTS].[dbo].[ProductParts] where ProductId = @PaymentGatewayId)

DECLARE @VisaId UNIQUEIDENTIFIER
SELECT TOP 1 @VisaId = ID FROM [PRODUCTS].[dbo].[Products] WHERE Code = 'VISA_GW' AND Availability = 'Live' AND Counterparty = 'GEIDEA_EGYPT'
AND [Id] in (SELECT PartId FROM [PRODUCTS].[dbo].[ProductParts] where ProductId = @PaymentGatewayId)

DECLARE @MeezaId UNIQUEIDENTIFIER
SELECT TOP 1 @MeezaId = ID FROM [PRODUCTS].[dbo].[Products] WHERE Code = 'MEEZA_GW' AND Availability = 'Live' AND Counterparty = 'GEIDEA_EGYPT'
AND [Id] in (SELECT PartId FROM [PRODUCTS].[dbo].[ProductParts] where ProductId = @PaymentGatewayId)

DECLARE @MasterCardPriceId UNIQUEIDENTIFIER
SELECT TOP 1 @MasterCardPriceId = ID FROM [PRODUCTS].[dbo].[Prices] WHERE ChargeType = 'PURCHASE_CNP' AND DeletedFlag = 0 AND [ProductId] = @MasterCardId

DECLARE @VisaPriceId UNIQUEIDENTIFIER
SELECT TOP 1 @VisaPriceId = ID FROM [PRODUCTS].[dbo].[Prices] WHERE ChargeType = 'PURCHASE_CNP' AND DeletedFlag = 0 AND [ProductId] = @VisaId

DECLARE @MeezaPriceId UNIQUEIDENTIFIER
SELECT TOP 1 @MeezaPriceId = ID FROM [PRODUCTS].[dbo].[Prices] WHERE ChargeType = 'PURCHASE_CNP' AND DeletedFlag = 0 AND [ProductId] = @MeezaId

UPDATE [PRODUCTS].[dbo].[Prices] SET PerItemPrice = 300, PercentagePrice = 275 WHERE Id = @MasterCardPriceId 
UPDATE [PRODUCTS].[dbo].[Prices] SET PerItemPrice = 300, PercentagePrice = 275 WHERE Id = @VisaPriceId 
UPDATE [PRODUCTS].[dbo].[Prices] SET PerItemPrice = 300, PercentagePrice = 275 WHERE Id = @MeezaPriceId 

COMMIT;