﻿using Common.Entities;
using Common.Models.MccManagement;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Common.Repositories;
public interface IMccRepository : IRepository<Guid, Mcc>
{
    Task<Mcc?> GetByIdAsync(Guid id);
    Task<GetMccListResponse> GetMCCList(GetMccListRequest request);
    Task<MccDetailsResponse?> GetMccDetails(Guid Id);
    // New method for importing MCC data
    Task<(int SuccessCount, int FailureCount, byte[] InvalidRecordsExcel)> ImportExcelToDatabase(Guid categoryId, Stream fileStream, CancellationToken cancellationToken);
    Task<List<MccListCategoryResponse>> GetAllMccsWithCategoryAsync();
    Task<List<Mcc>> GetMCCsByCodesAsync(List<string?> codes);
}
