﻿using System;
using Geidea.Messages.Gsdk;

namespace Common.Models;

public class PointOfSaleResponseMessageReceivedEventArgs : EventArgs
{
    public PointOfSaleResponseMessageReceivedEventArgs(GsdkPointOfSaleResponse message, string counterparty)
    {
        Message = message;
        Counterparty = counterparty;
    }

    public GsdkPointOfSaleResponse Message { get; }
    public string Counterparty { get; }
}
