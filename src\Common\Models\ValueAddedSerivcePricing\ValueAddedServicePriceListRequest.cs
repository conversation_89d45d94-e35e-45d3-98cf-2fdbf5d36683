﻿using Common.Enums.UnitPrice;
using Common.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Common.Enums.ProductCommisssionPrices;

namespace Common.Models.ValueAddedSerivcePricing;
public class ValueAddedServicePriceListRequest
{
    [DefaultValue(Constants.AddOnsSearchDictionary)]
    public Dictionary<AddOnsSearchKey, string> SearchTerms { get; set; } = new Dictionary<AddOnsSearchKey, string>();
    public List<Guid>? FilterByProducts { get; set; }
    public List<Guid>? FilterByMccType { get; set; }
    public List<Guid>? FilterByBusinessType { get; set; }
    public List<Guid>? FilterByAddOns { get; set; }
    public List<BillingType>? FilterByBillingType { get; set; }
    public List<Status>? FilterByAddOnsStatus { get; set; }
    public List<PriceBillingFrequency>? FilterByBillingFrequency { get; set; }
    [DefaultValue("desc")]
    public string OrderType { get; set; } = SortType.desc.ToString();
    [DefaultValue("CreatedDate")]
    public string OrderFieldName { get; set; } = "CreatedDate";
    [DefaultValue(1)]
    public int Page { get; set; } = 1;
    [DefaultValue(10)]
    public int Size { get; set; } = 10;
}
public enum AddOnsSearchKey
{
    All,
    ProductName,
    ProductCode,
    MccName,
    MccCode,
    AddOnsName,
    AddOnsCode
}

