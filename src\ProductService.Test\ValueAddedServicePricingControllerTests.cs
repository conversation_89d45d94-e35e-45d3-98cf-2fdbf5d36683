﻿using Common.Enums.UnitPrice;
using Common.Enums;
using Common.Models.ValueAddedSerivcePricing;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using ProductService.Controllers;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using Xunit;
using Common.Models.UnitPrice;
using Common.Enums.ProductCommisssionPrices;

namespace ProductService.Test;

public class ValueAddedServicePricingControllerTests
{
    private readonly Mock<IValueAddedServicePricingService> valueAddedServicePricingServiceMock;
    private readonly ValueAddedServicePricingController valueAddedServicePricingController;

    public ValueAddedServicePricingControllerTests()
    {
        valueAddedServicePricingServiceMock = new Mock<IValueAddedServicePricingService>();
        valueAddedServicePricingController = new ValueAddedServicePricingController(valueAddedServicePricingServiceMock.Object);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturn_OkObjectResult_With_ValueAddedServicePriceResponse()
    {
        // Arrange
        var request = new ValueAddedServicesPricingCreateRequest
        {
            ProductIDs = new List<Guid> { Guid.NewGuid() },
            MCCIDs = new List<Guid> { Guid.NewGuid() },
            BusinessTypeIDs = new List<Guid> { Guid.NewGuid() },
            VASIDs = new List<Guid> { Guid.NewGuid() },
            SubscriptionFee = 200,
            FeeType = VatType.Flat,
            BillingType = BillingType.PrePaid,
            BillingFrequency = PriceBillingFrequency.Annual
        };

        var expectedResponse = new ValueAddedServicePriceResponse
        {
            CreatedVASPrices = new List<ValueAddedServicesPricingDetails>
            {
                new ValueAddedServicesPricingDetails
                {
                    Id = Guid.NewGuid(),
                    ProductID = request.ProductIDs[0],
                    MCCID = request.MCCIDs[0],
                    BusinessTypeID = request.BusinessTypeIDs[0],
                    VASID = request.VASIDs[0],
                    SubscriptionFee = request.SubscriptionFee,
                    FeeType = request.FeeType,
                    BillingType = request.BillingType,
                    BillingFrequency = request.BillingFrequency
                }
            },
            NewExistingVASPrices = new List<ValueAddedServicesPricingDetails>()
        };

        valueAddedServicePricingServiceMock.Setup(s => s.CreateAsync(request))
                                           .ReturnsAsync(expectedResponse);

        // Act
        var result = await valueAddedServicePricingController.CreateAsync(request);

        // Assert
        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);
        Assert.Equal(StatusCodes.Status200OK, okResult.StatusCode);
        Assert.Equal(expectedResponse, okResult.Value);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturn_OkObjectResult_With_UpdatedValueAddedServicePrice()
    {
        // Arrange
        var id = Guid.NewGuid();
        var request = new ValueAddedServicePricingUpdateRequest
        {
            SubscriptionFee = 150,
            FeeType = VatType.Percentage,
            BillingType = BillingType.PostPaid,
            BillingFrequency = PriceBillingFrequency.Monthly
        };

        var expectedUpdatedPrice = new ValueAddedServicesPricingDetails
        {
            Id = id,
            ProductID = Guid.NewGuid(),
            MCCID = Guid.NewGuid(),
            BusinessTypeID = Guid.NewGuid(),
            VASID = Guid.NewGuid(),
            SubscriptionFee = request.SubscriptionFee,
            FeeType = request.FeeType,
            BillingType = request.BillingType,
            BillingFrequency = request.BillingFrequency
        };

        valueAddedServicePricingServiceMock.Setup(s => s.UpdateAsync(id, request))
                                           .ReturnsAsync(expectedUpdatedPrice);

        // Act
        var result = await valueAddedServicePricingController.UpdateAsync(id, request);

        // Assert
        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);
        Assert.Equal(StatusCodes.Status200OK, okResult.StatusCode);
        Assert.Equal(expectedUpdatedPrice, okResult.Value);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturn_NotFound_WhenPriceDoesNotExist()
    {
        // Arrange
        var id = Guid.NewGuid();
        var request = new ValueAddedServicePricingUpdateRequest
        {
            SubscriptionFee = 150,
            FeeType = VatType.Percentage,
            BillingType = BillingType.PostPaid,
            BillingFrequency = PriceBillingFrequency.Monthly
        };

        valueAddedServicePricingServiceMock.Setup(s => s.UpdateAsync(id, request))
                                           .ThrowsAsync(new ServiceException(HttpStatusCode.NotFound));

        // Act
        var exception = await Assert.ThrowsAsync<ServiceException>(() => valueAddedServicePricingController.UpdateAsync(id, request));

        // Assert
        Assert.Equal(HttpStatusCode.NotFound, exception.StatusCode);
    }

    [Fact]
    public async Task CreateAsync_ShouldReturn_BadRequest_WhenRequestIsInvalid()
    {
        // Arrange
        ValueAddedServicesPricingCreateRequest request = null;

        valueAddedServicePricingServiceMock.Setup(s => s.CreateAsync(request))
                                           .ThrowsAsync(new ServiceException(HttpStatusCode.BadRequest));

        // Act
        var exception = await Assert.ThrowsAsync<ServiceException>(() => valueAddedServicePricingController.CreateAsync(request));

        // Assert
        Assert.Equal(HttpStatusCode.BadRequest, exception.StatusCode);
    }
    [Fact]
    public async Task GetAddOnsList_ShouldReturnOk_WhenRequestIsValid()
    {
        //Arrange
        ValueAddedServicePriceListRequest request = new ValueAddedServicePriceListRequest();
        var ExpectedResponse = new ValueAddedServicePriceListResponse();
        //Mock
        valueAddedServicePricingServiceMock.Setup(s => s.GetValueAddedServicesPricesList(request))
                               .ReturnsAsync(ExpectedResponse);
        //Act
        var result = await valueAddedServicePricingController.GetAddOnsPricesList(request);
        var OkResult = result as OkObjectResult;
        //Assert
        Assert.NotNull(OkResult);
        Assert.IsType<OkObjectResult>(OkResult);
        Assert.Equal(StatusCodes.Status200OK, OkResult.StatusCode);
    }
    [Fact]
    public async Task GetAddOnsPricesList_ShouldReturnServiceException_WhenRequestIsInvalid()
    {
        //Arrange
        ValueAddedServicePriceListRequest request = null;
        //Mock
        valueAddedServicePricingServiceMock.Setup(s => s.GetValueAddedServicesPricesList(request))
                               .ThrowsAsync(new ServiceException(HttpStatusCode.BadRequest));
        //Act
        var exception = await Assert.ThrowsAsync<ServiceException>(() => valueAddedServicePricingController.GetAddOnsPricesList(request));
        //Assert
        Assert.NotNull(exception);
        Assert.Equal(HttpStatusCode.BadRequest, exception.StatusCode);
    }
    [Fact]
    public async Task GetAddOnsPriceById_ShouldReturn_OkObjectResult_With_AddOnsPrice()
    {
        // Arrange
        var id = Guid.NewGuid();
        var expectedAddOnsPrice = new ValueAddedServicesPricingDetails
        {
            Id = id,
            ProductID = Guid.NewGuid(),
            MCCID = Guid.NewGuid(),
            BusinessTypeID = Guid.NewGuid(),
            VASID = Guid.NewGuid(),
            SubscriptionFee = 150,
            FeeType = VatType.Flat,
            BillingType = BillingType.PostPaid,
            BillingFrequency = PriceBillingFrequency.Monthly
        };

        valueAddedServicePricingServiceMock.Setup(s => s.GetAddOnsPriceByIdAsync(id))
                                           .ReturnsAsync(expectedAddOnsPrice);

        // Act
        var result = await valueAddedServicePricingController.GetAddOnsPriceById(id);

        // Assert
        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);
        Assert.Equal(StatusCodes.Status200OK, okResult.StatusCode);
        Assert.Equal(expectedAddOnsPrice, okResult.Value);
    }

}
