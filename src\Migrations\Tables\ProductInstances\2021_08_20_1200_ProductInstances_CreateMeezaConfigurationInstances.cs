﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_08_20_1200)]
public class ProductInstances_CreateMeezaConfigurationInstances : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"

DECLARE @meezaProductId AS uniqueidentifier='F453699A-74D5-4DAD-A910-6A1E0130375D'

--add new product with type MEEZA

INSERT INTO [dbo].[Products]
([Id]
,[Availability]
,[Code]
,[Type]
,[ValidFrom]
,[CreatedBy]
,[CreatedDate]
,[UpdatedBy]
,[Version]
,[CRMProductId]
,[Counterparty]
,[Flow]
,[QuickOnboarding])
VALUES
(@meezaProductId
,'Live'
,'MEEZA_INTEGRATION'
,'MEEZA'
,GETUTCDATE()
,'00000000-0000-0000-0000-000000000000'
,GETUTCDATE()
,'00000000-0000-0000-0000-000000000000'
,0
,'MEEZA INTEGRATION'
,'GEIDEA_EGYPT'
,'Normal',0)

--add to new MEEZA product as part of BUNDLE

INSERT INTO ProductParts (ProductId, PartId, Quantity) VALUES
((SELECT TOP 1 Id FROM Products WHERE type='BUNDLE' AND Counterparty='GEIDEA_EGYPT' AND Availability='Live'), @meezaProductId, 1)

--create MeezaConfiguration temp table

CREATE TABLE #MeezaConfiguration (
CompanyId uniqueidentifier NOT NULL,
RegistrationStatus NVARCHAR(20) NULL,
MeezaMerchantId NVARCHAR(MAX) NULL,
RegistrationHistory NVARCHAR(MAX) NOT NULL
);

--fill MeezaConfiguration temp table with - insert distinct company ids and set NotRegistered values

INSERT INTO #MeezaConfiguration
SELECT DISTINCT pi.CompanyId, 'NotRegistered', NULL, '[]'
FROM ProductInstances pi
INNER JOIN Products p ON p.id = pi.ProductId
WHERE
p.Counterparty='GEIDEA_EGYPT' AND p.Availability='Live' AND p.Type='GWAY' AND
pi.CompanyId IS NOT NULL AND pi.CompanyId != '00000000-0000-0000-0000-000000000000' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata)=1

--update MeezaConfiguration temp table - set registered configuration

UPDATE mc
SET CompanyId=pi.CompanyId, RegistrationStatus='Registered', MeezaMerchantId=JSON_VALUE(pi.Metadata, '$.MeezaMerchantId'), RegistrationHistory='[{""Status"":""Registered"",""Reason"":"""",""ChangeDate"":""2021-08-23T00:00:00Z""}]'
FROM #MeezaConfiguration mc
INNER JOIN ProductInstances pi ON pi.CompanyId = mc.CompanyId
INNER JOIN Products p ON p.id = pi.ProductId
WHERE
p.Counterparty = 'GEIDEA_EGYPT' AND p.Availability = 'Live' AND p.Type = 'GWAY' AND
pi.CompanyId IS NOT NULL AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) = 1 AND
JSON_VALUE(pi.Metadata, '$.MeezaMerchantId') IS NOT NULL AND
JSON_VALUE(pi.Metadata, '$.MeezaMerchantUnregisterInfo') IS NULL

--update MeezaConfiguration temp table - set unregistered configuration

UPDATE mc
SET CompanyId = pi.CompanyId, RegistrationStatus = 'Unregistered', MeezaMerchantId = JSON_VALUE(pi.Metadata, '$.MeezaMerchantId'), RegistrationHistory = '[{""Status"":""Registered"",""Reason"":"""",""ChangeDate"":""2021-08-22T00:00:00Z""},{""Status"":""Unregistered"",""Reason"":""Test reason"",""ChangeDate"":""2021-08-23T00:00:00Z""}]'
FROM #MeezaConfiguration mc
INNER JOIN ProductInstances pi ON pi.CompanyId = mc.CompanyId
INNER JOIN Products p ON p.id = pi.ProductId
WHERE
p.Counterparty = 'GEIDEA_EGYPT' AND p.Availability = 'Live' AND p.Type = 'GWAY' AND
pi.CompanyId IS NOT NULL AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) = 1 AND
JSON_VALUE(pi.Metadata, '$.MeezaMerchantId') IS NOT NULL AND
JSON_VALUE(pi.Metadata, '$.MeezaMerchantUnregisterInfo') IS NOT NULL

--create Meeza configurations for all rows in MeezaConfiguration temp table 

INSERT INTO [dbo].[ProductInstances]
([Id]
,[ProductId]
,[Metadata]
,[DeletedFlag]
,[ValidFrom]
,[CreatedBy]
,[CreatedDate]
,[CompanyId])
SELECT
NEWID()
,@meezaProductId
,'{""RegistrationStatus"":""'+RegistrationStatus+'"",""MeezaMerchantId"":'+(CASE WHEN MeezaMerchantId IS NULL THEN 'null' ELSE '""'+MeezaMerchantId+'""' END)+',""RegistrationHistory"":'+RegistrationHistory+'}'
,0
,'2021-08-23T00:00:00Z'
,'00000000-0000-0000-0000-000000000000'
,'2021-08-23T00:00:00Z'
,CompanyId
FROM #MeezaConfiguration

--remove MeezaMerchantId field for all gateway configurations

UPDATE pi
set Metadata=JSON_MODIFY(Metadata, '$.MeezaMerchantId', NULL)
FROM ProductInstances pi
INNER JOIN Products p ON p.id = pi.ProductId
WHERE
p.Type='GWAY' AND
pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata)=1

--remove MeezaMerchantUnregisterInfo field for all gateway configurations

UPDATE pi
set Metadata=JSON_MODIFY(Metadata, '$.MeezaMerchantUnregisterInfo', NULL)
FROM ProductInstances pi
INNER JOIN Products p ON p.id = pi.ProductId
WHERE
p.Type='GWAY' AND
pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata)=1

");
    }
}
