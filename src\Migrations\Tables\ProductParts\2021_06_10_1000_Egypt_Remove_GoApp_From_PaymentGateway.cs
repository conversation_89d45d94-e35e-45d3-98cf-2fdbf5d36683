﻿using FluentMigrator;

namespace Migrations.Tables.Products;

[Migration(2021_06_10_1000)]
public class Egypt_Remove_GoApp_From_PaymentGateway : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"DELETE FROM ProductParts WHERE
                            ProductId IN (SELECT Id FROM Products WHERE Code = 'PAYMENT_GATEWAY_BUNDLE' AND 
                                          Availability = 'Live' AND Counterparty = 'GEIDEA_EGYPT') AND
                            PartId IN (SELECT Id FROM Products WHERE Code = 'GEIDEA_GO_APP'
                                       AND Availability = 'Live' AND Counterparty = 'GEIDEA_EGYPT')");
    }
}
