﻿using Common.Models.NonTransactionalPrice;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProductService.Controllers;
[Route("api/v1/[controller]")]
[ApiController]
public class NonTransactionalFeePriceController : ControllerBase
{
    private readonly INonTransactionalPriceService nonTransactionalPriceService;
    public NonTransactionalFeePriceController(INonTransactionalPriceService nonTransactionalPriceService)
    {
        this.nonTransactionalPriceService = nonTransactionalPriceService;
    }
    /// <summary>
    /// Create a nonTransactional fee price for specific product
    /// </summary>
    /// <param name="request">An object containing the details of the product to be created.</param>
    /// <returns>
    ///  An <see cref="IActionResult"/> indicating the outcome of the operation.
    /// Returns a 200 OK status if the operation is successful.
    /// Returns a 400 Bad Request status if the input data is invalid.
    /// Returns a 500 Internal Server Error status if an unexpected error occurs.
    /// </returns>
    [HttpPost("CreateNonTransactionalPrices")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreateAsync([FromBody] NonTransactionalFeePriceCreateRequest request)
    {
        var result = await nonTransactionalPriceService.CreateAsync(request);
        return Ok(result);
    }
    /// <summary>
    /// Updates the NonTransactional prices for a specific product.
    /// </summary>
    /// <param name="id">The unique identifier of the product to be updated.</param>
    /// <param name="request">An object containing the updated NonTransactional price details.</param>
    /// <returns>
    /// An <see cref="IActionResult"/> indicating the outcome of the operation.
    /// Returns a 200 OK status with the updated NonTransactional price if the operation is successful.
    /// Returns a 400 Bad Request status if the input data is invalid.
    /// Returns 404 not found status if the id us wronh and not existed.
    /// Returns a 500 Internal Server Error status if an unexpected error occurs.
    /// </returns>
    [HttpPut("UpdateNonTransactionalPrice/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> UpdateAsync(Guid id, [FromBody] NonTransactionalFeePriceUpdateRequest request)
    {
        var result = await nonTransactionalPriceService.UpdateAsync(id, request);
        return Ok(result);
    }
    /// <summary>
    /// Get product nonTransactional price details by ID.
    /// </summary>
    /// <param name="id">The ID of the  nonTransactional price.</param>
    /// <returns>
    /// Returns the nonTransactional price details if found.
    /// </returns>
    [HttpGet("GetNonTransactionalPriceById/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetNonTransactionalPriceByIdAsync(Guid id)
    {
        var nonTransactionalPrice = await nonTransactionalPriceService.GetNonTransactionalPriceByIdAsync(id);
        return Ok(nonTransactionalPrice);
    }
    /// <summary>
    /// Get NonTransactional  prices list.
    /// </summary>
    /// <param name="request">An object containing pagination, search filter, and sort data.</param>
    /// <returns>
    /// An <see cref="IActionResult"/> indicating the outcome of the operation.
    /// Returns a 200 OK status if the operation is successful.
    /// Returns a 400 Bad Request status if the request or input data is invalid.
    /// Returns a 500 Internal Server Error status if an unexpected error occurs.
    /// </returns>
    [HttpPost("GetNonTransactionalPriceList")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> ListAsync([FromBody] NonTransactionalFeesPriceListRequest request)
    {
        var result = await nonTransactionalPriceService.GetNonTransactionalPriceList(request);
        return Ok(result);
    }
    /// <summary>
    /// Delete NonTransactional prices by a list of IDs.
    /// </summary>
    /// <param name="deletedBy"></param>
    /// <param name="ids">List of NonTransactional price IDs to delete.</param>
    /// <returns>Returns a status indicating success or failure.</returns>
    [HttpDelete("DeleteNonTransactionalPrices")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DeleteNonTransactionalPricesAsync([FromQuery] string deletedBy, [FromBody] List<Guid> ids)
    {
        await nonTransactionalPriceService.DeleteNonTransactionalPricesAsync(ids, deletedBy);

        return Ok(new { Message = "deleted successfully" });
    }
    /// <summary>
    /// Create or Update NonTransactional prices based on the request data.
    /// </summary>
    /// <param name="request">An object containing the details of the product to be created or updated.</param>
    /// <returns>
    ///  An <see cref="IActionResult"/> indicating the outcome of the operation.
    /// Returns a 200 OK status if the operation is successful.
    /// Returns a 400 Bad Request status if the input data is invalid.
    /// Returns a 500 Internal Server Error status if an unexpected error occurs.
    /// </returns>
    [HttpPost("CreateOrUpdateNonTransactionalPrices")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreateOrUpdateAsync([FromBody] NonTransactionalFeePriceCreateRequest request)
    {
        var result = await nonTransactionalPriceService.CreateOrUpdateAsync(request);
        return Ok(result);
    }
    [HttpPost("GetNonTransactionalPricesByIdsAsync")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetNonTransactionalPricesByIdsAsync([FromBody] List<Guid> ids)
    {
        var nonTransactionalPrice = await nonTransactionalPriceService.GetNonTransactionalPricesByIdsAsync(ids);
        return Ok(nonTransactionalPrice);
    }
}
