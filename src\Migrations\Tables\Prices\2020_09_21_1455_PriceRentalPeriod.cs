﻿using FluentMigrator;

namespace Migrations.Tables.Prices;

[Migration(2020_09_21_1455)]
public class PriceRentalPeriod : ForwardOnlyMigration
{
    public override void Up()
    {
        Alter.Table("Prices").AddColumn("RentalPeriod").AsInt32().Nullable();

        Execute.Sql(@"DECLARE @bundles TABLE (id uniqueidentifier)
                         INSERT @bundles SELECT Id FROM Products WHERE Type='BUNDLE'
                         UPDATE Prices SET RentalPeriod=24 WHERE ProductId IN (SELECT * FROM @bundles)");
    }
}
