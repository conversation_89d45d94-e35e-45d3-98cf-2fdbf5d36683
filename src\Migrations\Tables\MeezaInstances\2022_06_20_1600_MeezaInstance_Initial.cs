﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_06_20_1600)]
public class MeezaInstance_Initial : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("MeezaInstances")
            .WithColumn("ProductInstanceId").AsGuid().PrimaryKey().ForeignKey("ProductInstances", "Id")
            .WithColumn("MeezaMerchantId").AsString(255).NotNullable();

        Execute.Sql(@"
            INSERT INTO MeezaInstances(ProductInstanceId, MeezaMerchantId)
            SELECT pi.Id, JSON_VALUE(pi.Metadata, '$.MeezaMerchantId')
            FROM ProductInstances pi
                INNER JOIN Products p ON p.id=pi.ProductId
            WHERE p.Type='MEEZA' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.MeezaMerchantId') IS NOT NULL");

        Execute.Sql(@"
            IF NOT EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'IDX_MeezaInstances_MeezaMerchantId' AND object_id = OBJECT_ID('[dbo].[MeezaInstances]'))
            BEGIN
                CREATE NONCLUSTERED INDEX [IDX_MeezaInstances_MeezaMerchantId] ON [dbo].[MeezaInstances] ([MeezaMerchantId])
            END");
    }
}
