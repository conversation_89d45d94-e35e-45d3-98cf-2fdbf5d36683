﻿using Geidea.Utils.DataAccess.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Common.Entities;
public class UnitPriceLogsEntity : Entity<Guid>
{
    public Guid ProductID { get; set; }
    public Guid MCCID { get; set; }
    public Guid BusinessTypeID { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal VATRate { get; set; }
    public int VATType { get; set; }
    public int BillingType { get; set; }
    public int BillingFrequency { get; set; }
    public string? DeletedBy { get; set; }
    public DateTime DeletedDate { get; set; }
}
