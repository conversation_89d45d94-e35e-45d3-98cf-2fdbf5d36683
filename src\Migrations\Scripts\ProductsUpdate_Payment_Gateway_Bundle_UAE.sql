﻿USE PRODUCTS;
GO
DECLARE @ProductId UNIQUEIDENTIFIER
DECLARE @PayFamily UNIQUEIDENTIFIER

SELECT TOP 1 @PayFamily = ID FROM Category where Code = 'PAY_FAMILY' and Counterparty='GEIDEA_UAE';

EXEC NewProductVersion_v2 'PAYMENT_GATEWAY_BUNDLE', 1, 'PAYMENT_GATEWAY_BUNDLE', 2, 1,'GEIDEA_UAE'
SELECT TOP 1 @ProductId = ID FROM Products where Code='PAYMENT_GATEWAY_BUNDLE' and [Version] = 2 and CounterParty='GEIDEA_UAE'
DELETE FROM ProductCategories where ProductId=@ProductId 

INSERT INTO ProductCategories(ProductId, CategoryId) VALUES (@ProductId, @PayFamily)
UPDATE Products SET [Availability] = 'Obsolete' WHERE Code IN ('GENERATE_REFERENCE', 'QR_PAYMENT', 'PAY_BY_LINK', 'CASHOUT_VOUCHER_CREATION') and CounterParty='GEIDEA_UAE'

Delete from ProductParts where ProductId = @ProductId  and PartId IN
    (Select ID FROM Products where Code IN ('GENERATE_REFERENCE', 'QR_PAYMENT', 'PAY_BY_LINK', 'CASHOUT_VOUCHER_CREATION') and CounterParty= 'GEIDEA_UAE')
