﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_10_06_1030)]
public class ProductInstance_Add_Multicurrency_To_Metadata : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
                UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsMulticurrencyEnabled', 'false')
	            FROM ProductInstances pi
		            INNER JOIN Products p ON p.id = pi.ProductId
            	WHERE [Metadata] IS NOT NULL
            		AND p.Type = 'GWAY' 
            		AND ISJSON([Metadata]) > 0 

                UPDATE ProductInstances SET [Metadata] =
                    CASE
                      WHEN p.Counterparty IS NOT NULL AND p.Counterparty = 'GEIDEA_SAUDI' THEN JSON_MODIFY([Metadata], '$.Currencies', JSON_QUERY(N'[""SAR""]'))
                      WHEN p.Counterparty IS NOT NULL AND p.Counterparty = 'GEIDEA_EGYPT' THEN JSON_MODIFY([Metadata], '$.Currencies', JSON_QUERY(N'[""EGP""]'))
                      ELSE [Metadata]
                    END
                FROM ProductInstances pi
                	INNER JOIN Products p ON p.id = pi.ProductId
                WHERE [Metadata] IS NOT NULL
                	AND p.Type = 'GWAY' 
                	AND ISJSON([Metadata]) > 0

                UPDATE ProductInstances SET [Metadata] = JSON_MODIFY(JSON_MODIFY([Metadata], '$.IsMulticurrencyEnabled', 'true'), '$.Currencies', JSON_QUERY(N'[""EGP"",""USD""]'))
                FROM ProductInstances pi
                    INNER JOIN GatewayInstances gi ON gi.ProductInstanceId = pi.Id
                WHERE gi.MerchantGatewayKey='38509f6a-84d0-4fc9-bb05-95911669216b'
        ");
    }
}