﻿using AutoMapper;
using Common.Entities;
using Common.Models;
using Common.Repositories;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace Services.Test.ProductServiceTests;
public class GetProductDetailsTests
{
    private readonly Mock<ILogger<ProductService>> logger = new Mock<ILogger<ProductService>>();
    private readonly Mock<IProductInstanceRepository> productInstanceRepository = new Mock<IProductInstanceRepository>();
    private readonly Mock<IProductRepository> productRepository = new Mock<IProductRepository>();
    private readonly Mock<ICategoryRepository> categoryRepository = new Mock<ICategoryRepository>();
    private readonly Mock<IMapper> mapper = new Mock<IMapper>();
    private readonly ProductService productService;

    public GetProductDetailsTests()
    {
        productService = new ProductService(logger.Object, productRepository.Object, mapper.Object, categoryRepository.Object, productInstanceRepository.Object);
    }
    [Fact]
    public async Task GetProductDetails_Returns_Product_For_Valid_Id()
    {
        var ProductId = new Guid("EA15C166-8C01-4378-ACBB-08DC79A39613");
        ProductDetailsResponse product = new ProductDetailsResponse();
        productRepository.Setup(x => x.GetProductDetails(ProductId))
                         .ReturnsAsync(product);

        var ReturnedProduct = await productService.GetProductDetails(ProductId);
        ReturnedProduct.Should().NotBeNull();
    }
    [Fact]
    public async Task GetProductDetails_Throws_ServiceException_For_Empty_Id()
    {
        var EmptyProductId = Guid.Empty;
        productRepository.Setup(x => x.GetProductDetails(EmptyProductId))
                         .ReturnsAsync((ProductDetailsResponse)null);

        await Assert.ThrowsAsync<ServiceException>(() => productService.GetProductDetails(EmptyProductId));
    }
    [Fact]
    public async Task GetProductDetails_Throws_ServiceException_For_Product_Not_Found()
    {
        var NotFoundId = new Guid("3fa85f64-5717-4562-b3fc-2c963f66afa6");
        productRepository.Setup(x => x.GetProductDetails(NotFoundId))
                         .ReturnsAsync((ProductDetailsResponse)null);

        await Assert.ThrowsAsync<ServiceException>(() => productService.GetProductDetails(NotFoundId));
    }
}
