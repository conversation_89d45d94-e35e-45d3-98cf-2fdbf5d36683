﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_07_08_1100)]
public class ProductInstances_AddTriggers : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
            CREATE TRIGGER ProductInstances_After_Insert
            ON ProductInstances
            AFTER INSERT
            AS

            SET NOCOUNT ON

            INSERT INTO [GatewayInstances](
                [ProductInstanceId],
                [CompanyId],
                [StoreId],
                [MerchantGatewayKey],
                [IsTest],
                [ShahryCpBnplMerchantCode],
                [ShahryCpBnplBranchCode])
            SELECT
                pi.Id,
                pi.CompanyId,
                pi.StoreId,
                CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0 and TRY_CONVERT(UNIQUEIDENTIFIER, JSON_VALUE(pi.Metadata, '$.MerchantGatewayKey')) IS NOT NULL THEN JSON_VALUE(pi.Metadata, '$.MerchantGatewayKey') ELSE NULL END,
                CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0 and TRY_CONVERT(BIT, JSON_VALUE(pi.Metadata, '$.IsTest')) IS NOT NULL THEN JSON_VALUE(pi.Metadata, '$.IsTest') ELSE NULL END,
                CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0  THEN JSON_VALUE(pi.Metadata, '$.ShahryCpBnplMerchantCode') ELSE NULL END,
                CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0  THEN JSON_VALUE(pi.Metadata, '$.ShahryCpBnplBranchCode') ELSE NULL END
            FROM [INSERTED] pi
                INNER JOIN Products p on p.Id=pi.ProductId
            WHERE
                p.Type='GWAY'
            
            INSERT INTO [MeezaInstances](
                [ProductInstanceId],
                [CompanyId],
                [MeezaMerchantId])
            SELECT
                pi.Id,
                pi.CompanyId,
                CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0 THEN JSON_VALUE(pi.Metadata, '$.MeezaMerchantId') ELSE NULL END
            FROM [INSERTED] pi
                INNER JOIN Products p on p.Id=pi.ProductId
            WHERE
                p.Type='MEEZA'
            
            GO");

        Execute.Sql(@"
            CREATE TRIGGER ProductInstances_After_Update
            ON ProductInstances
            AFTER UPDATE
            AS

            SET NOCOUNT ON

            UPDATE gi
            SET
                [CompanyId] = pi.CompanyId,
                [StoreId] = pi.StoreId,
                [MerchantGatewayKey] = CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0 and TRY_CONVERT(UNIQUEIDENTIFIER, JSON_VALUE(pi.Metadata, '$.MerchantGatewayKey')) IS NOT NULL THEN JSON_VALUE(pi.Metadata, '$.MerchantGatewayKey') ELSE NULL END,
                [IsTest] = CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0 and TRY_CONVERT(BIT, JSON_VALUE(pi.Metadata, '$.IsTest')) IS NOT NULL THEN JSON_VALUE(pi.Metadata, '$.IsTest') ELSE NULL END,
                [ShahryCpBnplMerchantCode] = CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0  THEN JSON_VALUE(pi.Metadata, '$.ShahryCpBnplMerchantCode') ELSE NULL END,
                [ShahryCpBnplBranchCode] = CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0  THEN JSON_VALUE(pi.Metadata, '$.ShahryCpBnplBranchCode') ELSE NULL END
            FROM [INSERTED] pi
                INNER JOIN GatewayInstances gi on gi.ProductInstanceId=pi.Id
            
            UPDATE mi
            SET
                [CompanyId] = pi.CompanyId,
                [MeezaMerchantId] = CASE WHEN pi.Metadata is not null and ISJSON(pi.Metadata) > 0 THEN JSON_VALUE(pi.Metadata, '$.MeezaMerchantId') ELSE NULL END
            FROM [INSERTED] pi
                INNER JOIN MeezaInstances mi on mi.ProductInstanceId=pi.Id
            
            GO");
    }
}
