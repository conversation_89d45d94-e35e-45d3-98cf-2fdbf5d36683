﻿using Common.Models.CommissionFees;
using Common.Models.MccManagement;
using Common.Models.NonTransactionalFees;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Services;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace ProductService.Controllers;
[Route("api/v1/[controller]")]
[ApiController]
public class MccController : ControllerBase
{
    private readonly IMccService mccService;
    public MccController(IMccService mccService)
    {
        this.mccService = mccService;
    }
    /// <summary>
    ///Create a new mcc based on the provided request data.
    /// </summary>
    /// <param name="request">The details of mcc to be added.</param>
    /// <returns>
    /// Returns an <see cref="IActionResult"/> Indicating the result of the operation.
    /// If sucessfully created, returns http 200 ok with the result of creation.
    /// If the creation failed, returns http 400 Bad Request, with the result indicating the failure.
    /// </returns>
    [HttpPost("CreateMcc")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateAsync(MccRequest request)
    {
        var Result = await mccService.CreateAsync(request);
        return Ok(Result);
    }
    /// <summary>
    /// Update an existing MCC in AdminPanel
    /// </summary>
    /// <param name="id">The ID of the Mcc to update</param>
    /// <param name="request">The updated Mcc details</param>
    /// <returns>Returns the updated Mcc</returns>
    [HttpPut("UpdateMcc/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateAsync(Guid id, [FromBody] MccRequest request)
    {
        var updatedMcc = await mccService.UpdateAsync(id, request);
        return Ok(updatedMcc);
    }
    /// <summary>
    /// Get MCC List
    /// </summary>
    /// <returns>
    /// Returns an <see cref="IActionResult"/> Indicating the result of operation.
    /// Success: returns http 200 ok with the list of MCC Categories.
    /// Bad request: returns http 4oo bad request, with the result indicating the reason.
    /// Failure: returns http 500 InternalServerError, with the result indicating the failure.
    /// </returns>
    [HttpPost("GetMCCList")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetMCCList(GetMccListRequest request)
    {
        var MCCList = await mccService.GetMCCList(request);
        return Ok(MCCList);
    }
    /// <summary>
    /// Get MCC record details, based on the passed Id. 
    /// </summary>
    /// <param name="Id">a passed guid param, to check idf existed or not.</param>
    /// <returns>MCC record etails as type [MccDetailsResponse]</returns>
    [HttpGet("GetMccDetails/{Id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]

    public async Task<IActionResult> DetailsAsync(Guid Id)
    {
        var MccDetails = await mccService.GetMccDetails(Id);
        return Ok(MccDetails);
    }
    /// <summary>
    /// Update MCC record status, active or inactive, based on the passed parameter.
    /// </summary>
    /// <param name="Id">MCC record Id</param>
    /// <param name="Status">boolean parameter to set the status, 0 for in active, and 1 for active</param>
    /// <returns>
    /// Returns an <see cref="IActionResult"/> Indicating the result of operation.
    /// Success: returns http 200 ok.
    /// Bad request: returns http 4oo bad request, with the result indicating the reason.
    /// Failure: returns http 404 NotFound, In case there is no record with the passed Id.
    /// </returns>
    [HttpPost("ToggleStatus/{Id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ToggleStatus(Guid Id, [FromHeader] bool Status)
    {
        var result = await mccService.ToggleStatus(Id, Status);
        if (!result)
            return NotFound();

        return Ok(result);
    }
    /// <summary>
    /// Export MCC list to excel file, based on the filter and search scenarios.
    /// </summary>
    /// <param name="request">a passed object that contains filter, search and sort data. the same like listing, but with no pagination.</param>
    /// <returns>an Excel file, contains MCC list data, based on search and filter ceiteria.</returns>
    [HttpPost("ExportMccToExcel")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> ExportToExcel(GetMccListRequest request)
    {
        var stream = await mccService.ExportMccToExcel(request);
        string excelName = $"MCC_List_Exported-{DateTime.Now.ToString("yyyyMMddHHmmss")}.xlsx";

        return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelName);
    }
    /// <summary>
    /// Import MCC data from an Excel file.
    /// </summary>
    /// <param name="categoryId">The category ID associated with the MCCs being imported.</param>
    /// <param name="file">The Excel file containing the MCC data.</param>
    /// <param name="cancellationToken"></param>
    /// <returns>
    /// Returns an <see cref="IActionResult"/> indicating the result of the operation.
    /// If the import is successful, returns http 200 OK with the count of successful and failed records.
    /// If the import failed, returns http 400 Bad Request, with the reason for the failure.
    /// </returns>
    [HttpPost("ImportMcc")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> ImportMcc(Guid categoryId, IFormFile file, CancellationToken cancellationToken = default)
    {
        if (file == null || file.Length == 0)
        {
            return BadRequest("No file uploaded or file is empty.");
        }


        var (successCount, failureCount, invalidRecordsExcel) = await mccService.ImportMccAsync(categoryId, file.OpenReadStream(), cancellationToken);

        if (failureCount > 0)
        {
            Response.Headers.Add("X-Success-Count", successCount.ToString());
            Response.Headers.Add("X-Failure-Count", failureCount.ToString());

            return File(invalidRecordsExcel, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "InvalidRecords.xlsx");
        }
        else
        {
            var result = new
            {
                SuccessCount = successCount,
                FailureCount = failureCount,
                Message = "All records were successfully imported."
            };

            return Ok(result);
        }

    }
    /// <summary>
    /// Get a list of MCCs by their Codes(IDs).
    /// </summary>
    /// <param name="codes"></param>
    /// <returns>
    /// Returns an <see cref="IActionResult"/> indicating the result of the operation.
    /// If successful, returns http 200 OK with the list of MCCs.
    /// If the request is invalid, returns http 400 Bad Request.
    /// If there is an error, returns http 500 Internal Server Error.
    /// </returns>
    [HttpPost("GetMCCsByCodes")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetMCCsByCodes([FromBody] List<string?> codes)
    {
        var mccList = await mccService.GetMCCsByCodesAsync(codes);
        return Ok(mccList);
    }

    [HttpGet("GetAllMccsWithCategory")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAllMccsWithCategory()
    {
        var mccsWithCategories = await mccService.GetAllMccsWithCategoryAsync();
        return Ok(mccsWithCategories);
    }
}
