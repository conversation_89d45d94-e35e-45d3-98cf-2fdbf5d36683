﻿using Common.Models;
using Common.Repositories;
using Common.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Services;
public class OrderMigrationSyncTriggerMessageReceiver : IDisposable
{
    private readonly IServiceScopeFactory scopeFactory;
    private readonly IOrderMigrationSyncScriptClient messageClient;
    private readonly ILogger<OrderMigrationSyncTriggerMessageReceiver> logger;

    public OrderMigrationSyncTriggerMessageReceiver(
        IServiceScopeFactory scopeFactory,
        IOrderMigrationSyncScriptClient messageClient,
        ILogger<OrderMigrationSyncTriggerMessageReceiver> logger)
    {
        this.scopeFactory = scopeFactory;
        this.messageClient = messageClient;
        this.logger = logger;
    }

    public void StartReceiving()
    {
        messageClient.OnOrderMigrationTriggerSyncScript += OnOrderMigrationAndTerminalDataSyncMessageReceived;
        messageClient.Connect();
    }

    [ExcludeFromCodeCoverage]
    private void OnOrderMigrationAndTerminalDataSyncMessageReceived(object? sender, CreateOrderMigrationSyncScriptMessageReceivedRequestEventArgs args)
    {
        try
        {
            using var scope = scopeFactory.CreateScope();

            var terminalDataSetRepository = scope.ServiceProvider.GetRequiredService<ITerminalDataSetRepository>();

            terminalDataSetRepository.TriggerOrderMigrationSyncAsync();

            logger.LogInformation("Stored procedure have been executed for following OrderNumbers: {OrderNumbers}", string.Join(",", args.OrderNumbers.OrderNumbers));
        }
        catch (Exception e)
        {
            logger.LogError("OrderMigrationSyncTerminalData throw error while trying to execute stored procedure {@ex}", e);
        }
    }

    public void Dispose()
    {
        messageClient.OnOrderMigrationTriggerSyncScript -= OnOrderMigrationAndTerminalDataSyncMessageReceived;
        GC.SuppressFinalize(this);
    }
}
