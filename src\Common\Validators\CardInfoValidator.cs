﻿using FluentValidation;
using Geidea.ProductService.Models;

namespace Common.Validators;

public class CardInfoValidator : AbstractValidator<CardInfo>
{
    public CardInfoValidator()
    {
        RuleFor(cardInfo => cardInfo.CardBrand)
            .NotEmpty()
            .MaximumLength(255);

        RuleFor(cardInfo => cardInfo.PrimaryGatewayId)
            .NotEmpty()
            .MaximumLength(255);
    }
}