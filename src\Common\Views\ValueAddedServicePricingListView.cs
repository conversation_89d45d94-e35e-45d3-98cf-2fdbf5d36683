﻿using Common.Enums;
using Common.Enums.ProductCommisssionPrices;
using Common.Enums.UnitPrice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Views;
public class ValueAddedServicePricingListView
{
    public Guid Id { get; set; }
    public string? ProductName { get; set; }
    public string ProductCode { get; set; } = null!;
    public Guid ProductId { get; set; }
    public string MccName { get; set; } = null!;
    public string MccCode { get; set; } = null!;
    public Guid MccId { get; set; }
    public string MccCategory { get; set; } = null!;
    public string BusinessType { get; set; } = null!;
    public Guid BusinessTypeId { get; set; }
    public string ValueAddedServiceName { get; set; } = null!;
    public string ValueAddedServiceNameAr { get; set; } = null!;
    public string ValueAddedServiceDescription { get; set; } = null!;
    public string ValueAddedServiceDescriptionAr { get; set; } = null!;
    public Guid ValueAddedServiceImageId { get; set; }
    public Status ValueAddedServiceStatus { get; set; }
    public Guid ValueAddedServiceId { get; set; }
    public string VASCode { get; set; } = null!;
    public decimal SubscriptionFee { get; set; }
    public VatType FeeType { get; set; }
    public BillingType BillingType { get; set; }
    public PriceBillingFrequency BillingFrequency { get; set; }
    public DateTime CreatedDate { get; set; }

}
