﻿using System;

namespace Common.Models.TerminalDataSets;

public class TerminalDataSet
{
    public string AcquiringLedger { get; set; } = null!;
    public string? Mid { get; set; } = null!;
    public string? Tid { get; set; } = null!;
    public string? FullTid { get; set; } = null!;
    public string? Trsm { get; set; } = null!;
    public string? MCC { get; set; }
    public string? Availability { get; set; }
    public string? OrderNumber { get; set; }
    public string? MerchantTag { get; set; }
    public DateTime? ConfigDate { get; set; }
    public Guid? StoreId { get; set; }
    public Guid? ProductInstanceId { get; set; }
    public bool Extracted { get; set; }
    public DateTime? ExtractionDate { get; set; }
    public string? CountryPrefix { get; set; }
    public string? PhoneNumber { get; set; }
    public string? ChannelType { get; set; }
    public string? ConnectionType { get; set; }
    public string? MPGSMID { get; set; }
    public string? MPGSKEY { get; set; }
    public string? Model { get; set; }
    public string? BusinessId { get; set; }
    public string? CardChannelType { get; set; }
    public Guid? VendorId { get; set; }

}
