﻿using FluentValidation;
using MadaAccount = Geidea.ProductService.Models.MadaAccount;

namespace Common.Validators;

public class MadaAccountValidator : AbstractValidator<MadaAccount>
{
    public MadaAccountValidator()
    {
        RuleFor(madaAccount => madaAccount.MadaApiKey)
            .NotEmpty()
            .MaximumLength(255);

        RuleFor(madaAccount => madaAccount.MadaMerchantId)
            .MaximumLength(255);

        RuleFor(madaAccount => madaAccount.AcquirerMid).NotNull().NotEmpty().Length(15).Matches("^[a-zA-Z0-9]*$");

        RuleFor(madaAccount => madaAccount.CardInfo).NotNull();

        RuleForEach(madaAccount => madaAccount.CardInfo).SetValidator(new CardInfoValidator());
    }
}