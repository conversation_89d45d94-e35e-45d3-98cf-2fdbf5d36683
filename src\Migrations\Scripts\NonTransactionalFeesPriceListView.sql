CREATE OR ALTER VIEW NonTransactionalFeesPriceList
AS
  SELECT 
        ntfp.[Id], 
        p.Id AS ProductId,
        p.[Name] AS ProductName,
        m.Id AS MccId,
        m.[Name] AS MccName,
        b.Id AS BusinessTypeId,
        b.[Name] AS BusinessType,
        ntf.Id AS NonTransFeeId,
		ntf.Name AS NonTransFeeName,
        ntf.NameAr AS NonTransFeeNameAr,
		ntf.Status As FeeStatus,
        ntfp.FeeValue,
        ntf.Code as NonTransFeeCode,
        p.Code AS ProductCode,
        m.Code AS MccCode,
        mc.Name AS MccCategory,
        ntfp.BillingType,
        ntfp.BillingFrequency,
        ntfp.CreatedDate,
        ntfp.FeeType
    FROM 
        [dbo].[NonTransactionalFeesPrice] ntfp
        JOIN [dbo].[Products] p ON ntfp.ProductID = p.Id
        JOIN [dbo].[BusinessTypes] b ON ntfp.BusinessTypeID = b.Id
        JOIN [dbo].[Mcc] m ON ntfp.MCCID = m.Id
        JOIN [dbo].[NonTransactionalFees] ntf ON ntfp.NonTransFeeID = ntf.Id
        JOIN [dbo].[MccCategory] mc ON m.MccCategoryId = mc.Id
GO
