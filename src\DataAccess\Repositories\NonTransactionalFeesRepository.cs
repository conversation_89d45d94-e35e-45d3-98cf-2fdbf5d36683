﻿using AutoMapper;
using Common.Entities;
using Common.Enums;
using Common.Models.CommissionFees;
using Common.Models.NonTransactionalFees;
using Common.Repositories;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Common.Data.Extensions;
using Common.Models.businessType;

namespace DataAccess.Repositories;
public class NonTransactionalFeesRepository : AuditableRepository<Guid, NonTransactionalFeesEntity>, INonTransactionalFeesRepository
{
    private readonly IMapper mapper;
    public NonTransactionalFeesRepository(DataContext context, IHttpContextAccessor contextAccessor, IMapper mapper)
        : base(context, contextAccessor)
    {
        this.mapper = mapper;
    }
    public async Task<NonTransactionalFeesEntity?> GetByIdAsync(Guid id)
    {
        return await context.Set<NonTransactionalFeesEntity>().FindAsync(id);
    }
    public async Task<GetNonTransactionalFeesListResponse> GetNonTransactionalFeesList(GetNonTransactionalFeesListRequest request)
    {
        var NonTransactionalFeesQuery = GetAllNonTransactionalFees();

        NonTransactionalFeesQuery = SearchNonTransactionalFees(request, NonTransactionalFeesQuery);
        NonTransactionalFeesQuery = FilterNonTransactionalFees(request, NonTransactionalFeesQuery);
        NonTransactionalFeesQuery = SortNonTransactionalFees(request, NonTransactionalFeesQuery);

        int TotalCount = await NonTransactionalFeesQuery.CountAsync();

        // Conditional pagination: If Size is 0, return all records
        var NonTransactionalFeesList = request.Size > 0
            ? await NonTransactionalFeesQuery.Skip((request.Page - 1) * request.Size).Take(request.Size).ToArrayAsync()
            : await NonTransactionalFeesQuery.ToArrayAsync();

        return new GetNonTransactionalFeesListResponse
        {
            NonTransactionalFeesList = NonTransactionalFeesList,
            TotalPages = request.Size > 0 ? (int)Math.Ceiling((double)TotalCount / request.Size) : 1,
            TotalCount = TotalCount
        };
    }
    public IQueryable<NonTransactionalFeesListResponse> GetAllNonTransactionalFees()
    {
        var NonTransactionalFeesList = context.Set<NonTransactionalFeesEntity>().AsNoTracking();
        return mapper.ProjectTo<NonTransactionalFeesListResponse>(NonTransactionalFeesList);
    }
    public static IQueryable<NonTransactionalFeesListResponse> SearchNonTransactionalFees(GetNonTransactionalFeesListRequest request, IQueryable<NonTransactionalFeesListResponse> NonTransactionalFees)
    {
        var SearchTerms = request.SearchTerms.Where(s => !string.IsNullOrEmpty(s.Value)).ToList();
        if (SearchTerms.Count != 0)
        {
            foreach (var term in SearchTerms)
            {
                switch (term.Key)
                {
                    case NonTransactionalFeesSearchKey.All:
                        NonTransactionalFees = NonTransactionalFees.Where(c =>
                            (c.Code != null && c.Code.Contains(term.Value)) ||
                            (c.Name != null && c.Name.Contains(term.Value)));
                        break;

                    case NonTransactionalFeesSearchKey.Id:
                        NonTransactionalFees = NonTransactionalFees.Where(c => c.Code != null && c.Code.Contains(term.Value));
                        break;

                    case NonTransactionalFeesSearchKey.Name:
                        NonTransactionalFees = NonTransactionalFees.Where(c => c.Name != null && c.Name.Contains(term.Value));
                        break;
                }
            }
        }
        return NonTransactionalFees;
    }
    public static IQueryable<NonTransactionalFeesListResponse> FilterNonTransactionalFees(GetNonTransactionalFeesListRequest request, IQueryable<NonTransactionalFeesListResponse> NonTransactionalFees)
    {
        if (request.FilterByStatus != null && request.FilterByStatus.Any())
            NonTransactionalFees = NonTransactionalFees.Where(c => request.FilterByStatus.Contains(c.Status));

        return NonTransactionalFees;
    }
    public static IQueryable<NonTransactionalFeesListResponse> SortNonTransactionalFees(GetNonTransactionalFeesListRequest request, IQueryable<NonTransactionalFeesListResponse> NonTransactionalFees)
    {
        SortType orderType;

        if (request.OrderType.ToLower() == SortType.asc.ToString())
            orderType = SortType.asc;
        else if (request.OrderType.ToLower() == SortType.desc.ToString())
            orderType = SortType.desc;
        else
            throw new ArgumentException("Invalid value for OrderType. Allowed values are 'asc' and 'desc'.", nameof(request));

        if (!string.IsNullOrEmpty(request.OrderFieldName))
            NonTransactionalFees = NonTransactionalFees.OrderBy(request.OrderFieldName, orderType);

        return NonTransactionalFees;
    }
    public async Task<List<BasicNonTransactionalFeeInfo>> GetNonTransactionalFeesNamesAsync()
    {
        return await context.Set<NonTransactionalFeesEntity>()
        .AsNoTracking()
        .Select(p => new BasicNonTransactionalFeeInfo
        {
            Id = p.Id,
            Name = p.Name,
            NameAr = p.NameAr,
            Code = p.Code
        })
        .ToListAsync();
    }
}


