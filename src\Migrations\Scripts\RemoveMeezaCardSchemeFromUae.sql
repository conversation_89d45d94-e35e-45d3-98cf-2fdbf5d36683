﻿USE PRODUCTS; 
GO

DECLARE @CounterParty VARCHAR(10);
DECLARE @GeideaPOS VARCHAR(10);
DECLARE @CardScheme VARCHAR(10);
DECLARE @ProductId UNIQUEIDENTIFIER;
DECLARE @PartId UNIQUEIDENTIFIER;

BEGIN TRANSACTION;

SET @CounterParty ='GEIDEA_UAE'
SET @GeideaPOS = 'GEIDEA_POS'
SET @CardScheme = 'MEEZA'

SELECT @ProductId = ID FROM Products where Code = @GeideaPOS and  CounterParty = @CounterParty
SELECT @PartId = ID FROM Products where Code = @CardScheme and  CounterParty = @CounterParty

IF EXISTS (
    SELECT * FROM ProductParts
    WHERE ProductId = @ProductId
    AND PartId = @PartId     
)
BEGIN
    DELETE from ProductParts where ProductId = @ProductId and PartId = @PartId;
END

COMMIT TRANSACTION;