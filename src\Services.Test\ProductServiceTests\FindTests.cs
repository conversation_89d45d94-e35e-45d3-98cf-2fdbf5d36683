﻿using AutoMapper;
using Common;
using Common.Entities;
using Common.Models;
using DataAccess;
using DataAccess.Repositories;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using ProductService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Geidea.Utils.Counterparty.Providers;
using Xunit;
using Common.Options;
using Microsoft.Extensions.Options;
using NSubstitute;

namespace Services.Test.ProductServiceTests;

public class FindTests
{
    private readonly Mock<ILogger<ProductService>> logger = new Mock<ILogger<ProductService>>();
    private readonly Mock<ILogger<ProductInstanceRepository>> repoLogger = new Mock<ILogger<ProductInstanceRepository>>();
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly DataContext context;
    private readonly ProductService productService;
    private readonly IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;
    private readonly ICounterpartyProvider counterpartyProvider;

    public FindTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
           .UseInMemoryDatabase(databaseName: "ProductFindTests" + Guid.NewGuid().ToString())
           .Options;

        context = new DataContext(options, new CounterpartyProvider());

        context.Products.AddRange(new List<ProductEntity>
            {
                new ProductEntity
                {
                    Code="BBB",
                    DisplayOrder=0
                },
                new ProductEntity
                {
                    Code="AAA",
                    DisplayOrder=0,
                    Version=1
                },
                new ProductEntity
                {
                    Code="AAA",
                    DisplayOrder=0,
                    Version=0,
                    Availability= Constants.Availability.Obsolete,
                    SalesChannel = Constants.SalesChannel.All
                },
                new ProductEntity
                {
                    Code="CCC",
                    DisplayOrder=1,
                    Version=0
                },
                new ProductEntity
                {
                    Code="CCC",
                    DisplayOrder=1,
                    Version=1
                }
            });
        context.SaveChanges();

        softposEgyptFeatureToggle = Substitute.For<IOptions<SoftposEgyptFeatureToggle>>();
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = true });
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();

        var productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);
        var categoryRepository = new CategoryRepository(context, httpContext.Object);
        var productInstanceRepo = new ProductInstanceRepository(context, httpContext.Object, repoLogger.Object, mapper);
        productService = new ProductService(logger.Object, productRepository, mapper, categoryRepository, productInstanceRepo);
    }

    [Fact]
    public async Task FindAsync_WithoutParameters_DisplaysOrderCheck()
    {
        var products = await productService.FindAsync(new FindProductRequest(), false);
        products.Should().NotBeNullOrEmpty();
        products.Should().HaveCount(4);

        products[0].Code.Should().Be("AAA");
        products[0].Version.Should().Be(1);
        products[1].Code.Should().Be("BBB");
        products[2].Code.Should().Be("CCC");
        products[2].Version.Should().Be(1);
        products[3].Code.Should().Be("CCC");
        products[3].Version.Should().Be(0);
    }

    [Fact]
    public async Task FindByIds()
    {
        var ids = await context.Products.Select(p => p.Id).ToArrayAsync();

        var products = await productService.FindByIdsAsync(new IdsRequest { Ids = ids });
        products.Should().NotBeNullOrEmpty();
        products.Should().HaveCount(ids.Length);
    }
}
