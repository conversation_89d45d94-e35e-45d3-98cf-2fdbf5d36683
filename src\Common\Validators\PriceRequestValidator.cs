﻿using Common.Models;
using FluentValidation;
using System;
using System.Collections.Generic;

namespace Common.Validators;

public class PriceRequestValidator : AbstractValidator<PriceRequest>
{
    public PriceRequestValidator()
    {
        var conditions = new List<string> { "LT", "LTE", "GT", "GTE" };

        RuleFor(a => a.ChargeType).NotEmpty();
        RuleFor(a => a.ProductId).NotEqual(Guid.Empty).WithMessage("ProductId is mandatory.");

        RuleFor(a => a.ThresholdType).Null().Unless(a => a.Threshold.HasValue);
        RuleFor(a => a.ThresholdType)
            .Must(a => a == null || conditions.Contains(a))
            .WithMessage("Threshold must be either null or 'LT','LTE','GT','GTE'");

        RuleFor(a => a.Threshold).Null().Unless(a => a.ThresholdType != null);

        RuleFor(x => x.ValidFrom).NotEmpty().When(x => x.ValidTo != null)
            .WithMessage("ValidFrom must have value when ValidTo has value.");
        RuleFor(x => x.ValidTo).GreaterThan(x => x.ValidFrom).When(x => x.ValidTo != null)
            .WithMessage("ValidTo must be greater than ValidFrom.");
    }
}
