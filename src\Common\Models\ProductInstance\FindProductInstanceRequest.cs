﻿using System;

namespace Common.Models.ProductInstance;

public class FindProductInstanceRequest
{
    public string[]? Types { get; set; }
    public Guid[]? AgreementId { get; set; }
    public Guid? ProductId { get; set; }
    public Guid? StoreId { get; set; }
    public Guid? CompanyId { get; set; }
    public Guid[]? ProductInstanceId { get; set; }
    public int Skip { get; set; }
    public int Take { get; set; } = 10;
    public string? EposTicketId { get; set; }
}
