﻿using Common.Models.PendingCommissionPrice;
using Common.Models.ProductCommissionPrice;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Common.Services;
using System;

namespace ProductService.Controllers;
[Route("api/v1/[controller]")]
[ApiController]
public class PendingCommissionPriceController : ControllerBase
{
    private readonly IPendingCommissionPriceService pendingCommissionPriceService;
    public PendingCommissionPriceController(IPendingCommissionPriceService pendingCommissionPriceService)
    {
        this.pendingCommissionPriceService = pendingCommissionPriceService;
    }

    [HttpPost("HandleCreateAction")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> HandleCreateAction([FromBody] ProductCommissionPriceCreateRequest request)
    {
        var result = await pendingCommissionPriceService.CreateAsync(request);
        return Ok(result);
    }

    [HttpPost("HandleBulkEditAction")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> BulkEdit([FromBody] ProductCommissionPriceCreateRequest request)
    {
        var result = await pendingCommissionPriceService.BulkEdit(request);
        return Ok(result);
    }

    [HttpDelete("HandleBulkDeleteAction")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> HandleBulkDeleteAction(ComissionPriceBulkDeleteRequest request)
    {
        var Result = await pendingCommissionPriceService.DeleteComissionPricesAsync(request);

        return Ok(Result);
    }
    [HttpPut("HandleUpdateAction/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> HandleUpdateAction(Guid id, [FromBody] ProductComissionPriceUpdateRequest request)
    {
        await pendingCommissionPriceService.UpdateAsync(id, request);
        return Ok();
    }

    [HttpPost("Review")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> Review([FromBody] PendingComissionPriceReviewRequest request)
    {
        var result = await pendingCommissionPriceService.ReviewAsync(request);
        return Ok(result);
    }
}
