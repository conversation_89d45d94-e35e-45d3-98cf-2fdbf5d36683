﻿using System;
using System.Collections.Generic;
using Common.Models.Gle;
using FluentValidation;

namespace Common.Validators.Gle;

public class GleMerchantRequestValidator : AbstractValidator<GleMerchantRequest>
{
    public GleMerchantRequestValidator()
    {
        Include(new GleBaseValidator());

        RuleFor(x => x.MerchantId)
            .NotNull()
            .NotEqual(Guid.Empty)
            .WithErrorCode(Errors.InvalidMerchantId.Code)
            .WithMessage(Errors.InvalidMerchantId.Message);

        var gleUserCodeList = new List<string>
        {
            Constants.GleUserCategoryCode.Retailer, Constants.GleUserCategoryCode.Chain,
            Constants.GleUserCategoryCode.MultiStore,
            Constants.GleUserCategoryCode.MasterBusiness, Constants.GleUserCategoryCode.Wholesaler
        };
        RuleFor(x => x.UserCategoryCode)
            .Must(x => gleUserCodeList.Contains(x!))
            .When(x => !string.IsNullOrEmpty(x.UserCategoryCode))
            .WithErrorCode(Errors.InvalidGleUserCategoryCode.Code)
            .WithMessage(Errors.InvalidGleUserCategoryCode.Message);
    }
}