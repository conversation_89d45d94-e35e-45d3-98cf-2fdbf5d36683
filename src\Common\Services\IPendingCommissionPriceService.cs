﻿using Common.Models.PendingCommissionPrice;
using Common.Models.ProductCommissionPrice;
using System;
using System.Threading.Tasks;

namespace Common.Services;
public interface IPendingCommissionPriceService
{
    Task<CommissionPriceCreateResponse> CreateAsync(ProductCommissionPriceCreateRequest request);
    Task UpdateAsync(Guid id, ProductComissionPriceUpdateRequest request);
    Task<ComissionPriceBulkEditResponse> BulkEdit(ProductCommissionPriceCreateRequest request);
    Task<CommissionPriceBulkDeleteResponse> DeleteComissionPricesAsync(ComissionPriceBulkDeleteRequest request);
    Task<PendingComissionPriceReviewResponse> ReviewAsync(PendingComissionPriceReviewRequest request);
}
