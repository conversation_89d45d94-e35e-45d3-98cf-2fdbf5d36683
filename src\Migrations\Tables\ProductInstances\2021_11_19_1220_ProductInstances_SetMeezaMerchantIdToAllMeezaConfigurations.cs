﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_11_19_1220)]
public class ProductInstances_SetMeezaMerchantIdToAllMeezaConfigurations : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
                SELECT pi.Id, CAST(NULL as NVARCHAR(9)) as MeezaMerchantId
                    INTO #TempMeezaMerchantIds
                FROM ProductInstances pi
                    INNER JOIN Products p ON p.id=pi.ProductId
                WHERE p.Type='MEEZA' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.MeezaMerchantId') IS NULL

                DECLARE @Id UNIQUEIDENTIFIER;
                DECLARE cursor_setid CURSOR FOR SELECT Id FROM #TempMeezaMerchantIds;
                OPEN cursor_setid;
                FETCH NEXT FROM cursor_setid INTO @Id;
                WH<PERSON>E @@FETCH_STATUS = 0
                    BEGIN
                    UPDATE #TempMeezaMerchantIds SET MeezaMerchantId='675' + FORMAT(FLOOR(RAND() * (1000000)), '000000') WHERE Id=@Id
                        FETCH NEXT FROM cursor_setid INTO @Id;
                    END;
                CLOSE cursor_setid;
                DEALLOCATE cursor_setid;

                UPDATE pi
                    SET Metadata = JSON_MODIFY(pi.Metadata, '$.MeezaMerchantId', t.MeezaMerchantId)
                FROM ProductInstances pi
                INNER JOIN #TempMeezaMerchantIds t on t.Id=pi.Id");
    }
}
