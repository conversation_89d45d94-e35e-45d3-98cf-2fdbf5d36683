﻿using Common.Enums.ProductCommisssionPrices;
using Common.Enums.UnitPrice;
using System;

namespace Common.Models.NonTransactionalPrice;
public class NonTransactionalPriceDetailsResponse
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public Guid MccId { get; set; }
    public Guid BusinessTypeId { get; set; }
    public Guid NonTransFeeId { get; set; }
    public decimal FeeValue { get; set; }
    public FeeType FeeType { get; set; }
    public BillingType BillingType { get; set; }
    public PriceBillingFrequency BillingFrequency { get; set; }
}
