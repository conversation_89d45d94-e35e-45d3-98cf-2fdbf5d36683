﻿using Common.Entities;
using Common.Models;
using Common.Repositories;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace DataAccess.Repositories;

public class PriceRepository : AuditableRepository<Guid, PriceEntity>, IPriceRepository
{
    public PriceRepository(DbContext context, IHttpContextAccessor contextAccessor) : base(context, contextAccessor)
    {
    }

    public async Task<PriceEntity[]> FindAsync(FindPriceRequest request, bool attach)
    {
        var query = context.Set<PriceEntity>().AsQueryable();

        query = OnlyValid(query, request.OnlyValid);

        if (!attach)
            query = query.AsNoTracking();

        if (request.Id.HasValue)
            query = query.Where(a => a.Id == request.Id);

        if (!string.IsNullOrWhiteSpace(request.ChargeFrequency))
            query = query.Where(a => a.ChargeFrequency == request.ChargeFrequency);

        if (!string.IsNullOrWhiteSpace(request.ChargeType))
            query = query.Where(a => a.ChargeType == request.ChargeType);

        if (request.ExemptFromVAT.HasValue)
            query = query.Where(a => a.ExemptFromVAT == request.ExemptFromVAT);

        if (request.PercentagePrice.HasValue)
            query = query.Where(a => a.PercentagePrice == request.PercentagePrice);

        if (request.PerItemPrice.HasValue)
            query = query.Where(a => a.PerItemPrice == request.PerItemPrice);

        if (request.ProductId.HasValue)
            query = query.Where(a => a.ProductId == request.ProductId);

        if (request.Threshold.HasValue)
            query = query.Where(a => a.Threshold == request.Threshold);

        if (!string.IsNullOrWhiteSpace(request.ThresholdType))
            query = query.Where(a => a.ThresholdType == request.ThresholdType);

        if (!string.IsNullOrWhiteSpace(request.Group))
            query = query.Where(a => a.Group == request.Group);

        if (!string.IsNullOrWhiteSpace(request.Currency))
            query = query.Where(a => a.Currency == request.Currency);

        return await query.ToArrayAsync();
    }

    public async Task HardDeleteAsync(PriceEntity price)
    {
        context.Set<PriceEntity>().Remove(price);
        await context.SaveChangesAsync();
    }

    public IQueryable<PriceEntity> OnlyValid(IQueryable<PriceEntity> query, bool onlyValid)
    {
        if (onlyValid)
            query = query.Where(a => !a.DeletedFlag &&
                (a.ValidFrom == null || DateTime.UtcNow >= a.ValidFrom) &&
                (a.ValidTo == null || DateTime.UtcNow < a.ValidTo));
        return query;
    }
}
