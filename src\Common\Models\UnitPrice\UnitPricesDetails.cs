﻿using Common.Enums.UnitPrice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.UnitPrice;
public class UnitPricesDetails
{
    public Guid Id { get; set; }
    public Guid ProductID { get; set; }
    public string? ProductName { get; set; }
    public Guid MccId { get; set; }
    public string? MccName { get; set; }
    public Guid BusinessTypeId { get; set; }
    public string? BusinessTypeName { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal VATRate { get; set; }
    public VatType VATType { get; set; }
    public BillingType BillingType { get; set; }
    public BillingFrequency BillingFrequency { get; set; }
}

