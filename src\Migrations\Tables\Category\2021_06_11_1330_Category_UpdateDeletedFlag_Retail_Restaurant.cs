﻿using FluentMigrator;

namespace Migrations.Tables.Products;

[Migration(2021_06_11_1330)]
public class Category_UpdateDeletedFlag_Retail_Restaurant : ForwardOnlyMigration
{
    public override void Up()
    {
        Update.Table("Category").Set(new { DeletedFlag = 1 }).Where(new { Code = "RETAIL", Counterparty = "GEIDEA_SAUDI" });
        Update.Table("Category").Set(new { DeletedFlag = 1 }).Where(new { Code = "RESTAURANT", Counterparty = "GEIDEA_SAUDI" });
    }
}
