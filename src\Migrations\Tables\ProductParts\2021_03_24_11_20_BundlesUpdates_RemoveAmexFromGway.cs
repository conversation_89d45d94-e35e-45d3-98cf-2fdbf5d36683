﻿using FluentMigrator;

namespace Migrations.Tables.Products;

[Migration(2021_03_24_1120)]
public class BundlesUpdates_RemoveAmexFromGway : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"  DELETE FROM ProductParts WHERE
                               ProductId IN (SELECT Id FROM Products WHERE Code = 'PAYMENT_GATEWAY_BUNDLE' AND 
                                             Availability = 'Live' AND Counterparty = 'GEIDEA_SAUDI') AND
                               PartId IN (SELECT Id FROM Products WHERE Code = 'AMEX_ONLINE' 
                                             AND Availability = 'Live' AND Counterparty = 'GEIDEA_SAUDI')");

        Execute.Sql(@"  DELETE FROM ProductParts WHERE
                               ProductId IN (SELECT Id FROM Products WHERE Code = 'WEBSITE_BUILDER_BUNDLE' AND 
                                             Availability = 'Live' AND Counterparty = 'GEIDEA_SAUDI') AND
                               PartId IN (SELECT Id FROM Products WHERE Code = 'AMEX_ONLINE' 
                                             AND Availability = 'Live' AND Counterparty = 'GEIDEA_SAUDI')");
    }
}
