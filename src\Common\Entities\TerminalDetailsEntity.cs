﻿using Common.Attributes;
using Geidea.Utils.DataAccess.Entities;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
namespace Common.Entities;
public class TerminalDetailsEntity : AuditableEntity<Guid>
{
    [Queryable(canSearch: true, canOrder: true)]
    public string? TID { get; set; } = string.Empty;
    public string? Make { get; set; }
    public string? Model { get; set; }
    public string? SerialNumber { get; set; }
    public string? Family { get; set; }
}

