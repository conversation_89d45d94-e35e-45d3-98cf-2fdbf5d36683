﻿using System;
using System.Collections.Generic;

namespace Common.Models.ProductInstance;
public class BasicInstanceInfoWithChildren
{
    public Guid ParentProductInstanceId { get; set; }
    public Guid ParentProductId { get; set; }
    public string ParentProductType { get; set; } = null!;
    public string ParentProductCode { get; set; } = null!;

    public List<BasicInstanceInfoChildren> Children { get; set; } = null!;
}

public class BasicInstanceInfoChildren
{
    public Guid ProductInstanceId { get; set; }
    public Guid ProductId { get; set; }
    public string ProductType { get; set; } = null!;
    public string ProductCode { get; set; } = null!;
}
