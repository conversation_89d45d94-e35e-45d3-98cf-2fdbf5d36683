﻿using System;
using System.Collections.Generic;

namespace Geidea.ProductService.Models;

public class MpgsAccount
{
    public string? MpgsMerchantId { get; set; }
    public string? MpgsApiKey { get; set; }
    public IReadOnlyCollection<string> CardBrands { get; set; } = new List<string>();
    public IReadOnlyCollection<string> Currencies { get; set; } = new List<string>();
    public DateTimeOffset CreatedDate { get; set; }
}