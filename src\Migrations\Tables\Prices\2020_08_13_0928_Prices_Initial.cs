﻿using FluentMigrator;

namespace Migrations.Tables.Prices;

[Migration(2020_08_13_0928)]
public class Prices_Initial : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("Prices")
          .WithColumn("Id").AsGuid().NotNullable().PrimaryKey().WithDefault(SystemMethods.NewGuid)
          .WithColumn("ChargeFrequency").AsString(255).NotNullable()
          .WithColumn("ChargeType").AsString(255).NotNullable()
          .WithColumn("ExemptFromVAT").AsBoolean().NotNullable().WithDefaultValue(false)
          .WithColumn("ProductId").AsGuid().NotNullable().ForeignKey("Products", "Id")
          .WithColumn("PerItemPrice").AsInt32().Nullable()
          .WithColumn("PercentagePrice").AsInt32().Nullable()
          .WithColumn("Threshold").AsInt32().Nullable()
          .WithColumn("ThresholdType").AsString(5).Nullable()
          .WithColumn("Priority").AsInt32().NotNullable().WithDefaultValue(1)
          .WithColumn("Group").AsString(50).Nullable()
          .WithColumn("ValidFrom").AsDateTime2().Nullable()
          .WithColumn("ValidTo").AsDateTime2().Nullable()
          .WithColumn("IsDeleted").AsBoolean().NotNullable().WithDefaultValue(false)
          .WithColumn("CreatedBy").AsString(150).NotNullable()
          .WithColumn("CreatedDateUtc").AsDateTime2().NotNullable()
          .WithColumn("UpdatedBy").AsString(150).Nullable()
          .WithColumn("UpdatedDateUtc").AsDateTime2().Nullable()
          ;
    }
}
