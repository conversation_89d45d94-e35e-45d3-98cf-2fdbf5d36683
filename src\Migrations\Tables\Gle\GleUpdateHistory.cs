﻿using FluentMigrator;
using System.IO;
using System;

namespace Migrations.Tables.Gle;

[Migration(2024_01_27_2100)]
public class GleUpdateHistory : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("GleUpdateHistory")
            .WithColumn("Id").AsGuid().PrimaryKey().WithDefaultValue(SystemMethods.NewGuid)
            .WithColumn("GleUserId").AsString(250).Nullable()
            .WithColumn("GleLoginId").AsString(250).Nullable()
            .WithColumn("GleLoginId2").AsString(250).Nullable()
            .WithColumn("ParentGleUserId").AsString(250).Nullable()
            .WithColumn("RequestType").AsString(100).Nullable()
            .WithColumn("MerchantId").AsGuid().NotNullable()
            .WithColumn("OldValue").AsString(250).Nullable()
            .WithColumn("NewValue").AsString(250).Nullable()
            .WithColumn("GleStatus").AsString(250).NotNullable()
            .WithColumn("GleResponse").AsString(int.MaxValue).NotNullable()
            .WithColumn("CreatedBy").AsString(150).NotNullable()
            .WithColumn("CreatedDate").AsDateTime2().NotNullable()
            .WithColumn("UpdatedBy").AsString(150).Nullable()
            .WithColumn("UpdatedDate").AsDateTime2().Nullable();
    }
}

[Migration(2024_05_08_1130)]
public class UpdateGleUpdateHierarchyData : ForwardOnlyMigration
{
    public override void Up()
    {
        Alter.Table("GleUpdateHistory")
            .AddColumn("TypeId").AsGuid().Nullable()
            .AddColumn("Type").AsString(20).Nullable();

        Execute.Script(AppDomain.CurrentDomain.BaseDirectory + Path.Combine("Scripts", "GleUpdateHierarchyData.sql"));

        Alter.Table("GleUpdateHistory")
          .AlterColumn("TypeId").AsGuid().NotNullable()
          .AlterColumn("Type").AsString(20).NotNullable();
    }
}
