﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;
[Migration(2023_11_16_1155)]
public class ProductInstance_AddAddOnAndExtraCharges : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
             USE [PRODUCTS]
             GO
             
             Update PRI set Metadata = JSON_MODIFY(PRI.Metadata,'$.AddOnFees', JSON_QUERY('{""IsAddOnFeesEnabled"": false,""PBLAddOnFees"": {""Label"": """",""Value"": 0,""ValueType"": ""Amount""}}'))
             from [PRODUCTS].[dbo].[ProductInstances] PRI
             join [PRODUCTS].[dbo].Products P
             on PRI.ProductId = P.Id
             WHERE [Metadata] IS NOT NULL
             AND p.Type = 'GWAY'
             AND ISJSON([Metadata]) > 0
        ");

        Execute.Sql(@"
             USE [PRODUCTS]
             GO
             
             Update PRI set Metadata = JSON_MODIFY(PRI.Metadata,'$.ExtraCharges', JSON_QUERY('{""IsExtraChargesEnabled"": false,""PBLExtraCharges"": {""Label"": """",""Value"": 0,""ValueType"": ""Amount""}}'))
             from [PRODUCTS].[dbo].[ProductInstances] PRI
             join [PRODUCTS].[dbo].Products P
             on PRI.ProductId = P.Id
             WHERE [Metadata] IS NOT NULL
             AND p.Type = 'GWAY'
             AND ISJSON([Metadata]) > 0
        ");
    }
}
