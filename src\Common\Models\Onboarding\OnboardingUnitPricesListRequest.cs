﻿using Common.Enums.UnitPrice;
using Common.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using static Common.Constants;

namespace Common.Models.Onboarding;
public class OnboardingUnitPricesListRequest
{
    [DefaultValue(Constants.UnitPriceSearchDictionary)]
    public Dictionary<UnitPriceSearchKey, string> SearchTerms { get; set; } = new Dictionary<UnitPriceSearchKey, string>();
    public List<Guid>? FilterByProducts { get; set; }
    public List<string>? FilterByProductCodes { get; set; }
    public List<Guid>? FilterByMccType { get; set; }
    public List<Guid>? FilterByBusinessType { get; set; }
    public List<Guid>? FilterByCategory { get; set; }
    public List<string>? FilterBySalesChannel { get; set; }
    public List<string>? FilterByProductType { get; set; }
    public List<string>? FilterByAvailability { get; set; } = new List<string> { ProductAvailability.Live };
    public List<VatType>? FilterByVatType { get; set; }
    [DefaultValue("desc")]
    public string OrderType { get; set; } = SortType.desc.ToString();
    [DefaultValue("CreatedDate")]
    public string OrderFieldName { get; set; } = "CreatedDate";
    [DefaultValue(1)]
    public int Page { get; set; } = 1;
    [DefaultValue(10)]
    public int Size { get; set; } = 10;
}
public enum UnitPriceSearchKey
{
    All,
    ProductName,
    ProductCode,
    MccName,
    MccCode,
    BusinessType
}