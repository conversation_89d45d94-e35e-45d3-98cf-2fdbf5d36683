﻿using FluentValidation;
using Geidea.ProductService.Models;

namespace Common.Validators;

public class CybersourceAccountValidator : AbstractValidator<CyberSourceAccount>
{
    public CybersourceAccountValidator()
    {
        RuleFor(cybersourceAccount => cybersourceAccount.CyberSourceMerchantId)
            .NotEmpty()
            .MaximumLength(255)
            .When(cybersourceAccount => cybersourceAccount.CyberSourceMerchantId is not null);

        RuleFor(cybersourceAccount => cybersourceAccount.CyberSourceMerchantKeyId)
            .NotEmpty()
            .MaximumLength(255)
            .When(cybersourceAccount => cybersourceAccount.CyberSourceMerchantKeyId is not null);

        RuleFor(cybersourceAccount => cybersourceAccount.CyberSourceSharedSecretKey)
            .NotEmpty()
            .MaximumLength(255)
            .When(cybersourceAccount => cybersourceAccount.CyberSourceSharedSecretKey is not null);

        RuleFor(cybersourceAccount => cybersourceAccount.CardBrands).NotNull();

        RuleForEach(cybersourceAccount => cybersourceAccount.CardBrands)
            .NotEmpty()
            .MaximumLength(255)
            .WithMessage("Cybersource card brands cannot be null or empty");
    }
}