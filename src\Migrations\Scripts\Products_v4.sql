﻿--new amex online version
EXEC NewProductVersion 'AMEX_ONLINE', 'AMEX_ONLINE_V1', 0

DECLARE @AMEX_ONLINE_V1 UNIQUEIDENTIFIER
SELECT TOP 1 @AMEX_ONLINE_V1 = ID FROM Products where Code='AMEX_ONLINE_V1' 

INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
VALUES('PURCHASE_CNP', 1, @AMEX_ONLINE_V1, 290, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
VALUES('REFUND_CNP', 1, @AMEX_ONLINE_V1, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

--new mc online version
EXEC NewProductVersion 'MC_ONLINE', 'MC_ONLINE_V1', 0

DECLARE @MC_ONLINE_V1 UNIQUEIDENTIFIER
SELECT TOP 1 @MC_ONLINE_V1 = ID FROM Products where Code='MC_ONLINE_V1' 

DELETE FROM Prices WHERE ProductId = @MC_ONLINE_V1

INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
VALUES('PURCHASE_CNP', 1, @MC_ONLINE_V1, 290, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
VALUES('REFUND_CNP', 1, @MC_ONLINE_V1, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

--new payment gateway with new card schemes
EXEC NewProductVersion 'PAYMENT_GATEWAY_BUNDLE_V2','PAYMENT_GATEWAY_BUNDLE_V3', 1

EXEC ChangePartFromProduct 'PAYMENT_GATEWAY_BUNDLE_V3', 'MC_ONLINE', 'MC_ONLINE_V1'
EXEC ChangePartFromProduct 'PAYMENT_GATEWAY_BUNDLE_V3', 'AMEX_ONLINE', 'AMEX_ONLINE_V1'

--new website builder with new card schemes
EXEC NewProductVersion 'WEBSITE_BUILDER_BUNDLE_V2','WEBSITE_BUILDER_BUNDLE_V3', 1

EXEC ChangePartFromProduct 'WEBSITE_BUILDER_BUNDLE_V3', 'MC_ONLINE', 'MC_ONLINE_V1'
EXEC ChangePartFromProduct 'WEBSITE_BUILDER_BUNDLE_V3', 'AMEX_ONLINE', 'AMEX_ONLINE_V1'