﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.Products;
[Migration(2024_08_21_1230)]
public class UnitPriceList : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "UnitPriceList.sql")
            );
    }
}
[Migration(2025_01_08_1730)]
public class UnitPriceList_IncludePendingRequestsData : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "AlterUnitPriceListView.sql")
            );
    }
}
[Migration(2025_01_09_1452)]
public class UnitPriceList_IncludePendingRequestsData_CreatedByAsString : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "AlterUnitPriceListView.sql")
            );
    }
}
[Migration(2025_01_09_6004)]
public class UnitPriceList_IncludeDisplayuOrder : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "AlterUnitPriceListView.sql")
            );
    }
}
[Migration(2025_01_12_2320)]
public class UnitPriceList_IncludePendingRequestId : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
            AppDomain.CurrentDomain.BaseDirectory +
            Path.Combine("Scripts", "AlterUnitPriceListView.sql")
            );
    }
}