﻿using Common.Entities;
using System.Collections.Generic;
using System.Linq;

namespace Common.Services.Acquirers;
public interface IAcquirer
{
    string GenerateUniqueTID(IQueryable<TerminalDataSetEntity> terminalDataSetEntities, List<string> generatedTidsInMemory, string counterparty, string? merchantTag);

    string GenerateUniqueUAETID(IQueryable<TerminalDataSetEntity> terminalDataSetEntities, List<string> generatedTidsInMemory, string counterparty, string? merchantTag, string? Model, string? acquirerBank);
    string GenerateUniqueMID(string? maxMID);

    string GenerateUAEUniqueMID(string? maxMID, string? BusinessId, string? CardChannelType);
}
