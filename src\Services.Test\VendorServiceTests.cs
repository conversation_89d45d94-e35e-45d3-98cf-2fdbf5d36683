﻿using Common.Models.Vendor;
using Common.Repositories;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Services.Test;
public class VendorServiceTests
{
    private Mock<ILogger<VendorService>> _loggerMock;
    private Mock<IVendorRepository> _vendorRepositoryMock;

    [SetUp]
    public void Setup()
    {
        _loggerMock = new Mock<ILogger<VendorService>>();
        _vendorRepositoryMock = new Mock<IVendorRepository>();
    }

    [Test]
    public async Task CreateAsync_ValidRequest_AddsVendor()
    {
        // Arrange
        var vendorRequest = new VendorRequest { Name = "Test Vendor", TerminalType = "Type", Prefix = new List<string> { "12", "13" } };
        var vendorResponse = new VendorResponse { Name = "Test Vendor", Prefix = new List<string> { "12", "13" } };
        _vendorRepositoryMock.Setup(repo => repo.CreateAsync(vendorRequest)).ReturnsAsync(vendorResponse);
        var service = new VendorService(_loggerMock.Object, _vendorRepositoryMock.Object);

        // Act
        var result = await service.CreateAsync(vendorRequest);

        // Assert
        Assert.AreEqual(vendorResponse, result);
    }

    [Test]
    public async Task GetByIdAsync_ExistingId_ReturnsVendor()
    {
        // Arrange
        var vendorId = Guid.NewGuid();
        var vendorResponse = new VendorResponse { Name = "Test Vendor", Prefix = new List<string> { "12", "13" } };
        _vendorRepositoryMock.Setup(repo => repo.GetByIdAsync(vendorId)).ReturnsAsync(vendorResponse);
        var service = new VendorService(_loggerMock.Object, _vendorRepositoryMock.Object);

        // Act
        var result = await service.GetByIdAsync(vendorId);

        // Assert
        Assert.AreEqual(vendorResponse, result);
    }

    [Test]
    public async Task GetAsync_NoTerminalType_ReturnsAllVendors()
    {
        // Arrange
        var vendors = new List<VendorResponse>
        {
            new VendorResponse { Name = "Vendor 1", Prefix = new List<string> { "12" , "13"} },
            new VendorResponse { Name = "Vendor 2", Prefix = new List<string> { "12" , "13"} }
        };
        _vendorRepositoryMock.Setup(repo => repo.GetAsync(null)).ReturnsAsync(vendors);
        var service = new VendorService(_loggerMock.Object, _vendorRepositoryMock.Object);

        // Act
        var result = await service.GetAsync();

        // Assert
        Assert.AreEqual(vendors, result);
    }

    [Test]
    public async Task GetAsync_WithTerminalType_ReturnsFilteredVendors()
    {
        // Arrange
        var terminalType = "Type";
        var vendors = new List<VendorResponse>
        {
            new VendorResponse { Name = "Vendor 1", Prefix = new List<string> { "12" , "13"}  },
            new VendorResponse { Name = "Vendor 2", Prefix = new List<string> { "12", "13" }}
        };
        _vendorRepositoryMock.Setup(repo => repo.GetAsync(terminalType)).ReturnsAsync(vendors);
        var service = new VendorService(_loggerMock.Object, _vendorRepositoryMock.Object);

        // Act
        var result = await service.GetAsync(terminalType);

        // Assert
        Assert.AreEqual(vendors, result);
    }

    [Test]
    public async Task AdvancedSearch_ValidRequest_ReturnsSearchResult()
    {
        var searchRequest = new VendorSearchRequest { };
        var searchResult = new VendorSearchResponse
        {
            Records = new List<VendorResponse>
            {
                new VendorResponse { Name = "Vendor 1", Prefix = new List<string> { "12", "13" } },
                new VendorResponse { Name = "Vendor 2", Prefix = new List<string> { "12", "13" } }
            },
            TotalRecordCount = 2,
            ReturnedRecordCount = 5
        };
        _vendorRepositoryMock.Setup(repo => repo.AdvancedSearch(searchRequest)).ReturnsAsync(searchResult);
        var service = new VendorService(_loggerMock.Object, _vendorRepositoryMock.Object);

        var result = await service.AdvancedSearch(searchRequest);

        Assert.AreEqual(searchResult, result);
    }

    [Test]
    public async Task AdvancedSearch_EmptyResult_ReturnsNull()
    {
        var searchRequest = new VendorSearchRequest { };
        _vendorRepositoryMock.Setup(repo => repo.AdvancedSearch(searchRequest)).ReturnsAsync(new VendorSearchResponse { });
        var service = new VendorService(_loggerMock.Object, _vendorRepositoryMock.Object);

        var result = await service.AdvancedSearch(searchRequest);

        Assert.IsNotNull(result);
    }


    [Test]
    public async Task GetDefaultVendorAsync_ReturnsVendor()
    {
        // Arrange
        var vendorId = Guid.NewGuid();
        var vendorResponse = new VendorResponse { Name = "Test Vendor", IsDefault = true, Prefix = new List<string> { "12", "13" } };
        _vendorRepositoryMock.Setup(repo => repo.GetDefaultVendorAsync()).ReturnsAsync(vendorResponse);
        var service = new VendorService(_loggerMock.Object, _vendorRepositoryMock.Object);

        // Act
        var result = await service.GetDefaultVendorAsync();

        // Assert
        Assert.AreEqual(vendorResponse, result);
    }

    [Test]
    public async Task SetDefaultVendorAsync_ValidRequest_SetDefaultVendor()
    {
        // Arrange
        var vendorId = Guid.NewGuid();
        var vendorResponse = new VendorResponse { Id = vendorId, IsDefault = true, Name = "Test Vendor", Prefix = new List<string> { "12", "13" } };
        _vendorRepositoryMock.Setup(repo => repo.SetDefaultVendorAsync(vendorId)).ReturnsAsync(vendorResponse);
        var service = new VendorService(_loggerMock.Object, _vendorRepositoryMock.Object);

        // Act
        var result = await service.SetDefaultVendorAsync(vendorId);

        // Assert
        Assert.AreEqual(vendorResponse, result);
    }
}
