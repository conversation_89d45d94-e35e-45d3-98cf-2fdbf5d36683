﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<LangVersion>latest</LangVersion>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
		<Nullable>enable</Nullable>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<ProjectGuid>{d3ec21a0-312d-40c8-b408-15ee556bbf1a}</ProjectGuid>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="6.0.2" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
		<PackageReference Include="Geidea.PaymentGateway.ConfigServiceClient" Version="3.1.123" />
		<PackageReference Include="FluentMigrator.Runner" Version="3.3.2" />
	  <PackageReference Include="Geidea.Utils.Counterparty" Version="2.0.232" />
		<PackageReference Include="Geidea.Utils.Policies" Version="2.0.812" />
		<PackageReference Include="Geidea.Utils.Versioning" Version="1.1.247" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" Version="6.0.15" />
		<PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="6.0.15" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.15" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.15">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="6.0.0" />
		<PackageReference Include="Microsoft.OpenApi" Version="1.6.3" />
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="6.0.13" />
		<PackageReference Include="Serilog" Version="2.12.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="6.1.0" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="2.2.0" />
		<PackageReference Include="Serilog.Enrichers.Process" Version="2.0.2" />
		<PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="3.1.0" />
		<PackageReference Include="Serilog.Formatting.Compact" Version="1.1.0" />
		<PackageReference Include="Serilog.Settings.AppSettings" Version="2.2.2" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="3.4.0" />
		<PackageReference Include="Serilog.Sinks.Debug" Version="2.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="7.0.6" />
		<PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="6.5.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.5.0" />
		<PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.23.0" />
		<PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Common\Common.csproj" />
		<ProjectReference Include="..\DataAccess\DataAccess.csproj" />
		<ProjectReference Include="..\Messaging\Messaging.csproj" />
		<ProjectReference Include="..\Migrations\Migrations.csproj" />
		<ProjectReference Include="..\Services\Services.csproj" />
	</ItemGroup>

</Project>
