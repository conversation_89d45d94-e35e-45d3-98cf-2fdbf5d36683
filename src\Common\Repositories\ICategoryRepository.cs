﻿using Common.Entities;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Threading.Tasks;
using Common.Models.CategoryRequests;
using Common.Models;
using System.Collections.Generic;

namespace Common.Repositories;

public interface ICategoryRepository : IRepository<Guid, CategoryEntity>
{
    public Task HardDeleteCategory(CategoryEntity category);
    public Task<bool> ExistsAsync(Guid categoryId);
    public Task<CategoryEntity[]> FindCategories(FindCategoryRequest request);
    public Task<List<CategoriesListResponse>> GetCategoriesList();
}
