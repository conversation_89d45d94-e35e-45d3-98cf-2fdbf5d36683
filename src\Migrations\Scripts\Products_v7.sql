﻿
 DECLARE @Ids TABLE(ID UNIQUEIDENTIFIER)
 DECLARE @Id UNIQUEIDENTIFIER

 DECLARE @ProScreenLiteId UNIQUEIDENTIFIER
 DECLARE @IpadStandId UNIQUEIDENTIFIER
 DECLARE @ProScannerId UNIQUEIDENTIFIER
 DECLARE @ProScreenId UNIQUEIDENTIFIER
 DECLARE @ProPrinterId UNIQUEIDENTIFIER
 DECLARE @ProCashDrawerId UNIQUEIDENTIFIER
 DECLARE @AccessoriesCategoryId UNIQUEIDENTIFIER
 
 --Pro Screen Lite

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Normal', 'PRO_SCREEN_LITE', 'ACCESSORIES', 'n/a', GETUTCDATE(), GETUTCDATE())

 SELECT TOP 1 @ProScreenLiteId = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('ONE_OFF','SETUP_CHARGE',0, @ProScreenLiteId, 2900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')
 

  --Ipad Stand

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Normal', 'IPAD_STAND', 'ACCESSORIES', 'n/a', GETUTCDATE(), GETUTCDATE())

 SELECT TOP 1 @IpadStandId = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('ONE_OFF','SETUP_CHARGE',0, @IpadStandId, 500, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')


  --Pro Scanner

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Normal', 'PRO_SCANNER', 'ACCESSORIES', 'n/a', GETUTCDATE(), GETUTCDATE())

 SELECT TOP 1 @ProScannerId = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('ONE_OFF','SETUP_CHARGE',0, @ProScannerId, 1499, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')


  --Pro Screen

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Normal', 'PRO_SCREEN', 'ACCESSORIES', 'n/a', GETUTCDATE(), GETUTCDATE())

 SELECT TOP 1 @ProScreenId = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('ONE_OFF','SETUP_CHARGE',0, @ProScreenId, 4000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')


  --Pro Printer

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Normal', 'PRO_PRINTER', 'ACCESSORIES', 'n/a', GETUTCDATE(), GETUTCDATE())

 SELECT TOP 1 @ProPrinterId = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('ONE_OFF','SETUP_CHARGE',0, @ProPrinterId, 1799, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')


  --Pro Cash Drawer

 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
 VALUES('Normal', 'PRO_CASH_DRAWER', 'ACCESSORIES', 'n/a', GETUTCDATE(), GETUTCDATE())

 SELECT TOP 1 @ProCashDrawerId = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
 VALUES('ONE_OFF','SETUP_CHARGE',0, @ProCashDrawerId, 1499, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

   --Accesories Category

 INSERT INTO Category(Code, Type, CreatedBy, CreatedDateUtc, DisplayOrder) OUTPUT inserted.Id INTO @Ids
 VALUES('ACCESSORIES', 0,'n/a', GETUTCDATE(), 6)

 SELECT TOP 1 @AccessoriesCategoryId = ID FROM @Ids 
 DELETE FROM @Ids

 INSERT INTO ProductCategories(ProductId, CategoryId)
 VALUES(@ProScreenLiteId,@AccessoriesCategoryId),
		(@IpadStandId,@AccessoriesCategoryId),
		(@ProScannerId,@AccessoriesCategoryId),
		(@ProScreenId,@AccessoriesCategoryId),
		(@ProPrinterId,@AccessoriesCategoryId),
		(@ProCashDrawerId,@AccessoriesCategoryId)