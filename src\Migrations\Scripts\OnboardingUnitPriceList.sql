CREATE OR ALTER VIEW OnboardingUnitPriceList
As
	
	WITH ProductImagesCTE AS (
    SELECT 
        pi.ProductId, 
        pi.ImageId,
        pi.Language,  
        ROW_NUMBER() OVER (PARTITION BY pi.ProductId, pi.Language ORDER BY pi.DisplayOrder ASC) AS RowNum
    FROM 
        [dbo].[ProductImages] pi
)
SELECT 
    u.Id,               
    p.Id AS ProductId,
    p.[Name] AS ProductName,
    p.NameAr AS ProductNameAR,              
    p.Subname AS ProductSubname,              
    p.SubnameAr AS ProductSubnameAr,          
    p.ProductLink AS ProductLink,             
    p.Description AS ProductDescription,     
    p.DescriptionAr AS ProductDescriptionAr,  
    p.Flow AS ProductFlow,                    
    p.SalesChannel AS ProductSalesChannel,    
	p.Availability As ProductAvailability,
    P.Type As ProductType,
    p.ReferralChannel AS ProductReferralChannel,  
    p.QuickOnboarding AS ProductQuickOnboarding,  
    p.DisplayOrder AS ProductDisplayOrder,
    piEN.ImageId AS ImageIdEn,                
    piAR.ImageId AS ImageIdAr,                 
    p.Code AS ProductCode,
    p.IsCNP,
    m.Id AS MccId,
    m.[Name] AS MccName,
    b.Id AS BusinessTypeId,
    b.[Name] AS BusinessType,
	b.Code As BusinessTypeCode,
    u.UnitPrice,
    u.VATRate,
    m.Code AS MccCode,
    mc.Name AS MccCategory,
    u.VATType,
    u.BillingType,
    u.BillingFrequency,
    u.CreatedDate,
    c.Id AS CategoryId,                      
    c.Name AS CategoryName,                  
    c.Code AS CategoryCode                    
FROM 
    [dbo].[UnitPrice] u
JOIN 
    [dbo].[Products] p ON u.ProductID = p.Id
JOIN 
    [dbo].[BusinessTypes] b ON u.BusinessTypeID = b.Id
JOIN 
    [dbo].[Mcc] m ON u.MCCID = m.Id
JOIN 
    [dbo].[MccCategory] mc ON m.MccCategoryId = mc.Id
LEFT JOIN 
    ProductImagesCTE piEN ON p.Id = piEN.ProductId AND piEN.Language = 'EN' AND piEN.RowNum = 1
LEFT JOIN 
    ProductImagesCTE piAR ON p.Id = piAR.ProductId AND piAR.Language = 'AR' AND piAR.RowNum = 1 
JOIN 
    [dbo].[ProductCategories] pc ON p.Id = pc.ProductId          
JOIN 
    [dbo].[Category] c ON pc.CategoryId = c.Id  
WHERE 
    c.DeletedFlag = 0;
GO

