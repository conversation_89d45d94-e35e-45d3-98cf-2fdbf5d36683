﻿using Common.Entities;
using Common.Models.MccManagement;
using Common.Models.NonTransactionalFees;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Common.Services;
public interface IMccService
{
    Task<Mcc> CreateAsync(MccRequest request);
    Task<Mcc> UpdateAsync(Guid id, MccRequest request);
    Task<GetMccListResponse> GetMCCList(GetMccListRequest request);
    Task<MccDetailsResponse?> GetMccDetails(Guid Id);
    Task<bool> ToggleStatus(Guid Id, bool Status);
    Task<MemoryStream> ExportMccToExcel(GetMccListRequest request);
    Task<(int SuccessCount, int FailureCount, byte[] InvalidRecordsExcel)> ImportMccAsync(Guid categoryId, Stream fileStream, CancellationToken cancellationToken);
    Task<List<Mcc>> GetMCCsByCodesAsync(List<string?> codes);
    Task<List<MccListCategoryResponse>> GetAllMccsWithCategoryAsync();

}
