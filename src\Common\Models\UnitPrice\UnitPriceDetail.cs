﻿using Common.Entities;
using Common.Enums.UnitPrice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.UnitPrice;
public class UnitPriceDetails
{
    public Guid Id { get; set; }
    public Guid ProductID { get; set; }
    public Guid MCCID { get; set; }
    public Guid BusinessTypeID { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal VATRate { get; set; }
    public VatType VATType { get; set; }
    public BillingType BillingType { get; set; }
    public BillingFrequency BillingFrequency { get; set; }
}
