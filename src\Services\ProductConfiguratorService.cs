﻿using AutoMapper;
using Common;
using Common.Data;
using Common.Data.ProductType;
using Common.Entities;
using Common.Models;
using Common.Models.ProductInstance;
using Common.Repositories;
using Common.Services;
using Common.Validators;
using Geidea.ProductService.Models;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Models;
using Services.Settings;
using System;
using System.Collections.Generic;
using ProductCodes = Common.Constants.ProductCodes;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using static Common.Data.Helpers.CounterpartyHelper;
using static Common.Data.Helpers.DataDefaultValuesHelper;
using GatewayData = Common.Data.GatewayData;
using MeezaData = Common.Data.MeezaData;

namespace Services;

public class ProductConfiguratorService : IProductConfiguratorService
{
    private readonly IProductInstanceRepository productInstanceRepository;
    private readonly IProductRepository productRepository;
    private readonly ILogger<ProductConfiguratorService> logger;
    private readonly IMapper mapper;
    private readonly IProductService productService;
    private readonly ICounterpartyProvider counterpartyProvider;
    private readonly IProductChangeSenderService productChangeSenderService;
    private readonly MeezaSettings meezaSettings;
    private readonly CurrencySettings currencySettings;
    private readonly MpgsAccountsSettings mpgsSettings;

    public ProductConfiguratorService(
        IOptionsMonitor<MeezaSettings> meezaOptions,
        ILogger<ProductConfiguratorService> logger,
        IMapper mapper,
        IProductService productService,
        IProductInstanceRepository productInstanceRepository,
        IProductRepository productRepository,
        ICounterpartyProvider counterpartyProvider,
        IProductChangeSenderService productChangeSenderService,
        IOptionsMonitor<CurrencySettings> currencyOptions, IOptionsMonitor<MpgsAccountsSettings> MpgsSettings
)
    {
        this.meezaSettings = meezaOptions.CurrentValue;
        currencySettings = currencyOptions.CurrentValue;
        this.logger = logger;
        this.mapper = mapper;
        this.productInstanceRepository = productInstanceRepository;
        this.productService = productService;
        this.productRepository = productRepository;
        this.counterpartyProvider = counterpartyProvider;
        this.productChangeSenderService = productChangeSenderService;
        this.mpgsSettings = MpgsSettings.CurrentValue;
    }

    public async Task<ProductInstanceResponse> CreateAsync(CreateProductInstanceRequest productInstanceRequest, bool isFirstInstanceCreation = false)
    {
        await ValidateAsync(productInstanceRequest, productInstanceRequest.ParentId.HasValue && productInstanceRequest.ParentId != Guid.Empty);

        await SetPropertiesFromParentAsync(productInstanceRequest);
        await SetCompanyAndStoreFromParentConfigurationAsync(productInstanceRequest);

        var productInstance = await CreateProductInstanceAsync(productInstanceRequest, isFirstInstanceCreation);
        await productInstanceRepository.SaveChangesAsync();

        await PublishUpdatedEventIfParentIsGateway(productInstanceRequest.ParentId);

        return mapper.Map<ProductInstanceResponse>(productInstance);
    }

    public async Task<ProductInstanceResponse[]> BulkCreateAsync(IList<CreateProductInstanceRequest> productInstancesRequest)
    {
        var productInstances = new List<ProductInstanceResponse>();

        foreach (var productInstanceRequest in productInstancesRequest)
        {
            productInstances.Add(await CreateAsync(productInstanceRequest));
        }

        return productInstances.ToArray();
    }

    public async Task<Guid[]> DeleteAsync(DeleteProductInstanceRequest deleteProductInstanceRequest)
    {
        var validationResult = new DeleteProductInstanceValidator().Validate(deleteProductInstanceRequest);

        if (!validationResult.IsValid)
        {
            logger.LogError(Errors.InvalidDeleteParameters.Message);
            throw new ServiceException(Errors.InvalidDeleteParameters);
        }

        var deletedProductInstances = new List<Guid>();

        var findRequest = mapper.Map<FindProductInstanceRequest>(deleteProductInstanceRequest);
        var allProductInstances = await FindAsync(findRequest, true);

        foreach (var productInstance in allProductInstances)
            await DeleteParentConfigForChildrenAsync(await productInstanceRepository.FirstOrDefaultAsync(x => x.Id == productInstance.ProductInstanceId && !x.DeletedFlag));

        var selectedProductInstanceIds = RemoveChildProductInstances(allProductInstances.ToList());

        foreach (var productInstanceId in selectedProductInstanceIds)
            deletedProductInstances.AddRange(await DeleteWithChildren(productInstanceId));

        await productInstanceRepository.SaveChangesAsync();

        foreach (var productInstance in allProductInstances)
        {
            await PublishUpdatedEventIfParentIsGateway(productInstance.ParentId);
        }

        return deletedProductInstances.ToArray();
    }

    public async Task<ProductInstanceWithParentResponse[]> FindAsync(FindProductInstanceRequest findProductInstanceRequest, bool track = false)
    {
        var validationResult = new FindProductInstanceValidator().Validate(findProductInstanceRequest);
        if (!validationResult.IsValid)
        {
            var errorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
            logger.LogError("Product instance create request validation failed: {@errors}", errorDescription);
            throw new ValidationException(validationResult);
        }

        var productInstances = await productInstanceRepository.FindAsync(findProductInstanceRequest, false, track);
        logger.LogInformation($"Found '{productInstances.Count}' product instances.");

        return mapper.Map<ProductInstanceWithParentResponse[]>(productInstances);
    }
    public async Task<List<Guid?>> FindGatewayProductCompanyIds(bool track = false)
    {
        var companyIds = await productInstanceRepository.FindGatewayCompanyIds(new FindGatewayCompanyIds { Types = new string[] { "GWAY" }, }, false, false);
        logger.LogInformation("Found {@companyIds} product instances.", companyIds.Count);
        return companyIds;
    }

    public async Task<ProductInstanceResponse> FindByIdAsync(Guid id)
    {
        var productInstance = await productInstanceRepository.GetByIdAsync(id);
        return mapper.Map<ProductInstanceResponse>(productInstance);
    }

    public async Task<ProductInstanceResponse[]> FindByIdsAsync(IdsRequest request)
    {
        var productInstances = await productInstanceRepository.FindByIdsAsync(request);
        return mapper.Map<ProductInstanceResponse[]>(productInstances);
    }

    public async Task<List<StoreProductInstanceResponse>> GetProductInstancesForStore(Guid storeId)
    {
        var productInstances = await productInstanceRepository.GetProductInstancesForStore(storeId);
        var storeProductList = new List<StoreProductInstanceResponse>();

        if (!productInstances.Any())
        {
            return storeProductList;
        }

        foreach (var productInstanceEntity in productInstances)
        {
            storeProductList.Add(new StoreProductInstanceResponse()
            {
                Product = mapper.Map<ProductShortResponse>(productInstanceEntity.Product),
                Data = productInstanceEntity.Data,
                StoreId = storeId,
                ParentId = productInstanceEntity.ParentId,
                ProductInstanceId = productInstanceEntity.Id,
                CreatedDate = productInstanceEntity.CreatedDate
            });
        }

        return storeProductList;
    }

    public async Task<List<StoreProductInstanceResponse>> GetProductInstancesForMultipleStores(Guid[] storeIds)
    {
        var productInstances = await productInstanceRepository.GetProductInstancesForMultipleStores(storeIds);
        return productInstances;
    }

    public async Task<ProductInstanceResponse> PatchAsync(Guid id, JsonPatchDocument<UpdateProductInstanceRequest> patchDocument)
    {
        var productInstance = await productInstanceRepository.GetByIdAsync(id, true);
        var productInstanceUpdate = mapper.Map<UpdateProductInstanceRequest>(productInstance);

        try
        {
            patchDocument.ApplyTo(productInstanceUpdate);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Invalid patch for product instance update request.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidPatchRequest);
        }

        bool updateParent = productInstanceUpdate.ParentId != productInstance.ParentId;
        bool updateParentConfiguration = productInstanceUpdate.ParentConfigurationId != productInstance.ParentConfigurationId;
        bool updateCompany = productInstanceUpdate.CompanyId != productInstance.CompanyId;
        bool updateStore = productInstanceUpdate.StoreId != productInstance.StoreId;

        if (updateParent && productInstanceUpdate.ParentId.HasValue && productInstanceUpdate.ParentId != Guid.Empty)
            await CheckParentIdAsync(productInstanceUpdate.ParentId);

        if (updateParentConfiguration && productInstanceUpdate.ParentConfigurationId.HasValue && productInstanceUpdate.ParentConfigurationId != Guid.Empty)
            await CheckParentConfigurationIdAsync(productInstanceUpdate.ParentConfigurationId);

        if (updateStore)
            await CheckIfPaymentGatewayExistsOnStore(productInstanceUpdate.StoreId, productInstance);

        mapper.Map(productInstanceUpdate, productInstance);

        productInstance.Data?.ValidateAndThrow();

        if (updateParent)
        {
            await UpdateAgreementFromParentAsync(productInstance);
            await UpdateAgreementForChildrenAsync(productInstance);
        }

        if (updateParentConfiguration || updateCompany)
        {
            await UpdateCompanyFromParentAsync(productInstance);
            await UpdateCompanyForChildrenAsync(productInstance);
        }

        if (updateParentConfiguration || updateStore)
        {
            await UpdateStoreFromParentAsync(productInstance);
            await UpdateStoreForChildrenAsync(productInstance);
        }

        productInstanceRepository.Update(productInstance);

        await productInstanceRepository.SaveChangesAsync();

        productChangeSenderService.SendUpdatedEvent(productInstance);

        await PublishUpdatedEventIfParentIsGateway(productInstance.ParentId);

        return mapper.Map<ProductInstanceResponse>(productInstance);
    }

    public async Task<List<Guid>> UpdateTerminalProductInstancesMeta(List<UpdateProductInstanceMetaRequest> updateProductInstances)
    {
        var productInstanceIds = updateProductInstances.Select(c => c.ProductInstanceId).ToArray();

        var existingProductInstance = await productInstanceRepository.FindByIdsAsync(new IdsRequest { Ids = productInstanceIds }, true);

        for (var i = 0; i < updateProductInstances.Count; i++)
        {
            var productInstance = updateProductInstances[i];

            var existedInstance = existingProductInstance.Find(p => p.Id == productInstance.ProductInstanceId);

            if (existedInstance == null)
            {
                logger.LogError("UpdateTerminalProductInstancesMeta: product instance with id {ProductInstanceId} not found!", productInstance.ProductInstanceId);
                continue;
            }

            var terminalData = existedInstance.Data as TerminalData ?? new TerminalData();

            terminalData.ShortName_AR = productInstance.LegalNameAr;
            terminalData.ShortName_EN = productInstance.LegalName;
            terminalData.MIDMerchantReference = productInstance.MIDMerchantReference;
            terminalData.TId = productInstance.TId;
            terminalData.FullTId = productInstance.FullTId;
            terminalData.POSDataCode = productInstance.Trsm;
            terminalData.ProviderBank = productInstance.ProviderBank;
            terminalData.TradingCurrency = string.IsNullOrEmpty(terminalData.TradingCurrency) ? productInstance.TradingCurrency : terminalData.TradingCurrency;
            terminalData.Mcc = !string.IsNullOrEmpty(productInstance.MCC) ? productInstance.MCC : terminalData.Mcc;

            if (!string.IsNullOrEmpty(productInstance.ConnectionType))
                terminalData.ConnectionType = productInstance.ConnectionType;

            if (!string.IsNullOrEmpty(productInstance.ChannelType))
                terminalData.ChannelType = productInstance.ChannelType;

            existedInstance.Data = terminalData;
        }

        await productInstanceRepository.SaveChangesAsync();

        SendUpdatedEvent(existingProductInstance);

        return existingProductInstance.Select(c => c.Id).ToList();
    }

    private void SendUpdatedEvent(List<ProductInstanceEntity> updateInstances)
    {
        updateInstances.ForEach(item =>
        {
            try
            {
                productChangeSenderService.SendUpdatedEvent(item);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "UpdateTerminalProductInstancesMeta: failed to SendUpdatedEvent for product instance with id {Id}", item.Id);
            }
        });
    }

    public async Task<Guid[]> CreateDefaultProductInstancesAsync(DefaultProductInstanceRequest request)
    {
        logger.LogInformation("CreateDefaultProductInstancesAsync operation started {@request}", request);

        var createdInstances = await CreateProductInstanceWithChildren(request);

        logger.LogInformation("CreateDefaultProductInstancesAsync operation ended {@request} {@numberOfCreatedInstances}", request, createdInstances.Count);

        return createdInstances.Select(i => i.ProductInstanceId).ToArray();
    }

    public async Task<List<BasicInstanceInfoWithChildren>> CreateDefaultProductInstancesChildrenAsync(DefaultProductInstanceRequest request)
    {
        logger.LogInformation("CreateDefaultProductInstancesTerminalChildrenAsync operation started {@request}", request);

        List<ProductInstanceResponse> createdInstances = await CreateProductInstanceWithChildren(request);

        var basicInfo = createdInstances.Select(i => new BasicInstanceInfoWithChildren
        {
            ParentProductCode = i.Product.Code,
            ParentProductId = i.Product.ProductId,
            ParentProductInstanceId = i.ProductInstanceId,
            ParentProductType = i.Product.Type,
            Children = i.Children.Select(ic => new BasicInstanceInfoChildren
            {
                ProductCode = ic.Product.Code,
                ProductId = ic.Product.ProductId,
                ProductInstanceId = ic.ProductInstanceId,
                ProductType = ic.Product.Type,
            }).ToList()
        }).ToList();

        logger.LogInformation("CreateDefaultProductInstancesTerminalChildrenAsync operation ended {@request} {@numberOfCreatedInstances}", request, createdInstances.Count);
        return basicInfo;
    }

    public async Task<Guid> CreateMerchantMigrationDefaultProductInstancesAsync(
        MerchantMigrationProductInstanceRequest request)
    {
        logger.LogInformation("CreateMerchantMigrationDefaultProductInstancesAsync operation started {@request}", request);

        var instanceRequest = CreateMerchantMigrationProductInstanceRequest(request);
        var result = await CreateMerchantMigrationProductInstanceAsync(instanceRequest);

        logger.LogInformation("CreateMerchantMigrationDefaultProductInstancesAsync operation ended {@request}", request);

        return result;
    }

    public async Task<ProductInstanceResponse[]> SearchProductInstanceBaseAsync(SearchProductInstanceBaseRequest request)
    {
        var productInstances = await productInstanceRepository.SearchProductInstanceBaseAsync(request);
        logger.LogInformation($"Found '{productInstances.Length}' product instances.");

        return mapper.Map<ProductInstanceResponse[]>(productInstances);
    }

    public async Task<SearchGatewayConfigurationsResponse> SearchGatewayConfigurations(SearchGatewayConfigurationsRequest request)
    {
        return await productInstanceRepository.SearchGatewayConfigurations(request);
    }

    public async Task UpdateProductInstancesStoreNameForMerchant(MerchantStoreNameProductInstanceRequest request)
    {
        if (!(await new MerchantStoreNameProductInstanceValidator().ValidateAsync(request)).IsValid)
        {
            logger.LogError("Failed to Update ProductInstances with FutureWorksStoreName.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.UpdateMerchantProductInstanceStoreNameBadRequest);
        }

        var productInstances = await productInstanceRepository.FindByIdsAsync(new IdsRequest
        {
            Ids = request.ProductInstances!.ToArray()
        }, track: true);

        if (productInstances.Any())
        {
            foreach (var productInstance in productInstances)
            {
                SetStoreName(productInstance, request.StoreName!);
                productInstance.Children.ForEach(prodInstanceItem => SetStoreName(prodInstanceItem, request.StoreName!));

                await UpdateProductInstance(productInstance);
            }
        }
    }

    public async Task<bool> IsSubscribedToPaymentGateway(Guid merchantId)
    {
        if (merchantId == Guid.Empty)
        {
            logger.LogError("Invalid merchant ID {id}", merchantId);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidMerchantId.Message);
        }

        return await productInstanceRepository.IsSubscribedToPaymentGateway(merchantId);
    }

    private async Task<Guid> CreateMerchantMigrationProductInstanceAsync(CreateProductInstanceRequest productInstanceRequest)
    {
        var productInstance = mapper.Map<ProductInstanceEntity>(productInstanceRequest);
        var product = (await productService.FindAsync(new FindProductRequest
        {
            Id = productInstanceRequest.ProductId,
            OnlyValid = false
        }, false)).FirstOrDefault();

        if (product?.Parts == null)
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.ProductNotFound);

        productInstance.Product = mapper.Map<ProductEntity>(product);

        SetOrderMigrationProductInstanceDefaultEposData(productInstance);

        productInstanceRepository.Save(productInstance);

        try
        {
            foreach (var part in product!.Parts)
            {
                var productScheme = await productRepository.FindByIdsAsync(new IdsRequest() { Ids = new[] { part.PartId } });

                var cardSchemeChild = new CreateProductInstanceRequest()
                {
                    ParentId = productInstance.Id,
                    AgreementId = productInstance.AgreementId,
                    ProductId = part.PartId,
                    ValidFrom = DateTime.UtcNow,
                    Data = new object()
                };

                var cardSchemeProductInstance = mapper.Map<ProductInstanceEntity>(cardSchemeChild);
                cardSchemeProductInstance.Product = productScheme.First()!;

                productInstanceRepository.Save(cardSchemeProductInstance);
            }
        }
        catch (Exception ex)
        {
            logger.LogError("Error when creating nested product instances. Deleting product instance with id {productInstanceId}", productInstance.Id);
            await DeleteAsync(new DeleteProductInstanceRequest { ProductInstanceId = productInstance.Id });

            if (ex is ServiceException)
                throw;

            logger.LogCritical(ex, "Unexpected error when adding a new product instance.");
            throw new ServiceException(HttpStatusCode.InternalServerError, Errors.UnexpectedError);
        }

        await productInstanceRepository.SaveChangesAsync();
        await productChangeSenderService.SendCreatedEvent(productInstance);

        return productInstance.Id;
    }

    private static void SetStoreName(ProductInstanceEntity productInstance, string storeName)
    {
        switch (productInstance.Data)
        {
            case TerminalData data:
                data.ShortName_EN = storeName;
                productInstance.Data = data;
                break;

            case GatewayData data:
                data.MerchantName = storeName;
                productInstance.Data = data;
                break;
        }
    }

    private async Task UpdateProductInstance(ProductInstanceEntity productInstance)
    {
        productInstanceRepository.Update(productInstance);
        await productInstanceRepository.SaveChangesAsync();

        productChangeSenderService.SendUpdatedEvent(productInstance);
        await PublishUpdatedEventIfParentIsGateway(productInstance.ParentId);
    }

    private async Task<List<ProductInstanceResponse>> CreateProductInstanceWithChildren(DefaultProductInstanceRequest request)
    {
        var product = await GetProduct(request);
        if (product == null)
        {
            logger.LogError("Product with id {productId} not found.", request.ProductId);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductNotFound);
        }

        var createdInstances = new List<ProductInstanceResponse>();

        switch (Enum.Parse<ProductTypes>(product.Type))
        {
            case ProductTypes.MEEZA:
                {
                    createdInstances.Add(await CreateMeezaDefaultProductInstance(request));
                    break;
                }
            case ProductTypes.GWAY:
                {
                    createdInstances.AddRange(await CreateGatewayDefaultProductInstance(request));
                    break;
                }
            case ProductTypes.BUNDLE:
                {
                    createdInstances.Add(await CreateBundleDefaultProductInstance(request, product));
                    break;
                }
            default:
                {
                    var productInstanceOptions = new ProductInstanceOptions
                    {
                        ProductId = request.ProductId,
                        ProductType = product.Type,
                        ProductCode = product.Code,
                        StoreId = request.StoreId,
                        AccountNumber = request.AccountNumber,
                        Mid = request.Mid,
                    };
                    var result = CreateDefaultProductInstance(productInstanceOptions);
                    createdInstances.Add(await CreateAsync(result));
                    break;
                }
        }

        logger.LogError("CreateDefaultProductInstancesAsync operation ended {@request} {@numberOfCreatedInstances}", request, createdInstances.Count);
        return createdInstances;
    }

    private async Task<Product> GetProduct(DefaultProductInstanceRequest request)
    {
        var result = await productService.FindAsync(new FindProductRequest
        {
            Id = request.ProductId,
            OnlyValid = false
        }, false);

        return result.FirstOrDefault()!;
    }

    private async Task<ProductInstanceResponse> CreateMeezaDefaultProductInstance(DefaultProductInstanceRequest request)
    {
        try
        {
            var createRequest = await GetDefaultMeezaCreateRequest(request.ProductId, request.CompanyId);

            return await CreateAsync(createRequest);
        }
        catch (Exception)
        {
            logger.LogError("Cannot create default meeza instances.");
            throw;
        }
    }

    private async Task<List<ProductInstanceResponse>> CreateGatewayDefaultProductInstance(DefaultProductInstanceRequest request)
    {
        var createdProductInstanceResponses = new List<ProductInstanceResponse>();
        var productParts = new List<ProductPart>();

        try
        {
            foreach (var createRequest in await GetDefaultGatewayCreateRequest(request.ProductId, request, productParts))
            {
                createdProductInstanceResponses.Add(await CreateAsync(createRequest));
            }

            return createdProductInstanceResponses;
        }
        catch (Exception)
        {
            logger.LogError("Cannot create default gateway instances.");
            foreach (var createdProductInstance in createdProductInstanceResponses)
            {
                await DeleteAsync(new DeleteProductInstanceRequest { ProductInstanceId = createdProductInstance.ProductInstanceId });
            }
            throw;
        }
    }

    private async Task<ProductInstanceResponse> CreateBundleDefaultProductInstance(DefaultProductInstanceRequest request, Product product)
    {
        try
        {
            var productInstanceOptions = new ProductInstanceOptions
            {
                ProductId = product.Id,
                ProductType = product.Type,
                ProductCode = product.Code,
                StoreId = request.StoreId,
                CompanyId = request.CompanyId,
                AccountNumber = request.AccountNumber,
                Mid = request.Mid
            };
            var createProductInstanceRequest = CreateDefaultProductInstance(productInstanceOptions);

            var productParts = product.Parts.Where(p => Enum.Parse<ProductTypes>(p.Part.Type) == ProductTypes.GWAY ||
                                                        Enum.Parse<ProductTypes>(p.Part.Type) == ProductTypes.MEEZA ||
                                                        Enum.Parse<ProductTypes>(p.Part.Type) == ProductTypes.TERMINAL ||
                                                        Enum.Parse<ProductTypes>(p.Part.Type) == ProductTypes.MINI_ECR ||
                                                        Enum.Parse<ProductTypes>(p.Part.Type) == ProductTypes.M_POS ||
                                                        Enum.Parse<ProductTypes>(p.Part.Type) == ProductTypes.ACCESSORIES ||
                                                       (Enum.Parse<ProductTypes>(p.Part.Type) == ProductTypes.SERVICES && p.Part.Code == Constants.ProductCodes.BillPayment));

            createProductInstanceRequest.Children = await CreateChildProductInstanceRequests(request, productParts);

            //To exclude discover and diners when onboarding merchant
            bool isFirstInstanceCreation = product.Code == ProductCodes.PayByLinkBundle || product.Code == ProductCodes.PaymentGatewayBundle;
            return await CreateAsync(createProductInstanceRequest, isFirstInstanceCreation);
        }
        catch (Exception)
        {
            logger.LogError("Cannot create default bundle instances.");
            throw;
        }
    }

    private async Task<List<CreateProductInstanceRequest>> CreateChildProductInstanceRequests(DefaultProductInstanceRequest request, IEnumerable<ProductPart> productParts)
    {
        var children = new List<CreateProductInstanceRequest>();

        foreach (var productPart in productParts)
        {
            var productInstanceOptions = new ProductInstanceOptions
            {
                ProductId = productPart.PartId,
                ProductType = productPart.Part.Type,
                ProductCode = productPart.Part.Code,
                StoreId = request.StoreId,
                AccountNumber = request.AccountNumber,
                Mid = request.Mid
            };
            switch (Enum.Parse<ProductTypes>(productPart.Part.Type))
            {
                case ProductTypes.GWAY:
                    children.AddRange(await GetDefaultGatewayCreateRequest(productPart.Part.Id, request, productPart.Product.Parts, null, true));
                    break;
                case ProductTypes.MEEZA:
                    await CheckAndCreateDefaultMeezaProductInstanceAsync(productPart.Part.Id, request.CompanyId);
                    break;
                case ProductTypes.TERMINAL:
                    children.Add(CreateDefaultProductInstance(productInstanceOptions));
                    break;
                case ProductTypes.MINI_ECR:
                    children.Add(CreateDefaultProductInstance(productInstanceOptions));
                    break;
                case ProductTypes.M_POS:
                    children.Add(CreateDefaultProductInstance(productInstanceOptions));
                    break;
                case ProductTypes.ACCESSORIES:
                    children.Add(CreateDefaultProductInstance(productInstanceOptions));
                    break;
                case ProductTypes.SERVICES:
                    children.Add(CreateDefaultProductInstance(productInstanceOptions));
                    break;
            }
        }
        return children;
    }

    private async Task CheckAndCreateDefaultMeezaProductInstanceAsync(Guid productId, Guid companyId)
    {
        var meezaInstance = await productInstanceRepository.FindAsync(new FindProductInstanceRequest
        {
            ProductId = productId,
            CompanyId = companyId
        });

        if (!meezaInstance.Any())
            await CreateMeezaDefaultProductInstance(new DefaultProductInstanceRequest
            {
                ProductId = productId,
                CompanyId = companyId,
            });
    }

    private static CreateProductInstanceRequest CreateDefaultProductInstance(ProductInstanceOptions options)
    {
        return new CreateProductInstanceRequest()
        {
            ProductId = options.ProductId,
            ValidFrom = DateTime.UtcNow,
            Data = CreateDataType(options.ProductType, options.ProductCode, options.AccountNumber),
            StoreId = options.StoreId,
            CompanyId = options.CompanyId,
            ParentId = options.ParentId,
            Mid = options.Mid
        };
    }

    private static object CreateDataType(string type, string code, string? accountNumber = null)
    {
        return Enum.Parse<ProductTypes>(type) switch
        {
            ProductTypes.TERMINAL => new TerminalData(accountNumber, type, code),
            ProductTypes.M_POS => new TerminalData(accountNumber, type, code),
            ProductTypes.MINI_ECR => new MiniEcrData() { DeviceId = "" },
            ProductTypes.GWAY => new GatewayData(),
            _ => new object()
        };
    }

    private async Task<CreateProductInstanceRequest> GetDefaultMeezaCreateRequest(Guid productId, Guid companyId, string? mid = null)
    {
        var createRequest = new CreateProductInstanceRequest
        {
            ProductId = productId,
            CompanyId = companyId,
            Mid = mid,
            Data = new MeezaData
            {
                MeezaMerchantId = await GenerateMeezaMerchantId(),
                RegistrationStatus = Constants.MeezaRegistrationStatuses.NotRegistered
            }
        };

        return createRequest;
    }

    private async Task<string> GenerateMeezaMerchantId()
    {
        var random = new Random();
        while (true)
        {
            var meezaMerchantId = meezaSettings.MerchantPrefix + random.Next(1000000).ToString("D6");
            var meezaMerchantIdExists = await productInstanceRepository.MeezaMerchantIdExists(meezaMerchantId);
            if (!meezaMerchantIdExists)
                return meezaMerchantId;
        }
    }

    private async Task<CreateProductInstanceRequest[]> GetDefaultGatewayCreateRequest(Guid productId, DefaultProductInstanceRequest request, IEnumerable<ProductPart> productParts, Guid? parentId = null, bool excludeDiscoverDiners = false)
    {
        var gatewayProductInstances = await productInstanceRepository.FindAsync(new FindProductInstanceRequest
        {
            ProductId = productId,
            StoreId = request.StoreId
        });

        if (gatewayProductInstances.Any())
            return new CreateProductInstanceRequest[0];

        var schemeParts = productParts.Where(x => x.Part.Type == Constants.ProductTypes.Scheme);

        var cardCodes = schemeParts.Select(x => x.Part.Code);

        var cardIds = schemeParts.Select(x => x.PartId);

        var createChildrenRequests = cardIds
            .Select(cardId => new CreateProductInstanceRequest { ProductId = cardId }).ToList();

        var counterparty = counterpartyProvider.GetCode();

        var createRequest = new CreateProductInstanceRequest
        {
            CompanyId = request.CompanyId,
            StoreId = request.StoreId,
            Mid = request.Mid,
            Data = new GatewayData
            {
                MerchantGatewayKey = Guid.NewGuid().ToString(),
                MerchantName = request.MerchantName,
                TenantCode = request.TenantCode ?? Constants.DefaultTenantCode,
                MerchantNameAr = request.MerchantNameAr,
                DefaultPaymentOperation = request.DefaultOperation,
                IsTest = false,
                UseMpgsApiV60 = true,
                MpgsMsoProvider = GetGatewayMpgsMsoProvider(counterparty),
                Currencies = GetGatewayDefaultCurrency(counterparty),
                MerchantCountry = GetGatewayMerchantCountry(counterparty),
                IsTokenizationEnabled = false,
                CardBrandProviders = GetGatewayCardBrandProviders(cardCodes),
                IsFederationToGsdkEnabled = true,
                IsSmartRoutingEnabled = false,
                IsActive = true,
                IsRefundEnabled = true,
                SubMerchantInformation = request.SubMerchantInformation
            },
            ProductId = productId,
            ParentId = parentId,
        };

        var createTestRequest = new CreateProductInstanceRequest
        {
            CompanyId = request.CompanyId,
            StoreId = request.StoreId,
            Data = new GatewayData
            {
                MerchantGatewayKey = Guid.NewGuid().ToString(),
                MerchantName = request.MerchantName,
                MerchantNameAr = request.MerchantNameAr,
                IsTest = true,
                DefaultPaymentOperation = request.DefaultOperation,
                UseMpgsApiV60 = true,
                MpgsMsoProvider = GetGatewayMpgsMsoProvider(counterparty),
                MpgsAccounts = new List<MpgsAccount>
                {
                    new()
                    {
                        MpgsMerchantId = request.MpgsMerchantId,
                        MpgsApiKey = request.MpgsApiKey,
                          CardBrands = cardCodes.Distinct()
                             .Where(code => !excludeDiscoverDiners || (code != ProductCodes.DiscoverGw && code != ProductCodes.DinersGw))
                             .ToList(),
                        Currencies=GetGatewayDefaultCurrency(counterparty),
                        CreatedDate = DateTimeOffset.Now
                    }
                },
                TenantCode = request.TenantCode ?? Constants.DefaultTenantCode,
                MpgsHistory = new List<MpgsAccountHistory>(),
                Currencies = GetGatewayDefaultCurrency(counterparty),
                MerchantCountry = GetGatewayMerchantCountry(counterparty),
                IsTokenizationEnabled = false,
                CardBrandProviders = GetGatewayCardBrandProviders(cardCodes),
                IsActive = true,
                IsRefundEnabled = true,
                IsSmartRoutingEnabled = false,
                SubMerchantInformation = request.SubMerchantInformation
            },
            ProductId = productId,
            ParentId = parentId,
            Children = createChildrenRequests,
            Mid = request.Mid
        };

        return new[] { createRequest, createTestRequest };
    }

    private static List<CardBrandProvider> GetGatewayCardBrandProviders(IEnumerable<string> cardBrands)
    {
        return cardBrands
            .Select(cardBrand => new CardBrandProvider
            {
                CardBrand = cardBrand,
                AcquiringProvider = Constants.AcquiringProviders.MPGS,
                ThreeDSecureProvider = Constants.ThreeDSecureProviders.MPGS
            }).ToList();
    }

    private async Task ValidateAsync(CreateProductInstanceRequest productInstanceRequest, bool isChild = false)
    {
        var validationResult = new ProductInstanceCreateValidator(isChild).Validate(productInstanceRequest);
        if (!validationResult.IsValid)
        {
            var errorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
            logger.LogError("Product instance create request validation failed: {@errors}", errorDescription);
            throw new ValidationException(validationResult);
        }

        if (productInstanceRequest.ParentId.HasValue && productInstanceRequest.ParentId != Guid.Empty)
            await CheckParentIdAsync(productInstanceRequest.ParentId);

        await CheckProduct(productInstanceRequest.ProductId);

        var productInstance = mapper.Map<ProductInstanceEntity>(productInstanceRequest);
        var product = (await productService.FindAsync(new FindProductRequest { Id = productInstanceRequest.ProductId, OnlyValid = false }, false))
            .FirstOrDefault();
        productInstance.Product = mapper.Map<ProductEntity>(product);

        productInstance.Data?.ValidateAndThrow();

        foreach (var child in productInstanceRequest.Children)
            await ValidateAsync(child, true);
    }

    private async Task CheckParentIdAsync(Guid? parentId)
    {
        var parentExists = await productInstanceRepository.AnyAsync(c => c.Id == parentId && !c.DeletedFlag);

        if (!parentExists)
        {
            logger.LogError($"Parent product instance with id {parentId} was not found or has been deleted.");
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ParentProductInstanceNotFound);
        }
    }

    private async Task CheckParentConfigurationIdAsync(Guid? parentConfigurationId)
    {
        var parentExists = await productInstanceRepository.AnyAsync(c => c.Id == parentConfigurationId && !c.DeletedFlag);

        if (!parentExists)
        {
            logger.LogError($"Parent configuration instance with id {parentConfigurationId} was not found or has been deleted.");
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ParentConfigurationNotFound);
        }
    }

    private async Task CheckProduct(Guid productId)
    {
        var product = (await productService.FindAsync(new FindProductRequest { Id = productId, OnlyValid = false }, false)).FirstOrDefault();
        if (product == null)
        {
            logger.LogError("Product with id {productId} not found.", productId);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductNotFound);
        }
    }

    private async Task CheckIfPaymentGatewayExistsOnStore(Guid? storeId, ProductInstanceEntity productInstance)
    {
        var codesToCheckFor = new[] {
            Constants.ProductCodes.PaymentGateway,
            Constants.ProductCodes.PayByLinkBundle,
            Constants.ProductCodes.PaymentGatewayBundle
        };

        if (storeId == null || !codesToCheckFor.Contains(productInstance.Product.Code))
        {
            return;
        }

        var productInstancesOnStore = await productInstanceRepository.GetProductInstancesForStore(storeId.Value);

        var instances = productInstancesOnStore.Where(p => p.Product.Code == productInstance.Product.Code).ToList();

        if (instances.Count > 0)
        {
            if (productInstance.Product.Code == Constants.ProductCodes.PayByLinkBundle)
            {
                logger.LogError("Store with id {storeId} already has a pay by link bundle defined.", storeId);
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.StoreAlreadyHasPayByLinkBundleBadRequest);
            }
            if (productInstance.Product.Code == Constants.ProductCodes.PaymentGatewayBundle)
            {
                logger.LogError("Store with id {storeId} already has a payment gateway bundle defined.", storeId);
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.StoreAlreadyHasPaymentGatewayBundleBadRequest);
            }
        }

        if (instances.Count >= 2 && productInstance.Product.Code == Constants.ProductCodes.PaymentGateway)
        {
            logger.LogError("Store with id {storeId} already has 2 payment gateways defined.", storeId);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.StoreAlreadyHasPaymentGatewaysBadRequest);
        }
    }

    private async Task SetPropertiesFromParentAsync(CreateProductInstanceRequest productInstanceRequest)
    {
        if (!productInstanceRequest.ParentId.HasValue || productInstanceRequest.ParentId == Guid.Empty)
            return;

        var parent = await productInstanceRepository.SingleOrDefaultAsync(c => c.Id == productInstanceRequest.ParentId && !c.DeletedFlag);

        productInstanceRequest.AgreementId = parent.AgreementId;
    }

    private async Task<ProductInstanceEntity> CreateProductInstanceAsync(CreateProductInstanceRequest productInstanceRequest, bool isFirstInstanceCreation = false)
    {
        var productInstance = mapper.Map<ProductInstanceEntity>(productInstanceRequest);

        var product = (await productService.FindAsync(new FindProductRequest { Id = productInstanceRequest.ProductId, OnlyValid = false }, false))
            .FirstOrDefault();
        productInstance.Product = mapper.Map<ProductEntity>(product);

        if (isFirstInstanceCreation && productInstance.Product != null)
            SetDeletedFlagValue(productInstance.Product, productInstance);

        SetDefaultInitiatedByValues(productInstance);
        SetDefaultMpgsMsoProvider(productInstance);
        SetDefaultValues(productInstance, counterpartyProvider.GetCode());

        productInstanceRepository.Save(productInstance);

        try
        {
            foreach (var child in productInstanceRequest.Children)
            {
                child.ParentId = productInstance.Id;
                child.AgreementId = productInstance.AgreementId;
                child.Mid = productInstance.Mid;
                await CreateProductInstanceAsync(child, isFirstInstanceCreation);
            }
        }
        catch (Exception ex)
        {
            logger.LogError($"Error when creating nested product instances. Deleting product instance with id {productInstance.Id}");
            await DeleteAsync(new DeleteProductInstanceRequest { ProductInstanceId = productInstance.Id });

            if (!(ex is ServiceException))
            {
                logger.LogCritical(ex, "Unexpected error when adding a new product instance.");
                throw new ServiceException(HttpStatusCode.InternalServerError, Errors.UnexpectedError);
            }

            throw;
        }

        await productChangeSenderService.SendCreatedEvent(productInstance);

        return productInstance;
    }

    private static void SetDefaultInitiatedByValues(ProductInstanceEntity productInstance)
    {
        if (IsGatewayProduct(productInstance)
            && productInstance.Data is GatewayData gatewayData
            && !gatewayData.AllowedInitiatedByValues.Any())
        {
            var allowedInitiatedByValues = new List<string> { Constants.InitiatedByInternet };

            if (gatewayData.IsTokenizationEnabled && (gatewayData.IsRecurringPaymentsEnabled || gatewayData.IsUnscheduledPaymentsEnable))
                allowedInitiatedByValues.Add(Constants.InitiatedByMerchant);

            gatewayData.AllowedInitiatedByValues = allowedInitiatedByValues;

            productInstance.Data = gatewayData;
        }
    }

    private static void SetDeletedFlagValue(ProductEntity product, ProductInstanceEntity productInstance)
    {
        if (product.Code != null && product.Type != null && new[] { ProductCodes.Diners, ProductCodes.DinersGw, ProductCodes.Discover, ProductCodes.DiscoverGw }
     .Contains(product.Code.ToUpper()) && (product.Type == Constants.ProductType.Scheme))
        {
            productInstance.DeletedFlag = true;
        }
    }

    private void SetDefaultMpgsMsoProvider(ProductInstanceEntity productInstance)
    {
        if (IsGatewayProduct(productInstance)
            && productInstance.Data is GatewayData gatewayData
            && gatewayData.MpgsMsoProvider == null)
        {
            gatewayData.MpgsMsoProvider = GetGatewayMpgsMsoProvider(counterpartyProvider.GetCode());
            productInstance.Data = gatewayData;
        }
    }

    private static bool IsGatewayProduct(ProductInstanceEntity productInstance) =>
        productInstance.Product is not null &&
        Enum.Parse<ProductTypes>(productInstance.Product.Type) == ProductTypes.GWAY;

    private static bool IsMeezaProduct(ProductInstanceEntity productInstance) =>
        productInstance.Product is not null &&
        Enum.Parse<ProductTypes>(productInstance.Product.Type) == ProductTypes.MEEZA;

    private Guid[] RemoveChildProductInstances(List<ProductInstanceWithParentResponse> productInstances)
    {
        var remainingProductInstances = new List<ProductInstanceResponse>();
        remainingProductInstances.AddRange(productInstances);

        foreach (var productInstance in productInstances)
            if (productInstance.ParentId != null && productInstances.Select(x => x.ProductInstanceId).Contains((Guid)productInstance.ParentId))
                remainingProductInstances.Remove(productInstance);

        return remainingProductInstances.Select(x => x.ProductInstanceId).ToArray();
    }

    private async Task UpdateAgreementFromParentAsync(ProductInstanceEntity productInstance)
    {
        var parent = await productInstanceRepository.SingleOrDefaultAsync(c => c.Id == productInstance.ParentId && !c.DeletedFlag);
        if (parent == null)
            return;

        productInstance.AgreementId = parent.AgreementId;
        productInstanceRepository.Update(productInstance);
    }

    private async Task UpdateCompanyFromParentAsync(ProductInstanceEntity productInstance)
    {
        var parent = await productInstanceRepository.SingleOrDefaultAsync(c => c.Id == productInstance.ParentConfigurationId && !c.DeletedFlag);
        if (parent == null)
            return;

        productInstance.CompanyId = parent.CompanyId;
        productInstanceRepository.Update(productInstance);
    }

    private async Task UpdateStoreFromParentAsync(ProductInstanceEntity productInstance)
    {
        var parent = await productInstanceRepository.SingleOrDefaultAsync(c => c.Id == productInstance.ParentConfigurationId && !c.DeletedFlag);
        if (parent == null)
            return;

        productInstance.StoreId = parent.StoreId;
        productInstanceRepository.Update(productInstance);
    }

    private async Task UpdateAgreementForChildrenAsync(ProductInstanceEntity productInstance)
    {
        foreach (var child in await productInstanceRepository.WhereAsync(c => c.ParentId == productInstance.Id && !c.DeletedFlag))
        {
            child.AgreementId = productInstance.AgreementId;
            productInstanceRepository.Update(child);

            productChangeSenderService.SendUpdatedEvent(child);

            await UpdateAgreementForChildrenAsync(child);
        }
    }

    private async Task UpdateCompanyForChildrenAsync(ProductInstanceEntity productInstance)
    {
        foreach (var child in await productInstanceRepository.WhereAsync(c => c.ParentConfigurationId == productInstance.Id && !c.DeletedFlag))
        {
            child.CompanyId = productInstance.CompanyId;
            productInstanceRepository.Update(child);

            productChangeSenderService.SendUpdatedEvent(child);

            await UpdateCompanyForChildrenAsync(child);
        }
    }

    private async Task UpdateStoreForChildrenAsync(ProductInstanceEntity productInstance)
    {
        foreach (var child in await productInstanceRepository.WhereAsync(c => c.ParentConfigurationId == productInstance.Id && !c.DeletedFlag))
        {
            child.StoreId = productInstance.StoreId;
            productInstanceRepository.Update(child);

            productChangeSenderService.SendUpdatedEvent(child);

            await UpdateStoreForChildrenAsync(child);
        }
    }

    private async Task DeleteParentConfigForChildrenAsync(ProductInstanceEntity productInstance)
    {
        foreach (var child in await productInstanceRepository.WhereAsync(c => c.ParentConfigurationId == productInstance.Id && !c.DeletedFlag))
        {
            child.ParentConfigurationId = null;
            productInstanceRepository.Update(child);

            productChangeSenderService.SendUpdatedEvent(child);

            await DeleteParentConfigForChildrenAsync(child);
        }
    }

    private async Task<List<Guid>> DeleteWithChildren(Guid id)
    {
        var deletedProductInstances = new List<Guid> { id };
        var productInstance = await productInstanceRepository.GetByIdAsync(id, true);

        foreach (var child in productInstance.Children)
            deletedProductInstances.AddRange(await DeleteWithChildren(child.Id));

        await productInstanceRepository.DeleteAsync(id);

        productChangeSenderService.SendDeletedEvent(id, productInstance.Product.Type);

        return deletedProductInstances;
    }

    private async Task SetCompanyAndStoreFromParentConfigurationAsync(CreateProductInstanceRequest productInstanceRequest)
    {
        if (!productInstanceRequest.ParentConfigurationId.HasValue)
            return;

        var parentConfiguration = await productInstanceRepository.SingleOrDefaultAsync(c => c.Id == productInstanceRequest.ParentConfigurationId && !c.DeletedFlag);

        if (parentConfiguration == null)
        {
            logger.LogError($"Product configuration with id '{productInstanceRequest.ParentConfigurationId}' specified as parent configuration was not found.");
            throw new ServiceException(Errors.ParentConfigurationNotFound);
        }
        productInstanceRequest.CompanyId = parentConfiguration.CompanyId;
        productInstanceRequest.StoreId = parentConfiguration.StoreId;
        productInstanceRequest.Mid = parentConfiguration.Mid;
    }

    private async Task PublishUpdatedEventIfParentIsGateway(Guid? parentId)
    {
        if (!parentId.HasValue || parentId == Guid.Empty)
            return;

        var parent = await productInstanceRepository.GetByIdWithProductAndChildrenAsync(parentId!.Value);

        if (parent != null && parent.Product.Type == ProductTypes.GWAY.ToString())
        {
            productChangeSenderService.SendUpdatedEvent(parent);
        }
    }

    private static CreateProductInstanceRequest CreateMerchantMigrationProductInstanceRequest(
        MerchantMigrationProductInstanceRequest request, Guid? parentId = null, string? mid = null)
    {
        return new CreateProductInstanceRequest
        {
            ProductId = request.ProductId,
            ValidFrom = DateTime.UtcNow,
            Data = SetMerchantMigrationTerminalData(request),
            StoreId = request.StoreId,
            CompanyId = request.CompanyId,
            ParentId = parentId,
            Mid = mid
        };
    }

    private static object SetMerchantMigrationTerminalData(MerchantMigrationProductInstanceRequest request)
    {
        return new TerminalData
        {
            FullTId = request.FullTid,
            TId = request.Tid,
            MIDMerchantReference = request.Mid,
            POSDataCode = request.Trsm,
            ProviderBank = request.AcquiringLedger,
            ShortName_EN = request.MerchantName,
            ShortName_AR = request.LegalNameAr,
            AccountNo = Constants.DefaultAccountNumber
        };
    }

    private static void SetOrderMigrationProductInstanceDefaultEposData(ProductInstanceEntity productInstance)
    {
        productInstance.EPosTicketCompleted = true;
        productInstance.EPosTicketId = "-1";
    }
    public async Task CleanExpiredMPGSHistory()
    {
        logger.LogInformation("Stored procedure started to be executed for following Date: {ExpirationDate}", string.Join(",", GetMpgsHistoryExpirationDate(DateTimeOffset.Now, mpgsSettings.CleanHistoryPeriod)));
        await productInstanceRepository.CleanExpiredMPGSHistory(GetMpgsHistoryExpirationDate(DateTimeOffset.Now, mpgsSettings.CleanHistoryPeriod));
    }

    public DateTimeOffset GetMpgsHistoryExpirationDate(DateTimeOffset expiryDate, TimeSpan period)
    {
        DateTimeOffset ExpirationDate = expiryDate.Add(period);
        return ExpirationDate;
    }
    public async Task UpdateProductInstancesMsoSubMerchantInfo(IList<SubMerchantMigrationInfoProductInstance> request)
    {
        try
        {
            logger.LogInformation("Recieved Count of {Count} CompanyIds to migrate sub merchant info", request.Count);
            await productInstanceRepository.SendCompanyIdsToStoredProcedure(request);
            logger.LogInformation("Updated sub merchant information sucessfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while migrating old sub merchant info");
            throw new ServiceException(HttpStatusCode.BadRequest);
        }
    }
}

