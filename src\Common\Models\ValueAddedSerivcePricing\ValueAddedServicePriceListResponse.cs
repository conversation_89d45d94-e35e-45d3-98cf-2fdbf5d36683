﻿using Common.Views;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.ValueAddedSerivcePricing;
public class ValueAddedServicePriceListResponse
{
    public ValueAddedServicePricingListView[]? valueAddedServicePricingListViews { get; set; }
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }
}
