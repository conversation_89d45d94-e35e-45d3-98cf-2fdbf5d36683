﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Migrations.Tables.Products;
[Migration(2024_08_26_1230)]
public class AlterBusinessTypeCode : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(
         AppDomain.CurrentDomain.BaseDirectory +
         Path.Combine("Scripts", "AlterTableBusinessType.sql"));
    }
}
