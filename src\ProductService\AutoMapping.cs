﻿using System;
using Common.Entities;
using Common.Models;
using Common.Models.CategoryRequests;
using Common.Models.ProductInstance;
using Common.Models.TerminalDataSets;
using Newtonsoft.Json;
using AutoMapper;
using Common.Entities.Gle;
using Common.Models.Gle;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using static Common.Data.Helpers.DataDefaultValuesHelper;
using Common.Models.ValueAddedServices;
using Common.Models.CommissionFees;
using Common.Models.MccManagement;
using Common.Models.NonTransactionalFees;
using Common.Models.businessType;
using Common.Models.UnitPrice;
using Common.Models.ValueAddedSerivcePricing;
using Common.Models.ProductCommissionPrice;
using Common.Models.NonTransactionalPrice;
using System.Linq;

namespace ProductService;

public class AutoMapping : Profile
{
    public AutoMapping()
    {
        CreateMap<ProductEntity, ProductShortResponse>()
            .ForMember(dest => dest.ProductId, opt => opt.MapFrom(src => src.Id));
        CreateMap<ProductEntity, ProductDetailsResponse>()
           .ForMember(dest => dest.ProductDisplayOrder, opt => opt.MapFrom(src => src.DisplayOrder));


        CreateMap<PriceEntity, PriceShortResponse>()
            .ForMember(dest => dest.PriceId, opt => opt.MapFrom(src => src.Id));

        CreateMap<PriceRequest, PriceEntity>();

        CreateMap<PriceEntity, PriceRequest>();

        CreateMap<PriceEntity, Price>()
            .ForMember(dest => dest.CreatedDateUtc, opt => opt.MapFrom(src => src.CreatedDate))
            .ForMember(dest => dest.UpdatedDateUtc, opt => opt.MapFrom(src => src.UpdatedDate));

        CreateMap<Price, PriceEntity>()
            .ForMember(dest => dest.CreatedDate, opt => opt.MapFrom(src => src.CreatedDateUtc))
            .ForMember(dest => dest.UpdatedDate, opt => opt.MapFrom(src => src.UpdatedDateUtc));


        CreateMap<ProductInstanceEntity, ProductInstanceResponse>()
            .ForMember(dest => dest.ProductInstanceId, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Data, opts =>
            {
                opts.MapFrom(src => SetDefaultValues(src.Data));
            });

        CreateMap<ProductInstanceEntity, ProductInstanceWithParentResponse>()
            .ForMember(dest => dest.ProductInstanceId, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Data, opts =>
            {
                opts.MapFrom(src => SetDefaultValues(src.Data));
            });

        CreateMap<CreateProductInstanceRequest, ProductInstanceEntity>()
            .ForMember(x => x.Children, opt => opt.Ignore())
            .ForMember(x => x.Metadata, opts =>
            {
                opts.MapFrom(source => JsonConvert.SerializeObject(SetDefaultValues(source.Data, OperationType.Add)));
            })
            .ForMember(x => x.Data, opts => opts.Ignore());

        CreateMap<DeleteProductInstanceRequest, FindProductInstanceRequest>()
            .ForMember(x => x.AgreementId, opts =>
            {
                opts.PreCondition(src => src.AgreementId != null && src.AgreementId != Guid.Empty);
                opts.MapFrom(src => src.AgreementId.HasValue ? new[] { (Guid)src.AgreementId } : new Guid[0]);
            })
            .ForMember(x => x.ProductInstanceId, opts =>
            {
                opts.PreCondition(src => src.ProductInstanceId != null);
                opts.MapFrom(src => src.ProductInstanceId.HasValue ? new[] { (Guid)src.ProductInstanceId } : new Guid[0]);
            })
            .AfterMap((src, dest) => dest.Skip = 0)
            .AfterMap((src, dest) => dest.Take = int.MaxValue);

        CreateMap<ProductInstanceEntity, UpdateProductInstanceRequest>()
            .ForMember(dest => dest.Data, opts =>
            {
                opts.MapFrom(src => SetDefaultValues(src.Data, OperationType.Replace));
            });
        CreateMap<ProductRequest, ProductEntity>()
       .ForMember(dest => dest.DisplayOrder, opt => opt.MapFrom(src => src.ProductDisplayOrder))
    .ForMember(dest => dest.ProductCategories, opt => opt.Ignore()) // Ignore ProductCategories during mapping
    .ReverseMap();

        CreateMap<UpdateProductInstanceRequest, ProductInstanceEntity>()
            .ForMember(x => x.Children, opt => opt.Ignore())
            .ForMember(x => x.Metadata, opts =>
            {
                opts.MapFrom(source => JsonConvert.SerializeObject(SetDefaultValues(source.Data, OperationType.Replace)));
            })
            .ForMember(x => x.Data, opts => opts.Ignore());
        CreateMap<ProductEntity, ProductResponse>()
     .ForMember(dest => dest.CategoryId, opt => opt.MapFrom(src => src.ProductCategories.FirstOrDefault() != null
         ? src.ProductCategories.FirstOrDefault()!.CategoryId
         : Guid.Empty))
     .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => src.ProductCategories.FirstOrDefault() != null
         ? src.ProductCategories.FirstOrDefault()!.Category.Name // Accessing Category.Name
         : null))
     .ForMember(dest => dest.ProductImages, opt => opt.MapFrom(src => src.Images.Select(image => new ProductImagesResponse
     {
         ImageId = image.ImageId,
         DisplayOrder = image.DisplayOrder,
         Language = image.Language
     }).ToList()))
     .ForMember(dest => dest.ProductDisplayOrder, opt => opt.MapFrom(src => src.DisplayOrder))
     .ReverseMap();


        CreateMap<CategoryRequest, CategoryEntity>();
        CreateMap<CategoryEntity, CategoryRequest>();

        CreateMap<FindProductInstanceRequest, CountProductInstanceRequest>();

        CreateMap<CategoryEntity, Category>()
            .ForMember(dest => dest.CreatedDateUtc, opt => opt.MapFrom(src => src.CreatedDate))
            .ForMember(dest => dest.UpdatedDateUtc, opt => opt.MapFrom(src => src.UpdatedDate));

        CreateMap<Category, CategoryEntity>()
            .ForMember(dest => dest.CreatedDate, opt => opt.MapFrom(src => src.CreatedDateUtc))
            .ForMember(dest => dest.UpdatedDate, opt => opt.MapFrom(src => src.UpdatedDateUtc));

        CreateMap<ProductCategoriesEntity, ProductCategories>();
        CreateMap<ProductCategories, ProductCategoriesEntity>();

        CreateMap<ProductPartEntity, ProductPart>();
        CreateMap<ProductPart, ProductPartEntity>();

        CreateMap<ProductEntity, Product>()
            .ForMember(dest => dest.CreatedDateUtc, opt => opt.MapFrom(src => src.CreatedDate))
            .ForMember(dest => dest.UpdatedDateUtc, opt => opt.MapFrom(src => src.UpdatedDate))
            .ForMember(dest => dest.KnowMoreURL, opt => opt.MapFrom(src => src.ProductLink));

        CreateMap<Product, ProductEntity>()
            .ForMember(dest => dest.CreatedDate, opt => opt.MapFrom(src => src.CreatedDateUtc))
            .ForMember(dest => dest.UpdatedDate, opt => opt.MapFrom(src => src.UpdatedDateUtc));

        CreateMap<TerminalDataSetEntity, TerminalDataSetSearchResponse>()
             .ForMember(dest => dest.VendorName, opt => opt.MapFrom(src => src.Vendor != null ? src.Vendor.Name : ""))
             .ForMember(dest => dest.VendorId, opt => opt.MapFrom(src => src.Vendor != null ? src.Vendor.Id : Guid.Empty));

        CreateMap<TerminalDataSetSearchResponse, TerminalDataSetEntity>();

        CreateMap<TerminalDataSetEntity, TerminalDataSet>().ReverseMap();

        CreateMap<GleMerchantEntity, GleMerchant>()
            .ForMember(dest => dest.GleStores, opt
                => opt.MapFrom(src => src.GleStoreEntities));
        CreateMap<GleMerchant, GleMerchantEntity>()
            .ForMember(dest => dest.GleStoreEntities, opt
                => opt.MapFrom(src => src.GleStores));
        CreateMap<GleMerchantEntity, GleMerchantRequest>().ReverseMap();
        CreateMap<GleMerchantEntity, GleMerchantDto>().ReverseMap();
        CreateMap<GleMerchantEntity, GleBase>().ReverseMap();
        CreateMap<Operation<UpdateGleMerchantRequest>, Operation<GleMerchantEntity>>().ReverseMap();
        CreateMap<JsonPatchDocument<UpdateGleMerchantRequest>, JsonPatchDocument<GleMerchantEntity>>().ReverseMap();

        CreateMap<GleStoreEntity, GleStore>().ForMember(dest => dest.GleTerminals, opt
            => opt.MapFrom(src => src.GleTerminalEntities));
        CreateMap<GleStore, GleStoreEntity>().ForMember(dest => dest.GleTerminalEntities, opt
            => opt.MapFrom(src => src.GleTerminals));
        CreateMap<GleStoreEntity, GleStoreRequest>().ReverseMap();
        CreateMap<GleStoreEntity, GleStoreDto>().ReverseMap();
        CreateMap<GleStoreEntity, GleBase>().ReverseMap();
        CreateMap<Operation<UpdateGleStoreRequest>, Operation<GleStoreEntity>>().ReverseMap();
        CreateMap<JsonPatchDocument<UpdateGleStoreRequest>, JsonPatchDocument<GleStoreEntity>>().ReverseMap();

        CreateMap<GleTerminalEntity, GleTerminal>().ReverseMap();
        CreateMap<GleTerminalEntity, GleTerminalRequest>().ReverseMap();
        CreateMap<GleTerminalEntity, GleBase>().ReverseMap();
        CreateMap<GleTerminalEntity, UpdateGleTerminalRequest>().ReverseMap();
        CreateMap<Operation<UpdateGleTerminalRequest>, Operation<GleTerminalEntity>>().ReverseMap();
        CreateMap<JsonPatchDocument<UpdateGleTerminalRequest>, JsonPatchDocument<GleTerminalEntity>>().ReverseMap();

        CreateMap<GleUpdateHistoryRequest, GleUpdateHistoryEntity>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => Guid.NewGuid()));

        CreateMap<GleUpdateHistoryEntity, GleUpdateHistory>();

        CreateMap<ValueAddedServiceRequest, ValueAddedService>().ReverseMap();
        CreateMap<ValueAddedServiceResponse, ValueAddedService>().ReverseMap();
        CreateMap<ValueAddedServiceDetailsResponse, ValueAddedService>().ReverseMap();
        CreateMap<ImageData, ProductImage>().ReverseMap();
        CreateMap<ValueAddedService, GetValueAddedServicesListResponse>();
        CreateMap<CommissionFeesEntity, CommissionFeesListResponse>();
        CreateMap<CommissionFeesRequest, CommissionFeesEntity>().ReverseMap();
        CreateMap<CommissionFeesResponse, CommissionFeesEntity>().ReverseMap();
        CreateMap<CommissionFeesEntity, CommissionFeeDetailsResponse>().ReverseMap();
        CreateMap<Mcc, MccRequest>().ReverseMap();
        CreateMap<NonTransactionalFeesEntity, NonTransactionalFeesListResponse>();
        CreateMap<NonTransactionalFeesRequest, NonTransactionalFeesEntity>().ReverseMap();
        CreateMap<NonTransactionalFeesResponse, NonTransactionalFeesEntity>().ReverseMap();
        CreateMap<NonTransactionalFeesEntity, NonTransactionalFeesDetailsResponse>().ReverseMap();
        CreateMap<BusinessTypeEntity, BusinessTypesListResponse>();
        CreateMap<BusinessTypeEntity, BusinessTypeResponse>();
        CreateMap<BusinessTypeRequest, BusinessTypeEntity>().ReverseMap();
        CreateMap<BusinessTypeUpdateRequest, BusinessTypeEntity>().ReverseMap();

        CreateMap<UnitPriceEntity, UnitPriceDetails>()
          .ReverseMap();

        CreateMap<UnitPriceEntity, UnitPricesDetails>()
       .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product != null ? src.Product.Name : null))
       .ForMember(dest => dest.MccName, opt => opt.MapFrom(src => src.Mcc != null ? src.Mcc.Name : null))
       .ForMember(dest => dest.BusinessTypeName, opt => opt.MapFrom(src => src.BusinessType != null ? src.BusinessType.Name : null))
       .ReverseMap();

        CreateMap<PendingUnitPriceEntity, UnitPricesDetails>()
           .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product != null ? src.Product.Name : null))
           .ForMember(dest => dest.MccName, opt => opt.MapFrom(src => src.Mcc != null ? src.Mcc.Name : null))
           .ForMember(dest => dest.BusinessTypeName, opt => opt.MapFrom(src => src.BusinessType != null ? src.BusinessType.Name : null))
           .ReverseMap();

        CreateMap<PendingComissionPriceEntity, ProductCommissionPriceDetailsResponse>()
           .ReverseMap();

        CreateMap<UnitPriceCreateRequest, UnitPriceEntity>()
            .ForMember(dest => dest.ProductID, opt => opt.Ignore())  // Set in the loop manually
            .ForMember(dest => dest.MCCID, opt => opt.Ignore())  // Set in the loop manually
            .ForMember(dest => dest.BusinessTypeID, opt => opt.Ignore()).ReverseMap();  // If needed; // Set in the loop manually

        CreateMap<UnitPriceCreateRequest, PendingUnitPriceEntity>()
           .ForMember(dest => dest.ProductID, opt => opt.Ignore())  // Set in the loop manually
           .ForMember(dest => dest.MCCID, opt => opt.Ignore())  // Set in the loop manually
           .ForMember(dest => dest.BusinessTypeID, opt => opt.Ignore())
           .ReverseMap();


        CreateMap<ProductCommissionPriceCreateRequest, PendingComissionPriceEntity>()
           .ForMember(dest => dest.ProductId, opt => opt.Ignore())  // Set in the loop manually
           .ForMember(dest => dest.MccId, opt => opt.Ignore())  // Set in the loop manually
           .ForMember(dest => dest.BusinessTypeId, opt => opt.Ignore())
           .ForMember(dest => dest.CommissionFeesId, opt => opt.Ignore())
           .ReverseMap();

        CreateMap<UnitPriceEntity, UnitPriceResponse>()
         .ForMember(dest => dest.CreatedUnitPrices, opt => opt.Ignore()) // This will be manually mapped
         .ForMember(dest => dest.NewExistingUnitPrices, opt => opt.Ignore())// This will be manually mapped
         .ForMember(dest => dest.OldExistingUnitPrices, opt => opt.Ignore()); // This will be manually mapped

        CreateMap<ValueAddedServicePricingEntity, ValueAddedServicesPricingDetails>()
          .ForMember(dest => dest.SubscriptionFee, opt => opt.MapFrom(src => src.SubscriptionFee))
          .ReverseMap();

        CreateMap<ValueAddedServicesPricingCreateRequest, ValueAddedServicePricingEntity>()
          .ForMember(dest => dest.ProductID, opt => opt.Ignore())  // Set in the loop manually
          .ForMember(dest => dest.MCCID, opt => opt.Ignore())  // Set in the loop manually
          .ForMember(dest => dest.VASID, opt => opt.Ignore())  // Set in the loop manually
          .ForMember(dest => dest.BusinessTypeID, opt => opt.Ignore()).ReverseMap();  // If needed; // Set in the loop manually

        CreateMap<ValueAddedServicePricingEntity, ValueAddedServicePriceResponse>()
         .ForMember(dest => dest.CreatedVASPrices, opt => opt.Ignore()) // This will be manually mapped
         .ForMember(dest => dest.OldExistingVASPrices, opt => opt.Ignore()) // This will be manually mapped
         .ForMember(dest => dest.NewExistingVASPrices, opt => opt.Ignore()); // This will be manually mapped

        CreateMap<UnitPriceEntity, UnitPriceLogsEntity>()
          .ForMember(dest => dest.DeletedBy, opt => opt.Ignore()) // Ignore because it's set in the service
          .ForMember(dest => dest.DeletedDate, opt => opt.Ignore()); // Ignore because it's set in the service

        CreateMap<ProductCommissionPriceCreateRequest, ProductCommissionPriceEntity>()
           .ForMember(dest => dest.ProductID, opt => opt.Ignore())
           .ForMember(dest => dest.MCCID, opt => opt.Ignore())
           .ForMember(dest => dest.BusinessTypeID, opt => opt.Ignore()).ReverseMap();

        CreateMap<ProductCommissionPriceEntity, ProductCommissionPriceResponse>()
         .ForMember(dest => dest.NewCommissionPriceList, opt => opt.Ignore())
         .ForMember(dest => dest.NewExistedCommissionPriceList, opt => opt.Ignore())
         .ForMember(dest => dest.OldExistedCommissionPriceList, opt => opt.Ignore());

        CreateMap<ProductCommissionPriceEntity, ProductCommissionPriceDetailsResponse>();
        CreateMap<NonTransactionalPriceEntity, NonTransactionalPriceDetailsResponse>().ReverseMap();

        CreateMap<ValueAddedServicePricingEntity, ValueAddedServicePricingLogEntity>()
        .ForMember(dest => dest.DeletedBy, opt => opt.Ignore()) // Ignore because it's set in the service
        .ForMember(dest => dest.DeletedDate, opt => opt.Ignore()); // Ignore because it's set in the service

        CreateMap<ProductCommissionPriceEntity, ProductCommissionPriceLogEntity>()
       .ForMember(dest => dest.DeletedBy, opt => opt.Ignore()) // Ignore because it's set in the service
       .ForMember(dest => dest.DeletedDate, opt => opt.Ignore()); // Ignore because it's set in the service
        CreateMap<NonTransactionalFeePriceCreateRequest, NonTransactionalPriceEntity>()
         .ForMember(dest => dest.ProductId, opt => opt.Ignore())
         .ForMember(dest => dest.NonTransFeeId, opt => opt.Ignore())
         .ForMember(dest => dest.MccId, opt => opt.Ignore())
         .ForMember(dest => dest.BusinessTypeId, opt => opt.Ignore()).ReverseMap();

        CreateMap<NonTransactionalPriceEntity, NonTransactionalFeePriceResponse>()
      .ForMember(dest => dest.NewNonTransactionalPriceList, opt => opt.Ignore())
      .ForMember(dest => dest.NewExistedNonTransactionalPriceList, opt => opt.Ignore())
      .ForMember(dest => dest.OldExistedNonTransactionalPriceList, opt => opt.Ignore());

        CreateMap<NonTransactionalPriceEntity, NonTransactionalPriceLogEntity>()
      .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
      .ForMember(dest => dest.DeletedDate, opt => opt.Ignore());

        CreateMap<PendingUnitPriceEntity, UnitPriceEntity>()
            .ReverseMap();
        CreateMap<PendingComissionPriceEntity, ProductCommissionPriceEntity>()
          .ForMember(dest => dest.CommissionFeeID,
                     opt => opt.MapFrom(src => src.CommissionFeesId)).ReverseMap();


        CreateMap<PendingComissionPriceEntity, ProductCommissionPriceEntity>()
         .ForMember(dest => dest.Id, opt => opt.Ignore());
    }
}