using Common.Entities;
using DataAccess.Repositories;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Moq;
using System;
using System.Threading.Tasks;
using Geidea.Utils.Counterparty.Providers;
using Xunit;

namespace DataAccess.Test;

public class CategoryRepositoryTests
{
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly CategoryRepository categoryRepository;

    public CategoryRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<DataContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        var context = new DataContext(options, new CounterpartyProvider());
        categoryRepository = new CategoryRepository(context, httpContext.Object);
    }

    [Fact]
    public async Task HardDelete()
    {
        var category = new CategoryEntity
        {
            Code = "CODE",
            CreatedBy = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.UtcNow
        };
        categoryRepository.Save(category);
        await categoryRepository.SaveChangesAsync();

        await categoryRepository.HardDeleteCategory(category);

        var retrievedCategory = await categoryRepository.FirstOrDefaultAsync(c => c.Id == category.Id);
        retrievedCategory.Should().BeNull();
    }

    [Fact]
    public async Task Exists()
    {
        var category = new CategoryEntity
        {
            Code = "CODE",
            CreatedBy = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.UtcNow
        };
        categoryRepository.Save(category);
        await categoryRepository.SaveChangesAsync();

        var exists = await categoryRepository.ExistsAsync(category.Id);
        exists.Should().BeTrue();
    }

    [Fact]
    public async Task Exists_NonExisting()
    {
        var exists = await categoryRepository.ExistsAsync(Guid.NewGuid());
        exists.Should().BeFalse();
    }


    [Fact]
    public async Task Exists_SoftDeleted()
    {
        var category = new CategoryEntity
        {
            Code = "CODE",
            CreatedBy = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.UtcNow,
            DeletedFlag = true
        };
        categoryRepository.Save(category);
        await categoryRepository.SaveChangesAsync();

        var exists = await categoryRepository.ExistsAsync(Guid.NewGuid());
        exists.Should().BeFalse();
    }
    [Fact]
    public async Task GetCategoriesList_ShouldReturn_CategoriesList()
    {
        var CategoriesList = await categoryRepository.GetCategoriesList();
        CategoriesList.Should().NotBeNull();
    }
}
