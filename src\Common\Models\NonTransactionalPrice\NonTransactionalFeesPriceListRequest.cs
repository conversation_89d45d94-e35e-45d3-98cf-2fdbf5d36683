﻿using Common.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using Common.Enums.UnitPrice;
using Common.Enums.ProductCommisssionPrices;

namespace Common.Models.NonTransactionalPrice;
public class NonTransactionalFeesPriceListRequest
{
    public Dictionary<NonTransactionalFeesPriceSearchKey, string> SearchTerms { get; set; } = new Dictionary<NonTransactionalFeesPriceSearchKey, string>();
    public List<Guid>? ProductsFilter { get; set; }
    public List<Guid>? MccTypeFilter { get; set; }
    public List<Guid>? BusinessTypeFilter { get; set; }
    public List<Guid>? NonTransactionalFeeFilter { get; set; }
    public List<BillingType>? BillingTypeFilter { get; set; }
    public List<Status>? FilterByFeeStatus { get; set; }
    public List<PriceBillingFrequency>? BillingFrequencyFilter { get; set; }
    [DefaultValue("desc")]
    public string OrderType { get; set; } = SortType.desc.ToString();
    [DefaultValue("CreatedDate")]
    public string OrderFieldName { get; set; } = "CreatedDate";

    [DefaultValue(1)]
    public int Page { get; set; } = 1;
    [DefaultValue(10)]
    public int Size { get; set; } = 10;
}
public enum NonTransactionalFeesPriceSearchKey
{
    All,
    ProductName,
    ProductCode,
    MccName,
    MccCode,
    FeeName,
    FeeCode
}