﻿UPDATE Products SET Availability = 'Obsolete' WHERE Code IN ('GO_LITE_V2', 'GO_AIR_V2', 'GO_SMART_V2', 'GO_A920_V2', 'GO_MPOS_V2', 'PAYMENT_GATEWAY_BUNDLE', 'WEBSITE_BUILDER_BUNDLE', 
 'BUSINESS_RETAIL_V2', 'ENTERPRISE_RETAIL_V2', 'PRO_SMART_V2', 'BUSINESS_RESTAURANT_V2', 'ENTERPRISE_RESTAURANT')
 
 DECLARE @Ids TABLE(ID UNIQUEIDENTIFIER)
 DECLARE @Id UNIQUEIDENTIFIER
 DECLARE @MADA_POS_V2 UNIQUEIDENTIFIER
 DECLARE @VISA_POS UNIQUEIDENTIFIER
 DECLARE @MC_POS UNIQUEIDENTIFIER
 DECLARE @AMEX_POS UNIQUEIDENTIFIER
 DECLARE @MADA_ONLINE UNIQUEIDENTIFIER
 DECLARE @VISA_ONLINE UNIQUEIDENTIFIER
 DECLARE @MC_ONLINE UNIQUEIDENTIFIER
 DECLARE @AMEX_ONLINE UNIQUEIDENTIFIER
 DECLARE @GoLiteId UNIQUEIDENTIFIER
 DECLARE @GoFamilyCategoryId UNIQUEIDENTIFIER
 DECLARE @GeideaGoAppId UNIQUEIDENTIFIER
 DECLARE @D135Reader UNIQUEIDENTIFIER
 DECLARE @GoAirId UNIQUEIDENTIFIER
 DECLARE @SOFT_POS UNIQUEIDENTIFIER
 DECLARE @GoSmartId UNIQUEIDENTIFIER
 DECLARE @SMARTPOS_A920 UNIQUEIDENTIFIER
 DECLARE @GoA920Id UNIQUEIDENTIFIER
 DECLARE @GoMposId UNIQUEIDENTIFIER
 DECLARE @MOBILE_POS_SP530 UNIQUEIDENTIFIER
 DECLARE @OnlineFamilyCategoryId UNIQUEIDENTIFIER
 DECLARE @PaymentGatewayBundleId UNIQUEIDENTIFIER
 DECLARE @PAYMENT_GATEWAY UNIQUEIDENTIFIER
 DECLARE @WebsiteBuilderBundleId UNIQUEIDENTIFIER
 DECLARE @E_INVOICING_GW UNIQUEIDENTIFIER
 DECLARE @WEBSITE_BUILDER UNIQUEIDENTIFIER
 DECLARE @RetailCategoryId UNIQUEIDENTIFIER
 DECLARE @RestaurantCategoryId UNIQUEIDENTIFIER
 DECLARE @BusinessRetailId UNIQUEIDENTIFIER
 DECLARE @EnterpriseRetailId UNIQUEIDENTIFIER
 DECLARE @BusinessRestaurantId UNIQUEIDENTIFIER
 DECLARE @EnterpriseRestaurantId UNIQUEIDENTIFIER
 DECLARE @ProSmartId UNIQUEIDENTIFIER
 DECLARE @POS_ROCKET UNIQUEIDENTIFIER
 DECLARE @TILL_POINT UNIQUEIDENTIFIER
 DECLARE @LINGA_POS UNIQUEIDENTIFIER
 DECLARE @MC UNIQUEIDENTIFIER
 DECLARE @MADA_V2 UNIQUEIDENTIFIER
 DECLARE @VISA UNIQUEIDENTIFIER
 DECLARE @AMEX UNIQUEIDENTIFIER

 SELECT TOP 1 @GoFamilyCategoryId = ID FROM Category where  Code = 'GO_FAMILY'
 SELECT TOP 1 @GeideaGoAppId = ID FROM Products where  Code = 'GEIDEA_GO_APP'
 SELECT TOP 1 @D135Reader = ID FROM Products where  Code = 'D135_READER'
 SELECT TOP 1 @VISA_POS = ID FROM Products where  Code = 'VISA_POS'
 SELECT TOP 1 @MC_POS = ID FROM Products where  Code = 'MC_POS'
 SELECT TOP 1 @AMEX_POS = ID FROM Products where  Code = 'AMEX_POS'
 SELECT TOP 1 @SOFT_POS = ID FROM Products where  Code = 'SOFT_POS'
 SELECT TOP 1 @SMARTPOS_A920 = ID FROM Products where  Code = 'SMARTPOS_A920'
 SELECT TOP 1 @MOBILE_POS_SP530 = ID FROM Products where  Code = 'MOBILE_POS_SP530'
 SELECT TOP 1 @OnlineFamilyCategoryId = ID FROM Category where Code = 'ONLINE_FAMILY'
 SELECT TOP 1 @PAYMENT_GATEWAY = ID FROM Products where  Code = 'PAYMENT_GATEWAY'
 SELECT TOP 1 @E_INVOICING_GW = ID FROM Products where  Code = 'E_INVOICING_GW'
 SELECT TOP 1 @WEBSITE_BUILDER = ID FROM Products where  Code = 'WEBSITE_BUILDER'
 SELECT TOP 1 @RetailCategoryId = ID FROM Category where Code = 'RETAIL'
 SELECT TOP 1 @RestaurantCategoryId = ID FROM Category where Code = 'RESTAURANT'
 SELECT TOP 1 @POS_ROCKET = ID FROM Products where  Code = 'POS_ROCKET'
 SELECT TOP 1 @TILL_POINT = ID FROM Products where  Code = 'TILL_POINT'
 SELECT TOP 1 @LINGA_POS = ID FROM Products where  Code = 'LINGA_POS'
 SELECT TOP 1 @MC = ID FROM Products where  Code = 'MC'
 SELECT TOP 1 @VISA = ID FROM Products where  Code = 'VISA'
 SELECT TOP 1 @AMEX = ID FROM Products where  Code = 'AMEX'

 -- new card scheme
 	--mada_pos_v2
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
	 VALUES('Normal', 'MADA_POS_V2', 'SCHEME', 'n/a', GETUTCDATE(), GETUTCDATE())

	 SELECT TOP 1 @MADA_POS_V2 = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, Threshold)
	 VALUES('PURCHASE_CP', 1, @MADA_POS_V2, 70, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 10000)

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, Threshold, MaxPrice)
	 VALUES('PURCHASE_CP', 1, @MADA_POS_V2, 80, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 10000, 16000)

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('REFUND_CP', 1, @MADA_POS_V2, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 --mada_v2
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
	 VALUES('Normal', 'MADA_V2', 'SCHEME', 'n/a', GETUTCDATE(), GETUTCDATE())

	 SELECT TOP 1 @MADA_V2 = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, Threshold)
	 VALUES('PURCHASE_CP', 1, @MADA_V2, 70, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 10000)

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, Threshold, MaxPrice)
	 VALUES('PURCHASE_CP', 1, @MADA_V2, 80, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 10000, 16000)

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('REFUND_CP', 1, @MADA_V2, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('PURCHASE_CNP', 1, @MADA_V2, 175, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('REFUND_CNP', 1, @MADA_V2, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

 	--mada_online
     INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
	 VALUES('Normal', 'MADA_ONLINE', 'SCHEME', 'n/a', GETUTCDATE(), GETUTCDATE())
	 
	 SELECT TOP 1 @MADA_ONLINE = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('PURCHASE_CNP', 1, @MADA_ONLINE, 175, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('REFUND_CNP', 1, @MADA_ONLINE, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')


	 --visa_online
     INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
	 VALUES('Normal', 'VISA_ONLINE', 'SCHEME', 'n/a', GETUTCDATE(), GETUTCDATE())
	 
	 SELECT TOP 1 @VISA_ONLINE = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('PURCHASE_CNP', 1, @VISA_ONLINE, 290, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('REFUND_CNP', 1, @VISA_ONLINE, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 --mc_online
     INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
	 VALUES('Normal', 'MC_ONLINE', 'SCHEME', 'n/a', GETUTCDATE(), GETUTCDATE())
	 
	 SELECT TOP 1 @MC_ONLINE = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('PURCHASE_CNP', 1, @MC_ONLINE, 290, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('REFUND_CNP', 1, @MC_ONLINE, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 --amex_online
     INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom) OUTPUT inserted.Id INTO @Ids
	 VALUES('Normal', 'AMEX_ONLINE', 'SCHEME', 'n/a', GETUTCDATE(), GETUTCDATE())
	 
	 SELECT TOP 1 @AMEX_ONLINE = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PercentagePrice, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('PURCHASE_CNP', 1, @MC_ONLINE, 290, 100, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

	 INSERT INTO PRICES(ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('REFUND_CNP', 1, @MC_ONLINE, 1000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')

-- Go Family
	--Go Lite
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom, DisplayOrder) OUTPUT inserted.Id INTO @Ids
	 VALUES('Bundle_Preorder', 'GO_LITE_V3', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE(), 2)
 
	 SELECT TOP 1 @GoLiteId = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@GoLiteId, @GoFamilyCategoryId)

	 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
	 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @GoLiteId, 3900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @D135Reader)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @GeideaGoAppId)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @VISA_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @MC_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @AMEX_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoLiteId, @MADA_POS_V2)

	--Go Air
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom, DisplayOrder) OUTPUT inserted.Id INTO @Ids
	 VALUES('Bundle_Preorder', 'GO_AIR_V3', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE(), 1)
 
	 SELECT TOP 1 @GoAirId = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@GoAirId, @GoFamilyCategoryId)

	 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
	 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @GoAirId, 2900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @GeideaGoAppId)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @SOFT_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @VISA_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @MC_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @AMEX_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoAirId, @MADA_POS_V2)	
	 
	 --Go SMART
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom, DisplayOrder) OUTPUT inserted.Id INTO @Ids
	 VALUES('Bundle', 'GO_SMART_V3', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE(), 3)
 
	 SELECT TOP 1 @GoSmartId = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@GoSmartId, @GoFamilyCategoryId)

	 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
	 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @GoSmartId, 13900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @GeideaGoAppId)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @SMARTPOS_A920)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @VISA_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @MC_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @MADA_POS_V2)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoSmartId, @AMEX_POS)

	 --Go GO_A920
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom, DisplayOrder) OUTPUT inserted.Id INTO @Ids
	 VALUES('Bundle', 'GO_A920_V3', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE(), 4)
 
	 SELECT TOP 1 @GoA920Id = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@GoA920Id, @GoFamilyCategoryId)

	 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('ONE_OFF', 'SETUP_CHARGE', 0, @GoA920Id, 240000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')
 
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoA920Id, @GeideaGoAppId)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoA920Id, @SMARTPOS_A920)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoA920Id, @VISA_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoA920Id, @MC_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoA920Id, @MADA_POS_V2)
	 
	 --Go GO_MPOS
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom, DisplayOrder) OUTPUT inserted.Id INTO @Ids
	 VALUES('Bundle', 'GO_MPOS_V3', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE(), 5)
 
	 SELECT TOP 1 @GoMposId = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@GoMposId, @GoFamilyCategoryId)

	 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency)
	 VALUES('ONE_OFF', 'SETUP_CHARGE', 0, @GoMposId, 75000, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR')
 
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoMposId, @GeideaGoAppId)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoMposId, @MOBILE_POS_SP530)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoMposId, @VISA_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoMposId, @MC_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@GoMposId, @MADA_POS_V2)

-- Online Family
	--Payment Gateway
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom, DisplayOrder) OUTPUT inserted.Id INTO @Ids
	 VALUES('Bundle', 'PAYMENT_GATEWAY_BUNDLE_V2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE(), 1)
 
	 SELECT TOP 1 @PaymentGatewayBundleId = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@PaymentGatewayBundleId, @OnlineFamilyCategoryId)

	 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
	 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @PaymentGatewayBundleId, 9900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @MC_ONLINE)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @MADA_ONLINE)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @VISA_ONLINE)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @AMEX_ONLINE)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@PaymentGatewayBundleId, @PAYMENT_GATEWAY)
	 
	--Website Builder
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom, DisplayOrder) OUTPUT inserted.Id INTO @Ids
	 VALUES('Bundle', 'WEBSITE_BUILDER_BUNDLE_V2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE(), 2)
 
	 SELECT TOP 1 @WebsiteBuilderBundleId = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@WebsiteBuilderBundleId, @OnlineFamilyCategoryId)

	 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
	 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @WebsiteBuilderBundleId, 9900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @MC_ONLINE)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @MADA_ONLINE)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @VISA_ONLINE)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @AMEX_ONLINE)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @PAYMENT_GATEWAY)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @E_INVOICING_GW)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@WebsiteBuilderBundleId, @WEBSITE_BUILDER)

-- Retail Family
	--Business
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom, DisplayOrder) OUTPUT inserted.Id INTO @Ids
	 VALUES('Bundle_HelpRequired', 'BUSINESS_RETAIL_V3', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE(), 1)
 
	 SELECT TOP 1 @BusinessRetailId = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@BusinessRetailId, @RetailCategoryId)

	 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
	 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @BusinessRetailId, 32900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @AMEX_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @MADA_POS_V2)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @MC_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @VISA_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @POS_ROCKET)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRetailId, @SMARTPOS_A920)

	 --Enterprise
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom, DisplayOrder) OUTPUT inserted.Id INTO @Ids
	 VALUES('Bundle_HelpRequired', 'ENTERPRISE_RETAIL_V3', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE(), 2)
 
	 SELECT TOP 1 @EnterpriseRetailId = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@EnterpriseRetailId, @RetailCategoryId)

	 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
	 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @EnterpriseRetailId, 49900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @AMEX_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @MADA_POS_V2)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @MC_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @VISA_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @TILL_POINT)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRetailId, @SMARTPOS_A920)

	 --Smart
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom, DisplayOrder) OUTPUT inserted.Id INTO @Ids
	 VALUES('Bundle_HelpRequired', 'PRO_SMART_V3', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE(), 3)
 
	 SELECT TOP 1 @ProSmartId = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@ProSmartId, @RetailCategoryId)

	 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
	 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @ProSmartId, 22900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @AMEX_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @MADA_POS_V2)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @MC_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @VISA_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @GeideaGoAppId)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@ProSmartId, @SMARTPOS_A920)

-- Restaurant Family
	--Business
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom, DisplayOrder) OUTPUT inserted.Id INTO @Ids
	 VALUES('Bundle_HelpRequired', 'BUSINESS_RESTAURANT_V3', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE(), 1)
 
	 SELECT TOP 1 @BusinessRestaurantId = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@BusinessRestaurantId, @RestaurantCategoryId)

	 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
	 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @BusinessRestaurantId, 32900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @AMEX_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @MADA_POS_V2)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @MC_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @VISA_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @POS_ROCKET)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@BusinessRestaurantId, @SMARTPOS_A920)

	 --Smart
	 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@ProSmartId, @RestaurantCategoryId)

	 --Enterprise
	 INSERT INTO PRODUCTS(Availability, Code, Type, CreatedBy, CreatedDateUtc, ValidFrom, DisplayOrder) OUTPUT inserted.Id INTO @Ids
	 VALUES('Bundle_HelpRequired', 'ENTERPRISE_RESTAURANT_V2', 'BUNDLE', 'n/a', GETUTCDATE(), GETUTCDATE(), 2)
 
	 SELECT TOP 1 @EnterpriseRestaurantId = ID FROM @Ids 
	 DELETE FROM @Ids

	 INSERT INTO PRODUCTCATEGORIES(ProductId, CategoryId) VALUES(@EnterpriseRestaurantId, @RestaurantCategoryId)

	 INSERT INTO PRICES(ChargeFrequency, ChargeType, ExemptFromVAT, ProductId, PerItemPrice, ValidFrom, CreatedBy, CreatedDateUtc, Currency, RentalPeriod)
	 VALUES('MONTH', 'RECCURRING_CHARGE', 0, @EnterpriseRestaurantId, 49900, GETUTCDATE(), 'n/a', GETUTCDATE(), 'SAR', 24)
 
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRestaurantId, @LINGA_POS)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRestaurantId, @MC)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRestaurantId, @VISA)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRestaurantId, @AMEX)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRestaurantId, @MADA_V2)
	 INSERT INTO ProductParts(ProductId, PartId) VALUES(@EnterpriseRestaurantId, @SMARTPOS_A920)