-- Create Table: PendingComissionPrice
CREATE TABLE PendingComissionPrice (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    ProductCommissionPriceId UNIQUEIDENTIFIER NULL,  
    ProductId UNIQUEIDENTIFIER NOT NULL,             
    MCCID UNIQUEIDENTIFIER NOT NULL,                 
    BusinessTypeId UNIQUEIDENTIFIER NOT NULL,        
    CommissionFeesId UNIQUEIDENTIFIER NOT NULL,      
    FeeValue DECIMAL(9, 2)  NOT NULL,                    
    VATRate DECIMAL(9, 2) NOT NULL,                 
    VATType INT NOT NULL,                           
    BillingType INT NOT NULL,                        
    BillingFrequency INT NOT NULL,                   
    NewFeeValue DECIMAL(9, 2) NULL,                    
    ActionType INT NOT NULL,                        
    Status INT NOT NULL,
    ReviewedBy UNIQUEIDENTIFIER NULL,
    ReviewedDate DATETIME NULL,
    CreatedBy VARCHAR(255) NOT NULL,
    CreatedDate DATETIME NOT NULL, 
    UpdatedBy VARCHAR(255), 
    UpdatedDate DATETIME,             

    -- Foreign Key Constraints
    CONSTRAINT FK_PendingComissionPrice_ProductCommissionPrice 
        FOREIGN KEY (ProductCommissionPriceId) REFERENCES ProductCommissionPrice(Id) ON DELETE SET NULL,

    CONSTRAINT FK_PendingComissionPrice_Product 
        FOREIGN KEY (ProductId) REFERENCES Products(Id) ON DELETE CASCADE,

    CONSTRAINT FK_PendingComissionPrice_MCC 
        FOREIGN KEY (MCCID) REFERENCES Mcc(Id) ON DELETE CASCADE,

    CONSTRAINT FK_PendingComissionPrice_BusinessType 
        FOREIGN KEY (BusinessTypeId) REFERENCES BusinessTypes(Id) ON DELETE CASCADE,

    CONSTRAINT FK_PendingComissionPrice_CommissionFees 
        FOREIGN KEY (CommissionFeesId) REFERENCES CommissionFees(Id) ON DELETE CASCADE
);
GO
