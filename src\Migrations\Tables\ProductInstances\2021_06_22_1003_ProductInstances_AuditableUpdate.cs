﻿using System;
using System.Collections.Generic;
using System.Text;
using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_06_22_1003)]
public class ProductInstances_AuditableUpdate : ForwardOnlyMigration
{
    public override void Up()
    {
        Rename.Column("CreatedDateUtc").OnTable("ProductInstances").To("CreatedDate");
        Rename.Column("UpdatedDateUtc").OnTable("ProductInstances").To("UpdatedDate");
    }
}
