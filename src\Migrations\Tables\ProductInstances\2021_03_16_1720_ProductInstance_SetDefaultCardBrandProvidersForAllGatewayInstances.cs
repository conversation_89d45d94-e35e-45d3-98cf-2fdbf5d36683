﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_03_16_1720)]
public class ProductInstance_SetDefaultCardBrandProvidersForAllGatewayInstances : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
                DECLARE @SaudiCardBrandProviders NVARCHAR(256) = N'[{""cardBrand"":""VISA"",""acquiringProvider"":""MPGS"",""threeDSecureProvider"":""MPGS""},{""cardBrand"":""MC"",""acquiringProvider"":""MPGS"",""threeDSecureProvider"":""MPGS""},{""cardBrand"":""MADA"",""acquiringProvider"":""MPGS"",""threeDSecureProvider"":""MPGS""}]';
                DECLARE @EgyptCardBrandProviders NVARCHAR(256) = N'[{""cardBrand"":""VISA"",""acquiringProvider"":""MPGS"",""threeDSecureProvider"":""MPGS""},{""cardBrand"":""MC"",""acquiringProvider"":""MPGS"",""threeDSecureProvider"":""MPGS""},{""cardBrand"":""MEEZA"",""acquiringProvider"":""MPGS"",""threeDSecureProvider"":""MPGS""}]';

                UPDATE pi
                SET Metadata = JSON_MODIFY(pi.Metadata, '$.cardBrandProviders', JSON_Query(@SaudiCardBrandProviders))
                FROM ProductInstances pi
                INNER JOIN Products p ON p.id=pi.ProductId
                WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.MerchantCountry') = 'SAU'

                UPDATE pi
                SET Metadata = JSON_MODIFY(pi.Metadata, '$.cardBrandProviders', JSON_Query(@EgyptCardBrandProviders))
                FROM ProductInstances pi
                INNER JOIN Products p ON p.id=pi.ProductId
                WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(pi.Metadata, '$.MerchantCountry') = 'EGY'");
    }
}
