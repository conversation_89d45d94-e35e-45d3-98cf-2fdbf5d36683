﻿using Common.Data.Extensions;
using Common.Entities;
using Common.Enums;
using Common.Models.ProductCommissionPrice;
using Common.Models.UnitPrice;
using Common.Views;
using EFCore.BulkExtensions;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Repositories;
public class ProductCommissionPriceRepository : AuditableRepository<Guid, ProductCommissionPriceEntity>, IProductCommissionPriceRepository
{
    private IQueryable<ProductCommissionPriceListView> ProductCommissionPriceListQuery;
    public ProductCommissionPriceRepository(DataContext context, IHttpContextAccessor contextAccessor) : base(context, contextAccessor)
    {
        ProductCommissionPriceListQuery = context.ProductCommissionPriceListView.AsQueryable();
    }
    public void AddRange(IEnumerable<ProductCommissionPriceEntity> entities)
    {
        context.Set<ProductCommissionPriceEntity>().AddRange(entities);
    }
    public async Task SaveCommissionPricesAsync(List<ProductCommissionPriceEntity> CommissionPrices)
    {
        AddRange(CommissionPrices);
        await context.SaveChangesAsync();
    }
    public async Task<List<ProductCommissionPriceEntity>> GetExistCommissionPrices(Expression<Func<ProductCommissionPriceEntity, bool>> predicate)
    {
        return await context.Set<ProductCommissionPriceEntity>().Where(predicate).ToListAsync();
    }
    public async Task<ProductCommissionPriceEntity?> GetByIdAsync(Guid id)
    {
        return await context.Set<ProductCommissionPriceEntity>().FirstOrDefaultAsync(a => a.Id == id);
    }
    public async Task<ProductCommissionPriceListResponse> GetProductCommissionPriceList(ProductCommissionPriceListRequest request)
    {
        ProductCommissionPriceListQuery = ProductCommissionPriceListQuery.AsNoTracking();
        ProductCommissionPriceListQuery = SearchProductCommissionPrices(request, ProductCommissionPriceListQuery);
        ProductCommissionPriceListQuery = FilterProductCommissionPrices(request, ProductCommissionPriceListQuery);
        int TotalCount = await ProductCommissionPriceListQuery.CountAsync();

        ProductCommissionPriceListQuery = SortProductCommissionPrices(request, ProductCommissionPriceListQuery);

        var commissionPriceList = request.Size > 0
       ? await ProductCommissionPriceListQuery.Page(request.Page, request.Size).ToArrayAsync()
       : await ProductCommissionPriceListQuery.ToArrayAsync();

        return new ProductCommissionPriceListResponse
        {
            TotalCount = TotalCount,
            TotalPages = request.Size > 0 ? (int)Math.Ceiling((double)TotalCount / request.Size) : 1,
            CommissionPriceList = commissionPriceList
        };
    }
    public static IQueryable<ProductCommissionPriceListView> SearchProductCommissionPrices(ProductCommissionPriceListRequest request,
        IQueryable<ProductCommissionPriceListView> CommissionPriceList)
    {
        var searchTerm = request.SearchTerms.FirstOrDefault(s => !string.IsNullOrEmpty(s.Value));

        if (!string.IsNullOrEmpty(searchTerm.Value))
        {
            var searchDictionary = new Dictionary<ProductCommissionPriceSearchKey, Func<IQueryable<ProductCommissionPriceListView>, string, IQueryable<ProductCommissionPriceListView>>>
            {
                { ProductCommissionPriceSearchKey.All, (query, value) => query.Where(s => s.ProductName != null && s.ProductName.Contains(value) ||
                                                                              s.ProductCode.Contains(value) ||
                                                                              s.MccName.Contains(value) ||
                                                                              s.FeeName.Contains(value) ||
                                                                              s.CommissionFeeCode.Contains(value) ||
                                                                              s.MccCode.Contains(value)
                                                                             ) },
                { ProductCommissionPriceSearchKey.ProductName, (query, value) => query.Where(s => s.ProductName != null && s.ProductName.Contains(value)) },
                { ProductCommissionPriceSearchKey.ProductCode, (query, value) => query.Where(s => s.ProductCode.Contains(value)) },
                { ProductCommissionPriceSearchKey.MccName, (query, value) => query.Where(s => s.MccName.Contains(value)) },
                { ProductCommissionPriceSearchKey.MccCode, (query, value) => query.Where(s => s.MccCode.Contains(value)) },
                { ProductCommissionPriceSearchKey.FeeName, (query, value) => query.Where(s => s.FeeName.Contains(value)) },
                { ProductCommissionPriceSearchKey.FeeCode, (query, value) => query.Where(s => s.CommissionFeeCode.Contains(value)) }
            };

            if (searchDictionary.ContainsKey(searchTerm.Key))
            {
                CommissionPriceList = searchDictionary[searchTerm.Key](CommissionPriceList, searchTerm.Value);
            }
        }

        return CommissionPriceList;
    }
    public static IQueryable<ProductCommissionPriceListView> FilterProductCommissionPrices(ProductCommissionPriceListRequest request,
        IQueryable<ProductCommissionPriceListView> CommissionPriceList)
    {
        if (request.FilterByProducts != null && request.FilterByProducts.Any())
            CommissionPriceList = CommissionPriceList.Where(s => request.FilterByProducts.Contains(s.ProductId));

        if (request.FilterByBusinessType != null && request.FilterByBusinessType.Any())
            CommissionPriceList = CommissionPriceList.Where(s => request.FilterByBusinessType.Contains(s.BusinessTypeId));

        if (request.FilterByMccType != null && request.FilterByMccType.Any())
            CommissionPriceList = CommissionPriceList.Where(s => request.FilterByMccType.Contains(s.MccId));

        if (request.FilterByCommissionFee != null && request.FilterByCommissionFee.Any())
            CommissionPriceList = CommissionPriceList.Where(s => request.FilterByCommissionFee.Contains(s.FeeId));

        if (request.FilterByFeeStatus != null && request.FilterByFeeStatus.Any())
            CommissionPriceList = CommissionPriceList.Where(s => request.FilterByFeeStatus.Contains(s.FeeStatus));

        if (request.FilterByBillingType != null && request.FilterByBillingType.Any())
            CommissionPriceList = CommissionPriceList.Where(s => request.FilterByBillingType.Contains(s.BillingType));

        if (request.FilterByBillingFrequency != null && request.FilterByBillingFrequency.Any())
            CommissionPriceList = CommissionPriceList.Where(s => request.FilterByBillingFrequency.Contains(s.BillingFrequency));

        return CommissionPriceList;
    }
    public static IQueryable<ProductCommissionPriceListView> SortProductCommissionPrices(ProductCommissionPriceListRequest request,
        IQueryable<ProductCommissionPriceListView> CommissionPriceList)
    {
        if (Enum.TryParse(request.OrderType, true, out SortType orderType) && !string.IsNullOrEmpty(request.OrderFieldName))
            CommissionPriceList = CommissionPriceList.OrderBy(request.OrderFieldName, orderType);

        return CommissionPriceList;
    }
    public async Task<List<ProductCommissionPriceEntity>> GetProductCommissionPricesByIdsAsync(List<Guid> ids)
    {
        return await context.Set<ProductCommissionPriceEntity>()
            .Where(up => ids.Contains(up.Id))
            .ToListAsync();
    }
    public async Task<int> DeleteBulkAsync(List<Guid> ids)
    {
        var rowsAffected = await context.Set<ProductCommissionPriceEntity>()
            .Where(pc => ids.Contains(pc.Id))
            .BatchDeleteAsync();

        return rowsAffected;
    }
    public async Task AddLogsAsync(List<ProductCommissionPriceLogEntity> logs)
    {
        await context.Set<ProductCommissionPriceLogEntity>().AddRangeAsync(logs);
        await context.SaveChangesAsync();
    }
    public async Task<ProductCommissionPriceEntity?> GetCommissionPriceByIdAsync(Guid id)
    {
        return await context.Set<ProductCommissionPriceEntity>()
            .FirstAsync(up => up.Id == id);
    }

    public async Task UpdateCommissionPricesAsync(List<ProductCommissionPriceEntity> productCommissionPriceEntity)
    {
        context.Set<ProductCommissionPriceEntity>().UpdateRange(productCommissionPriceEntity);
        await context.SaveChangesAsync();
    }
}

