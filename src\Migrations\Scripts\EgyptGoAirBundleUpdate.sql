﻿BEGIN TRANSACTION;

-- Update Go Air (SoftPOS) Bundle Availability
DECLARE @ProductBundleId  UNIQUEIDENTIFIER
SELECT TOP 1 @ProductBundleId = ID FROM [PRODUCTS].[dbo].[Products] WHERE Counterparty = 'GEIDEA_EGYPT' AND Code = 'GO_AIR'

UPDATE [PRODUCTS].[dbo].[Products] SET Availability = 'Live' , Flow = 'Normal' ,SalesChannel='All' where Id = @ProductBundleId

-- Delete Go Air (SoftPOS) Parts Availability
DELETE FROM [PRODUCTS].[dbo].[ProductParts] WHERE ProductID =
( SELECT ID  FROM [PRODUCTS].[dbo].[Products] WHERE Code ='GO_Air' AND Counterparty ='GEIDEA_EGYPT')
and PartID <> 
( SELECT ID  FROM [PRODUCTS].[dbo].[Products] where Code ='SOFT_POS' AND Counterparty ='GEIDEA_EGYPT')

COMMIT;