﻿using AutoMapper;
using Common;
using Common.Entities;
using DataAccess;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using ProductService;
using System;
using System.Net;
using System.Threading.Tasks;
using Geidea.Utils.Counterparty.Providers;
using Xunit;
using Common.Options;
using Microsoft.Extensions.Options;
using NSubstitute;

namespace Services.Test.ProductServiceTests;

public class BindTests
{
    private readonly Mock<ILogger<ProductService>> logger = new Mock<ILogger<ProductService>>();
    private readonly Mock<ILogger<ProductInstanceRepository>> repoLogger = new Mock<ILogger<ProductInstanceRepository>>();
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly DataContext context;
    private readonly ProductService productService;
    private readonly IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;

    private readonly ProductEntity productA = new ProductEntity
    {
        Id = Guid.Parse("00000000-0000-0000-0000-000000000001"),
        Code = "AAA",
        DisplayOrder = 0,
        Version = 1
    };
    private readonly ProductEntity productB = new ProductEntity
    {
        Id = Guid.Parse("00000000-0000-0000-0000-000000000002"),
        Code = "BBB",
        DisplayOrder = 0
    };
    private readonly ProductEntity productC = new ProductEntity
    {
        Id = Guid.Parse("00000000-0000-0000-0000-000000000003"),
        Code = "CCC",
        DisplayOrder = 1,
        Version = 0
    };
    private readonly ProductInstanceEntity productInstance = new ProductInstanceEntity
    {
        Product = new ProductEntity
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000004"),
            Code = "DDD"
        }
    };
    private readonly ProductPartEntity productPart = new ProductPartEntity
    {
        Product = new ProductEntity
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000005"),
            Code = "PRODUCT"
        },
        Part = new ProductEntity
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000006"),
            Code = "PART"
        }
    };
    private readonly ICounterpartyProvider counterpartyProvider;

    public BindTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
           .UseInMemoryDatabase(databaseName: "ProductBindTests" + Guid.NewGuid().ToString())
           .Options;

        context = new DataContext(options, new CounterpartyProvider());

        context.Products.Add(productA);
        context.Products.Add(productB);
        context.Products.Add(productC);
        context.ProductParts.Add(new ProductPartEntity { Product = productB, Part = productC, Quantity = 1 });
        context.ProductParts.Add(productPart);
        context.ProductInstances.Add(productInstance);
        context.SaveChanges();

        softposEgyptFeatureToggle = Substitute.For<IOptions<SoftposEgyptFeatureToggle>>();
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = true });
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();

        var productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);
        var categoryRepository = new CategoryRepository(context, httpContext.Object);
        var productInstanceRepo = new ProductInstanceRepository(context, httpContext.Object, repoLogger.Object, mapper);
        productService = new ProductService(logger.Object, productRepository, mapper, categoryRepository, productInstanceRepo);
    }

    [Fact]
    public async Task Bind_EmptyProductId()
    {
        await productService
            .Invoking(x => x.BindPartAsync(Guid.NewGuid(), Guid.Empty, 1))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.InvalidIdentifier.Code);
    }

    [Fact]
    public async Task Bind_EmptyPartId()
    {
        await productService
            .Invoking(x => x.BindPartAsync(Guid.Empty, Guid.NewGuid(), 1))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.InvalidIdentifier.Code);
    }

    [Fact]
    public async Task Bind_SameProductIds()
    {
        var id = Guid.NewGuid();

        await productService
            .Invoking(x => x.BindPartAsync(id, id, 1))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.InvalidBindAttempt.Code);
    }

    [Fact]
    public async Task Bind_InexistingProduct()
    {
        await productService
            .Invoking(x => x.BindPartAsync(productB.Id, Guid.NewGuid(), 1))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
            x.ProblemDetails.Type == Errors.ProductNotFound.Code);
    }

    [Fact]
    public async Task Bind_InexistingPart()
    {
        await productService
            .Invoking(x => x.BindPartAsync(Guid.NewGuid(), productB.Id, 1))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
            x.ProblemDetails.Type == Errors.ProductNotFound.Code);
    }

    [Fact]
    public async Task Bind_UsedProduct()
    {
        await productService
            .Invoking(x => x.BindPartAsync(productB.Id, productInstance.Product.Id, 1))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.InvalidUpdateRequest.Code);
    }

    [Fact]
    public async Task Bind_InvalidQuantity()
    {
        await productService
            .Invoking(x => x.BindPartAsync(productA.Id, productB.Id, -1))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.InvalidQuantity.Code);
    }

    [Fact]
    public async Task Bind_OppositeBinding()
    {
        await productService
            .Invoking(x => x.BindPartAsync(productB.Id, productC.Id, 1))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.OppositeBindingAlreadyExists.Code);
    }

    [Fact]
    public async Task Bind_BindingAlreadyExists()
    {
        await productService
            .Invoking(x => x.BindPartAsync(productC.Id, productB.Id, 1))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.BindingAlreadyExists.Code);
    }

    [Fact]
    public async Task Bind_ValidBinding()
    {
        await productService.BindPartAsync(productB.Id, productA.Id, 1);
        var part = await context.ProductParts
            .FirstOrDefaultAsync(p => p.ProductId == productA.Id && p.PartId == productB.Id);
        part.Should().NotBeNull();
    }


    [Fact]
    public async Task Unbind_EmptyProductId()
    {
        await productService
            .Invoking(x => x.UnbindPartAsync(Guid.NewGuid(), Guid.Empty))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.InvalidIdentifier.Code);
    }

    [Fact]
    public async Task Unbind_EmptyPartId()
    {
        await productService
            .Invoking(x => x.UnbindPartAsync(Guid.Empty, Guid.NewGuid()))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.InvalidIdentifier.Code);
    }

    [Fact]
    public async Task Unbind_SameProductIds()
    {
        var id = Guid.NewGuid();

        await productService
            .Invoking(x => x.UnbindPartAsync(id, id))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.InvalidBindAttempt.Code);
    }

    [Fact]
    public async Task Unbind_InexistingProduct()
    {
        await productService
            .Invoking(x => x.UnbindPartAsync(productB.Id, Guid.NewGuid()))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
            x.ProblemDetails.Type == Errors.ProductNotFound.Code);
    }

    [Fact]
    public async Task Unbind_InexistingPart()
    {
        await productService
            .Invoking(x => x.UnbindPartAsync(Guid.NewGuid(), productB.Id))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
            x.ProblemDetails.Type == Errors.ProductNotFound.Code);
    }

    [Fact]
    public async Task Unbind_UsedProduct()
    {
        await productService
            .Invoking(x => x.UnbindPartAsync(productB.Id, productInstance.Product.Id))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.InvalidUpdateRequest.Code);
    }

    [Fact]
    public async Task Unbind_BoundedProducts()
    {
        await productService.UnbindPartAsync(productPart.PartId, productPart.ProductId);

        var retrievedProductPart = await context.ProductParts
            .FirstOrDefaultAsync(p => p.PartId == productPart.PartId && p.ProductId == productPart.ProductId);
        retrievedProductPart.Should().BeNull();
    }

    [Fact]
    public async Task Unbind_UnboundedProducts()
    {
        await productService.UnbindPartAsync(productC.Id, productA.Id);

        var retrievedProductPart = await context.ProductParts
            .FirstOrDefaultAsync(p => p.PartId == productC.Id && p.ProductId == productA.Id);
        retrievedProductPart.Should().BeNull();
    }
}
