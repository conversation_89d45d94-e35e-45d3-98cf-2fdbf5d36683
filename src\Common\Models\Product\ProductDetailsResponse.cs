﻿using Common.Entities;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models;
[ExcludeFromCodeCoverage]
public class ProductDetailsResponse
{
    #region ProductData
    public string? Code { get; set; }
    public string? Name { get; set; }
    public string? NameAr { get; set; }
    public string? Subname { get; set; }
    public string? SubnameAr { get; set; }
    public string? Description { get; set; }
    public string? DescriptionAr { get; set; }
    public string? SalesChannel { get; set; }
    public string? ProductLink { get; set; }
    public int? ProductDisplayOrder { get; set; }
    public bool? IsCNP { get; set; }
    #endregion
    public string? CategoryName { get; set; }
    public Guid CategoryId { get; set; }
    public string? ProductType { get; set; }
    public List<ProductImagesResponse>? ProductImages { get; set; }

}
