﻿using System.Collections.Generic;
using Common.Models.Gle;
using FluentValidation;

namespace Common.Validators.Gle;

public class GleBaseValidator : AbstractValidator<GleBase>
{
    public GleBaseValidator()
    {
        var gleStatusList = new List<string> { Constants.GleRegistrationStatus.Success, Constants.GleRegistrationStatus.Failed };
        RuleFor(x => x.GleRegistrationStatus)
            .NotNull()
            .Must(x => gleStatusList.Contains(x))
            .WithErrorCode(Errors.InvalidGleStatus.Code)
            .WithMessage(Errors.InvalidGleStatus.Message);

        RuleFor(x => x.GleRegistrationResponse)
            .NotNull()
            .WithErrorCode(Errors.InvalidGleResponse.Code)
            .WithMessage(Errors.InvalidGleResponse.Message);
    }
}