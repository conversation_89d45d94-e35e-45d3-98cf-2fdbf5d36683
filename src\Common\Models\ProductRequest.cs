﻿using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace Common.Models;

public class ProductRequest
{
    public Guid CategoryId { get; set; }
    [DefaultValue(Constants.Availability.Live)]
    public string Availability { get; set; } = Constants.Availability.Live;
    [DefaultValue(Constants.ProductType.Bundle)]
    public string Type { get; set; } = Constants.ProductType.Bundle;
    [DefaultValue(Constants.Flow.Normal)]
    public string Flow { get; set; } = Constants.Flow.Normal;
    [DefaultValue(Constants.ReferralChannel.Unassigned)]
    public string ReferralChannel { get; set; } = Constants.ReferralChannel.Unassigned;
    [DefaultValue(false)]
    public bool QuickOnboarding { get; set; } = false;
    [DefaultValue(0)]
    public int Version { get; set; } = 0;
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public string? SalesChannel { get; set; }
    public string? Code { get; set; }
    public int? ProductDisplayOrder { get; set; }
    // New fields
    public string? Name { get; set; }
    public string? NameAr { get; set; }
    public string? Subname { get; set; }
    public string? SubnameAr { get; set; }
    public string? Description { get; set; }
    public string? DescriptionAr { get; set; }
    public string? ProductLink { get; set; }
    public bool? IsCNP { get; set; }
    // Image data
    public List<ImageData>? Images { get; set; } = null!;

}

public class ImageData
{
    public string? Language { get; set; }
    public int? DisplayOrder { get; set; }
    public Guid ImageId { get; set; }
}


