﻿using FluentMigrator;
using System;
using System.IO;

namespace Migrations.Tables.InitialSeed;

[Migration(2021_02_12_0940)]
public class EgyptInitialSeed : ForwardOnlyMigration
{
    public override void Up()
    {
        if (Schema.Table("Products").Constraint("UQ_Code_Version").Exists())
        {
            Delete.UniqueConstraint("UQ_Code_Version").FromTable("Products");
        }

        Create.UniqueConstraint("UQ_Code_Version_Counterparty")
            .OnTable("Products")
            .Columns("Code", "Version", "Counterparty");

        Execute.Script(AppDomain.CurrentDomain.BaseDirectory + Path.Combine("Scripts", "EgyptInitialSeed.sql"));
    }
}
