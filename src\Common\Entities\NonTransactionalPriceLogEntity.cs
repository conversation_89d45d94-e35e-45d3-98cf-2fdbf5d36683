﻿using Common.Enums.ProductCommisssionPrices;
using Common.Enums.UnitPrice;
using Geidea.Utils.DataAccess.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Entities;
public class NonTransactionalPriceLogEntity : Entity<Guid>
{
    public Guid ProductId { get; set; }
    public Guid MccId { get; set; }
    public Guid BusinessTypeId { get; set; }
    public Guid NonTransFeeId { get; set; }
    public decimal FeeValue { get; set; }
    public FeeType FeeType { get; set; }
    public BillingType BillingType { get; set; }
    public BillingFrequency BillingFrequency { get; set; }
    public string? DeletedBy { get; set; }
    public DateTime DeletedDate { get; set; }
}
