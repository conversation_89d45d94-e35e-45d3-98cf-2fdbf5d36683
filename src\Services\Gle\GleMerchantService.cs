﻿using System;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Common;
using Common.Entities.Gle;
using Common.Models.Gle;
using Common.Repositories.Gle;
using Common.Services.Gle;
using Common.Validators.Gle;
using FluentValidation.Results;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using ValidationException = Geidea.Utils.Exceptions.ValidationException;

namespace Services.Gle;

public class GleMerchantService : IGleMerchantService
{
    private readonly ILogger<GleMerchantService> logger;
    private readonly IMapper mapper;
    private readonly IGleMerchantRepository gleMerchantRepository;

    public GleMerchantService(ILogger<GleMerchantService> logger,
        IMapper mapper,
        IGleMerchantRepository gleMerchantRepository)
    {
        this.logger = logger;
        this.mapper = mapper;
        this.gleMerchantRepository = gleMerchantRepository;
    }

    public async Task AddGleMerchantAsync(GleMerchantRequest createGleMerchantRequest)
    {
        ValidateRequest(await new GleMerchantRequestValidator().ValidateAsync(createGleMerchantRequest));

        var gleMerchantEntity = mapper.Map<GleMerchantEntity>(createGleMerchantRequest);
        await gleMerchantRepository.AddGle(gleMerchantEntity);

        logger.LogInformation("Added GLE merchant entity for merchantId: {id}", createGleMerchantRequest.MerchantId);
    }

    public async Task UpdateGleMerchantAsync(Guid gleMerchantId, JsonPatchDocument<UpdateGleMerchantRequest> updateDocument)
    {
        if (gleMerchantId == Guid.Empty)
        {
            logger.LogError("Invalid GLE merchant ID {id}", gleMerchantId);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidGleMerchantId.Message);
        }
        ValidateRequest(await new GleBasePatchRequestValidator<UpdateGleMerchantRequest>().ValidateAsync(updateDocument));

        var mappedDocument = mapper.Map<JsonPatchDocument<GleMerchantEntity>>(updateDocument);
        await gleMerchantRepository.UpdateGle(gleMerchantId, mappedDocument);

        logger.LogInformation("Updated GLE merchant with id:{id}", gleMerchantId);
    }

    public async Task<GleMerchant?> GetGleMerchantByMerchantIdAsync(Guid merchantId)
    {
        if (merchantId == Guid.Empty)
        {
            logger.LogError("Invalid merchant ID {id}", merchantId);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidMerchantId.Message);
        }
        var gleMerchantEntity = await gleMerchantRepository.GetGleMerchantByMerchantIdAsync(merchantId);

        return mapper.Map<GleMerchant?>(gleMerchantEntity);
    }

    public async Task<GleMerchant?> GetGleHierarchyByMerchantIdAsync(Guid merchantId)
    {
        if (merchantId == Guid.Empty)
        {
            logger.LogError("Invalid merchant ID {id}", merchantId);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidMerchantId.Message);
        }

        var gleHierarchy = await gleMerchantRepository.GetGleHierarchyByMerchantIdAsync(merchantId);
        return mapper.Map<GleMerchant?>(gleHierarchy);
    }

    public async Task<bool> IsMerchantRegisteredInGleAsync(Guid merchantId)
    {
        if (merchantId == Guid.Empty)
        {
            logger.LogError("Invalid merchant ID {id}", merchantId);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidMerchantId.Message);
        }

        return await gleMerchantRepository.IsMerchantRegisteredInGleAsync(merchantId);
    }

    private void ValidateRequest(ValidationResult validationResult)
    {
        if (!validationResult.IsValid)
        {
            var errorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
            logger.LogError("GLE Merchant Registration request validation failed: {@errors}", errorDescription);
            throw new ValidationException(validationResult);
        }
    }
}