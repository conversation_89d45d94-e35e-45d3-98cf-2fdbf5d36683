﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_12_04_2100)]
public class ProductInstance_Rename_IsSubscriptionEnabled_To_IsSubscriptionPayLinkEnabled : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
	        UPDATE pi
            SET pi.Metadata =  JSON_MODIFY(JSON_MODIFY(pi.Metadata,'$.IsSubscriptionPayLinkEnabled',
            JSON_VALUE(pi.Metadata,'$.IsSubscriptionEnabled')),'$.IsSubscriptionEnabled', NULL)
            FROM [dbo].[ProductInstances] pi
            INNER JOIN [dbo].[Products] p ON p.Id = pi.ProductId
            WHERE pi.Metadata IS NOT NULL AND p.[Type] = 'GWAY' AND ISJSON(pi.Metadata) = 1"
        );
    }
}