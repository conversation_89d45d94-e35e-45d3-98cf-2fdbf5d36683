﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_07_16_1800)]
public class ProductInstance_SetMissingMpgsMsoProvider : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
UPDATE pi
SET Metadata = JSON_MODIFY(pi.Metadata, '$.MpgsMsoProvider', N'Geidea')
FROM ProductInstances pi
INNER JOIN Products p ON p.id = pi.ProductId
WHERE p.Type = 'GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) = 1
AND JSON_VALUE(pi.Metadata, '$.MpgsMsoProvider') IS NULL AND JSON_VALUE(LOWER(pi.Metadata), '$.merchantcountry') = 'sau'

UPDATE pi
SET Metadata = JSON_MODIFY(pi.Metadata, '$.MpgsMsoProvider', N'BM')
FROM ProductInstances pi
INNER JOIN Products p ON p.id = pi.ProductId
WHERE p.Type = 'GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) = 1
AND JSON_VALUE(pi.Metadata, '$.MpgsMsoProvider') IS NULL AND JSON_VALUE(LOWER(pi.Metadata), '$.merchantcountry') = 'egy'
");
    }
}
