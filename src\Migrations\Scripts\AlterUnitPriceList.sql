CREATE OR ALTER VIEW UnitPriceList
AS
    SELECT 
        u.Id,
        p.Id AS ProductId,
        p.[Name] AS ProductName,
        p.NameAr AS ProductNameAR,            -- New field
        p.Subname AS ProductSubname,          -- New field
        p.SubnameAr AS ProductSubnameAr,      -- New field
        p.ProductLink AS ProductLink,         -- New field
        p.Description AS ProductDescription,  -- New field
        p.DescriptionAr AS ProductDescriptionAr,  -- New field
        p.Flow AS ProductFlow,                -- New field
        p.SalesChannel AS ProductSalesChannel,  -- New field
        p.ReferralChannel AS ProductReferralChannel,  -- New field
        p.QuickOnboarding AS ProductQuickOnboarding,  -- New field
        pi.ImageId,                           -- New field from ProductImages table
        p.Code AS ProductCode,
        m.Id AS MccId,
        m.[Name] AS MccName,
        b.Id AS BusinessTypeId,
        b.[Name] AS BusinessType,
        u.UnitPrice,
        u.VATRate,
        m.Code AS MccCode,
        mc.Name AS MccCategory,
        u.VATType,
        u.BillingType,
        u.BillingFrequency,
        u.CreatedDate
    FROM 
        [dbo].[UnitPrice] u
    JOIN 
        [dbo].[Products] p ON u.ProductID = p.Id
    JOIN 
        [dbo].[BusinessTypes] b ON u.BusinessTypeID = b.Id
    JOIN 
        [dbo].[Mcc] m ON u.MCCID = m.Id
    JOIN 
        [dbo].[MccCategory] mc ON m.MccCategoryId = mc.Id
    LEFT JOIN 
        [dbo].[ProductImages] pi ON p.Id = pi.ProductId -- Join with ProductImages to get ImageId
