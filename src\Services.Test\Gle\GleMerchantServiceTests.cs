﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using Common.Entities.Gle;
using Common.Models.Gle;
using Common.Repositories.Gle;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;
using Services.Gle;
using Services.Test.TestData;
using Constants = Common.Constants;

namespace Services.Test.Gle;

public class GleMerchantServiceTests
{
    private readonly ILogger<GleMerchantService> logger = Substitute.For<ILogger<GleMerchantService>>();
    private IMapper mapper;
    private readonly IGleMerchantRepository gleMerchantRepository = Substitute.For<IGleMerchantRepository>();
    private GleMerchantService gleMerchantService;

    [SetUp]
    public void SetUp()
    {
        mapper = new Mapper(new MapperConfiguration(x => x.AddMaps(typeof(MappingProfileHelper))));
        gleMerchantService = new GleMerchantService(logger, mapper, gleMerchantRepository);

        gleMerchantRepository.GetGleMerchantByMerchantIdAsync(Arg.Any<Guid>()).Returns(new GleMerchantEntity());
        gleMerchantRepository.GetGleHierarchyByMerchantIdAsync(Arg.Any<Guid>()).Returns(new GleMerchantEntity());
        gleMerchantRepository.IsMerchantRegisteredInGleAsync(Arg.Any<Guid>()).Returns(false);

        gleMerchantRepository.ClearReceivedCalls();
    }

    [Test]
    public async Task AddGleMerchantAsync_WhenRequestIsValid_ShouldCallRepository()
    {
        //Arrange
        var createRequest = new GleMerchantRequest
        {
            GleRegistrationStatus = Constants.GleRegistrationStatus.Failed,
            GleRegistrationResponse = "Phone number is missing!",
            ReferenceMmsId = "3472001",
            UserCategoryCode = Constants.GleUserCategoryCode.Chain,
            MerchantId = Guid.NewGuid()
        };

        //Act and assert
        await gleMerchantService.Invoking(x => x.AddGleMerchantAsync(createRequest))
            .Should().NotThrowAsync<ValidationException>();

        await gleMerchantRepository.Received(1)
            .AddGle(Arg.Is<GleMerchantEntity>(x => x.MerchantId == createRequest.MerchantId));
    }

    [Test]
    [TestCase("Pending", "Merchant is not verified!", "MSTRMER")]
    [TestCase("Failed", null, "CHNMBS")]
    [TestCase("Success", "Merchant was registered!", "CODE")]
    [TestCase(null, "Merchant was registered!", "CODE")]
    public async Task AddGleMerchantAsync_WhenRequestIsInvalid_ShouldThrowValidationException(string status, string response, string code)
    {
        //Arrange
        var createRequest = new GleMerchantRequest
        {
            GleRegistrationStatus = status,
            GleRegistrationResponse = response,
            UserCategoryCode = code,
            MerchantId = Guid.NewGuid()
        };

        //Act and assert
        await gleMerchantService.Invoking(x => x.AddGleMerchantAsync(createRequest))
            .Should().ThrowAsync<ValidationException>();

        await gleMerchantRepository.DidNotReceive()
            .AddGle(Arg.Is<GleMerchantEntity>(x => x.MerchantId == createRequest.MerchantId));
    }

    [Test]
    [TestCase("GleRegistrationStatus", "Success")]
    [TestCase("GleRegistrationStatus", "Failed")]
    [TestCase("GleUserId", "357629293")]
    [TestCase("GleLoginId", "10000123")]
    [TestCase("GleLoginId2", "100001")]
    [TestCase("ParentGleUserId", "400091")]
    [TestCase("GleRegistrationResponse", "This is a test")]
    [TestCase("ReferenceMmsId", "100983")]
    public async Task UpdateGleMerchantAsync_WhenRequestIsValid_ShouldCallRepository(string path, string value)
    {
        //Arrange
        var gleMerchantId = Guid.NewGuid();
        var updateDocument = new JsonPatchDocument<UpdateGleMerchantRequest>();
        updateDocument.Operations.Add(new Operation<UpdateGleMerchantRequest>
        {
            op = "replace",
            path = path,
            value = value
        });

        //Act and assert
        await gleMerchantService.Invoking(x => x.UpdateGleMerchantAsync(gleMerchantId, updateDocument))
            .Should().NotThrowAsync<ValidationException>();

        await gleMerchantRepository.Received(1)
            .UpdateGle(gleMerchantId, Arg.Any<JsonPatchDocument<GleMerchantEntity>>());
    }

    [Test]
    [TestCase("GleRegistrationStatus", "Pending")]
    [TestCase("GleRegistrationStatus", "Fail")]
    public async Task UpdateGleMerchantAsync_WhenRequestIsInvalid_ShouldThrowValidationException(string path, string value)
    {
        //Arrange
        var gleMerchantId = Guid.NewGuid();
        var updateDocument = new JsonPatchDocument<UpdateGleMerchantRequest>();
        updateDocument.Operations.Add(new Operation<UpdateGleMerchantRequest>
        {
            op = "replace",
            path = path,
            value = value
        });

        //Act and assert
        await gleMerchantService.Invoking(x => x.UpdateGleMerchantAsync(gleMerchantId, updateDocument))
            .Should().ThrowAsync<ValidationException>();

        await gleMerchantRepository.DidNotReceive()
            .UpdateGle(gleMerchantId, Arg.Any<JsonPatchDocument<GleMerchantEntity>>());
    }

    [Test]
    public async Task GetGleMerchantByMerchantIdAsync_WhenRequestIsValid_ShouldCallRepository()
    {
        //Arrange
        var merchantId = Guid.NewGuid();

        //Act
        var result = await gleMerchantService.GetGleMerchantByMerchantIdAsync(merchantId);

        //Assert
        Assert.NotNull(result);
        await gleMerchantRepository.Received(1).GetGleMerchantByMerchantIdAsync(merchantId);
    }

    [Test]
    public async Task GetGleMerchantByMerchantIdAsync_WhenRequestIsInvalid_ShouldThrowServiceException()
    {
        //Act and assert
        await gleMerchantService.Invoking(x => x.GetGleMerchantByMerchantIdAsync(Guid.Empty))
            .Should().ThrowAsync<ServiceException>();

        await gleMerchantRepository.DidNotReceive().GetGleMerchantByMerchantIdAsync(Arg.Any<Guid>());
    }

    [Test]
    public async Task GetGleHierarchyByMerchantIdAsync_WhenRequestIsValid_ShouldCallRepository()
    {
        //Arrange
        var merchantId = Guid.NewGuid();

        //Act
        var result = await gleMerchantService.GetGleHierarchyByMerchantIdAsync(merchantId);

        //Assert
        Assert.NotNull(result);
        await gleMerchantRepository.Received(1).GetGleHierarchyByMerchantIdAsync(merchantId);
    }

    [Test]
    public async Task GetGleHierarchyByMerchantIdAsync_WhenRequestIsInvalid_ShouldThrowServiceException()
    {
        //Act and assert
        await gleMerchantService.Invoking(x => x.GetGleHierarchyByMerchantIdAsync(Guid.Empty))
            .Should().ThrowAsync<ServiceException>();

        await gleMerchantRepository.DidNotReceive().GetGleHierarchyByMerchantIdAsync(Arg.Any<Guid>());
    }

    [Test]
    public async Task IsMerchantRegisteredInGleAsync_WhenRequestIsValid_ShouldCallRepository()
    {
        //Arrange
        var merchantId = Guid.NewGuid();

        //Act
        var result = await gleMerchantService.IsMerchantRegisteredInGleAsync(merchantId);

        //Assert
        Assert.NotNull(result);
        await gleMerchantRepository.Received(1).IsMerchantRegisteredInGleAsync(merchantId);
    }

    [Test]
    public async Task IsMerchantRegisteredInGleAsync_WhenRequestIsInvalid_ShouldThrowServiceException()
    {
        //Act and assert
        await gleMerchantService.Invoking(x => x.IsMerchantRegisteredInGleAsync(Guid.Empty))
            .Should().ThrowAsync<ServiceException>();

        await gleMerchantRepository.DidNotReceive().IsMerchantRegisteredInGleAsync(Arg.Any<Guid>());
    }
}