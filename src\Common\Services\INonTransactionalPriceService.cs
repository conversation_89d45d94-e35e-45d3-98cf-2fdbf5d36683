﻿using Common.Models.NonTransactionalPrice;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services;
public interface INonTransactionalPriceService
{
    Task<NonTransactionalFeePriceResponse> CreateAsync(NonTransactionalFeePriceCreateRequest request);
    Task<NonTransactionalPriceDetailsResponse> UpdateAsync(Guid Id, NonTransactionalFeePriceUpdateRequest request);
    Task<NonTransactionalPriceDetailsResponse> GetNonTransactionalPriceByIdAsync(Guid id);
    Task<NonTransactionalFeesPriceListResponse> GetNonTransactionalPriceList(NonTransactionalFeesPriceListRequest request);
    Task DeleteNonTransactionalPricesAsync(List<Guid> ids, string deletedBy);
    Task<NonTransactionalFeePriceResponse> CreateOrUpdateAsync(NonTransactionalFeePriceCreateRequest request);
    Task<List<NonTransactionalPriceDetailsResponse>> GetNonTransactionalPricesByIdsAsync(List<Guid> ids);
}
