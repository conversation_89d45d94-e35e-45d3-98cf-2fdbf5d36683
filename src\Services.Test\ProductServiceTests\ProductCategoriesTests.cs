﻿using AutoMapper;
using Common;
using Common.Entities;
using DataAccess;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using ProductService;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using Geidea.Utils.Counterparty.Providers;
using Xunit;
using Common.Options;
using Microsoft.Extensions.Options;
using NSubstitute;

namespace Services.Test.ProductServiceTests;

public class ProductCategoriesTests
{
    private readonly Mock<ILogger<ProductService>> logger = new Mock<ILogger<ProductService>>();
    private readonly Mock<ILogger<ProductInstanceRepository>> repoLogger = new Mock<ILogger<ProductInstanceRepository>>();
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly ProductRepository productRepository;
    private readonly ProductService productService;
    private readonly IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;
    private readonly ICounterpartyProvider counterpartyProvider;
    private readonly ProductEntity productNoCategory = new ProductEntity
    {
        Availability = "Bundle",
        Code = "TEST valid no category",
        Description = "Test description",
        Type = "BUNDLE",
        Version = 0
    };
    private readonly ProductEntity productEntity = new ProductEntity
    {
        Availability = "Bundle",
        Code = "TEST valid",
        Description = "Test description",
        Type = "BUNDLE",
        Version = 0
    };
    private readonly ProductInstanceEntity productInstance = new ProductInstanceEntity
    {
        Product = new ProductEntity
        {
            Code = "TEST USED"
        }
    };
    private readonly CategoryEntity category = new CategoryEntity
    {
        Code = "CATEGORY"
    };
    private readonly ProductEntity productWithCategory = new ProductEntity
    {
        Code = "Product With Category",
        ProductCategories = new List<ProductCategoriesEntity>
            {
                new ProductCategoriesEntity
                {
                    Category = new CategoryEntity
                    {
                        Code="TEST CATEGORY"
                    }
                }
            }
    };
    private DataContext context;

    public ProductCategoriesTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
           .UseInMemoryDatabase(databaseName: "ProductCategoriesTests" + Guid.NewGuid().ToString())
           .Options;

        context = new DataContext(options, new CounterpartyProvider());
        context.Products.Add(productNoCategory);
        context.Products.Add(productEntity);
        context.Products.Add(productWithCategory);
        context.ProductInstances.Add(productInstance);
        context.Categories.Add(category);
        context.SaveChanges();
        context.SaveChanges();
        softposEgyptFeatureToggle = Substitute.For<IOptions<SoftposEgyptFeatureToggle>>();
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = true });
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();

        productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);
        var categoryRepository = new CategoryRepository(context, httpContext.Object);
        var productInstanceRepo = new ProductInstanceRepository(context, httpContext.Object, repoLogger.Object, mapper);
        productService = new ProductService(logger.Object, productRepository, mapper, categoryRepository, productInstanceRepo);
    }

    [Fact]
    public async Task AddCategory_EmptyCategoryId()
    {
        await productService
            .Invoking(x => x.AddCategoryAsync(Guid.Empty, Guid.NewGuid()))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.InvalidIdentifier.Code);
    }

    [Fact]
    public async Task AddCategory_EmptyProductId()
    {
        await productService
            .Invoking(x => x.AddCategoryAsync(Guid.NewGuid(), Guid.Empty))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.InvalidIdentifier.Code);
    }

    [Fact]
    public async Task AddCategory_ProductNotFound()
    {
        await productService
            .Invoking(x => x.AddCategoryAsync(Guid.NewGuid(), Guid.NewGuid()))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
                        x.ProblemDetails.Type == Errors.ProductNotFound.Code);
    }

    [Fact]
    public async Task AddCategory_CategoryNotFound()
    {
        await productService
            .Invoking(x => x.AddCategoryAsync(productNoCategory.Id, Guid.NewGuid()))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
                        x.ProblemDetails.Type == Errors.CategoryNotFound.Code);
    }

    [Fact]
    public async Task AddCategory_UsedProduct()
    {
        await productService
            .Invoking(x => x.AddCategoryAsync(productInstance.Product.Id, category.Id))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        x.ProblemDetails.Type == Errors.InvalidUpdateRequest.Code);
    }

    [Fact]
    public async Task AddCategory_AlreadyAdded()
    {
        var oldCategoriesCount = await context.Categories.CountAsync();

        await productService.AddCategoryAsync(productWithCategory.Id, productWithCategory.ProductCategories[0].Category.Id);

        var newCategoryCount = await context.Categories.CountAsync();
        Assert.Equal(oldCategoriesCount, newCategoryCount);
    }

    [Fact]
    public async Task AddCategory()
    {
        await productService.AddCategoryAsync(productEntity.Id, category.Id);

        var retrievedProduct = await productRepository.FirstOrDefaultAsync(p => p.Id == productEntity.Id);
        retrievedProduct.ProductCategories.Should().NotBeNullOrEmpty();
        retrievedProduct.ProductCategories[0].CategoryId.Should().Be(category.Id);
    }

    [Fact]
    public async Task DeleteCategory_NoProductCategory()
    {
        var oldCategoriesCount = await context.Categories.CountAsync();

        await productService.DeleteCategoryAsync(productNoCategory.Id, category.Id);

        var newCategoryCount = await context.Categories.CountAsync();
        Assert.Equal(oldCategoriesCount, newCategoryCount);
    }

    [Fact]
    public async Task DeleteCategory()
    {
        await productService.DeleteCategoryAsync(productWithCategory.Id, productWithCategory.ProductCategories[0].CategoryId);

        var retrievedProduct = await productRepository.FirstOrDefaultAsync(p => p.Id == productWithCategory.Id);
        retrievedProduct.ProductCategories.Should().BeNullOrEmpty();
    }
}
