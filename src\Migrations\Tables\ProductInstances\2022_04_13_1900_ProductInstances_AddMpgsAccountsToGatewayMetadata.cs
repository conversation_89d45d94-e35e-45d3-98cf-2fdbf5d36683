﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_04_13_1900)]
public class ProductInstances_AddMpgsAccountsToGatewayMetadata : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
CREATE TABLE #Codes (Id UNIQUEIDENTIFIER, Code NVARCHAR(MAX))
CREATE TABLE #MpgsParams (Id UNIQUEIDENTIFIER, MpgsMerchantId NVARCHAR(MAX), MpgsApiKey NVARCHAR(MAX))

CREATE CLUSTERED INDEX ix_Id ON #Codes (Id)
CREATE CLUSTERED INDEX ix_Id ON #MpgsParams (Id)

INSERT INTO #Codes
SELECT PGW.Id, CONCAT('""', STRING_AGG(CAST(SCHEME_PRODUCT.Code AS NVARCHAR(MAX)), '"",""'), '""') AS Code
FROM [dbo].[ProductInstances] AS PGW
INNER JOIN [dbo].[ProductInstances] AS SCHEME ON SCHEME.ParentId = PGW.Id
INNER JOIN [dbo].[Products] AS SCHEME_PRODUCT ON SCHEME_PRODUCT.Id = SCHEME.ProductId
WHERE PGW.ProductId IN (SELECT Id FROM [dbo].[Products] WHERE [Type] = 'GWAY')
AND SCHEME.ProductId IN (SELECT Id FROM [dbo].[Products] WHERE [Type] = 'SCHEME')
GROUP BY PGW.Id

INSERT INTO #MpgsParams
SELECT pi.Id, JSON_VALUE(pi.Metadata, '$.MpgsMerchantId'), JSON_VALUE(pi.Metadata, '$.MpgsApiKey')
FROM [dbo].[ProductInstances] AS pi
INNER JOIN [dbo].[Products] AS p ON p.Id = pi.ProductId
WHERE pi.Metadata IS NOT NULL
AND p.Type = 'GWAY'
AND ISJSON(pi.Metadata) > 0 AND JSON_QUERY(pi.Metadata, '$.MpgsAccounts') IS NULL

UPDATE pi
SET Metadata = JSON_MODIFY(pi.Metadata, '$.MpgsAccounts', JSON_QUERY('[{""MpgsMerchantId"":null,""MpgsApiKey"":null,""CardBrands"":[]}]'))
FROM [dbo].[ProductInstances] AS pi
INNER JOIN #MpgsParams AS p ON p.Id = pi.Id

UPDATE pi
SET Metadata = JSON_MODIFY(pi.Metadata, 'strict $.MpgsAccounts[0].MpgsMerchantId', p.MpgsMerchantId)
FROM [dbo].[ProductInstances] AS pi
INNER JOIN #MpgsParams AS p ON p.Id = pi.Id

UPDATE pi
SET Metadata = JSON_MODIFY(pi.Metadata, 'strict $.MpgsAccounts[0].MpgsApiKey', p.MpgsApiKey)
FROM [dbo].[ProductInstances] AS pi
INNER JOIN #MpgsParams AS p ON p.Id = pi.Id

UPDATE pi
SET Metadata = JSON_MODIFY(pi.Metadata, '$.MpgsAccounts[0].CardBrands', JSON_QUERY(CONCAT('[', c.Code, ']}')))
FROM [dbo].[ProductInstances] AS pi
INNER JOIN #MpgsParams AS p ON p.Id = pi.Id
LEFT JOIN #Codes AS c ON c.Id = pi.Id

DROP TABLE #Codes
DROP TABLE #MpgsParams
");
    }
}