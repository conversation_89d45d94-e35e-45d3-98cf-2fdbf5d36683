
CREATE TABLE UnitPrice (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    ProductID UNIQUEIDENTIFIER NOT NULL,
    MCCID UNIQUEIDENTIFIER NOT NULL,
    BusinessTypeID UNIQUEIDENTIFIER NOT NULL,

    UnitPrice DECIMAL(9, 2) NOT NULL,  
    VATRate DECIMAL(9, 2) NOT NULL,  

    VATType INT NOT NULL,  
    BillingType INT NOT NULL, 
    BillingFrequency INT NOT NULL,  

    CreatedBy VARCHAR(255) NOT NULL,
    CreatedDate DATETIME NOT NULL,
    UpdatedBy VARCHAR(255),
    UpdatedDate DATETIME,

    -- Adding Unique Constraint
    CONSTRAINT UQ_Product_MCC_BusinessType UNIQUE (ProductID, MCCID, BusinessTypeID),

    FOREIGN KEY (ProductID) REFERENCES Products(Id),
    FOREIG<PERSON> KEY (MCCID) REFERENCES MCC(Id),
    FOR<PERSON><PERSON><PERSON> KEY (BusinessTypeID) REFERENCES BusinessTypes(Id)
);
