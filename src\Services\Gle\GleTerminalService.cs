﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Common;
using Common.Entities.Gle;
using Common.Models;
using Common.Models.Gle;
using Common.Repositories.Gle;
using Common.Services;
using Common.Services.Gle;
using Common.Validators.Gle;
using FluentValidation.Results;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;

namespace Services.Gle;

public class GleTerminalService : IGleTerminalService
{
    private readonly ILogger<GleTerminalService> logger;
    private readonly IMapper mapper;
    private readonly IGleTerminalRepository gleTerminalRepository;
    private readonly IProductService productService;

    public GleTerminalService(ILogger<GleTerminalService> logger,
        IMapper mapper,
        IGleTerminalRepository gleTerminalRepository,
        IProductService productService)
    {
        this.logger = logger;
        this.mapper = mapper;
        this.gleTerminalRepository = gleTerminalRepository;
        this.productService = productService;
    }

    public async Task AddGleTerminalAsync(GleTerminalRequest createGleTerminalRequest)
    {
        ValidateRequest(await new GleTerminalRequestValidator().ValidateAsync(createGleTerminalRequest));

        var gleTerminalEntity = mapper.Map<GleTerminalEntity>(createGleTerminalRequest);
        await gleTerminalRepository.AddGle(gleTerminalEntity);

        logger.LogInformation("Added GLE terminal entity for productInstanceId: {id}", createGleTerminalRequest.ProductInstanceId);
    }

    public async Task UpdateGleTerminalAsync(Guid gleTerminalId, JsonPatchDocument<UpdateGleTerminalRequest> updateDocument)
    {
        if (gleTerminalId == Guid.Empty)
        {
            logger.LogError("Invalid GLE terminal ID {id}", gleTerminalId);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidGleTerminalId.Message);
        }
        ValidateRequest(await new UpdateGleTerminalValidator().ValidateAsync(updateDocument));

        var mappedDocument = mapper.Map<JsonPatchDocument<GleTerminalEntity>>(updateDocument);
        await gleTerminalRepository.UpdateGle(gleTerminalId, mappedDocument);

        logger.LogInformation("Updated GLE terminal with id:{id}", gleTerminalId);
    }

    public async Task<GleTerminal?> GetGleTerminalByProductInstanceIdAsync(Guid productInstanceId)
    {
        if (productInstanceId == Guid.Empty)
        {
            logger.LogError("Invalid product instance ID {id}", productInstanceId);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidProductInstanceId.Message);
        }

        var gleTerminalEntity = await gleTerminalRepository.GetGleTerminalByProductInstanceIdAsync(productInstanceId);

        return mapper.Map<GleTerminal>(gleTerminalEntity);
    }

    public async Task<IReadOnlyCollection<GleTerminalRequest>> GetGleTerminalListByOrderIdAsync(Guid orderId)
    {
        if (orderId == Guid.Empty)
        {
            logger.LogError("Invalid order ID {id}", orderId);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidOrderId.Message);
        }

        var terminalList = await gleTerminalRepository.GetGleTerminalListByOrderIdAsync(orderId);

        return mapper.Map<IReadOnlyCollection<GleTerminalRequest>>(terminalList);
    }

    public async Task<IReadOnlyCollection<GleRegistrationStatusInfo>> GetGleRegistrationStatusHistoryAsync(GleHistoryLogRequest gleHistoryLogRequest)
    {
        ValidateRequest(await new GleHistoryLogRequestValidator().ValidateAsync(gleHistoryLogRequest));

        return await gleTerminalRepository.GetGleRegistrationStatusHistoryAsync(gleHistoryLogRequest);
    }

    public async Task<string> CalculateBillPaymentsStatusAsync(Guid orderId, IdsRequest productIdsRequest)
    {
        var orderHasBp = await productService.ProductsContainBillPaymentServiceOrBundleAsync(productIdsRequest);

        if (!orderHasBp)
        {
            return Constants.OrderBillPaymentsStatus.N_A;
        }

        return await GetGleRegistrationStatus(orderId);
    }

    public async Task<string?> CalculateGleRegistrationStatusForOrderWithBpAsync(Guid orderId)
    {
        var status = await GetGleRegistrationStatus(orderId);

        if (status == Constants.OrderBillPaymentsStatus.Pending)
        {
            logger.LogInformation("Oder with id: {id} was not found in GleTerminal", orderId);
            return null;
        }

        return status;
    }

    private async Task<string> GetGleRegistrationStatus(Guid orderId)
    {
        var isFullyRegistered = await gleTerminalRepository.OrderHasAllInstancesRegisteredToGle(orderId);

        return isFullyRegistered switch
        {
            null => Constants.OrderBillPaymentsStatus.Pending,
            true => Constants.OrderBillPaymentsStatus.Success,
            false => Constants.OrderBillPaymentsStatus.Fail
        };
    }

    private void ValidateRequest(ValidationResult validationResult)
    {
        if (!validationResult.IsValid)
        {
            var errorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
            logger.LogError("GLE Store Registration request validation failed: {@errors}", errorDescription);
            throw new ValidationException(validationResult);
        }
    }
}