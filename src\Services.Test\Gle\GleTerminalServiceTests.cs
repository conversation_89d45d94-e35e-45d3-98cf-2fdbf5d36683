﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using Common;
using Common.Entities.Gle;
using Common.Models;
using Common.Models.Gle;
using Common.Repositories.Gle;
using Common.Services;
using Common.Services.Gle;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;
using Services.Gle;
using Services.Test.TestData;

namespace Services.Test.Gle;

public class GleTerminalServiceTests
{
    private readonly ILogger<GleTerminalService> logger = Substitute.For<ILogger<GleTerminalService>>();
    private IMapper mapper;
    private readonly IGleTerminalRepository gleTerminalRepository = Substitute.For<IGleTerminalRepository>();
    private readonly IProductService productService = Substitute.For<IProductService>();
    private IGleTerminalService gleTerminalService;

    [SetUp]
    public void SetUp()
    {
        mapper = new Mapper(new MapperConfiguration(x => x.AddMaps(typeof(MappingProfileHelper))));
        gleTerminalService = new GleTerminalService(logger, mapper, gleTerminalRepository, productService);

        gleTerminalRepository.GetGleTerminalByProductInstanceIdAsync(Arg.Any<Guid>()).Returns(new GleTerminalEntity());
        gleTerminalRepository.OrderHasAllInstancesRegisteredToGle(Arg.Any<Guid>()).Returns(true);
        productService.ProductsContainBillPaymentServiceOrBundleAsync(Arg.Any<IdsRequest>()).Returns(true);
        gleTerminalRepository.GetGleRegistrationStatusHistoryAsync(Arg.Any<GleHistoryLogRequest>()).Returns(new List<GleRegistrationStatusInfo>{new()
            {
                RegistrationStatus = "Success.",
                ReferenceMMSId = "63046110012"
            }
        });

        gleTerminalRepository.ClearReceivedCalls();
    }

    [Test]
    public async Task AddGleTerminalAsync_WhenRequestIsValid_ShouldCallRepository()
    {
        //Arrange
        var createRequest = new GleTerminalRequest()
        {
            GleRegistrationStatus = Constants.GleRegistrationStatus.Failed,
            GleRegistrationResponse = "TID is missing!",
            ReferenceMmsId = "3472001",
            GleStoreId = Guid.NewGuid(),
            OrderId = Guid.NewGuid(),
            ProductInstanceId = Guid.NewGuid()
        };

        //Act and assert
        await gleTerminalService.Invoking(x => x.AddGleTerminalAsync(createRequest))
            .Should().NotThrowAsync<ValidationException>();

        await gleTerminalRepository.Received(1)
            .AddGle(Arg.Is<GleTerminalEntity>(x => x.ProductInstanceId == createRequest.ProductInstanceId));
    }

    [Test]
    [TestCase("Failed", null)]
    [TestCase(null, null)]
    public async Task AddGleTerminalAsync_WhenRequestIsInvalid_ShouldThrowValidationException(string status, string response)
    {
        //Arrange
        var createRequest = new GleTerminalRequest()
        {
            GleRegistrationStatus = status,
            GleRegistrationResponse = response,
            GleStoreId = Guid.NewGuid(),
            OrderId = Guid.NewGuid(),
            ProductInstanceId = Guid.NewGuid()
        };

        //Act and assert
        await gleTerminalService.Invoking(x => x.AddGleTerminalAsync(createRequest))
            .Should().ThrowAsync<ValidationException>();

        await gleTerminalRepository.DidNotReceive()
            .AddGle(Arg.Is<GleTerminalEntity>(x => x.ProductInstanceId == createRequest.ProductInstanceId));
    }

    [Test]
    [TestCase("GleRegistrationStatus", "Success")]
    [TestCase("GleRegistrationStatus", "Failed")]
    [TestCase("GleUserId", "357629293")]
    [TestCase("GleLoginId", "10000123")]
    [TestCase("GleLoginId2", "100001")]
    [TestCase("ParentGleUserId", "400091")]
    [TestCase("GleRegistrationResponse", "This is a test")]
    [TestCase("ReferenceMmsId", "100983")]
    [TestCase("TID", "874024")]
    public async Task UpdateGleTerminalAsync_WhenRequestIsValid_ShouldCallRepository(string path, string value)
    {
        //Arrange
        var gleStoreId = Guid.NewGuid();
        var updateDocument = new JsonPatchDocument<UpdateGleTerminalRequest>();
        updateDocument.Operations.Add(new Operation<UpdateGleTerminalRequest>
        {
            op = "replace",
            path = path,
            value = value
        });

        //Act and assert
        await gleTerminalService.Invoking(x => x.UpdateGleTerminalAsync(gleStoreId, updateDocument))
            .Should().NotThrowAsync<ValidationException>();

        await gleTerminalRepository.Received(1)
            .UpdateGle(gleStoreId, Arg.Any<JsonPatchDocument<GleTerminalEntity>>());
    }

    [Test]
    [TestCase("GleRegistrationStatus", "Pending")]
    [TestCase("GleRegistrationStatus", "Fail")]
    public async Task UpdateGleTerminalAsync_WhenRequestIsInvalid_ShouldThrowValidationException(string path, string value)
    {
        //Arrange
        var gleStoreId = Guid.NewGuid();
        var updateDocument = new JsonPatchDocument<UpdateGleTerminalRequest>();
        updateDocument.Operations.Add(new Operation<UpdateGleTerminalRequest>
        {
            op = "replace",
            path = path,
            value = value
        });

        //Act and assert
        await gleTerminalService.Invoking(x => x.UpdateGleTerminalAsync(gleStoreId, updateDocument))
            .Should().ThrowAsync<ValidationException>();

        await gleTerminalRepository.DidNotReceive()
            .UpdateGle(gleStoreId, Arg.Any<JsonPatchDocument<GleTerminalEntity>>());
    }

    [Test]
    public async Task GetGleTerminalByProductInstanceIdAsync_WhenRequestIsValid_ShouldCallRepository()
    {
        //Arrange
        var storeId = Guid.NewGuid();

        //Act
        var result = await gleTerminalService.GetGleTerminalByProductInstanceIdAsync(storeId);

        //Assert
        Assert.NotNull(result);
        await gleTerminalRepository.Received(1).GetGleTerminalByProductInstanceIdAsync(storeId);
    }

    [Test]
    public async Task GetGleTerminalByProductInstanceIdAsync_WhenRequestIsInvalid_ShouldThrowServiceException()
    {
        //Act and assert
        await gleTerminalService.Invoking(x => x.GetGleTerminalByProductInstanceIdAsync(Guid.Empty))
            .Should().ThrowAsync<ServiceException>();

        await gleTerminalRepository.DidNotReceive().GetGleTerminalByProductInstanceIdAsync(Arg.Any<Guid>());
    }

    [Test]
    public async Task CalculateBillPaymentsStatusAsync_WhenCalled_RepositoryIsCalled()
    {
        //Arrange
        var orderId = Guid.NewGuid();
        var idsRequest = new IdsRequest
        {
            Ids = new[] { Guid.NewGuid(), Guid.NewGuid() }
        };

        //Act
        var result = await gleTerminalService.CalculateBillPaymentsStatusAsync(orderId, idsRequest);

        result.Should().NotBeNullOrEmpty();

        await gleTerminalRepository.Received(1).OrderHasAllInstancesRegisteredToGle(orderId);
        await productService.Received(1).ProductsContainBillPaymentServiceOrBundleAsync(idsRequest);
    }

    [Test]
    public async Task CalculateGleRegistrationStatusForOrderWithBP_WhenCalled_RepositoryIsCalled()
    {
        var orderId = Guid.NewGuid();
        var result = await gleTerminalService.CalculateGleRegistrationStatusForOrderWithBpAsync(orderId);

        result.Should().NotBeNullOrEmpty();

        await gleTerminalRepository.Received(1).OrderHasAllInstancesRegisteredToGle(orderId);
    }

    [Test]
    public async Task GetGleRegistrationStatusHistoryAsync_WhenCalled_BaseRepositoryIsCalled()
    {
        var request = new GleHistoryLogRequest
        {
            MerchantId = Guid.NewGuid(),
            OrderId = Guid.NewGuid(),
            ParentMerchantId = Guid.NewGuid(),
        };
        var result = await gleTerminalService.GetGleRegistrationStatusHistoryAsync(request);

        result.Should().NotBeNullOrEmpty();

        await gleTerminalRepository.Received(1).GetGleRegistrationStatusHistoryAsync(request);
    }

    [Test]
    public async Task GetGleRegistrationStatusHistoryAsync_WhenInvalidRequest_ThrowsException()
    {
        var request = new GleHistoryLogRequest
        {
            MerchantId = Guid.Empty,
            OrderId = Guid.NewGuid()
        };
        await gleTerminalService.Invoking(x => x.GetGleRegistrationStatusHistoryAsync(request))
            .Should().ThrowAsync<ServiceException>();

        await gleTerminalRepository.DidNotReceive().GetGleRegistrationStatusHistoryAsync(request);
    }
}