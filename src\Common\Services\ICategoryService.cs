﻿using Common.Entities;
using Common.Models.CategoryRequests;
using Microsoft.AspNetCore.JsonPatch;
using System;
using System.Threading.Tasks;
using Common.Models;
using System.Collections.Generic;

namespace Common.Services;

public interface ICategoryService
{
    public Task<Category> CreateAsync(CategoryRequest categoryRequest);

    public Task DeleteAsync(Guid categoryId);

    public Task<Category[]> FindAsync(FindCategoryRequest findCategoryRequest);

    public Task<Category> PatchAsync(Guid categoryId, JsonPatchDocument<CategoryRequest> patchCategory);
    public Task<List<CategoriesListResponse>> GetCategoriesList();

}
