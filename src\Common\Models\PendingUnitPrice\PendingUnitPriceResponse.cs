﻿using Common.Entities;
using Common.Enums.UnitPrice;
using Common.Models.UnitPrice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.PendingUnitPrice;
public class PendingUnitPriceResponse
{
    public List<UnitPriceDetails> CreatedUnitPrices { get; set; } = new List<UnitPriceDetails>();
    public List<UnitPriceDetails> ExistingUnitPrices { get; set; } = new List<UnitPriceDetails>();
    public List<UnitPriceDetails> ExistingPendingUnitPrices { get; set; } = new List<UnitPriceDetails>();
}


