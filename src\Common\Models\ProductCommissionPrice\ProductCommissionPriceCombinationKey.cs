﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.ProductCommissionPrice;
public struct ProductCommissionPriceCombinationKey
{
    public Guid ProductID { get; }
    public Guid MCCID { get; }
    public Guid BusinessTypeID { get; }
    public Guid CommissionID { get; }

    public ProductCommissionPriceCombinationKey(Guid productId, Guid mccId, Guid businessTypeId, Guid CommissionId)
    {
        ProductID = productId;
        MCCID = mccId;
        BusinessTypeID = businessTypeId;
        CommissionID = CommissionId;
    }
    public override bool Equals(object? obj)
    {
        if (obj is ProductCommissionPriceCombinationKey key)
        {
            return ProductID == key.ProductID && MCCID == key.MCCID && BusinessTypeID == key.BusinessTypeID
                && CommissionID == key.CommissionID;
        }
        return false;
    }
    public override int GetHashCode()
    {
        return HashCode.Combine(ProductID, MCCID, BusinessTypeID, CommissionID);
    }
    public static bool operator ==(ProductCommissionPriceCombinationKey left, ProductCommissionPriceCombinationKey right)
    {
        return left.Equals(right);
    }
    public static bool operator !=(ProductCommissionPriceCombinationKey left, ProductCommissionPriceCombinationKey right)
    {
        return !(left == right);
    }
}
