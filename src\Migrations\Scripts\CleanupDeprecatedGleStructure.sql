﻿-- =============================================
-- Create date: 25.11.2022
-- Related JIRA item: GDADV005-42179
-- Description: Delete old GLE structure (GleInstances, GetGleRegistrationStatusHistory) after data migration was completed.
-- =============================================

-- @applyChanges = 0 TO run the script IN TEST mode without making ANY changes
-- @applyChanges = 1 to execute the script and perform changes
DECLARE @applyChanges BIT = 1 --1 COMMIT; 0 ROLLBACK the following TRANSACTION
DECLARE @NumberOfLeftoverMerchants AS INT;
DECLARE @NumberOfLeftoverStores AS INT;
DECLARE @NumberOfLeftoverTerminals AS INT;

USE PRODUCTS;

BEGIN TRY
	BEGIN TRAN

	IF (
			EXISTS (
				SELECT *
				FROM INFORMATION_SCHEMA.TABLES
				WHERE TABLE_SCHEMA = 'dbo'
					AND TABLE_NAME = 'GleInstances'
				)
			)
	BEGIN
		PRINT ('Step 1: Check to see if all the old data was migrated to the new tables')
		
		PRINT ('Step 1.1: Number of leftover merchants')
		SELECT @NumberOfLeftoverMerchants = COUNT(*)
		FROM [dbo].[GleInstances]
		WHERE [MerchantId] NOT IN (
				SELECT [MerchantId]
				FROM [dbo].[GleMerchant]
				)

		PRINT ('Step 1.2: Number of leftover stores')
		SELECT @NumberOfLeftoverStores = COUNT(*)
		FROM [dbo].[GleInstances]
		WHERE [StoreId] NOT IN (
				SELECT [StoreId]
				FROM [dbo].[GleStore]
				)

		PRINT ('Step 1.3: Number of leftover terminals')
		SELECT @NumberOfLeftoverTerminals = COUNT(*)
		FROM [dbo].[GleInstances]
		WHERE [ProductInstanceId] NOT IN (
				SELECT [ProductInstanceId]
				FROM [dbo].[GleTerminal]
				)

		IF (@NumberOfLeftoverMerchants = 0
				AND @NumberOfLeftoverStores = 0
				AND @NumberOfLeftoverTerminals = 0)
		BEGIN
			PRINT ('Step 2: Drop deprecated GLE stored procedure')
			DROP PROCEDURE IF EXISTS [dbo].[GetGleRegistrationStatusHistory];
			
			PRINT ('Step 3: Drop deprecated GLE table')
			
			PRINT ('Step 3.1: Set SYSTEM_VERSIONING OFF for [GleInstances] table')
			ALTER TABLE [dbo].[GleInstances] SET (SYSTEM_VERSIONING = OFF)

			PRINT ('Step 3.2: Drop [GleInstances] table')
			DROP TABLE [dbo].[GleInstances]

			PRINT ('Step 3.3: Drop [History].[GleInstances] history table')
			DROP TABLE [History].[GleInstances]
		END
		ELSE
		BEGIN
			PRINT ('Some records were not migrated correctly!')
			PRINT (@NumberOfLeftoverMerchants)
			PRINT (@NumberOfLeftoverStores)
			PRINT (@NumberOfLeftoverTerminals)
		END
	END

	-------- commit / rollback
	IF @applyChanges = 1
	BEGIN
		PRINT 'COMMIT TRANSACTION ...'

		COMMIT;
	END
	ELSE
	BEGIN
		PRINT 'ROLLBACK TRANSACTION ...'

		ROLLBACK;
	END
END TRY

BEGIN CATCH
	PRINT 'ERROR. ROLLBACK TRANSACTION ...'

	ROLLBACK;

	THROW;
END CATCH