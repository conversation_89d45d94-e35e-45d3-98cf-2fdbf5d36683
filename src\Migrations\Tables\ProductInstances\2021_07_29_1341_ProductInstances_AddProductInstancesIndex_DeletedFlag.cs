﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_07_29_1341)]
public class ProductInstances_AddProductInstancesIndex_DeletedFlag : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(
            $@"IF NOT EXISTS (SELECT NULL FROM sys.indexes WHERE name = 'IDX_ProductInstances_DeletedFlag' AND object_id = OBJECT_ID('[dbo].[ProductInstances]'))
                 BEGIN
                    CREATE NONCLUSTERED INDEX [IDX_ProductInstances_DeletedFlag]
                                ON [dbo].[ProductInstances] ([DeletedFlag])
                                INCLUDE ([CreatedDate])
                 END");
    }
}
