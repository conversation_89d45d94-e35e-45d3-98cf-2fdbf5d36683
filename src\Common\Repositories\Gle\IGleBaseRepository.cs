﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Models.Gle;
using Geidea.Utils.DataAccess.Entities;
using Microsoft.AspNetCore.JsonPatch;

namespace Common.Repositories.Gle;

public interface IGleBaseRepository<T> where T : AuditableEntity<Guid>
{
    Task AddGle(T entityToBeAdded);
    Task UpdateGle(Guid gleId, JsonPatchDocument<T> updateDocument);
    Task<IReadOnlyCollection<GleRegistrationStatusInfo>> GetGleRegistrationStatusHistoryAsync(GleHistoryLogRequest gleHistoryLogRequest);
}