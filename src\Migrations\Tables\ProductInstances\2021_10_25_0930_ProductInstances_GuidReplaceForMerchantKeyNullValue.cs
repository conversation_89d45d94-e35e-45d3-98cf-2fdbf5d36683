﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_10_25_0930)]
public class ProductInstances_GuidReplaceForMerchantKeyNullValue : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
UPDATE pi
SET Metadata = JSON_MODIFY(pi.Metadata, '$.MerchantGatewayKey', convert(nvarchar(50), LOWER(NEWID())))
FROM ProductInstances pi
INNER JOIN Products p ON p.id = pi.ProductId
WHERE p.Type = 'GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) = 1
AND JSON_VALUE(pi.Metadata, '$.MerchantGatewayKey') IS NULL
");
    }
}
