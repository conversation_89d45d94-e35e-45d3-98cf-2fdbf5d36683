﻿using System;

namespace Common.Models.ProductInstance;

public class UpdateProductInstanceRequest
{
    public Guid? CompanyId { get; set; }
    public Guid? StoreId { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public Guid? ParentId { get; set; }
    public Guid? ParentConfigurationId { get; set; }
    public object? Data { get; set; }
    public string? EPosTicketId { get; set; }
    public bool? EPosTicketCompleted { get; set; }
    public bool? EPosBillPayments { get; set; }
    public DateTime? EPosLastUpdated { get; set; }
    public string? Mid { get; set; }
}
