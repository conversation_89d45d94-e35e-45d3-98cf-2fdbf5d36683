﻿using Common;
using Common.Entities;
using Common.Models.Vendor;
using Common.Repositories;
using DataAccess.Mapping;
using Geidea.Utils.DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Net;
using System.Threading.Tasks;

namespace DataAccess.Repositories;
public class VendorRepository : AuditableRepository<Guid, VendorEntity>, IVendorRepository
{
    private readonly ILogger<VendorRepository> logger;
    public VendorRepository(DbContext context, IHttpContextAccessor contextAccessor, ILogger<VendorRepository> logger) : base(context, contextAccessor)
    {
        this.logger = logger;
    }

    public async Task<bool> IsVendorExistWithSameNameAndType(VendorRequest request)
    {
        return await context.Set<VendorEntity>()
            .AnyAsync(p => p.Name.Replace(" ", "").ToLower() == request.Name.Replace(" ", "").ToLower() && p.TerminalType == request.TerminalType);
    }

    public async Task<VendorResponse> CreateAsync(VendorRequest vendor)
    {
        var vendorEntity = VendorMapper.Map(vendor);

        this.Add(vendorEntity);
        await context.SaveChangesAsync();

        return VendorMapper.Map(vendorEntity)!;
    }

    public async Task<VendorResponse?> GetByIdAsync(Guid vendorId)
    {
        var vendor = await context.Set<VendorEntity>().FirstOrDefaultAsync(v => v.Id == vendorId);
        return VendorMapper.Map(vendor);
    }

    public async Task<List<VendorResponse>?> GetAsync(string? terminalType = null)
    {
        var vendorsQuery = context.Set<VendorEntity>().AsNoTracking();

        if (terminalType != null)
        {
            vendorsQuery = vendorsQuery.Where(v => v.TerminalType == terminalType);
        }

        return VendorMapper.Map(await vendorsQuery.ToListAsync());
    }

    public async Task<VendorResponse?> GetDefaultVendorAsync()
    {
        var vendor = await context.Set<VendorEntity>().SingleOrDefaultAsync(v => v.IsDefault);
        return VendorMapper.Map(vendor);
    }

    public async Task<VendorResponse?> SetDefaultVendorAsync(Guid vendorId)
    {
        var executionStrategy = context.Database.CreateExecutionStrategy();

        return await executionStrategy.ExecuteAsync(async () =>
        {
            try
            {
                List<VendorEntity> vendorUpdatedEntities = new List<VendorEntity>();

                var oldDefaultVendor = await updateVendorIsDefault(false, v => v.IsDefault, false);
                if (oldDefaultVendor != null)
                {
                    vendorUpdatedEntities.Add(oldDefaultVendor);
                }
                var vendor = await updateVendorIsDefault(true, v => v.Id == vendorId, true);
                vendorUpdatedEntities.Add(vendor!);

                context.Set<VendorEntity>().UpdateRange(vendorUpdatedEntities);
                await context.SaveChangesAsync();

                return VendorMapper.Map(vendor);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error when setting new default vendor.");
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidSetDefaultVendorRequest);
            }
        });
    }

    public async Task<VendorEntity?> updateVendorIsDefault(bool IsDefault, Expression<Func<VendorEntity, bool>> predicate, bool throwErrorFlag)
    {

        var vendor = await context.Set<VendorEntity>().SingleOrDefaultAsync(predicate);
        if (vendor != null)
        {
            vendor.IsDefault = IsDefault;
        }
        else if (throwErrorFlag)
        {
            throw new ServiceException(HttpStatusCode.NotFound, Errors.VendorNotFound);
        }
        return vendor;
    }

    public async Task<VendorSearchResponse> AdvancedSearch(VendorSearchRequest searchRequest)
    {
        var vendors = context.Set<VendorEntity>().AsNoTracking();

        vendors = ApplyVendorSearch(searchRequest, vendors);
        vendors = ApplyVendorFilterList(searchRequest, vendors);

        int totalCount = vendors.Count();

        var result = await vendors
            .Skip(searchRequest.Skip)
            .Take(searchRequest.Take)
            .OrderBy(searchRequest.OrderBy)
            .ToListAsync();

        return VendorMapper.Map(result, totalCount);
    }

    private static IQueryable<T> ApplyVendorSearch<T>(VendorSearchRequest filter,
        IQueryable<T> vendorSearch) where T : VendorEntity
    {
        if (!string.IsNullOrEmpty(filter.Name))
            return vendorSearch.Where(v => v.Name.Contains(filter.Name));

        return vendorSearch;
    }

    private static IQueryable<T> ApplyVendorFilterList<T>(VendorSearchRequest filter,
        IQueryable<T> vendorSearch) where T : VendorEntity
    {
        if (filter.TerminalType != null && filter.TerminalType.Any())
            return vendorSearch.Where(v => filter.TerminalType.Contains(v.TerminalType));

        return vendorSearch;
    }
}