﻿using System;
using System.Collections.Generic;

namespace Common.Models;

public class ProductShortResponse
{
    public Guid ProductId { get; set; }
    public string Availability { get; set; } = null!;
    public string Code { get; set; } = null!;
    public int Version { get; set; }
    public string Type { get; set; } = null!;
    public string? Description { get; set; }
    public int? DisplayOrder { get; set; }
    public bool DeletedFlag { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public List<PriceShortResponse> Prices { get; set; } = new List<PriceShortResponse>();
}
