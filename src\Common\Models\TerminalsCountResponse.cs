﻿using Newtonsoft.Json;

namespace Common.Models;

public class TerminalsCountResponse
{
    [JsonProperty("availableSmartPosTerminals")]
    public int AvailableSmartPosTerminals { get; set; }

    [JsonProperty("availableSoftPosTerminals")]
    public int AvailableSoftPosTerminals { get; set; }

    [JsonProperty("validTerminalDataSetCount")]
    public int ValidTerminalDataSetCount { get; set; }
}
