﻿using AutoMapper;
using Common.Data.ProductType;
using Common.Entities;
using Common.Models.ProductInstance;
using Geidea.ProductService.Models;
using NUnit.Framework;
using ProductService;
using System;
using System.Collections.Generic;
using System.Linq;
using GatewayData = Common.Data.GatewayData;

namespace Common.Tests;

public class GatewayDataSerializationTests
{
    private static readonly DateTime souhoolaCpLogChangeDate = DateTime.Now;

    private static readonly GatewayData createProductInstanceRequestGatewayData = new GatewayData
    {
        MerchantGatewayKey = "cdc37be7-ed01-47a4-af06-a4c8c0d103b2",
        MpgsAccounts = new List<MpgsAccount>
        {
            new MpgsAccount
            {
                MpgsMerchantId = "TESTENDAVA",
                MpgsApiKey = "ec05ce4c82047a25b0e2bdfca68cf8e5",
            }
        },
        GsdkTid = "FED1",
        GsdkMid = "FED1",
        GsdkSecretKey = "PD2JFIO3AWEAH5FIYLASS9KK3QHGKAQBPPLE",
        CyberSourceMerchantId = "geidea_test",
        CyberSourceMerchantKeyId = "5cc64896-68d2-429b-9861-b3c46d61febd",
        CyberSourceSharedSecretKey = "VFSU7YYbRyTNfZvtFnaAXlZ69fuZq6LaNZkJbxN34ng",
        DefaultPaymentOperation = "Pay",
        ApiPassword = "TEST_1234567890",
        IsTest = true,
        ApplePartnerInternalMerchantIdentifier = "dd9a6b47-c1da-4cfc-8929-5daca1f9932",
        MerchantName = "Common merchant EN",
        MerchantNameAr = "Common merchant AR",
        MerchantWebsite = "Apple",
        Mcc = "1234",
        AllowedInitiatedByValues = new List<string> { Constants.InitiatedByInternet },
        MinAmount = 10,
        MaxAmount = 10000,
        IsValuBnplEnabled = true,
        IsBankInstallmentsCnpEnabled = true,
        ValuProductId = "123",
        ValuStoreId = "234",
        ValuVendorId = "345",
        IsShahryCpBnplEnabled = true,
        ShahryCpBnplBranchCode = "123456",
        ShahryCpBnplMerchantCode = "654321",
        IsSouhoolaCnpBnplEnabled = true,
        SouhoolaMerchantNationalId = "123",
        SouhoolaMerchantPhoneNumber = "234",
        SouhoolaAccessKey = "345",
        IsShahryCnpBnplEnabled = true,
        ShahryCnpBnplBranchCode = "112233",
        ShahryCnpBnplMerchantCode = "445566",
        IsSouhoolaCpBnplEnabled = true,
        IsSouhoolaCpMerchantRegistered = false,
        SouhoolaCpBnplNationalId = "**************",
        SouhoolaCpBnplGlobalId = "11756",
        SouhoolaCpBnplUserName = "test",
        SouhoolaCpBnplPassword = "test",
        IsCustomerBillingAddressMandatory = true,
        IsCustomerEmailMandatory = true,
        IsCustomerShippingAddressMandatory = true,
        AllowCashOnDeliveryShahry = false,
        AllowCashOnDeliverySouhoola = false,
        AllowCashOnDeliveryValu = false,
        AllowDownPaymentValu = false,
        EnableSubMerchantInformation = true,
        IsRecurringPaylinksEnabled = false,
        IsTokenizationEnabled = true,
        IsRecurringPaymentsEnabled = false,
        IsCustomerInitiatedTransactionsEnabled = false,
        IsMerchantInitiatedTransactionsEnabled = false,
        IsUnscheduledPaymentsEnable = false,
        IsSmartRoutingEnabled = true,
        IsExcessiveCaptureEnabled = false,
        GooglePayMerchantId = "123456",
        IsGooglePayEnabled = true,
        IsTamaraEnabled = true,
        TamaraPublicKey = "123456",
        TamaraApiToken = "123456",
        StcPayMerchantId = "123456",
        IsStcPayEnabled = true,
        AddOnFeesPbl = new AddOnFees()
        {
            IsEnabled = true,
            Fees = new CafData()
            {
                Label = "AddOnFee",
                Value = 10,
                ValueType = AmountType.Percentage
            }
        },
        ExtraChargesPbl = new ExtraCharges()
        {
            IsEnabled = true,
            Charges = new CafData()
            {
                Label = "ExtraCharges",
                Value = 10,
                ValueType = AmountType.Amount
            }
        },
        MadaAccounts = new List<MadaAccount>
        {
            new()
            {
                MadaApiKey = "TESTMERCH_S_25P",
                MadaMerchantId = "5ecb4ccb35eeb498a0d2b8ba46486a4c",
                CardInfo = new List<CardInfo>
                {
                    new()
                    {
                        CardBrand = "MC_GW",
                        PrimaryGatewayId = "RJPY"
                    }
                }
            }
        },
        SouhoolaCpCredentialsLogs = new List<SouhoolaCpCredentialsLog>
        {
            new SouhoolaCpCredentialsLog
            {
                CreationDate = souhoolaCpLogChangeDate,
                Event = "User Regiseration",
                UserName = "test",
            }
        },
        IsSamsungPayWebEnabled = false,
        IsPartialRefundEnabled = true,
        IsAuthorizationRefundEnable = true,
        IsCaptureEnabled = true,
        IsPartialCaptureEnabled = true,
        IsUpdateAuthorizationPercentageEnabled = false,
        IsUpdateAuthorizationEnabled = false,
        UpdateAuthorizationPercentage = 0,
        IsGenerateAndUseNetworkTokenEnabled = false,
        IsUseNetworkTokenEnabled = false,
        IsSendNetworkTokenToMerchantEnabled = false,
        NetworkTokenCallBackUrl = "https://some-dummy-redirect-url.com",
        NetworkTokenEncryptionKey = "a50e8a13-7dc8-4ce8-99f8-c92a08bc8e19"
    };

    private static readonly CreateProductInstanceRequest createProductInstanceRequest = new CreateProductInstanceRequest
    {
        AgreementId = Guid.NewGuid(),
        Data = createProductInstanceRequestGatewayData
    };

    private static readonly ProductEntity ProductEntity = new ProductEntity
    {
        Type = ProductTypes.GWAY.ToString()
    };

    private IMapper mapper;


    [SetUp]
    public void Setup()
    {
        var profile = new AutoMapping();
        var config = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        mapper = new Mapper(config);
    }

    [Test]
    public void GatewayDataSerializationTest()
    {
        var productInstance = mapper.Map<ProductInstanceEntity>(createProductInstanceRequest);
        productInstance.Product = ProductEntity;

        Assert.That(productInstance, Is.Not.Null, "Result should not be null");

        var gatewayData = productInstance.Data as GatewayData;
        Assert.That(gatewayData, Is.Not.Null, "GatewayData should not be null");
        Assert.That(gatewayData.MpgsAccounts.Count, Is.EqualTo(createProductInstanceRequestGatewayData.MpgsAccounts.Count), "MpgsAccounts count should be same");
        Assert.That(gatewayData.MpgsAccounts.First().MpgsMerchantId, Is.EqualTo(createProductInstanceRequestGatewayData.MpgsAccounts.First().MpgsMerchantId), "MpgsMerchantId value should be same");
        Assert.That(gatewayData.MpgsAccounts.First().MpgsApiKey, Is.EqualTo(createProductInstanceRequestGatewayData.MpgsAccounts.First().MpgsApiKey), "MpgsApiKey value should be same");
        Assert.That(gatewayData.MpgsAccounts.First().CardBrands.Count, Is.EqualTo(createProductInstanceRequestGatewayData.MpgsAccounts.First().CardBrands.Count), "CardBrands count should be same");
        Assert.That(gatewayData.MpgsAccounts.First().Currencies.Count, Is.EqualTo(createProductInstanceRequestGatewayData.MpgsAccounts.First().Currencies.Count), "CardBrands count should be same");
        Assert.That(gatewayData.MaxAmount, Is.EqualTo(createProductInstanceRequestGatewayData.MaxAmount), "MaxAmount value should be same");
        Assert.That(gatewayData.MinAmount, Is.EqualTo(createProductInstanceRequestGatewayData.MinAmount), "MinAmount value should be same");
        Assert.That(gatewayData.IsValuBnplEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsValuBnplEnabled), "isValuBnplEnabled value should be same");
        Assert.That(gatewayData.IsBankInstallmentsCnpEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsBankInstallmentsCnpEnabled), "IsBankInstallmentsCnpEnabled value should be same");
        Assert.That(gatewayData.ValuProductId, Is.EqualTo(createProductInstanceRequestGatewayData.ValuProductId), "ValuProductId value should be same");
        Assert.That(gatewayData.ValuStoreId, Is.EqualTo(createProductInstanceRequestGatewayData.ValuStoreId), "ValuStoreId value should be same");
        Assert.That(gatewayData.ValuVendorId, Is.EqualTo(createProductInstanceRequestGatewayData.ValuVendorId), "ValuVendorId value should be same");
        Assert.That(gatewayData.IsShahryCpBnplEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsShahryCpBnplEnabled), "IsShahryCpBnplEnabled value should be same");
        Assert.That(gatewayData.ShahryCpBnplBranchCode, Is.EqualTo(createProductInstanceRequestGatewayData.ShahryCpBnplBranchCode), "ShahryCpBnplBranchCode value should be same");
        Assert.That(gatewayData.ShahryCpBnplMerchantCode, Is.EqualTo(createProductInstanceRequestGatewayData.ShahryCpBnplMerchantCode), "ShahryCpBnplMerchantCode value should be same");
        Assert.That(gatewayData.IsSouhoolaCnpBnplEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsSouhoolaCnpBnplEnabled), "IsSouhoolaCnpBnplEnabled value should be same");
        Assert.That(gatewayData.SouhoolaMerchantNationalId, Is.EqualTo(createProductInstanceRequestGatewayData.SouhoolaMerchantNationalId), "SouhoolaMerchantNationalId value should be same");
        Assert.That(gatewayData.SouhoolaMerchantPhoneNumber, Is.EqualTo(createProductInstanceRequestGatewayData.SouhoolaMerchantPhoneNumber), "SouhoolaMerchantPhoneNumber value should be same");
        Assert.That(gatewayData.SouhoolaAccessKey, Is.EqualTo(createProductInstanceRequestGatewayData.SouhoolaAccessKey), "SouhoolaAccessKey value should be same");
        Assert.That(gatewayData.IsShahryCnpBnplEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsShahryCnpBnplEnabled), "IsShahryCnpBnplEnabled value should be same");
        Assert.That(gatewayData.ShahryCnpBnplBranchCode, Is.EqualTo(createProductInstanceRequestGatewayData.ShahryCnpBnplBranchCode), "ShahryCnpBnplBranchCode value should be same");
        Assert.That(gatewayData.ShahryCnpBnplMerchantCode, Is.EqualTo(createProductInstanceRequestGatewayData.ShahryCnpBnplMerchantCode), "ShahryCnpBnplMerchantCode value should be same");
        Assert.That(gatewayData.IsSouhoolaCpBnplEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsSouhoolaCpBnplEnabled), "IsSouhoolaCpBnplEnabled value should be same");
        Assert.That(gatewayData.IsSouhoolaCpMerchantRegistered, Is.EqualTo(createProductInstanceRequestGatewayData.IsSouhoolaCpMerchantRegistered), "IsSouhoolaCpMerchantRegistered value should be same");
        Assert.That(gatewayData.SouhoolaCpBnplNationalId, Is.EqualTo(createProductInstanceRequestGatewayData.SouhoolaCpBnplNationalId), "SouhoolaCpBnplNationalId value should be same");
        Assert.That(gatewayData.SouhoolaCpBnplGlobalId, Is.EqualTo(createProductInstanceRequestGatewayData.SouhoolaCpBnplGlobalId), "SouhoolaCpBnplGlobalId value should be same");
        Assert.That(gatewayData.SouhoolaCpBnplUserName, Is.EqualTo(createProductInstanceRequestGatewayData.SouhoolaCpBnplUserName), "SouhoolaCpBnplUserName value should be same");
        Assert.That(gatewayData.SouhoolaCpBnplPassword, Is.EqualTo(createProductInstanceRequestGatewayData.SouhoolaCpBnplPassword), "SouhoolaCpBnplPassword value should be same");
        Assert.That(gatewayData.IsCustomerBillingAddressMandatory, Is.EqualTo(createProductInstanceRequestGatewayData.IsCustomerBillingAddressMandatory), "IsCustomerBillingAddressMandatory value should be same");
        Assert.That(gatewayData.IsCustomerShippingAddressMandatory, Is.EqualTo(createProductInstanceRequestGatewayData.IsCustomerShippingAddressMandatory), "IsCustomerShippingAddressMandatory value should be same");
        Assert.That(gatewayData.IsCustomerEmailMandatory, Is.EqualTo(createProductInstanceRequestGatewayData.IsCustomerEmailMandatory), "IsCustomerEmailMandatory value should be same");
        Assert.That(gatewayData.AllowCashOnDeliveryShahry, Is.EqualTo(createProductInstanceRequestGatewayData.AllowCashOnDeliveryShahry), "AllowCashOnDeliveryShahry value should be same");
        Assert.That(gatewayData.AllowCashOnDeliverySouhoola, Is.EqualTo(createProductInstanceRequestGatewayData.AllowCashOnDeliverySouhoola), "AllowCashOnDeliverySouhoola value should be same");
        Assert.That(gatewayData.AllowCashOnDeliveryValu, Is.EqualTo(createProductInstanceRequestGatewayData.AllowCashOnDeliveryValu), "AllowCashOnDeliveryValu value should be same");
        Assert.That(gatewayData.AllowDownPaymentValu, Is.EqualTo(createProductInstanceRequestGatewayData.AllowDownPaymentValu), "AllowCashOnDeliveryValu value should be same");
        Assert.That(gatewayData.EnableSubMerchantInformation, Is.EqualTo(createProductInstanceRequestGatewayData.EnableSubMerchantInformation), "EnableSubMerchantInformation value should be same");
        Assert.That(gatewayData.AddOnFeesPbl.IsEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.AddOnFeesPbl.IsEnabled), "IsAddOnFeesEnabled value should be same");
        Assert.That(gatewayData.AddOnFeesPbl.Fees.Label, Is.EqualTo(createProductInstanceRequestGatewayData.AddOnFeesPbl.Fees.Label), "AddOnFees Label value should be same");
        Assert.That(gatewayData.AddOnFeesPbl.Fees.Value, Is.EqualTo(createProductInstanceRequestGatewayData.AddOnFeesPbl.Fees.Value), "AddOnFees value should be same");
        Assert.That(gatewayData.AddOnFeesPbl.Fees.ValueType, Is.EqualTo(createProductInstanceRequestGatewayData.AddOnFeesPbl.Fees.ValueType), "AddOnFees valueType should be same");
        Assert.That(gatewayData.ExtraChargesPbl.IsEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.ExtraChargesPbl.IsEnabled), "IsExtraChargesEnabled value should be same");
        Assert.That(gatewayData.ExtraChargesPbl.Charges.Label, Is.EqualTo(createProductInstanceRequestGatewayData.ExtraChargesPbl.Charges.Label), "ExtraCharges Label value should be same");
        Assert.That(gatewayData.ExtraChargesPbl.Charges.Value, Is.EqualTo(createProductInstanceRequestGatewayData.ExtraChargesPbl.Charges.Value), "ExtraCharges amount value should be same");
        Assert.That(gatewayData.ExtraChargesPbl.Charges.ValueType, Is.EqualTo(createProductInstanceRequestGatewayData.ExtraChargesPbl.Charges.ValueType), "ExtraCharges valueType should be same");
        Assert.That(gatewayData.MadaAccounts.Single().CardInfo.Count, Is.EqualTo(createProductInstanceRequestGatewayData.MadaAccounts.Single().CardInfo.Count), "CardInfo count should be same");
        Assert.That(gatewayData.MadaAccounts.Single().CardInfo.Single().PrimaryGatewayId, Is.EqualTo(createProductInstanceRequestGatewayData.MadaAccounts.Single().CardInfo.Single().PrimaryGatewayId), "PrimaryGatewayId value should be same");
        Assert.That(gatewayData.MadaAccounts.Single().CardInfo.Single().CardBrand, Is.EqualTo(createProductInstanceRequestGatewayData.MadaAccounts.Single().CardInfo.Single().CardBrand), "CardBrand value should be same");
        Assert.That(gatewayData.MadaAccounts.Single().MadaApiKey, Is.EqualTo(createProductInstanceRequestGatewayData.MadaAccounts.Single().MadaApiKey), "MadaApiKey value should be same");
        Assert.That(gatewayData.MadaAccounts.Single().MadaMerchantId, Is.EqualTo(createProductInstanceRequestGatewayData.MadaAccounts.Single().MadaMerchantId), "MadaMerchantId value should be same");
        Assert.That(gatewayData.SouhoolaCpCredentialsLogs.Count, Is.EqualTo(createProductInstanceRequestGatewayData.SouhoolaCpCredentialsLogs.Count), "SouhoolaCpCredentialsLogs count should be same");
        Assert.That(gatewayData.SouhoolaCpCredentialsLogs.First().CreationDate, Is.EqualTo(createProductInstanceRequestGatewayData.SouhoolaCpCredentialsLogs.First().CreationDate), "SouhoolaCpLogCreationDate value should be same");
        Assert.That(gatewayData.SouhoolaCpCredentialsLogs.First().Event, Is.EqualTo(createProductInstanceRequestGatewayData.SouhoolaCpCredentialsLogs.First().Event), "SouhoolaCpLogEvent value should be same");
        Assert.That(gatewayData.SouhoolaCpCredentialsLogs.First().UserName, Is.EqualTo(createProductInstanceRequestGatewayData.SouhoolaCpCredentialsLogs.First().UserName), "SouhoolaCpLogUserName value should be same");
        Assert.That(gatewayData.IsRecurringPaylinksEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsRecurringPaylinksEnabled), "IsRecurringPaylinksEnabled value should be same");
        Assert.That(gatewayData.IsRecurringPaymentsEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsRecurringPaymentsEnabled), "IsRecurringPaymentsEnabled value should be same");
        Assert.That(gatewayData.IsTokenizationEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsTokenizationEnabled), "IsTokenizationEnabled value should be same");
        Assert.That(gatewayData.IsMerchantInitiatedTransactionsEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsMerchantInitiatedTransactionsEnabled), "IsMerchantInitiatedTransactionsEnabled value should be same");
        Assert.That(gatewayData.IsCustomerInitiatedTransactionsEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsCustomerInitiatedTransactionsEnabled), "IsCustomerInitiatedTransactionsEnabled value should be same");
        Assert.That(gatewayData.IsSmartRoutingEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsSmartRoutingEnabled), "IsSmartRoutingEnabled value should be same");
        Assert.That(gatewayData.IsExcessiveCaptureEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsExcessiveCaptureEnabled), "IsExcessiveCaptureEnabled value should be same");
        Assert.That(gatewayData.GooglePayMerchantId, Is.EqualTo(createProductInstanceRequestGatewayData.GooglePayMerchantId), "GooglePayMerchantId value should be same");
        Assert.That(gatewayData.IsGooglePayEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsGooglePayEnabled), "IsGooglePayEnabled value should be same");
        Assert.That(gatewayData.TamaraApiToken, Is.EqualTo(createProductInstanceRequestGatewayData.TamaraApiToken), "TamaraApiToken value should be same");
        Assert.That(gatewayData.TamaraPublicKey, Is.EqualTo(createProductInstanceRequestGatewayData.TamaraPublicKey), "TamaraPublicKey value should be same");
        Assert.That(gatewayData.IsTamaraEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsTamaraEnabled), "IsTamaraEnabled value should be same");
        Assert.That(gatewayData.IsSamsungPayWebEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsSamsungPayWebEnabled), "webEnabled value should be same");
        Assert.That(gatewayData.StcPayMerchantId, Is.EqualTo(createProductInstanceRequestGatewayData.StcPayMerchantId), "StcPayMerchantId value should be same");
        Assert.That(gatewayData.IsStcPayEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsStcPayEnabled), "IsStcPayEnabled value should be same");
        Assert.That(gatewayData.IsPartialRefundEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsPartialRefundEnabled), "IsPartialRefundEnabled value should be same");
        Assert.That(gatewayData.IsCaptureEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsCaptureEnabled), "IsCaptureEnabled value should be same");
        Assert.That(gatewayData.IsPartialCaptureEnabled, Is.EqualTo(createProductInstanceRequestGatewayData.IsPartialCaptureEnabled), "IsPartialCaptureEnabled value should be same");
        Assert.That(gatewayData.IsAuthorizationRefundEnable, Is.EqualTo(createProductInstanceRequestGatewayData.IsAuthorizationRefundEnable), "IsAuthorizationRefundEnable value should be same");


    }

    [TestCase(null, null)]
    [TestCase("", null)]
    [TestCase(" ", null)]
    [TestCase(";", null)]
    [TestCase(" ; ", null)]
    [TestCase(" ; ;;;; ", null)]
    [TestCase("<EMAIL>", "<EMAIL>")]
    [TestCase(";<EMAIL>;", "<EMAIL>")]
    [TestCase(";<EMAIL>;;; ;;", "<EMAIL>")]
    [TestCase("<EMAIL>;<EMAIL>", "<EMAIL>;<EMAIL>")]
    [TestCase("<EMAIL>;<EMAIL>;", "<EMAIL>;<EMAIL>")]
    [TestCase(" <EMAIL>; <EMAIL>", "<EMAIL>;<EMAIL>")]
    [TestCase("<EMAIL>;;<EMAIL>", "<EMAIL>;<EMAIL>")]
    [TestCase("<EMAIL>; ;<EMAIL>", "<EMAIL>;<EMAIL>")]
    [TestCase("; <EMAIL>; ;<EMAIL>;", "<EMAIL>;<EMAIL>")]
    [TestCase("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>", "<EMAIL>;<EMAIL>")]
    public void TranformMerchantNotificationEmailToValidFormat(string email, string expected)
    {
        var createProductInstanceRequest = new CreateProductInstanceRequest
        {
            AgreementId = Guid.NewGuid(),
            Data = new { MerchantNotificationEmail = email }
        };

        var productInstance = mapper.Map<ProductInstanceEntity>(createProductInstanceRequest);
        productInstance.Product = ProductEntity;

        var gatewayData = productInstance.Data as GatewayData;

        Assert.That(gatewayData.MerchantNotificationEmail, Is.EqualTo(expected), "MerchantNotificationEmail value should be same");
    }

    [Test]
    public void FormatMerchantNotificationEmail_ValidResult()
    {
        var result = Helpers.FormatMerchantNotificationEmail("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>");

        Assert.That(result, Is.EqualTo("<EMAIL>;<EMAIL>;<EMAIL>"));
    }

    [Test]
    public void FormatMerchantNotificationEmail_WhenOneEmailValueIsEmpty_ValidResult()
    {
        var result = Helpers.FormatMerchantNotificationEmail("<EMAIL>; ;<EMAIL>;<EMAIL>;<EMAIL>");

        Assert.That(result, Is.EqualTo("<EMAIL>;<EMAIL>;<EMAIL>"));
    }

    [Test]
    public void FormatMerchantNotificationEmail_WhenValueIsNull_ReturnNull()
    {
        var result = Helpers.FormatMerchantNotificationEmail(null);

        Assert.That(result, Is.EqualTo(null));
    }

    [TestCase("")]
    [TestCase(null)]
    [TestCase(" ; ")]
    public void FormatMerchantNotificationEmail_WhenValueHasNoElements_ReturnNull(string value)
    {
        var result = Helpers.FormatMerchantNotificationEmail(value);

        Assert.That(result, Is.EqualTo(null));
    }

    [Test]
    public void FormatMerchantNotificationEmail_SingleEmail()
    {
        var result = Helpers.FormatMerchantNotificationEmail("<EMAIL>");

        Assert.AreEqual("<EMAIL>", result);
    }

    [Test]
    public void FormatMerchantNotificationEmail_MultipleEmails()
    {
        var result = Helpers.FormatMerchantNotificationEmail("<EMAIL>;<EMAIL>");

        Assert.AreEqual("<EMAIL>;<EMAIL>", result);
    }

    [Test]
    public void FormatMerchantNotificationEmail_DuplicateEmails()
    {
        var result = Helpers.FormatMerchantNotificationEmail("<EMAIL>;<EMAIL>");

        Assert.AreEqual("<EMAIL>", result);
    }

    [Test]
    public void FormatMerchantNotificationEmail_WhitespaceAndEmptyEmails()
    {
        var result = Helpers.FormatMerchantNotificationEmail("   ;<EMAIL>;;<EMAIL>;  ");

        Assert.AreEqual("<EMAIL>;<EMAIL>", result);
    }
}