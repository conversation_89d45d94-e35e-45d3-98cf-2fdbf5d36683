﻿DECLARE @ProductId UNIQUEIDENTIFIER
DECLARE @PayFamily UNIQUEIDENTIFIER
DECLARE @PartId UNIQUEIDENTIFIER

SELECT TOP 1 @PayFamily = ID FROM Category where Code = 'PAY_FAMILY';

EXEC NewProductVersion_v2 'PAYMENT_GATEWAY_BUNDLE', 0, 'PAYMENT_GATEWAY_BUNDLE', 1, 1,'GEIDEA_UAE'
SELECT TOP 1 @ProductId = ID FROM Products where Code='PAYMENT_GATEWAY_BUNDLE' and [Version] = 1 and CounterParty='GEIDEA_UAE'
DELETE FROM ProductCategories where ProductId=@ProductId 

INSERT INTO ProductCategories(ProductId, CategoryId) VALUES (@ProductId, @PayFamily)
UPDATE Products SET [Availability] = 'Obsolete' WHERE Code='GEIDEA_GO_APP' and CounterParty='GEIDEA_UAE'

SELECT TOP 1 @PartId = ID FROM Products where Code='GEIDEA_GO_APP' and CounterParty='GEIDEA_UAE'
Delete from ProductParts where ProductId = @ProductId  and PartId= @PartId
