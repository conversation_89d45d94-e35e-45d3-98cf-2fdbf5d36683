﻿using FluentMigrator;
using System.Collections.Generic;

namespace Migrations.Tables;

[Migration(2020_10_02_1044)]
public class DropAllTables : ForwardOnlyMigration
{
    public override void Up()
    {
        var tablesToDrop = new List<string>()
            {
                "ProductCategories",
                "ProductParts",
                "Prices",
                "ProductInstances",
                "Products",
                "Category"
            };

        foreach (var table in tablesToDrop)
        {
            Execute.Sql($@"
                               IF EXISTS 
                               ( 
                                   SELECT NULL 
                                   FROM [sys].[tables] t 
                                   INNER JOIN [sys].[schemas] s ON s.schema_id = t.schema_id 
                                   WHERE t.name = '{table}' AND temporal_type = 2
                               ) 
                               BEGIN 
                                   ALTER TABLE [{table}] SET (SYSTEM_VERSIONING = OFF)

                                   IF EXISTS
                                   (
                                       SELECT *
                                       FROM [sys].[tables] t
	                                   WHERE t.name = 'history.{table}'
                                   )
                                   BEGIN
                                    DROP TABLE [History.{table}]
                                   END
                                    
                                   ELSE
                                   BEGIN
                                    DROP TABLE History.[{table}]
                                   END

                               END
                               IF EXISTS 
                               ( 
                                   SELECT NULL 
                                   FROM [sys].[tables] t 
                                   WHERE t.name = '{table}'
                               ) 
                               BEGIN
                                DROP TABLE [{table}]
                               END");
        }
    }
}
