﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.PendingUnitPrice;
public class PendingUnitPriceReviewResponse
{
    public List<Guid> ReviewedUnitPrices { get; set; } = new();
    public List<FailedUnitPrices> FailedUnitPrices { get; set; } = new();
}

public class FailedUnitPrices
{
    public Guid Id { get; set; }
    public string? ErrorCode { get; set; }
}