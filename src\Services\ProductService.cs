﻿using AutoMapper;
using Common;
using Common.Entities;
using Common.Models;
using Common.Models.ProductInstance;
using Common.Repositories;
using Common.Services;
using Common.Validators;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Threading.Tasks;
using static System.Net.Mime.MediaTypeNames;

namespace Services;

public class ProductService : IProductService
{
    private readonly ILogger<ProductService> logger;
    private readonly IProductRepository productRepo;
    private readonly IMapper mapper;
    private readonly ICategoryRepository categoryRepo;
    private readonly IProductInstanceRepository productInstanceRepo;

    public ProductService(ILogger<ProductService> logger,
                          IProductRepository productRepo,
                          IMapper mapper,
                          ICategoryRepository categoryRepo,
                          IProductInstanceRepository productInstanceRepo)
    {
        this.logger = logger;
        this.productRepo = productRepo;
        this.mapper = mapper;
        this.categoryRepo = categoryRepo;
        this.productInstanceRepo = productInstanceRepo;
    }

    public async Task<Product[]> FindAsync(FindProductRequest request, bool attach)
    {
        var products = await productRepo.FindAsync(request, attach);
        if (request.OnlyValid)
            ClearDeletedPrices(products);
        var result = mapper.Map<ProductEntity[], Product[]>(products.OrderBy(p => p.DisplayOrder).ThenBy(p => p.Code).ThenByDescending(p => p.Version).ToArray());
        return result;
    }

    public async Task<Product[]> FindWithInfoAsync(FindProductRequest request, bool attach)
    {
        var products = await productRepo.FindWithInfoAsync(request, attach);
        if (request.OnlyValid)
            ClearDeletedPrices(products);
        //CMS calling to get products info..
        var result = mapper.Map<ProductEntity[], Product[]>(products.OrderBy(p => p.DisplayOrder)
                                                                    .ThenBy(p => p.Code)
                                                                    .ThenByDescending(p => p.Version)
                                                                    .ToArray());
        return result;
    }

    public async Task<Product[]> FindByIdsAsync(IdsRequest request)
    {
        return mapper.Map<ProductEntity[], Product[]>(await productRepo.FindByIdsAsync(request));
    }

    public async Task BindPartAsync(Guid partId, Guid productId, int quantity)
    {
        await CheckProductPartAsync(productId, partId);

        if (quantity < 1)
        {
            logger.LogError($"Quantity has to be greater than 0 but was '{quantity}'.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidQuantity);
        }

        if (await productRepo.IsPartOfAsync(productId, partId))
        {
            logger.LogError($"BindPartAsync: Cannot bound part '{partId}' to product '{productId}' because they are already linked in the opposite direction.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.OppositeBindingAlreadyExists);
        }

        if (!await productRepo.IsPartOfAsync(partId, productId))
        {
            await productRepo.BindPartAsync(partId, productId, quantity);
            logger.LogInformation($"Bound part '{partId}' to product '{productId}'.");
        }
        else
        {
            logger.LogError($"BindPartAsync: Cannot bound part '{partId}' to product '{productId}' because they are already linked.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.BindingAlreadyExists);
        }
    }

    public async Task UnbindPartAsync(Guid partId, Guid productId)
    {
        await CheckProductPartAsync(productId, partId);

        if (await productRepo.IsPartOfAsync(partId, productId))
        {
            await productRepo.UnbindPartAsync(partId, productId);
            logger.LogInformation($"Unbound part '{partId}' from product '{productId}'.");
        }
        else
        {
            logger.LogWarning($"Tried to unbind part '{partId}' from product '{productId}' but it's already unbound. Did nothing.");
        }
    }
    public async Task<ProductResponse> CreateAsync(ProductRequest request)
    {
        if (request.CategoryId == Guid.Empty)
        {
            logger.LogError("CreateAsync: The provided Category ID '{CategoryId}' cannot be null or empty for the product with Code '{ProductCode}'.", request.CategoryId, request.Code);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.CategoryNotFound);
        }
        await ValidateProductAsync(request);
        var product = mapper.Map<ProductEntity>(request);

        var categoryExists = await categoryRepo.ExistsAsync(request.CategoryId);
        if (!categoryExists)
        {
            logger.LogError("CreateAsync: Invalid category ID '{CategoryId}' does not exist for product with Code '{ProductCode}'.", request.CategoryId.ToString().ToUpper(), request.Code);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.CategoryNotFound);
        }
        if (await productRepo.AnyAsync(x => x.Code == request.Code))
        {
            logger.LogError("A product with code '{ProductCode}' already exists.", request.Code);
            throw new ServiceException(HttpStatusCode.Conflict, Errors.CodeAlreadyExist);
        }
        try
        {
            product.ProductCategories.Add(new ProductCategoriesEntity { CategoryId = request.CategoryId });

            productRepo.Add(product);
            await productRepo.SaveChangesAsync();

            logger.LogInformation($"Added product with id '{product.Id}' and Code '{product.Code}'.");
            return mapper.Map<ProductResponse>(product);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating product with Code '{Code}'.", request.Code);

            throw new ServiceException(HttpStatusCode.InternalServerError, $"Error while creating product: {ex.Message}");
        }
    }

    public async Task DeleteProductAsync(Guid id)
    {
        if (Guid.Empty.Equals(id))
        {
            logger.LogWarning("Cannot delete product: " + Errors.InvalidIdentifier.Message);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidIdentifier.Code, Errors.InvalidIdentifier.Message);
        }

        if (await IsProductUsedAsync(id))
        {
            logger.LogCritical("Cannot delete product that is used.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.ProductInUse);
        }

        await DeleteProductAndPartsAsync(id);
    }

    public async Task<ProductResponse> UpdateAsync(Guid id, ProductRequest productRequest)
    {
        var product = await GetProductAndEnsureUniqueCodeAsync(id, productRequest);

        await UpdateProductDetailsAsync(product, productRequest);

        await SaveProductAsync(product);

        logger.LogInformation("Updated product with id '{productId}'", product.Id);

        return mapper.Map<ProductResponse>(product);
    }
    private async Task<ProductEntity> GetProductAndEnsureUniqueCodeAsync(Guid id, ProductRequest productRequest)
    {
        var product = await productRepo.Query().Include(p => p.ProductCategories).FirstOrDefaultAsync(p => p.Id == id);

        if (product == null)
        {
            logger.LogError("Product with id {productId} not found.", id);
            throw new ServiceException(HttpStatusCode.NotFound, "Product not found.");
        }

        if (await productRepo.AnyAsync(x => x.Code == productRequest.Code && x.Id != id))
        {
            logger.LogError("A product with code '{ProductCode}' already exists.", productRequest.Code);
            throw new ServiceException(HttpStatusCode.BadRequest, $"Product with code '{productRequest.Code}' already exists");
        }

        return product;
    }
    private async Task UpdateProductDetailsAsync(ProductEntity product, ProductRequest productRequest)
    {
        mapper.Map(productRequest, product);

        // Validate updated product data
        await ValidateProductAsync(productRequest);

        var categoryId = productRequest.CategoryId;
        var existingCategory = product.ProductCategories.FirstOrDefault(pc => pc.CategoryId == categoryId);

        if (existingCategory == null)
        {
            if (!await categoryRepo.ExistsAsync(categoryId))
            {
                logger.LogError("UpdateProductDetailsAsync: Invalid category ID '{CategoryId}' provided for product '{ProductId}'.", categoryId.ToString().ToUpper(), product.Id);
                throw new ServiceException(HttpStatusCode.BadRequest, "Invalid category ID: " + categoryId.ToString().ToUpper());
            }

            product.ProductCategories.Clear();
            product.ProductCategories.Add(new ProductCategoriesEntity { ProductId = product.Id, CategoryId = categoryId });
        }


        if (productRequest.Images != null)
        {
            //Delete all existing product images
            await productRepo.DeleteProductImages(product.Id);

            product.Images = productRequest.Images.Select(imgData => new ProductImage
            {
                Language = imgData.Language,
                DisplayOrder = imgData.DisplayOrder,
                ImageId = imgData.ImageId,
                ProductId = product.Id
            }).ToList();
        }
    }

    private async Task SaveProductAsync(ProductEntity product)
    {
        productRepo.Update(product);
        await productRepo.SaveChangesAsync();

        logger.LogInformation($"Updated product with id '{product.Id}'.");
    }


    public async Task AddCategoryAsync(Guid productId, Guid categoryId)
    {
        await CheckProductCategoriesAsync(productId, categoryId);

        if (!await productRepo.HasCategoryAsync(productId, categoryId))
        {
            await productRepo.AddCategoryAsync(productId, categoryId);
            logger.LogInformation($"Added category '{categoryId}' to product '{productId}'.");
        }
        else
        {
            logger.LogInformation($"Tried to add category '{categoryId}' to product '{productId}' but it's already there. Did nothing.");
        }
    }

    public async Task DeleteCategoryAsync(Guid productId, Guid categoryId)
    {
        if (await productRepo.HasCategoryAsync(productId, categoryId))
        {
            await productRepo.DeleteCategoryAsync(productId, categoryId);
            logger.LogInformation($"Removed category '{categoryId}' from product '{productId}'.");
        }
        else
        {
            logger.LogInformation($"Tried to remove category '{categoryId}' from product '{productId}' but it's already removed. Did nothing.");
        }
    }

    private async Task<ProductEntity> GetByIdAsync(Guid id)
    {
        var product = (await productRepo.FindAsync(new FindProductRequest() { Id = id }, true)).FirstOrDefault();
        if (product == null)
        {
            logger.LogError("Product with id {productId} not found.", id);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductNotFound);
        }

        return product;
    }

    public async Task<bool> IsProductUsedAsync(Guid id)
    {
        bool isUsed = (await productInstanceRepo.FindAsync(new FindProductInstanceRequest() { ProductId = id }, true)).Any();
        var parents = new Stack<ProductEntity>(await productRepo.GetParentProducts(id));

        while (!isUsed && parents.Count > 0)
        {
            var parent = parents.Pop();
            isUsed = isUsed ||
                (await productInstanceRepo.FindAsync(new FindProductInstanceRequest() { ProductId = parent.Id }, true)).Any();

            foreach (var newParent in await productRepo.GetParentProducts(parent.Id))
                parents.Push(newParent);
        }

        return isUsed;
    }

    public Guid[] GetRelatedProductsIds(ProductCodesRequest productCodesRequest)
    {
        return productRepo.GetRelatedProductsIds(productCodesRequest);
    }

    public async Task<bool> ProductsContainBillPaymentServiceOrBundleAsync(IdsRequest productIds)
    {
        return await productRepo.ProductsContainBillPaymentServiceOrBundleAsync(productIds);
    }

    public async Task<BillPaymentServiceAndBundleFlags> ProductsContainBillPaymentTypeAsync(IdsRequest productIds)
    {
        return await productRepo.ProductsContainBillPaymentTypeAsync(productIds);
    }

    private async Task CheckProductCategoriesAsync(Guid productId, Guid categoryId)
    {
        if (Guid.Empty.Equals(categoryId) || Guid.Empty.Equals(productId))
        {
            logger.LogError("Invalid id");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidIdentifier);
        }

        if (!productRepo.ExistsAsync(productId))
        {
            logger.LogError("Product not found.");
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductNotFound);
        }

        if (!await categoryRepo.ExistsAsync(categoryId))
        {
            logger.LogError("Category not found.");
            throw new ServiceException(HttpStatusCode.NotFound, Errors.CategoryNotFound);
        }

        if (await IsProductUsedAsync(productId))
        {
            logger.LogError("Product is used by a product instance and therefore it cannot be modified.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidUpdateRequest);
        }
    }

    private async Task DeleteProductAndPartsAsync(Guid id)
    {
        var product = await productRepo.SingleOrDefaultAsync(a => a.Id == id, a => a.Parts);

        if (product == null)
        {
            logger.LogError("Cannot find product with id {productId}.", id);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductNotFound);
        }

        foreach (var part in product.Parts.ToList())
            await DeleteProductAndPartsAsync(part.PartId);

        logger.LogInformation($"Hard-deleting product '{product.Id}'...");
        await productRepo.HardDeleteProductAsync(product);
        logger.LogInformation($"Hard-deleted product '{product.Id}'.");
    }

    private async Task CheckProductPartAsync(Guid productId, Guid partId)
    {

        if (Guid.Empty.Equals(partId) || Guid.Empty.Equals(productId))
        {
            logger.LogError("Invalid id");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidIdentifier);
        }

        if (partId.Equals(productId))
        {
            logger.LogError("Cannot bind / unbind part to same part.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidBindAttempt);
        }

        if (!productRepo.ExistsAsync(partId, productId))
        {
            logger.LogError("Product or part not found.");
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductNotFound);
        }

        if (await IsProductUsedAsync(productId))
        {
            logger.LogError("Product is used by a product instance and therefore it cannot be modified.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidUpdateRequest);
        }
    }

    private async Task ValidateProductAsync(ProductRequest productRequest)
    {
        var validationResult = await new ProductRequestValidator().ValidateAsync(productRequest);
        if (!validationResult.IsValid)
        {
            var errorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
            logger.LogError("The request to create a new product failed validation: {@errors}", errorDescription);

            throw new ValidationException(validationResult);
        }

        if (productRequest.Images != null)
        {
            var invalidLanguages = productRequest.Images.Where(img =>
                           img.Language == null ||
                           (!img.Language.Equals("en", StringComparison.OrdinalIgnoreCase) &&
                           !img.Language.Equals("ar", StringComparison.OrdinalIgnoreCase))
                       ).ToList();
            if (invalidLanguages.Any())
            {
                logger.LogError("CreateAsync: Invalid language found in images. Only 'en' and 'ar' are allowed.");
                throw new ServiceException(HttpStatusCode.BadRequest, "Invalid language found in images. Only 'en' and 'ar' are allowed");
            }
            var englishImageCount = productRequest.Images.Count(img => img.Language?.Equals("en", StringComparison.OrdinalIgnoreCase) ?? false);
            var arabicImageCount = productRequest.Images.Count(img => img.Language?.Equals("ar", StringComparison.OrdinalIgnoreCase) ?? false);

            if (englishImageCount > 6 || arabicImageCount > 6)
            {
                logger.LogError("CreateAsync: Cannot upload more than 6 images per language..");
                throw new ServiceException(HttpStatusCode.InternalServerError, "Cannot upload more than 6 images per language.");
            }
        }

    }

    private void ClearDeletedPrices(ProductEntity[] products)
    {
        foreach (var product in products)
        {
            foreach (var part in product.Parts)
            {
                part.Part.Prices = part.Part.Prices.Where(p => !p.DeletedFlag).ToList();
            }
        }
    }
    public async Task<ProductListResponse> GetProductsList(GetProductsListRequest request)
    {
        if (request == null)
        {
            logger.LogError("Invalid Get products List request.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidGetProductsListRequest);
        }
        try
        {
            return await productRepo.GetProductsList(request);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "something has gone wrong!");
            throw new ServiceException(HttpStatusCode.InternalServerError, Errors.InternalServerError);
        }
    }
    public async Task<TerminalDetails> GetProductsDetails(string TID)
    {
        return await productRepo.GetProductsDetails(TID);
    }
    public async Task<List<ProductOnBusiness>> GetProductsOnBusinessByMID(string MID)
    {
        return await productRepo.GetProductsOnBusinessByMID(MID);
    }

    public async Task<ProductsOnAccount> GetProductsOnAccountByaccountID(string accountID)
    {
        return await productRepo.GetProductsOnAccountAsync(accountID);
    }

    public async Task<TerminalDataSetEntity> GetTerminalDataSetByMID(string MID)
    {
        return await productRepo.GetTerminalDataSet(MID);
    }

    public async Task<List<ProductsOnAccount>> GetMultipleProductsOnAccountByaccountID(string accountID)
    {
        return await productRepo.GetMultipleProductsOnAccountAsync(accountID);
    }

    public async Task<ProductDetailsResponse> GetProductDetails(Guid ProductId)
    {
        if (ProductId == Guid.Empty)
        {
            logger.LogError("Invalid Product Id.");
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidIdentifier);
        }

        var Product = await productRepo.GetProductDetails(ProductId);
        if (Product == null)
        {
            logger.LogError("product with Id {ProductId} not found.", ProductId);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductNotFound);
        }
        return Product;
    }

    public async Task<List<BasicProductsInfo>> GetProductsNames()
    {
        try
        {
            var getProductsNames = await productRepo.GetProductsNamesAsync();
            return getProductsNames;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while retrieving product IDs and names.");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An error occurred while retrieving product data.");
        }
    }

    public async Task<List<ProductDetailsResponse>> GetProductsListByChannel(bool isCNP)
    {
        logger.LogInformation(" Retrieving  product list with images from {servicename}", this.GetType().Name);
        try
        {
            return await productRepo.GetProductsListByChannel(isCNP);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while retrieving product list with images ");
            throw new ServiceException(HttpStatusCode.InternalServerError, "An error occurred while retrieving product list data.");
        }
    }
}
