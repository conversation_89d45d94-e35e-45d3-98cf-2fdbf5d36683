﻿using Common.Models.CommissionFees;
using Common.Models.ValueAddedServices;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Services;
public interface ICommissionFeesService
{
    Task<GetCommissionFeesListResponse> GetCommissionFeesList(GetCommissionFeesListRequest request);
    Task<CommissionFeesResponse> CreateAsync(CommissionFeesRequest commissionFeesRequest);
    Task<CommissionFeesResponse> UpdateAsync(Guid id, CommissionFeesRequest commissionFeesRequest);
    Task<bool> ToggleStatus(Guid CommissionFeeId, bool Status);
    Task<CommissionFeeDetailsResponse> GetCommissionFeeDetails(Guid Id);
    Task<List<CommissionFees>> GetCommissionFees();
}
