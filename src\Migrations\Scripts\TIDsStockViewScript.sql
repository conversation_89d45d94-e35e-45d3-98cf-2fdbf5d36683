﻿CREATE OR ALTER VIEW TIDsStock AS
SELECT 
    v.Id AS VendorId,
    v.Name AS VendorName, 
    t.Acquiring<PERSON>edger, 
    t.ChannelType AS ProductType,
    COUNT(CASE WHEN t.Availability = 'AVAILABLE' THEN t.TID ELSE NULL END) AS NumberOfTids
FROM 
    dbo.TerminalDataSets AS t 
LEFT OUTER JOIN 
    dbo.Vendor AS v ON t.VendorId = v.Id
WHERE (t.Availability = 'AVAILABLE' or t.Availability = 'USED') 
    AND (t.Counterparty = 'GEIDEA_SAUDI')
GROUP BY 
    t.AcquiringLedger, 
    t.ChannelType, 
    v.Name, 
    v.Id