﻿using Common.Enums.UnitPrice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.UnitPrice;
public class UnitPriceListResponse
{
    public Guid Id { get; set; }
    public string? ProductName { get; set; }
    public string? ProductCode { get; set; }
    public string? MccName { get; set; }
    public string? MccCode { get; set; }
    public string? MccCategory { get; set; }
    public string? BusinessType { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal VATRate { get; set; }
    public VatType VATType { get; set; }
    public BillingType BillingType { get; set; }
    public BillingFrequency BillingFrequency { get; set; }
    public DateTime CreatedDate { get; set; }
}
