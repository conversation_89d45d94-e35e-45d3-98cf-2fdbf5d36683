﻿using System;
using System.Collections.Generic;

namespace Common.Models.TerminalDataSets;
public class TerminalDataSetsRequest
{
    public string AcquiringLedger { get; set; } = string.Empty;
    public string? OrderNumber { get; set; }
    public Guid? StoreId { get; set; }
    public string? Model { get; set; }
    public string? BusinessId { get; set; }
    public string? CardChannelType { get; set; }
    public string? Mid { get; set; }

    public List<ProductInstanceData>? ProductInstancesData { get; set; }
    public string? MerchantTag { get; set; }
    public string? MCC { get; set; }
    public List<Guid>? MerchantStoresIds { get; set; }

}
