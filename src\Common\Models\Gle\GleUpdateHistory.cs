﻿using System;
using System.Diagnostics.CodeAnalysis;

namespace Common.Models.Gle;

[ExcludeFromCodeCoverage]
public class GleUpdateHistory
{
    public Guid Id { get; set; }
    public string? GleUserId { get; set; }
    public string? GleLoginId { get; set; }
    public string? GleLoginId2 { get; set; }
    public string? ParentGleUserId { get; set; }
    public string? RequestType { get; set; }
    public Guid MerchantId { get; set; }
    public string? OldValue { get; set; }
    public string? NewValue { get; set; }
    public string? GleStatus { get; set; }
    public string? GleResponse { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public string? UpdatedBy { get; set; }
    public DateTime? UpdatedDate { get; set; }
}
