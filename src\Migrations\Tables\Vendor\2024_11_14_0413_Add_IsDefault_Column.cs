﻿using FluentMigrator;

namespace Migrations.Tables.Vendor;

[Migration(2024_10_27_0413)]
public class Add_IsDefault_Column : ForwardOnlyMigration
{
    public override void Up()
    {
        if (!Schema.Table("Vendor").Column("IsDefault").Exists())
        {
            Alter.Table("Vendor")
                .AddColumn("IsDefault")
                .AsBoolean()
                .WithDefaultValue(false)
                .NotNullable();
        }
    }
}