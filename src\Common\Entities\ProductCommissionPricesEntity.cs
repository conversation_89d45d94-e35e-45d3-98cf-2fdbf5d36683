﻿using Common.Enums.ProductCommisssionPrices;
using Geidea.Utils.DataAccess.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Entities;
public class ProductCommissionPriceEntity : AuditableEntity<Guid>
{
    public Guid ProductID { get; set; }
    public ProductEntity Product { get; set; } = null!;
    public Guid MCCID { get; set; }
    public Mcc Mcc { get; set; } = null!;
    public Guid BusinessTypeID { get; set; }
    public BusinessTypeEntity BusinessType { get; set; } = null!;
    public Guid CommissionFeeID { get; set; }
    public CommissionFeesEntity CommissionFee { get; set; } = null!;
    public FeeType FeeType { get; set; }
    public decimal FeeValue { get; set; }
    public CommisssionPricesBillingType BillingType { get; set; }
    public PriceBillingFrequency BillingFrequency { get; set; }
}
