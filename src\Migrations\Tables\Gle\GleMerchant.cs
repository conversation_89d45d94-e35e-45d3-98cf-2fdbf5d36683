﻿using FluentMigrator;
using System.IO;
using System;

namespace Migrations.Tables.Gle;

[Migration(2022_01_11_1100)]
public class GleMerchant : ForwardOnlyMigration
{
    public override void Up()
    {
        Create.Table("GleMerchant")
            .WithColumn("Id").AsGuid().PrimaryKey().WithDefaultValue(SystemMethods.NewGuid)
            .WithColumn("GleUserId").AsString(250).Nullable()
            .WithColumn("GleLoginId").AsString(250).Nullable()
            .WithColumn("GleLoginId2").AsString(250).Nullable()
            .WithColumn("UserCategoryCode").AsString(250).NotNullable()
            .WithColumn("ParentGleUserId").AsString(250).Nullable()
            .WithColumn("GleRegistrationStatus").AsString(250).NotNullable()
            .WithColumn("GleRegistrationResponse").AsString(int.MaxValue).NotNullable()
            .WithColumn("MerchantId").AsGuid().NotNullable()
            .WithColumn("ReferenceMmsId").AsString(250).Nullable()
            .WithColumn("CreatedBy").AsString(150).NotNullable()
            .WithColumn("CreatedDate").AsDateTime2().NotNullable()
            .WithColumn("UpdatedBy").AsString(150).Nullable()
            .WithColumn("UpdatedDate").AsDateTime2().Nullable();

        Execute.Sql(@"CREATE UNIQUE INDEX GleMerchant_MerchantId_UniqueIndex ON [dbo].[GleMerchant] ([MerchantId]);");

        if (!Schema.Schema("History").Exists())
        {
            Create.Schema("History");
        }

        Execute.Sql(@"ALTER TABLE [dbo].[GleMerchant] ADD
              [StartDate] DATETIME2 GENERATED ALWAYS AS ROW START HIDDEN CONSTRAINT DF_GleMerchant_StartDate DEFAULT SYSUTCDATETIME(),
              [EndDate] DATETIME2 GENERATED ALWAYS AS ROW END HIDDEN CONSTRAINT DF_GleMerchant_EndDate DEFAULT CONVERT(DATETIME2, '9999-12-31 23:59:59'),
              PERIOD FOR SYSTEM_TIME(StartDate, EndDate);");

        Execute.Sql("ALTER TABLE [dbo].[GleMerchant] SET (SYSTEM_VERSIONING = ON (HISTORY_TABLE = [History].[GleMerchant]));");
    }
}

[Migration(2022_25_11_1000)]
public class GleCleanup_DropOldGleStructure : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Script(AppDomain.CurrentDomain.BaseDirectory + Path.Combine("Scripts", "CleanupDeprecatedGleStructure.sql"));
    }
}

[Migration(2022_05_12_1000)]
public class GleMerchant_UserCategoryCode_Nullable : ForwardOnlyMigration
{
    public override void Up()
    {
        Alter.Table("GleMerchant").AlterColumn("UserCategoryCode").AsString(256).Nullable();
    }
}