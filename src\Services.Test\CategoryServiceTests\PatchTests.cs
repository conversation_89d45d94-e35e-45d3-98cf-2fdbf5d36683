﻿using AutoMapper;
using Common.Entities;
using Common.Repositories;
using Microsoft.Extensions.Logging;
using Moq;
using ProductService;
using System;
using System.Threading.Tasks;
using Xunit;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using System.Net;
using Common;
using Microsoft.EntityFrameworkCore;
using DataAccess;
using DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using System.Linq;
using Common.Models.CategoryRequests;
using Geidea.Utils.Counterparty.Providers;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Services.Test.Helpers;
using Common.Options;
using Microsoft.Extensions.Options;
using NSubstitute;

namespace Services.Test.CategoryServiceTests;

public class PatchTests
{
    private readonly Mock<ILogger<CategoryService>> logger = new Mock<ILogger<CategoryService>>();
    private readonly DataContext context;
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly CategoryService categoryService;
    private readonly Guid categoryId = Guid.NewGuid();
    private readonly IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;
    private readonly ICounterpartyProvider counterpartyProvider;

    public PatchTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
            .UseInMemoryDatabase(databaseName: "CategoryPatchTests" + Guid.NewGuid().ToString())
            .Options;

        context = new DataContext(options, new CounterpartyProvider());
        ICategoryRepository categoryRepository = new CategoryRepository(context, httpContext.Object);

        context.Categories.AddRange(new List<CategoryEntity>
            {
                new CategoryEntity
                {
                    Id=categoryId,
                    Code="AAA",
                    Type=1,
                    DisplayOrder=1
                },
                new CategoryEntity
                {
                    Code="BBB",
                    Type=1,
                    DisplayOrder=1
                },
            });
        context.SaveChanges();

        softposEgyptFeatureToggle = Substitute.For<IOptions<SoftposEgyptFeatureToggle>>();
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = true });
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();

        IProductRepository productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);

        categoryService = new CategoryService(categoryRepository, logger.Object, mapper, productRepository);
    }

    [Fact]
    public async Task InexistingCategory()
    {
        await categoryService
            .Invoking(x => x.PatchAsync(Guid.NewGuid(), new JsonPatchDocument<CategoryRequest>()))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
                        x.ProblemDetails.Type == Errors.CategoryNotFound.Code);
    }

    [Fact]
    public async Task InvalidPatch()
    {
        var patchDocument = new JsonPatchDocument<CategoryRequest>();
        patchDocument.Operations.Add(new Operation<CategoryRequest>("replace", "types", ""));

        await categoryService
            .Invoking(x => x.PatchAsync(categoryId, patchDocument))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.InvalidPatchRequest.Code);
    }

    [Fact]
    public async Task InexistingParent()
    {
        var patchDocument = new JsonPatchDocument<CategoryRequest>();
        patchDocument.Replace(x => x.ParentId, Guid.NewGuid());

        await categoryService
            .Invoking(x => x.PatchAsync(categoryId, patchDocument))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.NotFound &&
            x.ProblemDetails.Type == Errors.CategoryNotFound.Code);
    }

    [Fact]
    public async Task ExistingCode()
    {
        var patchDocument = new JsonPatchDocument<CategoryRequest>();
        patchDocument.Replace(x => x.Code, "BBB");

        await categoryService
            .Invoking(x => x.PatchAsync(categoryId, patchDocument))
            .Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
            x.ProblemDetails.Type == Errors.InvalidCategoryCode.Code);
    }

    [Fact]
    public async Task ValidPatch()
    {
        var patchDocument = new JsonPatchDocument<CategoryRequest>();
        patchDocument.Replace(x => x.Code, "CCC");
        patchDocument.Replace(x => x.SalesChannel, Constants.SalesChannel.Shop);
        await categoryService.PatchAsync(categoryId, patchDocument);

        var retrievedCategory = context.Categories.Local.FirstOrDefault(c => c.Id == categoryId);
        retrievedCategory.Should().NotBeNull();
        retrievedCategory.Code.Should().Be("CCC");
        retrievedCategory.SalesChannel.Should().Be(Constants.SalesChannel.Shop);
    }

    [Fact]
    public async Task InvalidAvailability()
    {
        var patchDocument = new JsonPatchDocument<CategoryRequest>();
        patchDocument.Replace(x => x.SalesChannel, "Something else");

        await categoryService
            .Invoking(x => x.PatchAsync(categoryId, patchDocument))
            .Should().ThrowAsync<ValidationException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        TestsHelper.HasErrorCode(x, Errors.InvalidSalesChannel.Code));
    }

    [Fact]
    public async Task PatchCategory_whenFlowValueIsInvalid_ShouldThrowValidationException()
    {
        var patchDocument = new JsonPatchDocument<CategoryRequest>();
        patchDocument.Replace(x => x.Flow, "Something else");

        await categoryService
            .Invoking(x => x.PatchAsync(categoryId, patchDocument))
            .Should().ThrowAsync<ValidationException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        TestsHelper.HasErrorCode(x, Errors.InvalidFlow.Code));
    }
}
