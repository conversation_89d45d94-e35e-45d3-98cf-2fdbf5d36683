﻿using System.Collections.Generic;


namespace Common.Models.UnitPrice;
public class UnitPriceResponse
{
    public List<UnitPriceDetails> CreatedUnitPrices { get; set; } = new List<UnitPriceDetails>();
    public List<UnitPriceDetails> OldExistingUnitPrices { get; set; } = new List<UnitPriceDetails>();
    public List<UnitPriceDetails> NewExistingUnitPrices { get; set; } = new List<UnitPriceDetails>();
}