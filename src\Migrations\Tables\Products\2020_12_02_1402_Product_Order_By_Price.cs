﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2020_12_02_1402)]
public class Product_Order_By_Price : ForwardOnlyMigration
{
    public override void Up()
    {
        Update.Table("Products").Set(new { DisplayOrder = 1 }).Where(new { Code = "GO_AIR_V3" });
        Update.Table("Products").Set(new { DisplayOrder = 2 }).Where(new { Code = "GO_LITE_V3" });
        Update.Table("Products").Set(new { DisplayOrder = 3 }).Where(new { Code = "GO_SMART_V3" });
        Update.Table("Products").Set(new { DisplayOrder = 4 }).Where(new { Code = "GO_MPOS_V3" });
        Update.Table("Products").Set(new { DisplayOrder = 5 }).Where(new { Code = "GO_A920_V3" });

        Update.Table("Products").Set(new { DisplayOrder = 1 }).Where(new { Code = "PRO_SMART_V3" });
        Update.Table("Products").Set(new { DisplayOrder = 2 }).Where(new { Code = "BUSINESS_RETAIL_V3" });
        Update.Table("Products").Set(new { DisplayOrder = 5 }).Where(new { Code = "ENTERPRISE_RETAIL_V3" });

        Update.Table("Products").Set(new { DisplayOrder = 2 }).Where(new { Code = "BUSINESS_RESTAURANT_V3" });
        Update.Table("Products").Set(new { DisplayOrder = 3 }).Where(new { Code = "ENTERPRISE_RESTAURANT_V2" });
    }
}
