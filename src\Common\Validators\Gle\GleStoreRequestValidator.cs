﻿using System;
using System.Collections.Generic;
using Common.Models.Gle;
using FluentValidation;

namespace Common.Validators.Gle;

public class GleStoreRequestValidator : AbstractValidator<GleStoreRequest>
{
    public GleStoreRequestValidator()
    {
        Include(new GleBaseValidator());

        RuleFor(x => x.StoreId)
            .NotNull()
            .NotEqual(Guid.Empty)
            .WithErrorCode(Errors.InvalidStoreId.Code)
            .WithMessage(Errors.InvalidStoreId.Message);

        RuleFor(x => x.GleMerchantId)
            .NotNull()
            .NotEqual(Guid.Empty)
            .WithErrorCode(Errors.InvalidMerchantId.Code)
            .WithMessage(Errors.InvalidMerchantId.Message);

        var gleUserCodeList = new List<string>
        {
            Constants.GleUserCategoryCode.MasterBusinessStore,
            Constants.GleUserCategoryCode.WholesalerStore
        };

        RuleFor(x => x.UserCategoryCode)
            .Must(x => gleUserCodeList.Contains(x!))
            .When(x => !string.IsNullOrEmpty(x.UserCategoryCode))
            .WithErrorCode(Errors.InvalidGleUserCategoryCode.Code)
            .WithMessage(Errors.InvalidGleUserCategoryCode.Message);
    }
}