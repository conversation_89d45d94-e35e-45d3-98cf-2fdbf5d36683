﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_11_19_1538)]
public class ProductInstance_Add_IsActive_To_Metadata : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
                UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsActive', 'true')
	            FROM ProductInstances pi
                INNER JOIN Products p ON p.id = pi.ProductId
                WHERE [Metadata] IS NOT NULL
                AND p.Type = 'GWAY' 
                AND ISJSON([Metadata]) > 0 
            ");
    }
}
