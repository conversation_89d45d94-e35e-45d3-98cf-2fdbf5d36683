﻿using Common.Views;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.ProductCommissionPrice;
public class ProductCommissionPriceListResponse
{
    public ProductCommissionPriceListView[] CommissionPriceList { get; set; } = Array.Empty<ProductCommissionPriceListView>();
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }
}
