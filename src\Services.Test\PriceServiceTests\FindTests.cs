﻿using System;
using System.Threading.Tasks;
using AutoMapper;
using Common.Entities;
using Common.Models;
using Common.Options;
using Common.Services;
using DataAccess;
using DataAccess.Repositories;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NSubstitute;
using ProductService;
using Xunit;

namespace Services.Test.PriceServiceTests;

public class FindTests
{
    private readonly Mock<ILogger<PriceService>> logger = new Mock<ILogger<PriceService>>();
    private readonly Mock<IHttpContextAccessor> httpContext = new Mock<IHttpContextAccessor>();
    private readonly Mock<IProductService> productService = new Mock<IProductService>();
    private readonly DataContext context;
    private readonly PriceService priceService;
    private readonly IOptions<SoftposEgyptFeatureToggle> softposEgyptFeatureToggle;
    private readonly ICounterpartyProvider counterpartyProvider;

    public FindTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        IMapper mapper = new Mapper(configuration);

        var options = new DbContextOptionsBuilder<DataContext>()
           .UseInMemoryDatabase(databaseName: "PriceFindTests" + Guid.NewGuid().ToString())
           .Options;

        context = new DataContext(options, new CounterpartyProvider());

        softposEgyptFeatureToggle = Substitute.For<IOptions<SoftposEgyptFeatureToggle>>();
        softposEgyptFeatureToggle.Value.Returns(new SoftposEgyptFeatureToggle() { EnableSoftposEgypt = true });
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();

        var productRepository = new ProductRepository(context, httpContext.Object, softposEgyptFeatureToggle, counterpartyProvider);
        var priceRepository = new PriceRepository(context, httpContext.Object);
        priceService = new PriceService(logger.Object, productRepository, priceRepository,
            mapper, productService.Object);
    }

    [Fact]
    public async Task Find()
    {
        var price1 = new PriceEntity
        {
            ChargeFrequency = "RECCURRING_CHARGE",
            ChargeType = "MONTH",
            Currency = "EUR",
            ExemptFromVAT = true,
            Group = "group",
            MaxPrice = 100,
            PercentagePrice = 10,
            PerItemPrice = 20,
            Priority = 0,
            RentalPeriod = 24,
            Threshold = 200,
            ThresholdType = "LT",
            Product = new ProductEntity
            {
                Code = "TEST"
            }
        };
        var price2 = new PriceEntity
        {
            ChargeFrequency = "REFUND_CNP",
            ChargeType = "MONTH",
            Currency = "EUR",
            ExemptFromVAT = true,
            Group = "group1",
            MaxPrice = 200,
            PercentagePrice = 30,
            PerItemPrice = 30,
            Priority = 0,
            RentalPeriod = 12,
            Threshold = 20,
            ThresholdType = "GT",
            Product = new ProductEntity
            {
                Code = "TEST1"
            }
        };

        context.Prices.Add(price1);
        context.Prices.Add(price2);
        context.SaveChanges();

        var prices = await priceService.FindAsync(new FindPriceRequest
        {
            ChargeFrequency = "RECCURRING_CHARGE",
            ChargeType = "MONTH",
            Currency = "EUR",
            ExemptFromVAT = true,
            Group = "group",
            PercentagePrice = 10,
            PerItemPrice = 20,
            Threshold = 200,
            ThresholdType = "LT"
        }, false);

        prices.Should().NotBeNullOrEmpty();
        prices.Should().HaveCount(1);

        prices = await priceService.FindAsync(new FindPriceRequest
        {
            Currency = "EUR",
        }, false);

        prices.Should().NotBeNullOrEmpty();
        prices.Should().HaveCount(2);
    }
}
