﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_09_29_1600)]
public class ProductInstance_Set_UseMpgsApiV60_To_True_In_Metadata : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
                UPDATE prod_inst
                SET prod_inst.Metadata = JSON_MODIFY(Metadata, '$.UseMpgsApiV60', CAST(1 AS BIT))
                FROM [dbo].[ProductInstances] prod_inst
                INNER JOIN [dbo].[Products] p ON p.Id = prod_inst.ProductId
                WHERE p.[Type] = 'GWAY' AND ISJSON(prod_inst.Metadata) = 1
            ");
    }
}