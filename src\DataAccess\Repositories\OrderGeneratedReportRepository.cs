﻿using Common.Entities;
using Common.Models.OrderGeneratedReport;
using Common.Repositories;
using Geidea.Utils.Counterparty.Providers;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace DataAccess.Repositories;

public class OrderGeneratedReportRepository : IOrderGeneratedReportRepository
{
    private readonly DbContext context;
    private readonly string counterparty;

    public OrderGeneratedReportRepository(DbContext context, ICounterpartyProvider counterpartyProvider)
    {
        this.context = context;

        counterparty = counterpartyProvider.GetCode();
    }

    public async Task AddOrdersReport(OrdersReportRequest reportOrdersRequest)
    {
        var DateTimeUtc = DateTime.UtcNow;

        reportOrdersRequest.OrderNumbers.ForEach(item =>
        {
            context.Set<OrderGeneratedReportEntity>().Add(new OrderGeneratedReportEntity
            {
                Counterparty = counterparty,
                AcquiringBank = reportOrdersRequest.AcquiringBank,
                OrderNumber = item,
                ReportName = reportOrdersRequest.ReportName,
                CreationDate = DateTimeUtc,
            });
        });

        await context.SaveChangesAsync();
    }
}
