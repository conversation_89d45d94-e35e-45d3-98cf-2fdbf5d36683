﻿using System;
using System.IO;
using System.Net;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Common.Data;
using Common.Data.ProductType;
using Common.Entities;
using Common.Models.ProductInstance;
using Common.Repositories;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Services;

public class CertificateService : ICertificateService
{
    private readonly IProductInstanceRepository productInstanceRepository;
    private readonly IOpenSslCommandProcessor openSslCommandProcessor;
    private readonly IProductInstancePublisherSelector publisherSelector;
    private readonly ApplePaySettings applePaySettings;
    private readonly IMapper mapper;
    private readonly ILogger<CertificateService> logger;

    public CertificateService(
        IProductInstanceRepository productInstanceRepository,
        IOpenSslCommandProcessor openSslCommandProcessor,
        IProductInstancePublisherSelector publisherSelector,
        IOptionsMonitor<ApplePaySettings> applePaySettings,
        IMapper mapper,
        ILogger<CertificateService> logger)
    {
        this.productInstanceRepository = productInstanceRepository;
        this.openSslCommandProcessor = openSslCommandProcessor;
        this.publisherSelector = publisherSelector;
        this.applePaySettings = applePaySettings.CurrentValue;
        this.mapper = mapper;
        this.logger = logger;
    }

    public async Task<string> CreateCsrAndPrivateKey(GenerateCsrRequest request)
    {
        var productInstance = await productInstanceRepository.GetByIdAsync(request.ProductInstanceId, true);

        if (!(productInstance.Data is GatewayData gatewayData))
        {
            logger.LogError("Incorrect product instance data {@productInstanceId}", productInstance.Id);
            throw new ServiceException(HttpStatusCode.BadRequest,
                ("In order to generate CSR the product instance data should not be null and the type should be 'GatewayData'",
                    HttpStatusCode.BadRequest.ToString()));
        }

        var result = GenerateCsrAndPrivateKey(request);

        if (gatewayData.AppleCertificatePrivateKey == null || gatewayData.ApplePaymentProcessingCertificate == null)
        {
            gatewayData.AppleCertificatePrivateKey = result.CertificatePrivateKey;
        }
        else if (gatewayData.AppleCertificatePrivateKeyNew == null)
        {
            gatewayData.AppleCertificatePrivateKeyNew = result.CertificatePrivateKey;
        }
        else
        {
            if (gatewayData.ApplePaymentProcessingCertificateNew != null)
            {
                gatewayData.AppleCertificatePrivateKey = gatewayData.AppleCertificatePrivateKeyNew;
                gatewayData.ApplePaymentProcessingCertificate = gatewayData.ApplePaymentProcessingCertificateNew;
                gatewayData.ApplePaymentProcessingCertificateExpiryDate = gatewayData.ApplePaymentProcessingCertificateExpiryDateNew;
            }

            gatewayData.AppleCertificatePrivateKeyNew = result.CertificatePrivateKey;
            gatewayData.ApplePaymentProcessingCertificateNew = null;
            gatewayData.ApplePaymentProcessingCertificateExpiryDateNew = null;
        }

        gatewayData.AppleCsr = result.Csr;

        await UpdateProductInstanceData(productInstance, gatewayData);
        PublishUpdateEvent(productInstance);

        return result.Csr;
    }

    public async Task<ProductInstanceResponse> UploadSignedCertificate(Guid productInstanceId, byte[] signedCertificate)
    {
        var productInstance = await productInstanceRepository.GetByIdAsync(productInstanceId, true);

        if (!(productInstance.Data is GatewayData gatewayData))
        {
            logger.LogError("Incorrect product instance data {@productInstanceId}", productInstance.Id);
            throw new ServiceException(HttpStatusCode.BadRequest,
                ("In order to generate Pfx the product instance data should not be null and the type should be 'GatewayData'",
                    HttpStatusCode.BadRequest.ToString()));
        }

        var expiryDate = GetCertificateExpiryDate(signedCertificate);

        var privateKey = Convert.FromBase64String(gatewayData.AppleCertificatePrivateKeyNew ?? gatewayData.AppleCertificatePrivateKey!);
        var pfxCertificate = GeneratePfx(privateKey, signedCertificate, productInstanceId);
        var base64EncodedPfxCertificate = Convert.ToBase64String(pfxCertificate);

        gatewayData.IsApplePayMobileCertificateAvailable = true;

        if (gatewayData.AppleCertificatePrivateKeyNew != null)
        {
            gatewayData.ApplePaymentProcessingCertificateNew = base64EncodedPfxCertificate;
            gatewayData.ApplePaymentProcessingCertificateExpiryDateNew = expiryDate;
        }
        else
        {
            gatewayData.ApplePaymentProcessingCertificate = base64EncodedPfxCertificate;
            gatewayData.ApplePaymentProcessingCertificateExpiryDate = expiryDate;
        }

        await UpdateProductInstanceData(productInstance, gatewayData);
        PublishUpdateEvent(productInstance);

        return mapper.Map<ProductInstanceResponse>(productInstance);
    }

    public async Task<string> CreateSamsungCsrAndPrivateKey(GenerateCsrRequest request)
    {
        var productInstance = await productInstanceRepository.GetByIdAsync(request.ProductInstanceId, true);

        if (!(productInstance.Data is GatewayData gatewayData))
        {
            logger.LogError("Incorrect product instance data {@productInstanceId}", productInstance.Id);
            throw new ServiceException(HttpStatusCode.BadRequest,
                ("In order to generate CSR the product instance data should not be null and the type should be 'GatewayData'",
                    HttpStatusCode.BadRequest.ToString()));
        }

        var result = GenerateCsrAndRSAPrivateKey(request);

        gatewayData.SamsungPayCsr = result.Csr;
        gatewayData.SamsungPayCertificatePrivateKey = result.CertificatePrivateKey;

        await UpdateProductInstanceData(productInstance, gatewayData);
        PublishUpdateEvent(productInstance);

        return result.Csr;
    }
    private static CsrResponse GenerateCsrAndRSAPrivateKey(GenerateCsrRequest request)
    {
        var csrResponse = new CsrResponse();

        var subject = $"CN={request.CommonName}, O={request.Organization}, C={request.Country}, ST={request.State}, L={request.Location}, OU={request.OrganizationalUnit}, E={request.Email}";

        using (RSACryptoServiceProvider rsa = new RSACryptoServiceProvider(2048))
        {
            var certificateRequest = new CertificateRequest(subject, rsa, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);

            byte[] csrBytes = certificateRequest.CreateSigningRequest();

            string base64CSR = Convert.ToBase64String(csrBytes);

            string privateKey = Convert.ToBase64String(rsa.ExportPkcs8PrivateKey());

            csrResponse.Csr = base64CSR;

            csrResponse.CertificatePrivateKey = privateKey;
        }

        return csrResponse;
    }


    private CsrResponse GenerateCsrAndPrivateKey(GenerateCsrRequest request)
    {
        var csrResponse = new CsrResponse();

        var directoryName = request.ProductInstanceId.ToString();
        var keyFile = Path.Combine(directoryName, $"{Guid.NewGuid()}.key");
        var keyDerFile = Path.Combine(directoryName, $"{Guid.NewGuid()}.der");
        var csrFile = Path.Combine(directoryName, $"{Guid.NewGuid()}.csr");

        var subject = $"/C={request.Country}/ST={request.State}/L={request.Location}/O={request.Organization}/OU={request.OrganizationalUnit}/CN={request.CommonName}/emailAddress={request.Email}";

        try
        {
            CreateDirectory(directoryName);

            var genKeyCommand = $"ecparam -out12 {keyFile} -name prime256v1 -genkey";
            openSslCommandProcessor.Execute(genKeyCommand);

            logger.LogInformation("Key file generated successfully.");

            var genKeyDerCommand = $"pkcs8 -topk8 -inform PEM -outform DER -nocrypt -in {keyFile} -out {keyDerFile}";
            openSslCommandProcessor.Execute(genKeyDerCommand);

            logger.LogInformation("KeyDer file generated successfully.");

            var genCsrCommand = $"req -new -sha256 -key {keyFile} -nodes -out {csrFile} -subj \"{subject}\"";
            openSslCommandProcessor.Execute(genCsrCommand);

            logger.LogInformation("Csr file generated successfully.");

            csrResponse.CertificatePrivateKey = Convert.ToBase64String(File.ReadAllBytes(keyDerFile));

            logger.LogInformation("Certificate private key converted successfully.");

            csrResponse.Csr = File.ReadAllText(csrFile);
        }
        catch (Exception ex)
        {
            logger.LogError("Generating private key and csr completed with error {@error}", ex);
            throw new ServiceException(HttpStatusCode.BadRequest, "Failed to generate private key and csr");
        }
        finally
        {
            DeleteDirectoryIfExists(directoryName);
        }

        return csrResponse;
    }

    private byte[] GeneratePfx(byte[] privateKey, byte[] signedCertificate, Guid productInstanceId)
    {
        byte[] pfxCertificate;

        var directoryName = productInstanceId.ToString();
        var keyDerFile = Path.Combine(directoryName, $"{Guid.NewGuid()}.der");
        var keyPemFile = Path.Combine(directoryName, $"{Guid.NewGuid()}.key");
        var cerFile = Path.Combine(directoryName, $"{Guid.NewGuid()}.cer");
        var crtFile = Path.Combine(directoryName, $"{Guid.NewGuid()}.crt");
        var pfxFile = Path.Combine(directoryName, $"{Guid.NewGuid()}.pfx");

        try
        {
            CreateDirectory(directoryName);
            File.WriteAllBytes(keyDerFile, privateKey);
            File.WriteAllBytes(cerFile, signedCertificate);

            var derToPemCertificateCommand = $"x509 -in {cerFile} -inform DER -out {crtFile}";
            openSslCommandProcessor.Execute(derToPemCertificateCommand);

            logger.LogInformation("DerToPem certificate generated successfully.");

            var derToPemKeyCommand = $"pkcs8 -topk8 -inform DER -outform PEM -nocrypt -in {keyDerFile} -out {keyPemFile}";
            openSslCommandProcessor.Execute(derToPemKeyCommand);

            logger.LogInformation("DerToPem key generated successfully.");

            var genPfxCommand = $"pkcs12 -export -nodes -passout pass:{applePaySettings.CertificatePassword} -inkey {keyPemFile} -in {crtFile} -out {pfxFile}";
            openSslCommandProcessor.Execute(genPfxCommand);

            logger.LogInformation("Pfx file generated successfully.");

            pfxCertificate = File.ReadAllBytes(pfxFile);
        }
        catch (Exception ex)
        {
            logger.LogError("Merging private key with signed certificate completed with error {@error}", ex);
            throw new ServiceException(HttpStatusCode.BadRequest, "Failed to generate pfx");
        }
        finally
        {
            DeleteDirectoryIfExists(directoryName);
        }

        return pfxCertificate;
    }

    private static DateTime GetCertificateExpiryDate(byte[] signedCertificate)
    {
        var certificate = new X509Certificate2(signedCertificate);

        return certificate.NotAfter.ToUniversalTime();
    }

    private async Task UpdateProductInstanceData(ProductInstanceEntity productInstance, GatewayData gatewayData)
    {
        productInstance.Data = gatewayData;
        productInstanceRepository.Update(productInstance);

        await productInstanceRepository.SaveChangesAsync();
    }

    private void PublishUpdateEvent(ProductInstanceEntity productInstance)
    {
        if (Enum.TryParse(productInstance.Product.Type, out ProductTypes productType))
            publisherSelector.GetProductInstancePublisher(productType)
                ?.PublishUpdatedEvent(productInstance);
    }

    private static void CreateDirectory(string directoryName)
    {
        Directory.CreateDirectory(directoryName);
    }

    private void DeleteDirectoryIfExists(string directoryName)
    {
        if (Directory.Exists(directoryName))
        {
            logger.LogInformation("Deleting directory {@directoryName} with all files", directoryName);
            Directory.Delete(directoryName, true);
        }
    }


}
