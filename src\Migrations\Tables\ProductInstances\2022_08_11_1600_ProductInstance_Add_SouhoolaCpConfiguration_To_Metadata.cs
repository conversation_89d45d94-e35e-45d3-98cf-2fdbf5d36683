﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2022_08_11_1600)]
public class ProductInstance_Add_SouhoolaCpConfiguration_To_Metadata : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
                UPDATE ProductInstances SET [Metadata] = JSON_MODIFY([Metadata], '$.IsSouhoolaCpBnplEnabled', CAST(0 as BIT))
	            FROM ProductInstances pi
                INNER JOIN Products p ON p.id = pi.ProductId
                WHERE [Metadata] IS NOT NULL
                AND p.Type = 'GWAY' 
                AND ISJSON([Metadata]) > 0 

                UPDATE ProductInstances SET [Metadata] = JSON_MODIFY(JSON_MODIFY([Metadata], 'lax $.SouhoolaCpBnplNationalId', ''), 
			    'strict $.SouhoolaCpBnplNationalId',NULL)
	            FROM ProductInstances pi
                INNER JOIN Products p ON p.id = pi.ProductId
                WHERE [Metadata] IS NOT NULL
                AND p.Type = 'GWAY' 
                AND ISJSON([Metadata]) > 0 

                UPDATE ProductInstances SET [Metadata] = JSON_MODIFY(JSON_MODIFY([Metadata], 'lax $.SouhoolaCpBnplGlobalId', ''), 
				'strict $.SouhoolaCpBnplGlobalId',NULL)
	            FROM ProductInstances pi
                INNER JOIN Products p ON p.id = pi.ProductId
                WHERE [Metadata] IS NOT NULL
                AND p.Type = 'GWAY' 
                AND ISJSON([Metadata]) > 0 

                UPDATE ProductInstances SET [Metadata] = JSON_MODIFY(JSON_MODIFY([Metadata], 'lax $.SouhoolaCpBnplUserName', ''), 
				'strict $.SouhoolaCpBnplUserName',NULL)
	            FROM ProductInstances pi
                INNER JOIN Products p ON p.id = pi.ProductId
                WHERE [Metadata] IS NOT NULL
                AND p.Type = 'GWAY' 
                AND ISJSON([Metadata]) > 0 

                UPDATE ProductInstances SET [Metadata] = JSON_MODIFY(JSON_MODIFY([Metadata], 'lax $.SouhoolaCpBnplPassword', ''), 
				'strict $.SouhoolaCpBnplPassword',NULL)
	            FROM ProductInstances pi
                INNER JOIN Products p ON p.id = pi.ProductId
                WHERE [Metadata] IS NOT NULL
                AND p.Type = 'GWAY' 
                AND ISJSON([Metadata]) > 0
            ");
    }
}
