﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<ProjectGuid>{a894b464-f308-45e2-83d9-5880ba31f147}</ProjectGuid>
		<IsPackable>false</IsPackable>
		<LangVersion>latest</LangVersion>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="coverlet.msbuild" Version="3.2.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.5.0" />
		<PackageReference Include="Moq" Version="4.18.4" />
		<PackageReference Include="NSubstitute" Version="5.0.0" />
		<PackageReference Include="xunit" Version="2.4.2" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="coverlet.collector" Version="3.2.0">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Common\Common.csproj" />
		<ProjectReference Include="..\ProductService\ProductService.csproj" />
	</ItemGroup>
</Project>
