using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Common.Models;
using Common.Models.ProductInstance;
using Common.Services;
using Geidea.ProductService.Models;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Moq;
using ProductService.Controllers;
using Xunit;

namespace ProductService.Test;

public class ProductInstanceControllerTests
{
    private readonly Mock<IProductConfiguratorService> productConfiguratorService;
    private readonly ProductInstanceController controller;

    private readonly ProductInstanceResponse[] instances =
    {
            new ProductInstanceResponse {ProductInstanceId = Guid.NewGuid()},
            new ProductInstanceResponse {ProductInstanceId = Guid.NewGuid()}
        };
    private readonly ProductInstanceWithParentResponse[] instancesWithParents =
     {
            new ProductInstanceWithParentResponse {ProductInstanceId = Guid.NewGuid()},
            new ProductInstanceWithParentResponse {ProductInstanceId = Guid.NewGuid()}
        };

    private static readonly Guid ProductInstanceId = Guid.NewGuid();
    private static readonly Guid GatewayCompanyId = Guid.NewGuid();
    private readonly List<Guid?> GatewayComapnyIdResponse = new List<Guid?>() { GatewayCompanyId };
    private readonly ProductInstanceResponse instance = new ProductInstanceResponse { ProductInstanceId = ProductInstanceId };

    public ProductInstanceControllerTests()
    {
        productConfiguratorService = new Mock<IProductConfiguratorService>();

        productConfiguratorService
            .Setup(x => x.CreateAsync(It.IsAny<CreateProductInstanceRequest>(), false))
            .Returns(Task.FromResult(instances.First()));
        productConfiguratorService
            .Setup(x => x.PatchAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<UpdateProductInstanceRequest>>()))
            .Returns(Task.FromResult(instances.First()));
        productConfiguratorService
            .Setup(x => x.FindAsync(It.IsAny<FindProductInstanceRequest>(), It.IsAny<bool>()))
            .Returns(Task.FromResult(instancesWithParents));
        productConfiguratorService
            .Setup(x => x.DeleteAsync(It.IsAny<DeleteProductInstanceRequest>()))
            .Returns(Task.FromResult(instances.Select(x => x.ProductInstanceId).ToArray()));
        productConfiguratorService
            .Setup(x => x.FindByIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(instance));
        productConfiguratorService
            .Setup(x => x.GetProductInstancesForStore(It.IsAny<Guid>()))
            .Returns(Task.FromResult(new List<StoreProductInstanceResponse>()));
        productConfiguratorService
            .Setup(x => x.GetProductInstancesForMultipleStores(It.IsAny<Guid[]>()))
            .Returns(Task.FromResult(new List<StoreProductInstanceResponse>()));
        productConfiguratorService
            .Setup(x => x.UpdateProductInstancesStoreNameForMerchant(It.IsAny<MerchantStoreNameProductInstanceRequest>()))
            .Returns(Task.CompletedTask);
        productConfiguratorService
            .Setup(x => x.SearchGatewayConfigurations(It.IsAny<SearchGatewayConfigurationsRequest>()))
            .Returns(Task.FromResult(new SearchGatewayConfigurationsResponse()));
        productConfiguratorService
          .Setup(x => x.UpdateProductInstancesMsoSubMerchantInfo(It.IsAny<IList<SubMerchantMigrationInfoProductInstance>>()))
          .Returns(Task.CompletedTask);
        productConfiguratorService
        .Setup(x => x.FindGatewayProductCompanyIds(It.IsAny<bool>())).Returns(Task.FromResult(GatewayComapnyIdResponse));

        controller = new ProductInstanceController(productConfiguratorService.Object);
    }

    [Fact]
    public async Task CreateProductInstanceShouldCreateProductInstance()
    {
        var createProductInstanceRequest = new CreateProductInstanceRequest();
        var result = await controller.CreateProductInstance(createProductInstanceRequest);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as ProductInstanceResponse;
        Assert.NotNull(resultInstance);
        Assert.Equal(instances.First(), resultInstance);

        productConfiguratorService.Verify(x => x.CreateAsync(createProductInstanceRequest, false), Times.Once);
    }

    [Fact]
    public async Task PatchProductInstanceShouldPatchProductInstance()
    {
        var instanceId = Guid.NewGuid();
        var jsonPatchDocument = new JsonPatchDocument<UpdateProductInstanceRequest>();
        var result = await controller.PatchProductInstance(instanceId, jsonPatchDocument);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as ProductInstanceResponse;
        Assert.NotNull(resultInstance);
        Assert.Equal(instances.First(), resultInstance);

        productConfiguratorService.Verify(x => x.PatchAsync(instanceId, jsonPatchDocument), Times.Once);
    }

    [Fact]
    public async Task UpdateTerminalProductInstancesMetaShouldPatchProductInstance()
    {
        var request = new List<UpdateProductInstanceMetaRequest>
        {
            new UpdateProductInstanceMetaRequest { ProductInstanceId = Guid.NewGuid(), LegalName = "Name", LegalNameAr = "NameAr",
                MIDMerchantReference = "************", ProviderBank = "NBE_BANK", TId = "********",FullTId = "********" ,Trsm = "212125", TradingCurrency = "EGP" }
        };

        var result = await controller.UpdateTerminalProductInstancesMeta(request);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        productConfiguratorService.Verify(x => x.UpdateTerminalProductInstancesMeta(request), Times.Once);
    }

    [Fact]
    public async Task SearchProductInstancesShouldReturnTheSpecifiedProductInstances()
    {
        var findProductInstanceRequest = new FindProductInstanceRequest();
        var result = await controller.SearchProductInstances(findProductInstanceRequest);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstances = okResult.Value as ProductInstanceWithParentResponse[];
        Assert.NotNull(resultInstances);
        Assert.Equal(instancesWithParents.Length, resultInstances.Length);

        productConfiguratorService.Verify(x => x.FindAsync(findProductInstanceRequest, false), Times.Once);
    }

    [Fact]
    public async Task DeleteProductInstancesShouldDeleteProductInstances()
    {
        var deleteProductInstanceRequest = new DeleteProductInstanceRequest();
        var result = await controller.DeleteProductInstances(deleteProductInstanceRequest);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstanceIds = okResult.Value as Guid[];
        Assert.NotNull(resultInstanceIds);
        Assert.Equal(instances.Select(x => x.ProductInstanceId), resultInstanceIds);

        productConfiguratorService.Verify(x => x.DeleteAsync(deleteProductInstanceRequest), Times.Once);
    }

    [Fact]
    public async Task GetProductInstanceShouldReturnTheSpecifiedProductInstance()
    {
        var result = await controller.GetProductInstance(ProductInstanceId);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as ProductInstanceResponse;
        Assert.NotNull(resultInstance);
        Assert.Equal(instance, resultInstance);

        productConfiguratorService.Verify(x => x.FindByIdAsync(ProductInstanceId), Times.Once);
    }

    [Fact]
    public async Task GetProductStores_ShouldReturnSuccessfully()
    {
        var result = await controller.GetStoreProducts(ProductInstanceId);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as List<StoreProductInstanceResponse>;
        Assert.NotNull(resultInstance);

        productConfiguratorService.Verify(x => x.GetProductInstancesForStore(ProductInstanceId), Times.Once);
    }

    [Fact]
    public async Task GetMultipleStoresWithProducts_ShouldReturnSuccessfully()
    {
        var request = new IdsRequest()
        {
            Ids = new Guid[] { Guid.NewGuid() }
        };

        var result = await controller.GetMultipleStoresWithProducts(request);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as List<StoreProductInstanceResponse>;
        Assert.NotNull(resultInstance);

        productConfiguratorService.Verify(x => x.GetProductInstancesForMultipleStores(request.Ids), Times.Once);
    }

    [Fact]
    public async Task UpdateProductInstancesForFreelancer_ShouldUpdateStoreName()
    {
        var freelancerProductInstanceRequest = new MerchantStoreNameProductInstanceRequest();
        var result = await controller.UpdateProductInstancesStoreNameForMerchant(freelancerProductInstanceRequest);

        var okResult = result as OkResult;
        Assert.NotNull(okResult);

        productConfiguratorService.Verify(
            x => x.UpdateProductInstancesStoreNameForMerchant(freelancerProductInstanceRequest), Times.Once);
    }

    [Fact]
    public async Task SearchGatewayConfigurations_ShouldCallCorrectServiceMethodAndReturnSuccessfully()
    {
        var request = new SearchGatewayConfigurationsRequest();
        var result = await controller.SearchGatewayConfigurations(request);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as SearchGatewayConfigurationsResponse;
        Assert.NotNull(resultInstance);

        productConfiguratorService.Verify(x => x.SearchGatewayConfigurations(request), Times.Once);
    }

    [Fact]
    public async Task DeleteMpgsHistoryOverEstimatedPeriod_ShouldCleanupExpiredHistory()
    {
        var result = await controller.DeleteMpgsHistoryOverEstimatedPeriod();

        var okResult = result as OkResult;
        Assert.NotNull(okResult);
        productConfiguratorService.Verify(x => x.CleanExpiredMPGSHistory(), Times.Once);
    }

    [Fact]
    public async Task UpdateProductInstancesMsoSubMerchantInfo_ShouldUpdateSubMerchantInfo()
    {
        var subMerchantProductInstanceRequest = new List<SubMerchantMigrationInfoProductInstance>();
        var result = await controller.MigrateOldSubMerchantMetaData(subMerchantProductInstanceRequest);

        var okResult = result as OkResult;
        Assert.NotNull(okResult);

        productConfiguratorService.Verify(
            x => x.UpdateProductInstancesMsoSubMerchantInfo(subMerchantProductInstanceRequest), Times.Once);
    }

    [Fact]
    public async Task GetGatewayCompanyIdsShouldReturnTheAllGatewayCompanyIds()
    {
        var result = await controller.GetGatewayCompanyIds();

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        var resultInstance = okResult.Value as List<Guid?>;
        Assert.NotNull(resultInstance);
        Assert.Equal(GatewayComapnyIdResponse, resultInstance);

        productConfiguratorService.Verify(x => x.FindGatewayProductCompanyIds(false), Times.Once);
    }

    [Fact]
    public async Task CreateDefaultProductInstancesTerminalChildren()
    {
        var request = new DefaultProductInstanceRequest
        {
            AccountNumber = string.Empty,
            CompanyId = Guid.NewGuid(),
            DefaultOperation = string.Empty,
            MerchantName = string.Empty,
            MerchantNameAr = string.Empty,
            MpgsApiKey = string.Empty,
            MpgsMerchantId = string.Empty,
            ProductId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            SubMerchantInformation = new SubMerchantInformation(),
            TenantCode = string.Empty
        };

        var result = await controller.CreateDefaultProductInstancesChildren(request);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        productConfiguratorService.Verify(x => x.CreateDefaultProductInstancesChildrenAsync(request), Times.Once);
    }

    [Fact]
    public async Task CreateDefaultProductInstances()
    {
        var request = new DefaultProductInstanceRequest
        {
            AccountNumber = string.Empty,
            CompanyId = Guid.NewGuid(),
            DefaultOperation = string.Empty,
            MerchantName = string.Empty,
            MerchantNameAr = string.Empty,
            MpgsApiKey = string.Empty,
            MpgsMerchantId = string.Empty,
            ProductId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            SubMerchantInformation = new SubMerchantInformation(),
            TenantCode = string.Empty
        };

        var result = await controller.CreateDefaultProductInstances(request);

        var okResult = result as OkObjectResult;
        Assert.NotNull(okResult);

        productConfiguratorService.Verify(x => x.CreateDefaultProductInstancesAsync(request), Times.Once);
    }
}
