using System;
using System.Collections.Generic;
using System.Linq;
using Common.Validators;
using Geidea.ProductService.Models;
using Models;
using NUnit.Framework;
using static Geidea.Utils.Common.Constants;
using GatewayData = Common.Data.GatewayData;

namespace Common.Tests;

public class GatewayDataValidatorTests
{
    private readonly GatewayDataValidator validator = new();
    private GatewayData gatewayData = null!;

    private const string StringWith256Length = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa" +
                                               "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa" +
                                               "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";

    [SetUp]
    public void Setup()
    {
        gatewayData = new GatewayData();
    }

    [TestCase(10.0, 1200.5)]
    [TestCase(100, 101)]
    [TestCase(null, 100)]
    [TestCase(100, null)]
    [TestCase(null, null)]
    public void GatewayDataMinAmountMaxAmountValidationSuccess(decimal? minAmount, decimal? maxAmount)
    {
        gatewayData.MinAmount = minAmount;
        gatewayData.MaxAmount = maxAmount;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase("12")]
    public void GatewayDataValuProductIdParameterValidationSuccess(string value)
    {
        gatewayData.IsValuBnplEnabled = true;
        gatewayData.ValuVendorId = value;
        gatewayData.ValuProductId = value;
        gatewayData.ValuStoreId = value;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase("12")]
    public void GatewayDataGooglePayMerchantIdParameterValidationSuccess(string value)
    {
        gatewayData.IsGooglePayEnabled = true;
        gatewayData.GooglePayMerchantId = value;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase("12")]
    public void GatewayDataTamaraPublicKeyParameterValidationSuccess(string value)
    {
        gatewayData.IsTamaraEnabled = true;
        gatewayData.TamaraPublicKey = value;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase("12")]
    public void GatewayDataTamaraApiTokenParameterValidationSuccess(string value)
    {
        gatewayData.IsTamaraEnabled = true;
        gatewayData.TamaraApiToken = value;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }
    [TestCase("12")]
    public void GatewayDataSTCPayMerchantIdParameterValidationSuccess(string value)
    {
        gatewayData.IsStcPayEnabled = true;
        gatewayData.StcPayMerchantId = value;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase(true)]
    [TestCase(false)]
    public void GatewayDataSamsungPayMerchantIsSamsungPayWebEnabled(bool isSamsungPayWebEnabled)
    {
        gatewayData.IsSamsungPayWebEnabled = isSamsungPayWebEnabled;

        var result = validator.Validate(gatewayData);

        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }
    [Test]
    public void GatewayDataValuStoreIdParameterValidationFail()
    {
        string value = new string('a', 260);
        gatewayData.IsValuBnplEnabled = true;
        gatewayData.ValuProductId = value;
        gatewayData.ValuVendorId = "a";
        gatewayData.ValuStoreId = "a";

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be not valid");
    }

    [Test]
    public void GatewayDataGooglePayMerchantIdParameterValidationFail()
    {
        string value = new string('a', 19);
        gatewayData.IsGooglePayEnabled = true;
        gatewayData.GooglePayMerchantId = value;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be not valid");
    }

    [Test]
    public void GatewayDataValuVendorIdParameterValidationFail()
    {
        string value = new string('a', 260);
        gatewayData.IsValuBnplEnabled = true;
        gatewayData.ValuVendorId = value;
        gatewayData.ValuProductId = "a";
        gatewayData.ValuStoreId = "a";

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be not valid");
    }

    [Test]
    public void GatewayDataValuProductIdParameterValidationFail()
    {
        string value = new string('a', 260);
        gatewayData.IsValuBnplEnabled = true;
        gatewayData.ValuVendorId = "a";
        gatewayData.ValuProductId = "a";
        gatewayData.ValuStoreId = value;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be not valid");
    }

    [TestCase(false, "a", "a", "")]
    [TestCase(false, "a", "a", null)]
    [TestCase(true, "a", "a", "a")]
    public void GatewayDataisValuBnplEnabledParameterValidationSuccess(bool isValuBnplEnabled, string ValuProductId, string ValuStoreId, string ValuVendorId)
    {
        gatewayData.IsValuBnplEnabled = isValuBnplEnabled;
        gatewayData.ValuProductId = ValuProductId;
        gatewayData.ValuStoreId = ValuStoreId;
        gatewayData.ValuVendorId = ValuVendorId;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase(true, "", "", "")]
    [TestCase(true, "a", "a", "")]
    [TestCase(true, null, "a", "")]
    public void GatewayDataisValuBnplEnabledParameterValidationFail(bool isValuBnplEnabled, string ValuProductId, string ValuStoreId, string ValuVendorId)
    {
        gatewayData.IsValuBnplEnabled = isValuBnplEnabled;
        gatewayData.ValuProductId = ValuProductId;
        gatewayData.ValuStoreId = ValuStoreId;
        gatewayData.ValuVendorId = ValuVendorId;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be not valid");
    }

    [TestCase(MpgsMsoProviders.Geidea)]
    [TestCase(MpgsMsoProviders.Bm)]
    [TestCase(MpgsMsoProviders.Nbe)]
    [TestCase(MpgsMsoProviders.Magnati)]
    [TestCase(MpgsMsoProviders.Mock)]
    [TestCase(null)]
    public void GatewayDataMpgsMsoProviderValidationSuccess(string providerName)
    {
        gatewayData.MpgsMsoProvider = providerName;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase(CybersourceMsoProviders.GEIDEAUAE)]
    [TestCase(null)]
    public void GatewayDataCybersourceMsoProviderValidationSuccess(string providerName)
    {
        gatewayData.CyberSourceMsoProvider = providerName;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase(10.0, 10)]
    [TestCase(1001, 101)]
    [TestCase(-1, null)]
    [TestCase(null, -1)]
    [TestCase(0, 0)]
    public void GatewayDataMinAmountMaxAmountValidationFail(decimal? minAmount, decimal? maxAmount)
    {
        gatewayData.MinAmount = minAmount;
        gatewayData.MaxAmount = maxAmount;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [TestCase("SOME_OTHER_PROVIDER")]
    [TestCase("")]
    public void GatewayDataMpgsMsoProviderValidationFail(string providerName)
    {
        gatewayData.MpgsMsoProvider = providerName;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [Test]
    public void GatewayDataShahryCpBnplBranchCodeParameterValidationSuccess()
    {
        gatewayData.ShahryCpBnplBranchCode = "123415";

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase("1234567")]
    [TestCase("12345")]
    public void GatewayDataShahryCpBnplBranchCodeParameterValidationFail(string branchCode)
    {
        gatewayData.ShahryCpBnplBranchCode = branchCode;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be valid");
    }

    [Test]
    public void GatewayDataShahryCpBnplMerchantCodeParameterValidationSuccess()
    {
        gatewayData.ShahryCpBnplMerchantCode = "654321";

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase("1234567")]
    [TestCase("12345")]
    public void GatewayDataShahryCpBnplMerchantCodeParameterValidationFail(string branchCode)
    {
        gatewayData.ShahryCpBnplBranchCode = branchCode;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be valid");
    }

    [Test]
    public void GatewayDataIsOfflineShahryBnplEnabledParameterValidationSuccess()
    {
        gatewayData.IsShahryCpBnplEnabled = true;
        gatewayData.ShahryCpBnplBranchCode = "123456";
        gatewayData.ShahryCpBnplMerchantCode = "123456";

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase(true, "", "")]
    [TestCase(true, "123456", "")]
    [TestCase(true, "", "123456")]
    [TestCase(true, "12*$56", "123456")]
    [TestCase(true, "123456", "123!#6")]
    [TestCase(true, "523***", "123!#6")]
    public void GatewayDataIsOfflineShahryBnplEnabledParameterValidationFails(bool isShahryCpBnplEnabled, string shahryCpBnplBranchCode, string shahryCpBnplMerchantCode)
    {
        gatewayData.IsShahryCpBnplEnabled = isShahryCpBnplEnabled;
        gatewayData.ShahryCpBnplBranchCode = shahryCpBnplBranchCode;
        gatewayData.ShahryCpBnplMerchantCode = shahryCpBnplMerchantCode;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }


    [TestCase("123")]
    public void GatewayDataSouhoolaParameterValidationSuccess(string value)
    {
        gatewayData.IsSouhoolaCnpBnplEnabled = true;
        gatewayData.SouhoolaCnpUserName = value;
        gatewayData.SouhoolaCnpPassword = value;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [Test]
    public void GatewayDataSouhoolaMerchantNationalIdParameterValidationFail()
    {
        string value = new string('a', 260);
        gatewayData.IsSouhoolaCnpBnplEnabled = true;
        gatewayData.SouhoolaMerchantNationalId = value;
        gatewayData.SouhoolaMerchantPhoneNumber = "a";
        gatewayData.SouhoolaAccessKey = "a";

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be not valid");
    }

    [Test]
    public void GatewayDataSouhoolaMerchantPhoneNumberParameterValidationFail()
    {
        string value = new string('a', 260);
        gatewayData.IsSouhoolaCnpBnplEnabled = true;
        gatewayData.SouhoolaMerchantNationalId = "a";
        gatewayData.SouhoolaMerchantPhoneNumber = value;
        gatewayData.SouhoolaAccessKey = "a";

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be not valid");
    }

    [Test]
    public void GatewayDataSouhoolaAccessKeyParameterValidationFail()
    {
        string value = new string('a', 260);
        gatewayData.IsSouhoolaCnpBnplEnabled = true;
        gatewayData.SouhoolaMerchantNationalId = "a";
        gatewayData.SouhoolaMerchantPhoneNumber = "a";
        gatewayData.SouhoolaAccessKey = value;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be not valid");
    }

    [TestCase(true, "", "", "")]
    [TestCase(true, "a", "a", "")]
    [TestCase(true, null, "a", "")]
    [TestCase(true, "123%56", "123%56", "$542&1")]
    [TestCase(true, "-5+451", "-5/451", "123**6")]
    [TestCase(true, "", "523***", "123!#6")]
    [TestCase(true, null, "<script>", "<script>")]
    [TestCase(false, "test", "<script>", "<script>")]
    [TestCase(false, "a", "a", "")]
    [TestCase(false, "a", "a", null)]
    [TestCase(true, "a", "a", "a")]
    public void GatewayDataIsSouhoolaCnpBnplEnabledParameterValidationFail(bool isSouhoolaCnpBnplEnabled, string souhoolaMerchantNationalId, string souhoolaMerchantPhoneNumber, string souhoolaAccessKey)
    {
        gatewayData.IsSouhoolaCnpBnplEnabled = isSouhoolaCnpBnplEnabled;
        gatewayData.SouhoolaMerchantNationalId = souhoolaMerchantNationalId;
        gatewayData.SouhoolaMerchantPhoneNumber = souhoolaMerchantPhoneNumber;
        gatewayData.SouhoolaAccessKey = souhoolaAccessKey;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be not valid");
    }

    [Test]
    public void GatewayDataShahryCnpBnplBranchCodeParameterValidationSuccess()
    {
        gatewayData.ShahryCnpBnplBranchCode = "123415";

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase("1234567")]
    [TestCase("12345")]
    public void GatewayDataShahryCnpBnplBranchCodeParameterValidationFail(string branchCode)
    {
        gatewayData.ShahryCnpBnplBranchCode = branchCode;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be valid");
    }

    [Test]
    public void GatewayDataShahryCnpBnplMerchantCodeParameterValidationSuccess()
    {
        gatewayData.ShahryCnpBnplMerchantCode = "654321";

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase("1234567")]
    [TestCase("12345")]
    public void GatewayDataShahryCnpBnplMerchantCodeParameterValidationFail(string branchCode)
    {
        gatewayData.ShahryCnpBnplBranchCode = branchCode;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be valid");
    }

    [Test]
    public void GatewayDataIsOnlineShahryBnplEnabledParameterValidationSuccess()
    {
        gatewayData.IsShahryCnpBnplEnabled = true;
        gatewayData.ShahryCnpBnplBranchCode = "123456";
        gatewayData.ShahryCnpBnplMerchantCode = "123456";

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase(true, "", "")]
    [TestCase(true, "123456", "")]
    [TestCase(true, "", "123456")]
    [TestCase(true, "123%56", "$542&1")]
    [TestCase(true, "-5/451", "123**6")]
    public void GatewayDataIsOnlineShahryBnplEnabledParameterValidationFails(bool isShahryCnpBnplEnabled, string shahryCnpBnplBranchCode, string shahryCnpBnplMerchantCode)
    {
        gatewayData.IsShahryCnpBnplEnabled = isShahryCnpBnplEnabled;
        gatewayData.ShahryCnpBnplBranchCode = shahryCnpBnplBranchCode;
        gatewayData.ShahryCnpBnplMerchantCode = shahryCnpBnplMerchantCode;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [Test]
    public void GatewayDataMpgsAccountsValidationIsSuccessful()
    {
        gatewayData.MpgsAccounts = new List<MpgsAccount>
        {
            new(),
            new()
            {
                MpgsMerchantId = "TEST1",
                MpgsApiKey = "key1",
                CardBrands = new List<string>
                {
                    "VISA", "MADA"
                }
            },
            new()
            {
                MpgsMerchantId = "TEST2",
                MpgsApiKey = "key2",
                CardBrands = new List<string>
                {
                    "MC"
                }
            }
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [Test]
    public void GatewayDataCyberSourceAccountsValidationFailsWhenCyberSourceAccountsIsNull()
    {
        gatewayData.CyberSourceAccounts = null!;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [Test]
    public void GatewayDataMpgsAccountsValidationFailsWhenAnyOfMpgsAccountsIsNull()
    {
        gatewayData.MpgsAccounts = new List<MpgsAccount>
        {
            new()
            {
                MpgsMerchantId = "TEST1",
                MpgsApiKey = "key1",
                CardBrands = new List<string>
                {
                    "MC"
                }
            },
            null
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [TestCase("")]
    [TestCase(StringWith256Length)]
    public void GatewayDataMpgsAccountsValidationFailsWhenMpgsMerchantIdIsNotValid(string mpgsMerchantId)
    {
        gatewayData.MpgsAccounts = new List<MpgsAccount>
        {
            new()
            {
                MpgsMerchantId = mpgsMerchantId,
                MpgsApiKey = "key",
                CardBrands = new List<string>
                {
                    "MC"
                }
            }
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [TestCase("")]
    [TestCase(StringWith256Length)]
    public void GatewayDataMpgsAccountsValidationFailsWhenMpgsApiKeyIsNotValid(string mpgsApiKey)
    {
        gatewayData.MpgsAccounts = new List<MpgsAccount>
        {
            new()
            {
                MpgsMerchantId = "TEST",
                MpgsApiKey = mpgsApiKey,
                CardBrands = new List<string>
                {
                    "MC"
                }
            }
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [Test]
    public void GatewayDataMpgsAccountsValidationFailsWhenCardBrandsIsNull()
    {
        gatewayData.MpgsAccounts = new List<MpgsAccount>
        {
            new()
            {
                MpgsMerchantId = "TEST",
                MpgsApiKey = "key",
                CardBrands = null!
            }
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [TestCase(null)]
    [TestCase("")]
    [TestCase(StringWith256Length)]
    public void GatewayDataMpgsAccountsValidationFailsWhenAnyOfCardBrandsIsNotValid(string cardBrand)
    {
        gatewayData.MpgsAccounts = new List<MpgsAccount>
        {
            new()
            {
                MpgsMerchantId = "TEST",
                MpgsApiKey = "key",
                CardBrands = new List<string>
                {
                    "MC",
                    cardBrand
                }
            }
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }
    [Test]
    public void GatewayDataSubMerchantInformationValidationIsSuccessful()
    {
        gatewayData.SubMerchantInformation = new SubMerchantInformation
        {
            Id = "STARBUCKS",
            RegisteredName = "GEIDEA_FASTFOOD",
            TradingName = "NBEMID",
            City = "City",
            Country = "Country",
            Governorate = "Governorate",
            StreetAndNumber = "StreetAndNumber",
            ZipCode = "ZipCode",
            Email = "<EMAIL>",
            PhoneNumber = "+966511111111",
            CountryPrefix = "+20"
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [Test]
    public void GatewayDataSubMerchantInformationValidationFailsWhenSubMerchantInformationIsNull()
    {
        gatewayData.SubMerchantInformation = null!;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [TestCase("")]
    [TestCase(StringWith256Length)]
    public void GatewayDataSubMerchantInformationValidationFailsWhenIdIsNotValid(string id)
    {
        gatewayData.SubMerchantInformation = new SubMerchantInformation
        {
            Id = id,
            RegisteredName = "GEIDEA_FASTFOOD",
            TradingName = "NBEMID"
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [TestCase("GEIDEA_FASTFOOD", "")]
    [TestCase("", "NBEMID")]
    [TestCase("GEIDEA_FASTFOOD", "NBEMID")]
    public void GatewayDataSubMerchantInformationValidationFailsWhenAnyOfRegisteredNameOrTradingNameIsPassed(string registerName, string tradingName)
    {
        gatewayData.SubMerchantInformation = new SubMerchantInformation
        {
            Id = null,
            RegisteredName = registerName,
            TradingName = tradingName
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [TestCase("")]
    [TestCase(StringWith256Length)]
    public void GatewayDataSubMerchantInformationValidationFailsWhenRegisteredNameIsNotValid(string registeredName)
    {
        gatewayData.SubMerchantInformation = new SubMerchantInformation
        {
            Id = "STARBUCKS",
            RegisteredName = registeredName,
            TradingName = "NBEMID"
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [TestCase("")]
    [TestCase(StringWith256Length)]
    public void GatewayDataSubMerchantInformationValidationFailsWhenTradingNameIsNotValid(string tradingName)
    {
        gatewayData.SubMerchantInformation = new SubMerchantInformation
        {
            Id = "STARBUCKS",
            RegisteredName = "GEIDEA_FASTFOOD",
            TradingName = tradingName
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [TestCase("", "", "", "", "")]
    [TestCase(StringWith256Length, StringWith256Length, StringWith256Length, StringWith256Length, StringWith256Length)]
    public void GatewayDataSubMerchantInformationValidationFailsWhenAddressFieldsIsNotValid(string city, string country, string governorate, string streetAndNumber, string zipCode)
    {
        gatewayData.SubMerchantInformation = new SubMerchantInformation
        {
            City = city,
            Country = country,
            Governorate = governorate,
            StreetAndNumber = streetAndNumber,
            ZipCode = zipCode
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [TestCase("", "", "")]
    [TestCase(StringWith256Length, StringWith256Length, StringWith256Length)]
    public void GatewayDataSubMerchantInformationValidationFailsWhenContactFieldsIsNotValid(string email, string phoneNumber, string countryPrefix)
    {
        gatewayData.SubMerchantInformation = new SubMerchantInformation
        {
            Email = email,
            PhoneNumber = phoneNumber,
            CountryPrefix = countryPrefix
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [Test]
    public void GatewayDataSouhoolaCpBnplNationalIdParameterValidationSuccess()
    {
        gatewayData.SouhoolaCpBnplNationalId = "12345678912345";

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase("ab123")]
    [TestCase("1234567")]
    [TestCase("1234567891234567")]
    public void GatewayDataSouhoolaCpBnplNationalIdParameterValidationFail(string souhoolaCpBnplNationalId)
    {
        gatewayData.SouhoolaCpBnplNationalId = souhoolaCpBnplNationalId;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [Test]
    public void GatewayDataSouhoolaCpBnplGlobalIdParameterValidationSuccess()
    {
        gatewayData.SouhoolaCpBnplGlobalId = "123456";

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase("a123!")]
    [TestCase(StringWith256Length)]
    public void GatewayDataSouhoolaCpBnplGlobalIdParameterValidationFail(string souhoolaCpBnplGlobalId)
    {
        gatewayData.SouhoolaCpBnplGlobalId = souhoolaCpBnplGlobalId;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [Test]
    public void GatewayDataIsSouhoolaCpBnplEnabledParameterValidationSuccess()
    {
        gatewayData.IsSouhoolaCpBnplEnabled = true;
        gatewayData.SouhoolaCpBnplNationalId = "12345678912345";
        gatewayData.SouhoolaCpBnplGlobalId = "123456";
        gatewayData.SouhoolaCpBnplUserName = "test";
        gatewayData.SouhoolaCpBnplPassword = "test1";

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [TestCase(true, "", "", "", "")]
    [TestCase(true, "12345678912345", "", "test", "test")]
    [TestCase(true, "", "123456", "test", "test")]
    [TestCase(true, "12*$56", "123456", "test", "test")]
    [TestCase(true, "12345678912345", "123!#6", "test", "test")]
    [TestCase(true, "523abc", "123!#6", "test", "test")]
    public void GatewayDataIsSouhoolaCpBnplEnabledParameterValidationFails(bool isSouhoolaCpBnplEnabled, string souhoolaCpBnplNationalId, string souhoolaCpBnplGlobalId, string souhoolaCpBnplUserName, string souhoolaCpBnplPassword)
    {
        gatewayData.IsSouhoolaCpBnplEnabled = isSouhoolaCpBnplEnabled;
        gatewayData.SouhoolaCpBnplNationalId = souhoolaCpBnplNationalId;
        gatewayData.SouhoolaCpBnplGlobalId = souhoolaCpBnplGlobalId;
        gatewayData.SouhoolaCpBnplUserName = souhoolaCpBnplUserName;
        gatewayData.SouhoolaCpBnplPassword = souhoolaCpBnplPassword;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [Test]
    public void GatewayDataIsSouhoolaCpMerchantRegisteredParameterValidationSuccess()
    {
        gatewayData.IsSouhoolaCpMerchantRegistered = true;
        gatewayData.SouhoolaCpBnplNationalId = "12345678912345";
        gatewayData.SouhoolaCpBnplGlobalId = "123456";
        gatewayData.SouhoolaCpBnplUserName = "test";
        gatewayData.SouhoolaCpBnplPassword = "test1";

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }


    [TestCase(true, "", "", "", "")]
    [TestCase(true, "12345678912345", "", "test", "test")]
    [TestCase(true, "", "123456", "test", "test")]
    [TestCase(true, "12*$56", "123456", "test", "test")]
    [TestCase(true, "12345678912345", "123!#6", "test", "test")]
    [TestCase(true, "523abc", "123!#6", "test", "test")]
    public void GatewayDataIsSouhoolaCpMerchantRegisteredParameterValidationFails(bool isSouhoolaCpMerchantRegistered, string souhoolaCpBnplNationalId, string souhoolaCpBnplGlobalId, string souhoolaCpBnplUserName, string souhoolaCpBnplPassword)
    {
        gatewayData.IsSouhoolaCpMerchantRegistered = isSouhoolaCpMerchantRegistered;
        gatewayData.SouhoolaCpBnplNationalId = souhoolaCpBnplNationalId;
        gatewayData.SouhoolaCpBnplGlobalId = souhoolaCpBnplGlobalId;
        gatewayData.SouhoolaCpBnplUserName = souhoolaCpBnplUserName;
        gatewayData.SouhoolaCpBnplPassword = souhoolaCpBnplPassword;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [Test]
    public void GatewayDataMadaAccountsValidationIsSuccessful()
    {
        gatewayData.MadaAccounts = new List<MadaAccount>
        {
            new()
            {
                MadaApiKey = "MadaApiKey",
                MadaMerchantId = "MadaMerchantId",
                CardInfo = new List<CardInfo>
                {
                    new CardInfo()
                    {
                        PrimaryGatewayId = "PGI",
                        CardBrand = "CardBrand"
                    }
                },
                AcquirerMid = "AcquirerMidMaxL"
            }
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.True, "Result should be valid");
    }

    [Test]
    public void GatewayDataMadaAccountsValidationIsSuccessfulWhenMadaAccountsIsNull()
    {
        gatewayData.MadaAccounts = null!;

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should not be valid");
    }

    [Test]
    public void GatewayDataMadaAccountsValidationFailsWhenCardInfoIsNull()
    {
        gatewayData.MadaAccounts = new List<MadaAccount>
        {
            new()
            {
                MadaApiKey = "MadaApiKey",
                MadaMerchantId = "MadaMerchantId",
                CardInfo = null!
            }
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
    }

    [TestCase("")]
    [TestCase(StringWith256Length)]
    public void GatewayDataMadaAccountsValidationFailsWhenFieldsAreNotValid(string invalidValue)
    {
        gatewayData.MadaAccounts = new List<MadaAccount>
        {
            new()
            {
                MadaApiKey = invalidValue,
                AcquirerMid = invalidValue,
                CardInfo = new List<CardInfo>
                {
                    new()
                    {
                        PrimaryGatewayId = invalidValue,
                        CardBrand = invalidValue
                    }
                }
            }
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
        Assert.That(result.Errors.Any(m => m.PropertyName.Contains(nameof(MadaAccount.AcquirerMid))), Is.True, $"{nameof(MadaAccount.AcquirerMid)} should not be invalid");
        Assert.That(result.Errors.Any(m => m.PropertyName.Contains(nameof(MadaAccount.MadaApiKey))), Is.True, $"{nameof(MadaAccount.MadaApiKey)} should not be invalid");
        Assert.That(result.Errors.Any(m => m.PropertyName.Contains(nameof(CardInfo.CardBrand))), Is.True, $"{nameof(CardInfo.CardBrand)} should not be invalid");
        Assert.That(result.Errors.Any(m => m.PropertyName.Contains(nameof(CardInfo.PrimaryGatewayId))), Is.True, $"{nameof(CardInfo.PrimaryGatewayId)} should not be invalid");
    }

    [TestCase("AcquirerMid123_")]
    [TestCase("AcquirerMid123")]
    [TestCase("AcquirerMid12345")]
    public void GatewayDataMadaAccountsValidationFailsWhenAcquirerMidNotValid(string acquirerMid)
    {
        gatewayData.MadaAccounts = new List<MadaAccount>
        {
            new()
            {
                MadaApiKey = "MadaApiKey",
                MadaMerchantId = "MadaMerchantId",
                AcquirerMid = acquirerMid,
                CardInfo = new List<CardInfo>
                {
                    new CardInfo()
                    {
                        PrimaryGatewayId = "PGI",
                        CardBrand = "CardBrand"
                    }
                }
            }
        };

        var result = validator.Validate(gatewayData);

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(result.IsValid, Is.False, "Result should be invalid");
        Assert.That(result.Errors.Any(m => m.PropertyName.Contains(nameof(MadaAccount.AcquirerMid))), Is.True, $"{nameof(MadaAccount.AcquirerMid)} should not be invalid");
    }
}
