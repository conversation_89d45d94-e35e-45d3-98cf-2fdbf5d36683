﻿using Common.Models.ProductInstance;
using FluentValidation;
using System;

namespace Common.Validators;

public class ProductInstanceCreateValidator : AbstractValidator<CreateProductInstanceRequest>
{
    public ProductInstanceCreateValidator(bool isChildConfiguration)
    {
        RuleFor(c => c.AgreementId).NotEqual(Guid.Empty)
            .When(c => !isChildConfiguration && c != null)
            .WithMessage("AgreementId cannot be an empty Guid");

        RuleForEach(c => c.Children)
            .NotEmpty().WithMessage("Configuration cannot have null children.");
    }
}
