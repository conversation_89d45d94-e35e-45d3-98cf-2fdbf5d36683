﻿using Common.Entities;
using Common.Models.CommissionFees;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Repositories;

public interface ICommissionFeesRepository : IRepository<Guid, CommissionFeesEntity>
{
    Task<CommissionFeesEntity?> GetByIdAsync(Guid id);
    Task<GetCommissionFeesListResponse> GetCommissionFeesList(GetCommissionFeesListRequest request);
    Task<List<CommissionFees>> GetCommissionFees();
}
