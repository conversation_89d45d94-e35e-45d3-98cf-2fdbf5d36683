﻿using Common.Entities;
using Common.Models.businessType;
using Common.Models.CommissionFees;
using Common.Models.NonTransactionalFees;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProductService.Controllers;
[Route("api/v1/[controller]")]
[ApiController]
public class NonTransactionalFeesController : ControllerBase
{
    private readonly INonTransactionalFeesService nonTransactionalFeesService;
    public NonTransactionalFeesController(INonTransactionalFeesService nonTransactionalFeesService)
    {
        this.nonTransactionalFeesService = nonTransactionalFeesService;
    }

    /// <summary>
    /// Get NonTransactional FeesList, with search, sort and filter functionalities.
    /// </summary>
    [HttpPost("GetNonTransactionalFeesList")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetNonTransactionalFeesList(GetNonTransactionalFeesListRequest request)
    {
        var NonTransactionalFeesList = await nonTransactionalFeesService.GetNonTransactionalFeesList(request);
        return Ok(NonTransactionalFeesList);
    }
    /// <summary>
    /// Toggle NonTransactional Fees record status, active or inactive
    /// </summary>
    /// <param name="Id">The Id of the NonTransactional Fees record, to change its status, if existed.</param>
    /// <param name="Status">boolean parameter to set the status, 0 for in active, and 1 for active</param>
    [HttpPost("ToggleStatus/{Id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ToggleStatus(Guid Id, [FromHeader] bool Status)
    {
        var result = await nonTransactionalFeesService.ToggleStatus(Id, Status);
        if (!result)
            return NotFound();

        return Ok(result);
    }
    /// <summary>
    /// Get NonTransactional Fees details by Id
    /// </summary>
    /// <param name="Id">A passed parameter to check if it is existed in database, or not</param>
    /// <returns>Commission fee details of type [NonTransactionalFeesDetails]</returns>
    [HttpGet("GetNonTransactionalFeesDetails/{Id}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(NonTransactionalFeesEntity), statusCode: StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DetailsAsync(Guid Id)
    {
        var NonTransactionalFeesDetails = await nonTransactionalFeesService.GetNonTransactionalFeesDetails(Id);
        return Ok(NonTransactionalFeesDetails);
    }
    /// <summary>
    ///Create a non-transactional fee based on the provided request data.
    /// </summary>
    /// <param name="request">The details of non-transactional fee to be added.</param>
    /// <returns>
    /// Returns an <see cref="IActionResult"/> Indicating the result of the operation.
    /// If sucessfully created, returns http 200 ok with the result of creation.
    /// If the creation failed, returns http 400 Bad Request, with the result indicating the failure.
    /// </returns>
    [HttpPost("CreateNonTransactionalFee")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateAsync(NonTransactionalFeesRequest request)
    {
        var Result = await nonTransactionalFeesService.CreateAsync(request);
        return Ok(Result);
    }
    /// <summary>
    /// get non-transactional fee with given id, and update it if existed.
    /// </summary>
    /// <param name="id">The ID non-transactional fee record to be updated</param>
    /// <param name="request">The updated non-transactional fees details</param>
    /// <returns>Returns the updated non-transactional fee record</returns>
    [HttpPut("UpdateNonTransactionalFee/{id}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateAsync(Guid id, [FromBody] NonTransactionalFeesRequest request)
    {
        var updatedCommissionFees = await nonTransactionalFeesService.UpdateAsync(id, request);
        return Ok(updatedCommissionFees);
    }
    /// <summary>
    /// get NonTransactional fee  Ids , Names ;
    /// </summary>
    [HttpGet("GetNonTransactionalFeesNames")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(List<BasicNonTransactionalFeeInfo>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetNonTransactionalFeesNamesAsync()
    {
        var nonTransactionalFeesNames = await nonTransactionalFeesService.GetNonTransactionalFeesNamesAsync();
        return Ok(nonTransactionalFeesNames);
    }
}
