﻿using AutoMapper;
using Common;
using Common.Data.ProductType;
using Common.Entities;
using Common.Models;
using Common.Models.ProductInstance;
using Common.Repositories;
using Geidea.ProductService.Models;
using Geidea.Utils.DataAccess.Repositories;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace DataAccess.Repositories;

public class ProductInstanceRepository : AuditableRepository<Guid, ProductInstanceEntity>, IProductInstanceRepository
{
    private readonly ILogger<ProductInstanceRepository> logger;
    private readonly IMapper mapper;

    public ProductInstanceRepository(DbContext context, IHttpContextAccessor contextAccessor,
        ILogger<ProductInstanceRepository> logger, IMapper mapper) : base(context, contextAccessor)
    {
        this.logger = logger;
        this.mapper = mapper;
    }

    public async Task DeleteAsync(Guid productInstanceId)
    {
        var productInstance = await GetByIdAsync(productInstanceId, true);

        productInstance.DeletedFlag = true;
        context.Set<ProductInstanceEntity>().Update(productInstance);

        await context.SaveChangesAsync();
    }

    public async Task<List<ProductInstanceEntity>> GetProductInstancesForStore(Guid storeId)
    {
        var query = context.Set<ProductInstanceEntity>()
            .AsQueryable()
            .Include(x => x.Product)
            .Include(x => x.Children)
            .AsNoTracking()
            .Where(x => x.StoreId == storeId && !x.DeletedFlag);

        return await query.ToListAsync();
    }

    public async Task<List<StoreProductInstanceResponse>> GetProductInstancesForMultipleStores(Guid[] storeIds)
    {
        var query = context.Set<ProductInstanceEntity>()
            .AsQueryable()
            .Where(x => x.StoreId != null && storeIds.Contains(x.StoreId.Value) && !x.DeletedFlag)
            .Select(x =>
                new StoreProductInstanceResponse
                {
                    StoreId = x.StoreId,
                    ParentId = x.ParentId,
                    ProductInstanceId = x.Id,
                    CreatedDate = x.CreatedDate,
                    Product = new ProductShortResponse()
                    {
                        ProductId = x.ProductId,
                        Code = x.Product.Code,
                        Type = x.Product.Type,
                        Availability = x.Product.Availability,
                        ValidFrom = x.Product.ValidFrom,
                        ValidTo = x.Product.ValidTo,
                    },
                    Data = x.Metadata,
                    EPosTicketId = x.EPosTicketId
                })
            .AsNoTracking();
        var multipleStoresWithProductInstances = await query.ToListAsync();
        return multipleStoresWithProductInstances;
    }
    public async Task<List<Guid?>> FindGatewayCompanyIds(FindGatewayCompanyIds request, bool includeDeleted = false, bool track = false)
    {
        var query = context.Set<ProductInstanceEntity>().AsQueryable();

        if (!track)
            query = query.AsNoTracking();

        if (request.Types != null && request.Types.Any())
        {
            var productQuery = context.Set<ProductEntity>().AsQueryable();

            if (!track)
                productQuery = productQuery.AsNoTracking();

            var productTypes = request.Types.Select(t => t.ToUpper());

            var productIds = productQuery.Where(x => productTypes.Contains(x.Type)).Select(x => x.Id);
            query = query.Where(x => productIds.Contains(x.ProductId));
        }


        if (request.CompanyId != null)
            query = query.Where(x => x.CompanyId == request.CompanyId);



        query = query.Include(x => x.Product)
                        .ThenInclude(x => x.Prices)
                    .Include(x => x.Children)
                        .ThenInclude(x => x.Children)
                            .ThenInclude(x => x.Children)
                    .Include(x => x.Children)
                        .ThenInclude(x => x.Product)
                    .Include(x => x.Children)
                        .ThenInclude(x => x.Children)
                                .ThenInclude(x => x.Product)
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                        .Include(x => x.Parent).ThenInclude(x => x.Product)
                    .Include(x => x.Parent).ThenInclude(x => x.Parent).ThenInclude(x => x.Product);
#pragma warning restore CS8602 // Dereference of a possibly null reference.

        if (!includeDeleted)
            query = query.Where(x => !x.DeletedFlag && x.Metadata != null);

        var productInstances = await query
            .OrderBy(x => x.CreatedDate)
            .Select(x => x.CompanyId)
            .Distinct()
            .ToListAsync();


        return productInstances;
    }

    public async Task<List<ProductInstanceEntity>> FindAsync(FindProductInstanceRequest findProductInstanceRequest, bool includeDeleted = false, bool track = false)
    {
        var query = context.Set<ProductInstanceEntity>().AsQueryable();

        if (!track)
            query = query.AsNoTracking();

        if (findProductInstanceRequest.ProductInstanceId != null && findProductInstanceRequest.ProductInstanceId.Any())
            query = query.Where(x => findProductInstanceRequest.ProductInstanceId.Contains(x.Id));

        if (findProductInstanceRequest.Types != null && findProductInstanceRequest.Types.Any())
        {
            var productQuery = context.Set<ProductEntity>().AsQueryable();

            if (!track)
                productQuery = productQuery.AsNoTracking();

            var productTypes = findProductInstanceRequest.Types.Select(t => t.ToUpper());

            var productIds = productQuery.Where(x => productTypes.Contains(x.Type)).Select(x => x.Id);
            query = query.Where(x => productIds.Contains(x.ProductId));
        }

        if (findProductInstanceRequest.AgreementId != null && findProductInstanceRequest.AgreementId.Any())
            query = query.Where(c => c.AgreementId != null && findProductInstanceRequest.AgreementId.Contains(c.AgreementId.Value));

        if (findProductInstanceRequest.StoreId != null)
            query = query.Where(x => x.StoreId == findProductInstanceRequest.StoreId);

        if (findProductInstanceRequest.CompanyId != null)
            query = query.Where(x => x.CompanyId == findProductInstanceRequest.CompanyId);

        if (findProductInstanceRequest.ProductId != null)
            query = query.Where(x => x.ProductId == findProductInstanceRequest.ProductId);

        if (findProductInstanceRequest.EposTicketId != null)
            query = query.Where(x => x.EPosTicketId == findProductInstanceRequest.EposTicketId);

        query = query.Include(x => x.Product)
                        .ThenInclude(x => x.Prices)
                    .Include(x => x.Children)
                        .ThenInclude(x => x.Children)
                            .ThenInclude(x => x.Children)
                    .Include(x => x.Children)
                        .ThenInclude(x => x.Product)
                    .Include(x => x.Children)
                        .ThenInclude(x => x.Children)
                                .ThenInclude(x => x.Product)
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                        .Include(x => x.Parent).ThenInclude(x => x.Product)
                    .Include(x => x.Parent).ThenInclude(x => x.Parent).ThenInclude(x => x.Product);
#pragma warning restore CS8602 // Dereference of a possibly null reference.

        if (!includeDeleted)
            query = query.Where(x => !x.DeletedFlag);

        var productInstances = await query
            .OrderBy(x => x.CreatedDate)
            .Skip(findProductInstanceRequest.Skip)
            .Take(findProductInstanceRequest.Take)
            .ToListAsync();

        productInstances = RemoveDeletedChildren(productInstances);

        return productInstances;
    }

    public Task<int> CountAsync(CountProductInstanceRequest request, bool includeDeleted = false)
    {
        var query = context.Set<ProductInstanceEntity>().AsNoTracking().AsQueryable();

        if (request.ProductInstanceId != null && request.ProductInstanceId.Any())
            query = query.Where(x => request.ProductInstanceId.Contains(x.Id));

        if (request.Types != null && request.Types.Any())
        {
            var productIds = context.Set<ProductEntity>().Where(x => request.Types.Select(t => t.ToLower()).Contains(x.Type.ToLower())).Select(x => x.Id);
            query = query.Where(x => productIds.Contains(x.ProductId));
        }

        if (request.AgreementId != null && request.AgreementId.Any())
            query = query.Where(c => c.AgreementId != null && request.AgreementId.Contains(c.AgreementId.Value));

        if (request.StoreId != null)
            query = query.Where(x => x.StoreId == request.StoreId);

        if (request.ProductId != null)
            query = query.Where(x => x.ProductId == request.ProductId);

        if (request.EposTicketId != null)
            query = query.Where(x => x.EPosTicketId == request.EposTicketId);

        if (!includeDeleted)
            query = query.Where(x => !x.DeletedFlag);

        return query.CountAsync();
    }

    public async Task<List<ProductInstanceEntity>> FindByIdsAsync(IdsRequest request, bool track = false)
    {
        var query = context.Set<ProductInstanceEntity>().Where(p => request.Ids.Contains(p.Id)).AsQueryable();
        if (!track)
            query = query.AsNoTracking();

        return await query.Include(x => x.Product)
            .Include(x => x.Children)
                .ThenInclude(x => x.Children)
                .ThenInclude(x => x.Children)
            .Include(x => x.Children)
                .ThenInclude(x => x.Product)
            .Include(x => x.Children)
                .ThenInclude(x => x.Children)
                .ThenInclude(x => x.Product)
            .ToListAsync();
    }

    public async Task<ProductInstanceEntity> GetByIdAsync(Guid productInstanceId, bool track = false)
    {
        var productInstance = (await FindAsync(new FindProductInstanceRequest { ProductInstanceId = new[] { productInstanceId } }, false, track)).SingleOrDefault();

        if (productInstance == null)
        {
            logger.LogError($"ProductInstance with id {productInstanceId} was not found or has already been deleted.");
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductInstanceNotFound);
        }

        return productInstance;
    }

    public async Task<ProductInstanceEntity?> GetByIdWithProductAndChildrenAsync(Guid productInstanceId)
    {
        var productInstance = await context.Set<ProductInstanceEntity>()
            .Include(pi => pi.Product)
            .Include(pi => pi.Children)
                .ThenInclude(c => c.Product)
            .SingleOrDefaultAsync(pi => pi.Id == productInstanceId && !pi.DeletedFlag);

        if (productInstance != null)
            productInstance.Children.RemoveAll(c => c.DeletedFlag);

        return productInstance;
    }

    public Task<ProductInstanceEntity[]> SearchProductInstanceBaseAsync(SearchProductInstanceBaseRequest request)
    {
        var query = context.Set<ProductInstanceEntity>().AsQueryable().AsNoTracking();

        if (request.StoreIds != null && request.StoreIds.Any())
            query = query.Where(x => x.StoreId != null && request.StoreIds.Contains((Guid)x.StoreId));

        if (request.Types != null && request.Types.Any())
        {
            var productIds = context.Set<ProductEntity>().Where(x => request.Types.Select(t => t.ToLower()).Contains(x.Type.ToLower())).Select(x => x.Id);
            query = query.Where(x => productIds.Contains(x.ProductId));
        }

        return query.Include(x => x.Product).Where(x => !x.DeletedFlag).ToArrayAsync();
    }

    public async Task<SearchGatewayConfigurationsResponse> SearchGatewayConfigurations(SearchGatewayConfigurationsRequest request)
    {
        var query =
            (from gi in context.Set<GatewayInstanceEntity>().IgnoreQueryFilters().AsNoTracking()
                .Include(e => e.ProductInstance)
                    .ThenInclude(x => x.Product)
                .Include(e => e.ProductInstance)
                    .ThenInclude(x => x.Children)
                        .ThenInclude(x => x.Product)
             join mi in context.Set<MeezaInstanceEntity>().IgnoreQueryFilters().AsNoTracking()
                .Include(e => e.ProductInstance)
                    .ThenInclude(x => x.Product)
             on gi.CompanyId equals mi.CompanyId into gm
             from mi in gm.DefaultIfEmpty()
             where !gi.ProductInstance.DeletedFlag && (mi == null || !mi.ProductInstance.DeletedFlag)
             select new
             {
                 GatewayInstance = gi,
                 MeezaInstance = mi
             });

        if (request.MerchantGatewayKey is not null)
            query = query.Where(g => g.GatewayInstance.MerchantGatewayKey == request.MerchantGatewayKey);

        if (request.CompanyId is not null)
            query = query.Where(g => g.GatewayInstance.CompanyId == request.CompanyId);

        if (request.StoreId is not null)
            query = query.Where(g => g.GatewayInstance.StoreId == request.StoreId);

        if (request.ShahryCpBnplMerchantCode is not null)
            query = query.Where(g => g.GatewayInstance.ShahryCpBnplMerchantCode == request.ShahryCpBnplMerchantCode);

        if (request.ShahryCpBnplBranchCode is not null)
            query = query.Where(g => g.GatewayInstance.ShahryCpBnplBranchCode == request.ShahryCpBnplBranchCode);

        if (request.IsTest is not null)
            query = query.Where(g => g.GatewayInstance.IsTest == request.IsTest);

        if (request.MeezaMerchantId is not null)
            query = query.Where(g => g.MeezaInstance.MeezaMerchantId == request.MeezaMerchantId);

        var resultsCount = await query.CountAsync();

        var searchResponse = new SearchGatewayConfigurationsResponse
        {
            TotalCount = resultsCount
        };

        if (resultsCount > 0)
        {
            var results = await query
                .OrderBy(e => e.GatewayInstance.ProductInstance.CreatedDate)
                 .Skip(request.Skip)
                 .Take(request.Take)
                 .ToListAsync();

            searchResponse.Count = results.Count;
            searchResponse.Configurations = results.Select(c => new GatewayConfigurationResponse
            {
                GatewayProductInstance = mapper.Map<ProductInstanceResponse>(c.GatewayInstance.ProductInstance),
                MeezaProductInstance = c.MeezaInstance is not null ? mapper.Map<ProductInstanceResponse>(c.MeezaInstance.ProductInstance) : null
            }).ToList();
        }

        return searchResponse;
    }

    public async Task<bool> MeezaMerchantIdExists(string meezaMerchantId)
    {
        return await context.Set<MeezaInstanceEntity>().AsNoTracking()
            .AnyAsync(mi => mi.MeezaMerchantId == meezaMerchantId);
    }

    public async Task<bool> IsSubscribedToPaymentGateway(Guid merchantId)
    {
        var products = context
            .Set<ProductEntity>()
            .AsNoTracking()
            .AsQueryable()
            .Where(t => t.Type == ProductTypes.GWAY.ToString())
            .Select(p => p.Id);

        var productInstances = context
            .Set<ProductInstanceEntity>()
            .AsNoTracking()
            .AsQueryable()
            .Where(productInstance =>
                    productInstance.CompanyId == merchantId
                    && products.Contains(productInstance.ProductId));

        return await productInstances.AnyAsync();
    }

    private List<ProductInstanceEntity> RemoveDeletedChildren(List<ProductInstanceEntity> productInstances)
    {
        foreach (var productInstance in productInstances)
        {
            productInstance.Children = productInstance.Children.Where(child => !child.DeletedFlag).ToList();
            productInstance.Children = RemoveDeletedChildren(productInstance.Children);
        }

        return productInstances;
    }

    public async Task<List<ProductInstanceEntity>> GetProductInstanceByCompanyIdAsync(Guid companyId, bool track = false)
    {
        var productInstance = await FindAsync(new FindProductInstanceRequest { CompanyId = companyId, Types = new string[] { "GWAY" }, }, false, false);

        if (productInstance == null)
        {
            logger.LogError("ProductInstance with companyid {companyId} was not found or has already been deleted.", companyId);
            throw new ServiceException(HttpStatusCode.NotFound, Errors.ProductInstanceNotFound);
        }

        return productInstance;
    }

    [ExcludeFromCodeCoverage]
    public async Task CleanExpiredMPGSHistory(DateTimeOffset ExpirationDate)
    {
        try
        {
            SqlParameter periodParam = new SqlParameter("@ExpirationDate", ExpirationDate);
            using var command = context.Database.GetDbConnection().CreateCommand();
            command.CommandText = "[dbo].[DeleteExpiredMPGSHistory]";

            command.CommandType = System.Data.CommandType.StoredProcedure;
            command.CommandTimeout = 300;
            command.Parameters.Add(periodParam);

            if (command.Connection?.State == System.Data.ConnectionState.Closed)
            {
                command.Connection.Open();
            }

            await command.ExecuteNonQueryAsync();
        }
        catch (Exception ex)
        {
            logger.LogError("Error occured while trying to execute stored procedure to Delete Mpgs History data, " +
                "at TimeStamp {datetime} with the message: {message}.", DateTime.UtcNow, ex.Message);
            throw new ServiceException(Errors.MpgsHistorySpCleanupError);
        }
    }

    [ExcludeFromCodeCoverage]
    public async Task SendCompanyIdsToStoredProcedure(IList<SubMerchantMigrationInfoProductInstance> request)
    {
        DataTable dataTable = new DataTable();
        dataTable.Columns.Add("CmpanyId", typeof(Guid));
        dataTable.Columns.Add("Mcc", typeof(string));
        dataTable.Columns.Add("Mso", typeof(string));
        foreach (var item in request)
        {
            dataTable.Rows.Add(item.CompanyId, item.Mcc, item.Mso);
        }
        SqlParameter parameter = new SqlParameter("@merchantServiceData", SqlDbType.Structured);
        parameter.Value = dataTable;

        using var command = context.Database.GetDbConnection().CreateCommand();
        command.CommandText = "[dbo].[MigrateOldSubMerchantData]";

        command.CommandType = System.Data.CommandType.StoredProcedure;
        command.CommandTimeout = 300;
        command.Parameters.Add(parameter);

        if (command.Connection?.State == System.Data.ConnectionState.Closed)
        {
            command.Connection.Open();
        }

        await command.ExecuteNonQueryAsync();
    }

    public async Task<List<string>> SelectByQueryString(string queryStatemet)
    {
        var query = context.Set<MetaDataMigrationFilesEntity>().FromSqlRaw(queryStatemet).AsNoTracking();
        var MetaDataMigrationFiles = await query.Select(x => x.Id.ToString()).ToListAsync();
        return MetaDataMigrationFiles;
    }

    public async Task<bool> UpdateByQueryString(string queryStatemet)
    {
        await context.Database.ExecuteSqlRawAsync(queryStatemet);

        return true;
    }
}
