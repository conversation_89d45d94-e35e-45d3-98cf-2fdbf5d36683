﻿using AutoMapper;
using Common.Data.Extensions;
using Common.Entities;
using Common.Enums;
using Common.Models.UnitPrice;
using Common.Models.ValueAddedSerivcePricing;
using Common.Repositories;
using Common.Views;
using DocumentFormat.OpenXml.InkML;
using EFCore.BulkExtensions;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Repositories;
public class ValueAddedServicePricingRepository : AuditableRepository<Guid, ValueAddedServicePricingEntity>, IValueAddedServicePricingRepository
{
    private IQueryable<ValueAddedServicePricingListView> ValueAddedServicePricingListQuery;
    public ValueAddedServicePricingRepository(DataContext context, IHttpContextAccessor contextAccessor, IMapper mapper) : base(context, contextAccessor)
    {
        ValueAddedServicePricingListQuery = context.ValueAddedServicePricingList.AsQueryable();
    }
    public void AddRange(IEnumerable<ValueAddedServicePricingEntity> entities)
    {
        context.Set<ValueAddedServicePricingEntity>().AddRange(entities);
    }
    public async Task<List<ValueAddedServicePricingEntity>> GetExistVasPrices(Expression<Func<ValueAddedServicePricingEntity, bool>> predicate)
    {
        return await context.Set<ValueAddedServicePricingEntity>().Where(predicate).ToListAsync();
    }
    public async Task<ValueAddedServicePricingEntity?> GetByIdAsync(Guid id)
    {
        return await context.Set<ValueAddedServicePricingEntity>().FindAsync(id);
    }
    public async Task SaveVasPricesAsync(List<ValueAddedServicePricingEntity> vasPrices)
    {
        AddRange(vasPrices);
        await context.SaveChangesAsync();
    }
    public async Task<int> DeleteBulkAsync(List<Guid> ids)
    {
        var rowsAffected = await context.Set<ValueAddedServicePricingEntity>()
            .Where(up => ids.Contains(up.Id))
            .BatchDeleteAsync();

        return rowsAffected;
    }
    public async Task<List<ValueAddedServicePricingEntity>> GetAddonsPricesByIdsAsync(List<Guid> ids)
    {
        return await context.Set<ValueAddedServicePricingEntity>()
            .Where(up => ids.Contains(up.Id))
            .ToListAsync();
    }
    public async Task AddLogsAsync(List<ValueAddedServicePricingLogEntity> logs)
    {
        await context.Set<ValueAddedServicePricingLogEntity>().AddRangeAsync(logs);
        await context.SaveChangesAsync();
    }

    #region Listing
    public async Task<ValueAddedServicePriceListResponse> GetValueAddedServicePricesList(ValueAddedServicePriceListRequest request)
    {
        ValueAddedServicePricingListQuery = ValueAddedServicePricingListQuery.AsNoTracking();

        ValueAddedServicePricingListQuery = SearchAddOnsPrices(request, ValueAddedServicePricingListQuery);
        ValueAddedServicePricingListQuery = FilterAddOnsPrices(request, ValueAddedServicePricingListQuery);

        ValueAddedServicePricingListQuery = SortAddOnsPrices(request, ValueAddedServicePricingListQuery);

        int totalCount = await ValueAddedServicePricingListQuery.CountAsync();

        // Conditional pagination: Apply pagination only if Size > 0
        var valueAddedServicePricingListViews = request.Size > 0
            ? await ValueAddedServicePricingListQuery.Page(request.Page, request.Size).ToArrayAsync()
            : await ValueAddedServicePricingListQuery.ToArrayAsync();

        return new ValueAddedServicePriceListResponse
        {
            TotalCount = totalCount,
            TotalPages = request.Size > 0 ? (int)Math.Ceiling((double)totalCount / request.Size) : 1,
            valueAddedServicePricingListViews = valueAddedServicePricingListViews
        };
    }
    private static IQueryable<ValueAddedServicePricingListView> SearchAddOnsPrices(ValueAddedServicePriceListRequest request, IQueryable<ValueAddedServicePricingListView> valueAddedServicePricingListViews)
    {
        var SearchTerm = request.SearchTerms.FirstOrDefault(s => !string.IsNullOrEmpty(s.Value));
        if (!string.IsNullOrEmpty(SearchTerm.Value))
        {
            switch (SearchTerm.Key)
            {
                case AddOnsSearchKey.All:
                    valueAddedServicePricingListViews = valueAddedServicePricingListViews.Where(s => s.ProductName != null && s.ProductName.Contains(SearchTerm.Value) ||
                                                                s.ProductCode.Contains(SearchTerm.Value) ||
                                                                s.MccName.Contains(SearchTerm.Value) ||
                                                                s.MccCode.Contains(SearchTerm.Value) ||
                                                                s.VASCode.Contains(SearchTerm.Value) ||
                                                                s.ValueAddedServiceName.Contains(SearchTerm.Value)
                                                               );
                    break;
                case AddOnsSearchKey.ProductName:
                    valueAddedServicePricingListViews = valueAddedServicePricingListViews.Where(s => s.ProductName != null && s.ProductName.Contains(SearchTerm.Value));
                    break;
                case AddOnsSearchKey.ProductCode:
                    valueAddedServicePricingListViews = valueAddedServicePricingListViews.Where(s => s.ProductCode.Contains(SearchTerm.Value));
                    break;
                case AddOnsSearchKey.MccName:
                    valueAddedServicePricingListViews = valueAddedServicePricingListViews.Where(s => s.MccName.Contains(SearchTerm.Value));
                    break;
                case AddOnsSearchKey.MccCode:
                    valueAddedServicePricingListViews = valueAddedServicePricingListViews.Where(s => s.MccCode.Contains(SearchTerm.Value));
                    break;
                case AddOnsSearchKey.AddOnsName:
                    valueAddedServicePricingListViews = valueAddedServicePricingListViews.Where(s => s.ValueAddedServiceName.Contains(SearchTerm.Value));
                    break;
                case AddOnsSearchKey.AddOnsCode:
                    valueAddedServicePricingListViews = valueAddedServicePricingListViews.Where(s => s.VASCode.Contains(SearchTerm.Value));
                    break;
                default:
                    break;
            }
        }
        return valueAddedServicePricingListViews;
    }
    private static IQueryable<ValueAddedServicePricingListView> FilterAddOnsPrices(
      ValueAddedServicePriceListRequest request,
      IQueryable<ValueAddedServicePricingListView> valueAddedServicePricingListViews)
    {
        if (request.FilterByProducts != null && request.FilterByProducts.Any())
        {
            valueAddedServicePricingListViews = valueAddedServicePricingListViews
                .Where(s => request.FilterByProducts.Contains(s.ProductId));
        }

        if (request.FilterByBusinessType != null && request.FilterByBusinessType.Any())
        {
            valueAddedServicePricingListViews = valueAddedServicePricingListViews
                .Where(s => request.FilterByBusinessType.Contains(s.BusinessTypeId));
        }

        if (request.FilterByMccType != null && request.FilterByMccType.Any())
        {
            valueAddedServicePricingListViews = valueAddedServicePricingListViews
                .Where(s => request.FilterByMccType.Contains(s.MccId));
        }

        if (request.FilterByAddOns != null && request.FilterByAddOns.Any())
        {
            valueAddedServicePricingListViews = valueAddedServicePricingListViews
                .Where(s => request.FilterByAddOns.Contains(s.ValueAddedServiceId));
        }

        if (request.FilterByBillingFrequency != null && request.FilterByBillingFrequency.Any())
        {
            valueAddedServicePricingListViews = valueAddedServicePricingListViews
                .Where(s => request.FilterByBillingFrequency.Contains(s.BillingFrequency));
        }

        if (request.FilterByBillingType != null && request.FilterByBillingType.Any())
        {
            valueAddedServicePricingListViews = valueAddedServicePricingListViews
                .Where(s => request.FilterByBillingType.Contains(s.BillingType));
        }
        if (request.FilterByAddOnsStatus != null && request.FilterByAddOnsStatus.Any())
        {
            valueAddedServicePricingListViews = valueAddedServicePricingListViews
                .Where(s => request.FilterByAddOnsStatus.Contains(s.ValueAddedServiceStatus));
        }
        return valueAddedServicePricingListViews;
    }

    private static IQueryable<ValueAddedServicePricingListView> SortAddOnsPrices(ValueAddedServicePriceListRequest request, IQueryable<ValueAddedServicePricingListView> valueAddedServicePricingListViews)
    {
        SortType orderType;

        if (request.OrderType.ToLower() == SortType.asc.ToString())
            orderType = SortType.asc;
        else if (request.OrderType.ToLower() == SortType.desc.ToString())
            orderType = SortType.desc;
        else
            throw new ArgumentException("Invalid value for OrderType. Allowed values are 'asc' and 'desc'.", nameof(request));

        if (!string.IsNullOrEmpty(request.OrderFieldName))
            valueAddedServicePricingListViews = valueAddedServicePricingListViews.OrderBy(request.OrderFieldName, orderType);

        return valueAddedServicePricingListViews;
    }
    #endregion
    public async Task<ValueAddedServicePricingEntity?> GetAddOnsPriceByIdAsync(Guid id)
    {
        return await context.Set<ValueAddedServicePricingEntity>()
            .FirstAsync(up => up.Id == id);
    }
    public async Task UpdateVasPricesAsync(List<ValueAddedServicePricingEntity> vasPrices)
    {
        context.Set<ValueAddedServicePricingEntity>().UpdateRange(vasPrices);
        await context.SaveChangesAsync();
    }
}
