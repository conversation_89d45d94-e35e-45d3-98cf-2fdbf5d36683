﻿using FluentMigrator;

namespace Migrations.Tables.ProductInstances;

[Migration(2021_05_25_1420)]
public class ProductInstance_SetMissingCurrencies : ForwardOnlyMigration
{
    public override void Up()
    {
        Execute.Sql(@"
                DECLARE @SaudiCurrencies NVARCHAR(256) = N'[""SAR""]';
                DECLARE @EgyptCurrencies NVARCHAR(256) = N'[""EGP""]';

                UPDATE pi
                SET Metadata = JSON_MODIFY(JSON_MODIFY(pi.Metadata,'$.Currencies', JSON_QUERY(pi.Metadata,'$.currencies')), '$.currencies', NULL)
                FROM ProductInstances pi
                INNER JOIN Products p ON p.id=pi.ProductId
                WHERE p.Type='GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0

                UPDATE pi
                SET Metadata = JSON_MODIFY(pi.Metadata, '$.Currencies', JSON_Query(@SaudiCurrencies))
                FROM ProductInstances pi
                INNER JOIN Products p ON p.id = pi.ProductId
                WHERE p.Type = 'GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(lower(pi.Metadata), '$.merchantcountry') = 'sau' AND JSON_VALUE(pi.Metadata, '$.Currencies[0]') IS NULL

                UPDATE pi
                SET Metadata = JSON_MODIFY(pi.Metadata, '$.Currencies', JSON_Query(@EgyptCurrencies))
                FROM ProductInstances pi
                INNER JOIN Products p ON p.id = pi.ProductId
                WHERE p.Type = 'GWAY' AND pi.Metadata IS NOT NULL AND ISJSON(pi.Metadata) > 0 AND JSON_VALUE(lower(pi.Metadata), '$.merchantcountry') = 'egy' AND JSON_VALUE(pi.Metadata, '$.Currencies[0]') IS NULL");
    }
}
