﻿using Common.Entities;
using Common.Models.MccCategory;
using Geidea.Utils.DataAccess.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Repositories;
public interface IMccCategoryRepository : IRepository<Guid, MccCategory>
{
    Task<List<MccCategory>> GetMccCategoriesAsync();

    Task<List<MccCategoriesListResponse>> GetMccCategoriesList();
    public Task<bool> ExistsAsync(Guid categoryId);
}
