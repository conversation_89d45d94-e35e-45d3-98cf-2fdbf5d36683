﻿using AutoMapper;
using Common.Entities;
using Common.Enums;
using Common.Models.CommissionFees;
using Common.Repositories;
using Geidea.Utils.DataAccess.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Common.Data.Extensions;


namespace DataAccess.Repositories;
public class CommissionFeesRepository : AuditableRepository<Guid, CommissionFeesEntity>, ICommissionFeesRepository
{
    private readonly IMapper mapper;
    public CommissionFeesRepository(DataContext context, IHttpContextAccessor contextAccessor, IMapper mapper)
        : base(context, contextAccessor)
    {
        this.mapper = mapper;
    }
    public async Task<CommissionFeesEntity?> GetByIdAsync(Guid id)
    {
        return await context.Set<CommissionFeesEntity>().FindAsync(id);
    }
    public async Task<GetCommissionFeesListResponse> GetCommissionFeesList(GetCommissionFeesListRequest request)
    {
        var CommissionFeesQuery = GetAllCommissionFees();
        CommissionFeesQuery = SearchCommissionFees(request, CommissionFeesQuery);
        CommissionFeesQuery = FilterCommissionFees(request, CommissionFeesQuery);
        CommissionFeesQuery = SortCommissionFees(request, CommissionFeesQuery);

        int TotalCount = await CommissionFeesQuery.CountAsync();
        int TotalPages = (int)Math.Ceiling((double)TotalCount / request.Size);

        var CommissionFeesList = await CommissionFeesQuery.Skip((request.Page - 1) * request.Size)
                                                          .Take(request.Size)
                                                          .ToArrayAsync();
        return new GetCommissionFeesListResponse
        {
            CommissionFeesList = CommissionFeesList,
            TotalPages = TotalPages,
            TotalCount = TotalCount
        };
    }
    public IQueryable<CommissionFeesListResponse> GetAllCommissionFees()
    {
        var CommissionFeesList = context.Set<CommissionFeesEntity>().AsNoTracking();
        return mapper.ProjectTo<CommissionFeesListResponse>(CommissionFeesList);
    }
    public static IQueryable<CommissionFeesListResponse> SearchCommissionFees(GetCommissionFeesListRequest request, IQueryable<CommissionFeesListResponse> CommissionFees)
    {
        var SearchTerms = request.SearchTerms.Where(s => !string.IsNullOrEmpty(s.Value)).ToList();
        if (SearchTerms.Count != 0)
        {
            foreach (var term in SearchTerms)
            {
                switch (term.Key)
                {
                    case CommissionFeesSearchKey.All:
                        CommissionFees = CommissionFees.Where(c =>
                            (c.Code != null && c.Code.Contains(term.Value)) ||
                            (c.Name != null && c.Name.Contains(term.Value)));
                        break;

                    case CommissionFeesSearchKey.Id:
                        CommissionFees = CommissionFees.Where(c => c.Code != null && c.Code.Contains(term.Value));
                        break;

                    case CommissionFeesSearchKey.Name:
                        CommissionFees = CommissionFees.Where(c => c.Name != null && c.Name.Contains(term.Value));
                        break;
                }
            }
        }
        return CommissionFees;
    }
    public static IQueryable<CommissionFeesListResponse> FilterCommissionFees(GetCommissionFeesListRequest request, IQueryable<CommissionFeesListResponse> CommissionFees)
    {
        if (request.FilterByStatus != null && request.FilterByStatus.Any())
            CommissionFees = CommissionFees.Where(c => request.FilterByStatus.Contains(c.Status));

        return CommissionFees;
    }
    public static IQueryable<CommissionFeesListResponse> SortCommissionFees(GetCommissionFeesListRequest request, IQueryable<CommissionFeesListResponse> CommissionFees)
    {
        SortType orderType;

        if (request.OrderType.ToLower() == SortType.asc.ToString())
            orderType = SortType.asc;
        else if (request.OrderType.ToLower() == SortType.desc.ToString())
            orderType = SortType.desc;
        else
            throw new ArgumentException("Invalid value for OrderType. Allowed values are 'asc' and 'desc'.", nameof(request));

        if (!string.IsNullOrEmpty(request.OrderFieldName))
            CommissionFees = CommissionFees.OrderBy(request.OrderFieldName, orderType);

        return CommissionFees;
    }
    public async Task<List<CommissionFees>> GetCommissionFees()
    {
        var Result = await context.Set<CommissionFeesEntity>()
                                  .AsNoTracking()
                                  .Select(c => new CommissionFees
                                  {
                                      Id = c.Id,
                                      Name = c.Name,
                                      Code = c.Code,
                                  }).ToListAsync();

        return Result;
    }

}
