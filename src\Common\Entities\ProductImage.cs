﻿using Geidea.Utils.DataAccess.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Entities;
public class ProductImage : AuditableEntity<Guid>
{
    public string? Language { get; set; }
    public int? DisplayOrder { get; set; }
    public Guid ImageId { get; set; }
    public Guid ProductId { get; set; } // Foreign key to the Product entity
    // Navigation property back to the Product
    public ProductEntity? Product { get; set; }

}



